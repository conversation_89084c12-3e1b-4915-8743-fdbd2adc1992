import { NextRequest, NextResponse } from 'next/server';
import { automaticGTTService } from '@/lib/automatic-gtt-service';
import { weeklyHighSignalDetector } from '@/lib/weekly-high-signal-detector';

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 Testing complete multi-order workflow...');

    const testResults = {
      timestamp: new Date().toISOString(),
      steps: [] as any[]
    };

    // Step 1: Signal Detection
    console.log('📊 Step 1: Testing Weekly High Signal Detection...');
    const signals = await weeklyHighSignalDetector.triggerManualScan();
    
    testResults.steps.push({
      step: 1,
      name: 'Weekly High Signal Detection',
      status: signals.length > 0 ? 'PASS' : 'FAIL',
      data: {
        totalSignals: signals.length,
        strongSignals: signals.filter(s => s.signalStrength === 'STRONG').length,
        moderateSignals: signals.filter(s => s.signalStrength === 'MODERATE').length,
        sampleSignals: signals.slice(0, 3).map(s => ({
          symbol: s.symbol,
          strength: s.signalStrength,
          triggerPrice: s.suggestedBuyPrice,
          quantity: s.suggestedGTTQuantity
        }))
      }
    });

    // Step 2: Order Storage and Retrieval
    console.log('📋 Step 2: Testing Order Storage and Retrieval...');
    const storedOrders = automaticGTTService.getAllOrders();
    
    testResults.steps.push({
      step: 2,
      name: 'Order Storage and Retrieval',
      status: 'PASS',
      data: {
        totalOrders: storedOrders.length,
        signalOrders: storedOrders.filter(o => o.source === 'SIGNAL').length,
        pendingOrders: storedOrders.filter(o => o.status === 'PENDING').length,
        autoCreatedOrders: storedOrders.filter(o => o.autoCreated).length
      }
    });

    // Step 3: Duplicate Prevention
    console.log('🔍 Step 3: Testing Duplicate Prevention...');
    const existingSymbols = new Set(
      storedOrders
        .filter(order => order.source === 'SIGNAL' && order.status === 'PENDING')
        .map(order => order.symbol)
    );
    
    const eligibleSignals = signals.filter(signal => !existingSymbols.has(signal.symbol));
    const duplicatePreventionWorking = existingSymbols.size > 0 && eligibleSignals.length < signals.length;
    
    testResults.steps.push({
      step: 3,
      name: 'Duplicate Prevention',
      status: duplicatePreventionWorking ? 'PASS' : 'WARNING',
      data: {
        totalSignals: signals.length,
        existingOrderSymbols: existingSymbols.size,
        eligibleForNewOrders: eligibleSignals.length,
        duplicatePreventionActive: duplicatePreventionWorking
      }
    });

    // Step 4: Order Creation Process
    console.log('🤖 Step 4: Testing Order Creation Process...');
    let orderCreationTest = { status: 'SKIP', reason: 'No eligible signals' };
    
    if (eligibleSignals.length > 0) {
      try {
        const testSymbol = eligibleSignals[0].symbol;
        const testOrder = await automaticGTTService.testCreateOrder(testSymbol);
        
        orderCreationTest = {
          status: testOrder ? 'PASS' : 'FAIL',
          reason: testOrder ? 'Order created successfully' : 'Order creation failed',
          data: testOrder ? {
            symbol: testOrder.symbol,
            orderId: testOrder.id,
            triggerPrice: testOrder.triggerPrice,
            quantity: testOrder.quantity,
            autoCreated: testOrder.autoCreated
          } : null
        };
      } catch (error) {
        orderCreationTest = {
          status: 'ERROR',
          reason: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    }
    
    testResults.steps.push({
      step: 4,
      name: 'Order Creation Process',
      ...orderCreationTest
    });

    // Step 5: UI Display Validation
    console.log('🖥️ Step 5: Testing UI Display Logic...');
    const requestUrl = new URL(request.url);
    const baseUrl = `${requestUrl.protocol}//${requestUrl.host}`;
    
    try {
      const displayResponse = await fetch(`${baseUrl}/api/check-order-display`);
      const displayData = await displayResponse.json();
      
      testResults.steps.push({
        step: 5,
        name: 'UI Display Logic',
        status: displayData.success && displayData.data.signalOrders > 0 ? 'PASS' : 'FAIL',
        data: {
          totalDisplayOrders: displayData.data?.totalAutoOrders || 0,
          signalOrdersForDisplay: displayData.data?.signalOrders || 0,
          conversionSuccessful: displayData.data?.convertedOrders === displayData.data?.totalAutoOrders,
          issues: displayData.data?.issues || []
        }
      });
    } catch (error) {
      testResults.steps.push({
        step: 5,
        name: 'UI Display Logic',
        status: 'ERROR',
        error: error instanceof Error ? error.message : 'Display test failed'
      });
    }

    // Step 6: End-to-End Validation
    console.log('🎯 Step 6: End-to-End Validation...');
    const finalOrders = automaticGTTService.getAllOrders();
    const finalSignalOrders = finalOrders.filter(o => o.source === 'SIGNAL');
    
    const endToEndValidation = {
      signalsDetected: signals.length > 0,
      ordersStored: finalOrders.length > 0,
      signalOrdersPresent: finalSignalOrders.length > 0,
      multipleOrdersCreated: finalSignalOrders.length > 1,
      duplicatePreventionWorking: duplicatePreventionWorking,
      orderPropertiesValid: finalSignalOrders.every(o => 
        o.id && o.symbol && o.triggerPrice > 0 && o.quantity > 0 && o.source === 'SIGNAL'
      )
    };
    
    const validationsPassed = Object.values(endToEndValidation).filter(Boolean).length;
    const totalValidations = Object.keys(endToEndValidation).length;
    
    testResults.steps.push({
      step: 6,
      name: 'End-to-End Validation',
      status: validationsPassed >= totalValidations - 1 ? 'PASS' : 'FAIL',
      data: {
        validationsPassed,
        totalValidations,
        validations: endToEndValidation,
        finalOrderCount: finalOrders.length,
        finalSignalOrderCount: finalSignalOrders.length
      }
    });

    // Calculate overall results
    const passedSteps = testResults.steps.filter(s => s.status === 'PASS').length;
    const totalSteps = testResults.steps.length;
    const overallStatus = passedSteps >= totalSteps - 1 ? 'SUCCESS' : 'PARTIAL_SUCCESS';

    const summary = {
      overallStatus,
      passedSteps,
      totalSteps,
      successRate: `${Math.round((passedSteps / totalSteps) * 100)}%`,
      multiOrderWorkflowWorking: finalSignalOrders.length > 1,
      currentOrderCount: finalSignalOrders.length,
      expectedBehavior: 'Multiple BOH eligible stocks should generate multiple visible GTT orders',
      actualBehavior: `${finalSignalOrders.length} GTT signal orders are visible and properly stored`
    };

    console.log(`🎉 Complete workflow test: ${summary.successRate} success rate`);
    console.log(`📊 Final result: ${finalSignalOrders.length} GTT signal orders created and displayed`);

    return NextResponse.json({
      success: true,
      message: 'Complete multi-order workflow test completed',
      summary,
      testResults
    });

  } catch (error) {
    console.error('❌ Complete workflow test failed:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Workflow test failed',
        message: 'Complete workflow test failed'
      },
      { status: 500 }
    );
  }
}
