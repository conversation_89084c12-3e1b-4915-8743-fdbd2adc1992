globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/api/network-test/route"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/components/layout/dashboard-layout.tsx":{"*":{"id":"(ssr)/./src/components/layout/dashboard-layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers/BackgroundDataProvider.tsx":{"*":{"id":"(ssr)/./src/components/providers/BackgroundDataProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"*":{"id":"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"*":{"id":"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/boh-eligible/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/boh-eligible/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/stocks/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/stocks/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/weekly-high/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/weekly-high/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\src\\components\\layout\\dashboard-layout.tsx":{"id":"(app-pages-browser)/./src/components/layout/dashboard-layout.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\src\\components\\providers\\BackgroundDataProvider.tsx":{"id":"(app-pages-browser)/./src/components/providers/BackgroundDataProvider.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\src\\app\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\node_modules\\next\\dist\\client\\components\\builtin\\global-error.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\node_modules\\next\\dist\\esm\\client\\components\\builtin\\global-error.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\node_modules\\next\\dist\\lib\\metadata\\generate\\icon-mark.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\node_modules\\next\\dist\\esm\\lib\\metadata\\generate\\icon-mark.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\node_modules\\next\\dist\\next-devtools\\userspace\\app\\segment-explorer-node.js":{"id":"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\node_modules\\next\\dist\\esm\\next-devtools\\userspace\\app\\segment-explorer-node.js":{"id":"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\src\\app\\dashboard\\boh-eligible\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/boh-eligible/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\src\\app\\dashboard\\stocks\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/stocks/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\src\\app\\dashboard\\weekly-high\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/weekly-high/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\src\\":[],"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Documents\\Niveshtor\\niveshtor-trading\\src\\app\\api\\network-test\\route":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/dashboard-layout.tsx":{"*":{"id":"(rsc)/./src/components/layout/dashboard-layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers/BackgroundDataProvider.tsx":{"*":{"id":"(rsc)/./src/components/providers/BackgroundDataProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"*":{"id":"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"*":{"id":"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/boh-eligible/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/boh-eligible/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/stocks/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/stocks/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/weekly-high/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/weekly-high/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}