// Mock data service to bypass database issues during development

export const mockBrokerBalance = {
  id: 'mock-balance-1',
  availableCash: 150000,
  marginUsed: 75000,
  marginAvailable: 225000,
  totalBalance: 300000,
  lastSyncAt: new Date().toISOString()
};

export const mockFundAllocation = {
  id: 'mock-allocation-1',
  strategyName: 'DARVAS_BOX',
  totalAllocatedAmount: 100000,
  maxPerStock: 10000,
  maxPerTrade: 2000,
  stockAllocations: [
    {
      id: 'stock-1',
      symbol: 'RELIANCE',
      allocatedAmount: 8000,
      usedAmount: 4000,
      tradesCount: 2
    },
    {
      id: 'stock-2',
      symbol: 'TCS',
      allocatedAmount: 6000,
      usedAmount: 2000,
      tradesCount: 1
    }
  ]
};

export const mockApiResponse = (data: any, success = true) => ({
  success,
  data,
  message: success ? 'Success' : 'Error',
  note: 'Using mock data for development'
});
