{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/app/dashboard/gtt-orders/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport {\n  Clock,\n  Plus,\n  Edit,\n  Trash2,\n  CheckCircle,\n  XCircle,\n  AlertCircle,\n  TrendingUp,\n  TrendingDown\n} from 'lucide-react';\nimport { formatCurrency, formatDateTime } from '@/lib/utils';\n\ninterface GTTOrder {\n  id: string;\n  symbol: string;\n  name: string;\n  orderType: 'BUY' | 'SELL';\n  triggerPrice: number;\n  quantity: number;\n  price?: number;\n  stopLoss?: number;\n  target?: number;\n  status: 'PENDING' | 'TRIGGERED' | 'CANCELLED' | 'EXPIRED';\n  validTill: Date;\n  createdAt: Date;\n}\n\nexport default function GTTOrdersPage() {\n  const [orders, setOrders] = useState<GTTOrder[]>([\n    {\n      id: '1',\n      symbol: 'RELIANCE',\n      name: 'Reliance Industries Ltd',\n      orderType: 'BUY',\n      triggerPrice: 2400,\n      quantity: 10,\n      price: 2405,\n      stopLoss: 2300,\n      target: 2600,\n      status: 'PENDING',\n      validTill: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),\n      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)\n    },\n    {\n      id: '2',\n      symbol: 'TCS',\n      name: 'Tata Consultancy Services',\n      orderType: 'SELL',\n      triggerPrice: 3200,\n      quantity: 5,\n      price: 3195,\n      stopLoss: 3300,\n      target: 3000,\n      status: 'TRIGGERED',\n      validTill: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),\n      createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)\n    },\n    {\n      id: '3',\n      symbol: 'INFY',\n      name: 'Infosys Limited',\n      orderType: 'BUY',\n      triggerPrice: 1450,\n      quantity: 15,\n      price: 1455,\n      status: 'EXPIRED',\n      validTill: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),\n      createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000)\n    }\n  ]);\n\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [filterStatus, setFilterStatus] = useState<'ALL' | 'PENDING' | 'TRIGGERED' | 'CANCELLED' | 'EXPIRED'>('ALL');\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'PENDING': return 'text-yellow-600 bg-yellow-100';\n      case 'TRIGGERED': return 'text-green-600 bg-green-100';\n      case 'CANCELLED': return 'text-red-600 bg-red-100';\n      case 'EXPIRED': return 'text-gray-600 bg-gray-100';\n      default: return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'PENDING': return <Clock className=\"h-4 w-4\" />;\n      case 'TRIGGERED': return <CheckCircle className=\"h-4 w-4\" />;\n      case 'CANCELLED': return <XCircle className=\"h-4 w-4\" />;\n      case 'EXPIRED': return <AlertCircle className=\"h-4 w-4\" />;\n      default: return <Clock className=\"h-4 w-4\" />;\n    }\n  };\n\n  const filteredOrders = filterStatus === 'ALL'\n    ? orders\n    : orders.filter(order => order.status === filterStatus);\n\n  const OrderRow = ({ order }: { order: GTTOrder }) => (\n    <div className=\"flex items-center justify-between p-4 hover:bg-gray-50 border-b border-gray-100\">\n      <div className=\"flex-1\">\n        <div className=\"flex items-center space-x-3\">\n          <div className={`p-2 rounded-full ${order.orderType === 'BUY' ? 'bg-green-100' : 'bg-red-100'}`}>\n            {order.orderType === 'BUY' ? (\n              <TrendingUp className=\"h-4 w-4 text-green-600\" />\n            ) : (\n              <TrendingDown className=\"h-4 w-4 text-red-600\" />\n            )}\n          </div>\n          <div>\n            <h4 className=\"font-medium text-gray-900\">{order.symbol}</h4>\n            <p className=\"text-sm text-gray-600 truncate max-w-xs\">{order.name}</p>\n          </div>\n          <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 ${getStatusColor(order.status)}`}>\n            {getStatusIcon(order.status)}\n            <span>{order.status}</span>\n          </span>\n        </div>\n      </div>\n\n      <div className=\"flex items-center space-x-6\">\n        <div className=\"text-right\">\n          <p className=\"font-medium text-gray-900\">Trigger: {formatCurrency(order.triggerPrice)}</p>\n          <p className=\"text-sm text-gray-600\">Qty: {order.quantity}</p>\n        </div>\n\n        <div className=\"text-right\">\n          {order.price && (\n            <p className=\"text-sm text-gray-600\">Price: {formatCurrency(order.price)}</p>\n          )}\n          {order.stopLoss && (\n            <p className=\"text-sm text-red-600\">SL: {formatCurrency(order.stopLoss)}</p>\n          )}\n          {order.target && (\n            <p className=\"text-sm text-green-600\">Target: {formatCurrency(order.target)}</p>\n          )}\n        </div>\n\n        <div className=\"text-right text-sm text-gray-600\">\n          <p>Valid till:</p>\n          <p>{formatDateTime(order.validTill)}</p>\n        </div>\n\n        <div className=\"flex items-center space-x-2\">\n          <button className=\"p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors\">\n            <Edit className=\"h-4 w-4\" />\n          </button>\n          <button className=\"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors\">\n            <Trash2 className=\"h-4 w-4\" />\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n\n  const CreateOrderModal = () => (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg p-6 w-full max-w-md\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900\">Create GTT Order</h3>\n          <button\n            onClick={() => setShowCreateModal(false)}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <XCircle className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        <form className=\"space-y-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">Stock Symbol</label>\n            <input\n              type=\"text\"\n              placeholder=\"e.g., RELIANCE\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            />\n          </div>\n\n          <div className=\"grid grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">Order Type</label>\n              <select className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                <option value=\"BUY\">BUY</option>\n                <option value=\"SELL\">SELL</option>\n              </select>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">Quantity</label>\n              <input\n                type=\"number\"\n                placeholder=\"10\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n            </div>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">Trigger Price</label>\n            <input\n              type=\"number\"\n              step=\"0.01\"\n              placeholder=\"2400.00\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">Limit Price (Optional)</label>\n            <input\n              type=\"number\"\n              step=\"0.01\"\n              placeholder=\"2405.00\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            />\n          </div>\n\n          <div className=\"grid grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">Stop Loss (Optional)</label>\n              <input\n                type=\"number\"\n                step=\"0.01\"\n                placeholder=\"2300.00\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">Target (Optional)</label>\n              <input\n                type=\"number\"\n                step=\"0.01\"\n                placeholder=\"2600.00\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n            </div>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">Valid Till</label>\n            <input\n              type=\"datetime-local\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            />\n          </div>\n\n          <div className=\"flex space-x-3 pt-4\">\n            <button\n              type=\"button\"\n              onClick={() => setShowCreateModal(false)}\n              className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              className=\"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Create Order\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;AAdA;;;;AA+Be,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;QAC/C;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,WAAW;YACX,cAAc;YACd,UAAU;YACV,OAAO;YACP,UAAU;YACV,QAAQ;YACR,QAAQ;YACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;YACpD,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;QACtD;QACA;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,WAAW;YACX,cAAc;YACd,UAAU;YACV,OAAO;YACP,UAAU;YACV,QAAQ;YACR,QAAQ;YACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;YACpD,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;QACtD;QACA;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,WAAW;YACX,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;YACpD,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK;QACvD;KACD;IAED,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6D;IAE5G,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAW,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YACxC,KAAK;gBAAa,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChD,KAAK;gBAAa,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5C,KAAK;gBAAW,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAC9C;gBAAS,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QACnC;IACF;IAEA,MAAM,iBAAiB,iBAAiB,QACpC,SACA,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;IAE5C,MAAM,WAAW;YAAC,EAAE,KAAK,EAAuB;6BAC9C,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAW,AAAC,oBAA6E,OAA1D,MAAM,SAAS,KAAK,QAAQ,iBAAiB;0CAC9E,MAAM,SAAS,KAAK,sBACnB,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;yDAEtB,6LAAC,yNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;0CAG5B,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B,MAAM,MAAM;;;;;;kDACvD,6LAAC;wCAAE,WAAU;kDAA2C,MAAM,IAAI;;;;;;;;;;;;0CAEpE,6LAAC;gCAAK,WAAW,AAAC,0EAAsG,OAA7B,eAAe,MAAM,MAAM;;oCACnH,cAAc,MAAM,MAAM;kDAC3B,6LAAC;kDAAM,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;8BAKzB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;;wCAA4B;wCAAU,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,YAAY;;;;;;;8CACpF,6LAAC;oCAAE,WAAU;;wCAAwB;wCAAM,MAAM,QAAQ;;;;;;;;;;;;;sCAG3D,6LAAC;4BAAI,WAAU;;gCACZ,MAAM,KAAK,kBACV,6LAAC;oCAAE,WAAU;;wCAAwB;wCAAQ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,KAAK;;;;;;;gCAExE,MAAM,QAAQ,kBACb,6LAAC;oCAAE,WAAU;;wCAAuB;wCAAK,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,QAAQ;;;;;;;gCAEvE,MAAM,MAAM,kBACX,6LAAC;oCAAE,WAAU;;wCAAyB;wCAAS,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,MAAM;;;;;;;;;;;;;sCAI9E,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAE;;;;;;8CACH,6LAAC;8CAAG,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,SAAS;;;;;;;;;;;;sCAGpC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAO,WAAU;8CAChB,cAAA,6LAAC,8MAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,6LAAC;oCAAO,WAAU;8CAChB,cAAA,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAO5B,MAAM,mBAAmB,kBACvB,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCACC,SAAS,IAAM,mBAAmB;gCAClC,WAAU;0CAEV,cAAA,6LAAC,+MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIvB,6LAAC;wBAAK,WAAU;;0CACd,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;0CAId,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAChE,6LAAC;gDAAO,WAAU;;kEAChB,6LAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,6LAAC;wDAAO,OAAM;kEAAO;;;;;;;;;;;;;;;;;;kDAGzB,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAChE,6LAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,WAAU;;;;;;;;;;;;;;;;;;0CAKhB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,6LAAC;wCACC,MAAK;wCACL,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;0CAId,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,6LAAC;wCACC,MAAK;wCACL,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;0CAId,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAChE,6LAAC;gDACC,MAAK;gDACL,MAAK;gDACL,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAGd,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAChE,6LAAC;gDACC,MAAK;gDACL,MAAK;gDACL,aAAY;gDACZ,WAAU;;;;;;;;;;;;;;;;;;0CAKhB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,6LAAC;wCACC,MAAK;wCACL,WAAU;;;;;;;;;;;;0CAId,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,mBAAmB;wCAClC,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOV;GA5OqB;KAAA", "debugId": null}}, {"offset": {"line": 697, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/square-pen.js", "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/node_modules/lucide-react/src/icons/square-pen.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7', key: '1m0v6g' }],\n  [\n    'path',\n    {\n      d: 'M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z',\n      key: 'ohrbg2',\n    },\n  ],\n];\n\n/**\n * @component @name SquarePen\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgM0g1YTIgMiAwIDAgMC0yIDJ2MTRhMiAyIDAgMCAwIDIgMmgxNGEyIDIgMCAwIDAgMi0ydi03IiAvPgogIDxwYXRoIGQ9Ik0xOC4zNzUgMi42MjVhMSAxIDAgMCAxIDMgM2wtOS4wMTMgOS4wMTRhMiAyIDAgMCAxLS44NTMuNTA1bC0yLjg3My44NGEuNS41IDAgMCAxLS42Mi0uNjJsLjg0LTIuODczYTIgMiAwIDAgMSAuNTA2LS44NTJ6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/square-pen\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SquarePen = createLucideIcon('square-pen', __iconNode);\n\nexport default SquarePen;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3F;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 739, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/trash-2.js", "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/node_modules/lucide-react/src/icons/trash-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 6h18', key: 'd0wm0j' }],\n  ['path', { d: 'M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6', key: '4alrt4' }],\n  ['path', { d: 'M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2', key: 'v07s0e' }],\n  ['line', { x1: '10', x2: '10', y1: '11', y2: '17', key: '1uufr5' }],\n  ['line', { x1: '14', x2: '14', y1: '11', y2: '17', key: 'xtxkd' }],\n];\n\n/**\n * @component @name Trash2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyA2aDE4IiAvPgogIDxwYXRoIGQ9Ik0xOSA2djE0YzAgMS0xIDItMiAySDdjLTEgMC0yLTEtMi0yVjYiIC8+CiAgPHBhdGggZD0iTTggNlY0YzAtMSAxLTIgMi0yaDRjMSAwIDIgMSAyIDJ2MiIgLz4KICA8bGluZSB4MT0iMTAiIHgyPSIxMCIgeTE9IjExIiB5Mj0iMTciIC8+CiAgPGxpbmUgeDE9IjE0IiB4Mj0iMTQiIHkxPSIxMSIgeTI9IjE3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/trash-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Trash2 = createLucideIcon('trash-2', __iconNode);\n\nexport default Trash2;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAyC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACtE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAsC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACnE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClE;QAAC,MAAA,CAAQ;QAAA,CAAA;YAAE,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,IAAM,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAS,CAAA;KAAA;CACnE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 808, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/circle-check-big.js", "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/node_modules/lucide-react/src/icons/circle-check-big.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M21.801 10A10 10 0 1 1 17 3.335', key: 'yps3ct' }],\n  ['path', { d: 'm9 11 3 3L22 4', key: '1pflzl' }],\n];\n\n/**\n * @component @name CircleCheckBig\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEuODAxIDEwQTEwIDEwIDAgMSAxIDE3IDMuMzM1IiAvPgogIDxwYXRoIGQ9Im05IDExIDMgM0wyMiA0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/circle-check-big\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleCheckBig = createLucideIcon('circle-check-big', __iconNode);\n\nexport default CircleCheckBig;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAmC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAChE;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACjD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,cAAA,CAAiB,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAoB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 850, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/circle-x.js", "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/node_modules/lucide-react/src/icons/circle-x.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['path', { d: 'm15 9-6 6', key: '1uzhvr' }],\n  ['path', { d: 'm9 9 6 6', key: 'z0biqf' }],\n];\n\n/**\n * @component @name CircleX\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJtMTUgOS02IDYiIC8+CiAgPHBhdGggZD0ibTkgOSA2IDYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-x\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleX = createLucideIcon('circle-x', __iconNode);\n\nexport default CircleX;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAU,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,AAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 901, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/circle-alert.js", "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/node_modules/lucide-react/src/icons/circle-alert.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['line', { x1: '12', x2: '12', y1: '8', y2: '12', key: '1pkeuh' }],\n  ['line', { x1: '12', x2: '12.01', y1: '16', y2: '16', key: '4dfq90' }],\n];\n\n/**\n * @component @name CircleAlert\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjgiIHkyPSIxMiIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMi4wMSIgeTE9IjE2IiB5Mj0iMTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-alert\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleAlert = createLucideIcon('circle-alert', __iconNode);\n\nexport default CircleAlert;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjE;QAAC,MAAA,CAAQ;QAAA,CAAA;YAAE,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,IAAM,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACvE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 958, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/trending-down.js", "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/node_modules/lucide-react/src/icons/trending-down.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 17h6v-6', key: 't6n2it' }],\n  ['path', { d: 'm22 17-8.5-8.5-5 5L2 7', key: 'x473p' }],\n];\n\n/**\n * @component @name TrendingDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMTdoNnYtNiIgLz4KICA8cGF0aCBkPSJtMjIgMTctOC41LTguNS01IDVMMiA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/trending-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrendingDown = createLucideIcon('trending-down', __iconNode);\n\nexport default TrendingDown;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC5C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAS,CAAA;KAAA;CACxD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}]}