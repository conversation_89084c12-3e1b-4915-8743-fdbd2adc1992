@echo off
echo ========================================
echo    Niveshtor Trading Platform Setup
echo ========================================
echo.

echo [1/4] Installing dependencies...
call npm install
if errorlevel 1 (
    echo Error: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo [2/4] Setting up database...
call npx prisma generate
if errorlevel 1 (
    echo Error: Failed to generate Prisma client
    pause
    exit /b 1
)

call npx prisma db push
if errorlevel 1 (
    echo Error: Failed to setup database
    pause
    exit /b 1
)

echo.
echo [3/4] Setup complete! Starting application...
echo.
echo Frontend will be available at: http://localhost:3000
echo Backend API will be available at: http://localhost:8000
echo.
echo [4/4] Starting servers...

start "Python Backend" cmd /k "cd python-backend && python main.py"
timeout /t 3 /nobreak > nul
start "Next.js Frontend" cmd /k "npm run dev"

echo.
echo ========================================
echo   Niveshtor Trading Platform Started
echo ========================================
echo.
echo Press any key to exit...
pause > nul
