{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/app/api/gtt/create-signal-orders/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\ninterface WeeklyHighStock {\n  symbol: string;\n  name: string;\n  currentPrice: number;\n  lastWeekHighest: number;\n  suggestedBuyPrice: number;\n  percentDifference: number;\n  suggestedGTTQuantity: number;\n  isBOHEligible: boolean;\n  inHoldings: boolean;\n}\n\ninterface GTTOrderRequest {\n  symbol: string;\n  name: string;\n  orderType: 'BUY' | 'SELL';\n  triggerPrice: number;\n  quantity: number;\n  source: 'SIGNAL' | 'HOLDING' | 'SALE';\n}\n\n// Generate mock OHLC data for calculating last week's high\nconst generateMockOHLCData = (currentPrice: number) => {\n  const data = [];\n  let price = currentPrice * 0.95; // Start 5% below current price\n  \n  for (let i = 0; i < 7; i++) {\n    const open = price;\n    const high = price * (1 + Math.random() * 0.08); // Up to 8% higher\n    const low = price * (1 - Math.random() * 0.05); // Up to 5% lower\n    const close = low + Math.random() * (high - low);\n    \n    data.push({ open, high, low, close });\n    price = close;\n  }\n  \n  return data;\n};\n\nexport async function POST(request: NextRequest) {\n  try {\n    console.log('🚀 API: Creating GTT orders for Weekly High Signal stocks...');\n\n    // Get the correct base URL (use current request URL to determine port)\n    const requestUrl = new URL(request.url);\n    const baseUrl = `${requestUrl.protocol}//${requestUrl.host}`;\n\n    console.log(`📡 Using base URL: ${baseUrl}`);\n\n    // Fetch BOH eligible stocks from the Nifty 200 API\n    const stocksResponse = await fetch(`${baseUrl}/api/stocks/nifty200?batchIndex=0&batchSize=200`);\n    const stocksData = await stocksResponse.json();\n\n    console.log(`📊 API Response: success=${stocksData.success}, stocks=${stocksData.data?.stocks?.length || 0}`);\n\n    if (!stocksData.success) {\n      throw new Error(stocksData.error || 'Failed to fetch stock data');\n    }\n\n    // Filter for BOH eligible stocks with valid prices (same logic as Weekly High Signal page)\n    const bohEligibleStocks = stocksData.data.stocks.filter((stock: any) => {\n      const isBOHEligible = stock.isBOHEligible === true;\n      const hasValidPrice = stock.price > 0;\n\n      console.log(`🔍 ${stock.symbol}: BOH=${isBOHEligible}, Price=₹${stock.price}, Valid=${hasValidPrice}`);\n\n      return isBOHEligible && hasValidPrice;\n    });\n\n    console.log(`✅ Found ${bohEligibleStocks.length} BOH eligible stocks with valid prices`);\n\n    // Calculate weekly high data for each stock (same logic as Weekly High Signal page)\n    const weeklyHighStocks: WeeklyHighStock[] = bohEligibleStocks.map((stock: any) => {\n      // Generate mock OHLC data for last week (same as Weekly High Signal page)\n      const ohlcData = generateMockOHLCData(stock.price);\n      const lastWeekHighest = Math.max(...ohlcData.map(d => d.high));\n      const suggestedBuyPrice = lastWeekHighest + 0.05;\n      const percentDifference = ((stock.price - suggestedBuyPrice) / suggestedBuyPrice) * 100;\n      const suggestedGTTQuantity = Math.floor(2000 / suggestedBuyPrice);\n\n      console.log(`📈 ${stock.symbol}: Current=₹${stock.price}, LastWeekHigh=₹${lastWeekHighest.toFixed(2)}, SuggestedBuy=₹${suggestedBuyPrice.toFixed(2)}, Qty=${suggestedGTTQuantity}`);\n\n      return {\n        symbol: stock.symbol,\n        name: stock.name,\n        currentPrice: stock.price,\n        lastWeekHighest,\n        suggestedBuyPrice,\n        percentDifference,\n        suggestedGTTQuantity,\n        isBOHEligible: stock.isBOHEligible,\n        inHoldings: stock.inHoldings || false\n      };\n    });\n\n    // Filter stocks that are valid for GTT orders (less restrictive to match Weekly High Signal page)\n    const validStocks = weeklyHighStocks.filter(stock => {\n      const hasValidQuantity = stock.suggestedGTTQuantity > 0;\n      const hasValidPrice = stock.suggestedBuyPrice > 0;\n      const isAffordable = stock.suggestedBuyPrice <= 3000; // Increased limit to be less restrictive\n\n      const isValid = hasValidQuantity && hasValidPrice && isAffordable;\n\n      if (!isValid) {\n        console.log(`❌ ${stock.symbol} filtered out: Qty=${stock.suggestedGTTQuantity}, Price=₹${stock.suggestedBuyPrice.toFixed(2)}, Affordable=${isAffordable}`);\n      }\n\n      return isValid;\n    });\n\n    console.log(`📊 ${validStocks.length} stocks are valid for GTT order creation out of ${weeklyHighStocks.length} BOH eligible stocks`);\n\n    // Create GTT order requests\n    const gttOrderRequests: GTTOrderRequest[] = validStocks.map(stock => {\n      const order = {\n        symbol: stock.symbol,\n        name: stock.name,\n        orderType: 'BUY' as const,\n        triggerPrice: stock.suggestedBuyPrice,\n        quantity: stock.suggestedGTTQuantity,\n        source: 'SIGNAL' as const\n      };\n\n      console.log(`✅ Creating GTT order: ${stock.symbol} - Trigger: ₹${stock.suggestedBuyPrice.toFixed(2)}, Qty: ${stock.suggestedGTTQuantity}`);\n      return order;\n    });\n\n    console.log(`🎯 Final result: ${gttOrderRequests.length} GTT orders ready to create`);\n\n    // Calculate stats\n    const stats = gttOrderRequests.length > 0 ? {\n      avgTriggerPrice: gttOrderRequests.reduce((sum, order) => sum + order.triggerPrice, 0) / gttOrderRequests.length,\n      totalQuantity: gttOrderRequests.reduce((sum, order) => sum + order.quantity, 0),\n      totalValue: gttOrderRequests.reduce((sum, order) => sum + (order.triggerPrice * order.quantity), 0)\n    } : {\n      avgTriggerPrice: 0,\n      totalQuantity: 0,\n      totalValue: 0\n    };\n\n    // Return the orders data for the frontend to process\n    return NextResponse.json({\n      success: true,\n      message: `Found ${gttOrderRequests.length} BOH eligible stocks for GTT orders`,\n      data: {\n        totalBOHStocks: bohEligibleStocks.length,\n        validForGTT: gttOrderRequests.length,\n        orders: gttOrderRequests,\n        stats\n      }\n    });\n\n  } catch (error) {\n    console.error('❌ Error creating signal orders:', error);\n    return NextResponse.json(\n      { \n        success: false, \n        error: error instanceof Error ? error.message : 'Failed to create signal orders' \n      },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function GET() {\n  return NextResponse.json({\n    success: true,\n    message: 'GTT Signal Orders API is working',\n    endpoints: {\n      POST: 'Create GTT orders for all BOH eligible stocks from Weekly High Signal'\n    }\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAuBA,2DAA2D;AAC3D,MAAM,uBAAuB,CAAC;IAC5B,MAAM,OAAO,EAAE;IACf,IAAI,QAAQ,eAAe,MAAM,+BAA+B;IAEhE,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC1B,MAAM,OAAO;QACb,MAAM,OAAO,QAAQ,CAAC,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG,kBAAkB;QACnE,MAAM,MAAM,QAAQ,CAAC,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG,iBAAiB;QACjE,MAAM,QAAQ,MAAM,KAAK,MAAM,KAAK,CAAC,OAAO,GAAG;QAE/C,KAAK,IAAI,CAAC;YAAE;YAAM;YAAM;YAAK;QAAM;QACnC,QAAQ;IACV;IAEA,OAAO;AACT;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,uEAAuE;QACvE,MAAM,aAAa,IAAI,IAAI,QAAQ,GAAG;QACtC,MAAM,UAAU,GAAG,WAAW,QAAQ,CAAC,EAAE,EAAE,WAAW,IAAI,EAAE;QAE5D,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,SAAS;QAE3C,mDAAmD;QACnD,MAAM,iBAAiB,MAAM,MAAM,GAAG,QAAQ,+CAA+C,CAAC;QAC9F,MAAM,aAAa,MAAM,eAAe,IAAI;QAE5C,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,WAAW,OAAO,CAAC,SAAS,EAAE,WAAW,IAAI,EAAE,QAAQ,UAAU,GAAG;QAE5G,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,MAAM,IAAI,MAAM,WAAW,KAAK,IAAI;QACtC;QAEA,2FAA2F;QAC3F,MAAM,oBAAoB,WAAW,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACvD,MAAM,gBAAgB,MAAM,aAAa,KAAK;YAC9C,MAAM,gBAAgB,MAAM,KAAK,GAAG;YAEpC,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,MAAM,MAAM,CAAC,MAAM,EAAE,cAAc,SAAS,EAAE,MAAM,KAAK,CAAC,QAAQ,EAAE,eAAe;YAErG,OAAO,iBAAiB;QAC1B;QAEA,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,kBAAkB,MAAM,CAAC,sCAAsC,CAAC;QAEvF,oFAAoF;QACpF,MAAM,mBAAsC,kBAAkB,GAAG,CAAC,CAAC;YACjE,0EAA0E;YAC1E,MAAM,WAAW,qBAAqB,MAAM,KAAK;YACjD,MAAM,kBAAkB,KAAK,GAAG,IAAI,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;YAC5D,MAAM,oBAAoB,kBAAkB;YAC5C,MAAM,oBAAoB,AAAC,CAAC,MAAM,KAAK,GAAG,iBAAiB,IAAI,oBAAqB;YACpF,MAAM,uBAAuB,KAAK,KAAK,CAAC,OAAO;YAE/C,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,MAAM,MAAM,CAAC,WAAW,EAAE,MAAM,KAAK,CAAC,gBAAgB,EAAE,gBAAgB,OAAO,CAAC,GAAG,gBAAgB,EAAE,kBAAkB,OAAO,CAAC,GAAG,MAAM,EAAE,sBAAsB;YAElL,OAAO;gBACL,QAAQ,MAAM,MAAM;gBACpB,MAAM,MAAM,IAAI;gBAChB,cAAc,MAAM,KAAK;gBACzB;gBACA;gBACA;gBACA;gBACA,eAAe,MAAM,aAAa;gBAClC,YAAY,MAAM,UAAU,IAAI;YAClC;QACF;QAEA,kGAAkG;QAClG,MAAM,cAAc,iBAAiB,MAAM,CAAC,CAAA;YAC1C,MAAM,mBAAmB,MAAM,oBAAoB,GAAG;YACtD,MAAM,gBAAgB,MAAM,iBAAiB,GAAG;YAChD,MAAM,eAAe,MAAM,iBAAiB,IAAI,MAAM,yCAAyC;YAE/F,MAAM,UAAU,oBAAoB,iBAAiB;YAErD,IAAI,CAAC,SAAS;gBACZ,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,MAAM,MAAM,CAAC,mBAAmB,EAAE,MAAM,oBAAoB,CAAC,SAAS,EAAE,MAAM,iBAAiB,CAAC,OAAO,CAAC,GAAG,aAAa,EAAE,cAAc;YAC3J;YAEA,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,YAAY,MAAM,CAAC,gDAAgD,EAAE,iBAAiB,MAAM,CAAC,oBAAoB,CAAC;QAEpI,4BAA4B;QAC5B,MAAM,mBAAsC,YAAY,GAAG,CAAC,CAAA;YAC1D,MAAM,QAAQ;gBACZ,QAAQ,MAAM,MAAM;gBACpB,MAAM,MAAM,IAAI;gBAChB,WAAW;gBACX,cAAc,MAAM,iBAAiB;gBACrC,UAAU,MAAM,oBAAoB;gBACpC,QAAQ;YACV;YAEA,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,MAAM,MAAM,CAAC,aAAa,EAAE,MAAM,iBAAiB,CAAC,OAAO,CAAC,GAAG,OAAO,EAAE,MAAM,oBAAoB,EAAE;YACzI,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,iBAAiB,MAAM,CAAC,2BAA2B,CAAC;QAEpF,kBAAkB;QAClB,MAAM,QAAQ,iBAAiB,MAAM,GAAG,IAAI;YAC1C,iBAAiB,iBAAiB,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,YAAY,EAAE,KAAK,iBAAiB,MAAM;YAC/G,eAAe,iBAAiB,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,QAAQ,EAAE;YAC7E,YAAY,iBAAiB,MAAM,CAAC,CAAC,KAAK,QAAU,MAAO,MAAM,YAAY,GAAG,MAAM,QAAQ,EAAG;QACnG,IAAI;YACF,iBAAiB;YACjB,eAAe;YACf,YAAY;QACd;QAEA,qDAAqD;QACrD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS,CAAC,MAAM,EAAE,iBAAiB,MAAM,CAAC,mCAAmC,CAAC;YAC9E,MAAM;gBACJ,gBAAgB,kBAAkB,MAAM;gBACxC,aAAa,iBAAiB,MAAM;gBACpC,QAAQ;gBACR;YACF;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;QACT,SAAS;QACT,WAAW;YACT,MAAM;QACR;IACF;AACF", "debugId": null}}]}