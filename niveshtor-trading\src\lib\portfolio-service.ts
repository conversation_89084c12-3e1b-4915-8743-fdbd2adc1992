// Portfolio service for real-time portfolio calculations
import { yahooFinanceService } from './yahoo-finance';

export interface PortfolioHolding {
  symbol: string;
  quantity: number;
  avgPrice: number;
  currentPrice: number;
  marketValue: number;
  pnl: number;
  pnlPercent: number;
  dayChange: number;
  dayChangePercent: number;
}

export interface PortfolioSummary {
  totalValue: number;
  totalInvested: number;
  totalPnL: number;
  totalPnLPercent: number;
  dayChange: number;
  dayChangePercent: number;
  holdings: PortfolioHolding[];
}

class PortfolioService {
  // Sample holdings for demonstration - in real app, this would come from database
  private sampleHoldings = [
    { symbol: 'RELIANCE', quantity: 50, avgPrice: 2200.00 },
    { symbol: 'TCS', quantity: 25, avgPrice: 3400.00 },
    { symbol: 'HDFC', quantity: 40, avgPrice: 1600.00 },
    { symbol: 'INFY', quantity: 60, avgPrice: 1500.00 }
  ];

  async getPortfolioSummary(): Promise<PortfolioSummary> {
    try {
      // Get current prices for all holdings
      const symbols = this.sampleHoldings.map(h => h.symbol);
      const quotes = await yahooFinanceService.getMultipleQuotes(symbols);
      
      const holdings: PortfolioHolding[] = this.sampleHoldings.map(holding => {
        const quote = quotes.find(q => q.symbol.includes(holding.symbol));
        const currentPrice = quote?.price || holding.avgPrice;
        const dayChange = quote?.change || 0;
        const dayChangePercent = quote?.changePercent || 0;
        
        const marketValue = holding.quantity * currentPrice;
        const invested = holding.quantity * holding.avgPrice;
        const pnl = marketValue - invested;
        const pnlPercent = invested > 0 ? (pnl / invested) * 100 : 0;

        return {
          symbol: holding.symbol,
          quantity: holding.quantity,
          avgPrice: holding.avgPrice,
          currentPrice,
          marketValue,
          pnl,
          pnlPercent,
          dayChange: dayChange * holding.quantity,
          dayChangePercent
        };
      });

      // Calculate portfolio totals
      const totalValue = holdings.reduce((sum, h) => sum + h.marketValue, 0);
      const totalInvested = holdings.reduce((sum, h) => sum + (h.quantity * h.avgPrice), 0);
      const totalPnL = totalValue - totalInvested;
      const totalPnLPercent = totalInvested > 0 ? (totalPnL / totalInvested) * 100 : 0;
      const dayChange = holdings.reduce((sum, h) => sum + h.dayChange, 0);
      const dayChangePercent = totalValue > 0 ? (dayChange / (totalValue - dayChange)) * 100 : 0;

      return {
        totalValue,
        totalInvested,
        totalPnL,
        totalPnLPercent,
        dayChange,
        dayChangePercent,
        holdings
      };

    } catch (error) {
      console.error('Error calculating portfolio summary:', error);
      
      // Return fallback data if API fails
      const fallbackHoldings: PortfolioHolding[] = this.sampleHoldings.map(holding => ({
        symbol: holding.symbol,
        quantity: holding.quantity,
        avgPrice: holding.avgPrice,
        currentPrice: holding.avgPrice,
        marketValue: holding.quantity * holding.avgPrice,
        pnl: 0,
        pnlPercent: 0,
        dayChange: 0,
        dayChangePercent: 0
      }));

      const totalInvested = fallbackHoldings.reduce((sum, h) => sum + (h.quantity * h.avgPrice), 0);

      return {
        totalValue: totalInvested,
        totalInvested,
        totalPnL: 0,
        totalPnLPercent: 0,
        dayChange: 0,
        dayChangePercent: 0,
        holdings: fallbackHoldings
      };
    }
  }

  async addHolding(symbol: string, quantity: number, avgPrice: number): Promise<void> {
    // In real app, this would update the database
    const existingIndex = this.sampleHoldings.findIndex(h => h.symbol === symbol);
    
    if (existingIndex >= 0) {
      // Update existing holding
      const existing = this.sampleHoldings[existingIndex];
      const totalQuantity = existing.quantity + quantity;
      const totalValue = (existing.quantity * existing.avgPrice) + (quantity * avgPrice);
      
      this.sampleHoldings[existingIndex] = {
        symbol,
        quantity: totalQuantity,
        avgPrice: totalValue / totalQuantity
      };
    } else {
      // Add new holding
      this.sampleHoldings.push({ symbol, quantity, avgPrice });
    }
  }

  async removeHolding(symbol: string): Promise<void> {
    // In real app, this would update the database
    const index = this.sampleHoldings.findIndex(h => h.symbol === symbol);
    if (index >= 0) {
      this.sampleHoldings.splice(index, 1);
    }
  }

  async updateHolding(symbol: string, quantity: number, avgPrice: number): Promise<void> {
    // In real app, this would update the database
    const index = this.sampleHoldings.findIndex(h => h.symbol === symbol);
    if (index >= 0) {
      this.sampleHoldings[index] = { symbol, quantity, avgPrice };
    }
  }
}

export const portfolioService = new PortfolioService();
