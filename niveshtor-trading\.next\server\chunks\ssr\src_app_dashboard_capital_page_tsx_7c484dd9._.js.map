{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/app/dashboard/capital/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport {\n  Wallet,\n  TrendingUp,\n  TrendingDown,\n  DollarSign,\n  Pie<PERSON>hart,\n  AlertTriangle,\n  Target,\n  Shield,\n  RefreshCw,\n  Settings,\n  Save,\n  Plug,\n  CheckCircle,\n  XCircle,\n  Calculator,\n  BarChart3\n} from 'lucide-react';\nimport { formatCurrency, formatPercentage, getChangeColor } from '@/lib/utils';\nimport { cacheService, CacheKeys } from '@/lib/cache-service';\nimport { PortfolioSummarySkeleton, InlineLoading, ButtonLoading } from '@/components/ui/LoadingStates';\n\ninterface BrokerBalance {\n  id: string;\n  availableCash: number;\n  marginUsed: number;\n  marginAvailable: number;\n  totalBalance: number;\n  lastSyncAt: string;\n}\n\ninterface FundAllocation {\n  id: string;\n  strategyName: string;\n  totalAllocatedAmount: number;\n  maxPerStock: number;\n  maxPerTrade: number;\n  stockAllocations: StockAllocation[];\n}\n\ninterface StockAllocation {\n  id: string;\n  symbol: string;\n  allocatedAmount: number;\n  usedAmount: number;\n  tradesCount: number;\n}\n\ninterface PortfolioSummary {\n  totalValue: number;\n  totalInvested: number;\n  totalPnL: number;\n  totalPnLPercent: number;\n  availableCash: number;\n  marginUsed: number;\n  marginAvailable: number;\n}\n\ninterface RiskMetrics {\n  portfolioRisk: number;\n  maxDrawdown: number;\n  sharpeRatio: number;\n  volatility: number;\n}\n\nexport default function CapitalManagementPage() {\n  // Broker Integration State\n  const [brokerBalance, setBrokerBalance] = useState<BrokerBalance | null>(null);\n  const [brokerConnected, setBrokerConnected] = useState(false);\n  const [loadingBalance, setLoadingBalance] = useState(false);\n  const [balanceError, setBalanceError] = useState<string | null>(null);\n\n  // Fund Allocation State\n  const [fundAllocation, setFundAllocation] = useState<FundAllocation | null>(null);\n  const [loadingAllocation, setLoadingAllocation] = useState(false);\n  const [allocationSettings, setAllocationSettings] = useState({\n    totalAllocatedAmount: 0,\n    maxPerStock: 10000,\n    maxPerTrade: 2000\n  });\n\n  // UI State\n  const [showAllocationSettings, setShowAllocationSettings] = useState(false);\n  const [savingSettings, setSavingSettings] = useState(false);\n\n  const [portfolioSummary, setPortfolioSummary] = useState<PortfolioSummary>({\n    totalValue: 0,\n    totalInvested: 0,\n    totalPnL: 0,\n    totalPnLPercent: 0,\n    availableCash: 0,\n    marginUsed: 0,\n    marginAvailable: 0\n  });\n  const [loadingPortfolio, setLoadingPortfolio] = useState(false);\n\n  const [riskMetrics, setRiskMetrics] = useState<RiskMetrics>({\n    portfolioRisk: 15.5,\n    maxDrawdown: -8.2,\n    sharpeRatio: 1.45,\n    volatility: 18.3\n  });\n\n  // Fetch broker balance with caching\n  const fetchBrokerBalance = async (forceRefresh = false) => {\n    setLoadingBalance(true);\n    setBalanceError(null);\n\n    try {\n      const cacheKey = CacheKeys.brokerBalance('default-user');\n\n      // Check cache first unless force refresh\n      if (!forceRefresh) {\n        const cached = cacheService.get<any>(cacheKey);\n        if (cached) {\n          setBrokerBalance(cached.data);\n          setBrokerConnected(cached.success);\n\n          if (cached.success) {\n            setPortfolioSummary(prev => ({\n              ...prev,\n              availableCash: cached.data.availableCash,\n              marginUsed: cached.data.marginUsed,\n              marginAvailable: cached.data.marginAvailable\n            }));\n          }\n\n          setLoadingBalance(false);\n          return;\n        }\n      }\n\n      const response = await fetch('/api/broker/balance?userId=default-user');\n      const data = await response.json();\n\n      // Cache the response\n      cacheService.set(cacheKey, data);\n\n      if (data.success) {\n        setBrokerBalance(data.data);\n        setBrokerConnected(true);\n        setBalanceError(null);\n\n        // Update portfolio summary with real broker data\n        setPortfolioSummary(prev => ({\n          ...prev,\n          availableCash: data.data.availableCash,\n          marginUsed: data.data.marginUsed,\n          marginAvailable: data.data.marginAvailable\n        }));\n      } else {\n        setBalanceError(data.error);\n        setBrokerConnected(false);\n\n        // Set fallback values when broker is not connected\n        if (data.data) {\n          setBrokerBalance(data.data);\n          setPortfolioSummary(prev => ({\n            ...prev,\n            availableCash: data.data.availableCash,\n            marginUsed: data.data.marginUsed,\n            marginAvailable: data.data.marginAvailable\n          }));\n        }\n      }\n    } catch (error) {\n      setBalanceError('Failed to connect to broker');\n      setBrokerConnected(false);\n    } finally {\n      setLoadingBalance(false);\n    }\n  };\n\n  // Fetch portfolio data\n  const fetchPortfolioData = async () => {\n    setLoadingPortfolio(true);\n\n    try {\n      const response = await fetch('/api/portfolio/summary');\n      const data = await response.json();\n\n      if (data.success) {\n        const portfolio = data.data;\n        setPortfolioSummary(prev => ({\n          ...prev,\n          totalValue: portfolio.totalValue,\n          totalInvested: portfolio.totalInvested,\n          totalPnL: portfolio.totalPnL,\n          totalPnLPercent: portfolio.totalPnLPercent\n        }));\n      }\n    } catch (error) {\n      console.error('Failed to fetch portfolio data:', error);\n    } finally {\n      setLoadingPortfolio(false);\n    }\n  };\n\n  // Fetch fund allocation\n  const fetchFundAllocation = async () => {\n    setLoadingAllocation(true);\n\n    try {\n      const response = await fetch('/api/fund-allocation?userId=default-user&strategy=DARVAS_BOX');\n      const data = await response.json();\n\n      if (data.success) {\n        setFundAllocation(data.data);\n        setAllocationSettings({\n          totalAllocatedAmount: data.data.totalAllocatedAmount,\n          maxPerStock: data.data.maxPerStock,\n          maxPerTrade: data.data.maxPerTrade\n        });\n      }\n    } catch (error) {\n      console.error('Failed to fetch fund allocation:', error);\n    } finally {\n      setLoadingAllocation(false);\n    }\n  };\n\n  // Save allocation settings\n  const saveAllocationSettings = async () => {\n    setSavingSettings(true);\n\n    try {\n      const response = await fetch('/api/fund-allocation', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          userId: 'default-user',\n          strategyName: 'DARVAS_BOX',\n          ...allocationSettings\n        }),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        setFundAllocation(data.data);\n        setShowAllocationSettings(false);\n      }\n    } catch (error) {\n      console.error('Failed to save allocation settings:', error);\n    } finally {\n      setSavingSettings(false);\n    }\n  };\n\n  // Calculate allocation metrics\n  const calculateAllocationMetrics = () => {\n    if (!fundAllocation || !brokerBalance) return null;\n\n    const totalAllocated = fundAllocation.totalAllocatedAmount;\n    const availableFunds = brokerBalance.availableCash;\n    const allocationPercentage = availableFunds > 0 ? (totalAllocated / availableFunds) * 100 : 0;\n    const maxTradesPerStock = Math.floor(fundAllocation.maxPerStock / fundAllocation.maxPerTrade);\n    const totalPossibleStocks = totalAllocated > 0 ? Math.floor(totalAllocated / fundAllocation.maxPerStock) : 0;\n\n    return {\n      totalAllocated,\n      availableFunds,\n      allocationPercentage,\n      maxTradesPerStock,\n      totalPossibleStocks,\n      remainingAllocation: Math.max(0, availableFunds - totalAllocated)\n    };\n  };\n\n  // Refresh all data\n  const refreshAllData = async () => {\n    await Promise.all([\n      fetchBrokerBalance(),\n      fetchFundAllocation(),\n      fetchPortfolioData()\n    ]);\n  };\n\n  // Load data on component mount\n  useEffect(() => {\n    refreshAllData();\n  }, []);\n\n  const StatCard = ({\n    title,\n    value,\n    change,\n    icon: Icon,\n    color = 'blue',\n    loading = false,\n    error = false\n  }: {\n    title: string;\n    value: string;\n    change?: string;\n    icon: any;\n    color?: string;\n    loading?: boolean;\n    error?: boolean;\n  }) => (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex-1\">\n          <p className=\"text-sm font-medium text-gray-600\">{title}</p>\n          {loading ? (\n            <div className=\"mt-1 h-8 bg-gray-200 rounded animate-pulse\"></div>\n          ) : error ? (\n            <p className=\"text-2xl font-bold text-red-500 mt-1\">Error</p>\n          ) : (\n            <p className=\"text-2xl font-bold text-gray-900 mt-1\">{value}</p>\n          )}\n          {change && !loading && !error && (\n            <p className={`text-sm mt-1 ${getChangeColor(parseFloat(change))}`}>\n              {change.startsWith('-') ? '' : '+'}{change}\n            </p>\n          )}\n        </div>\n        <div className={`p-3 rounded-full bg-${color}-100`}>\n          <Icon className={`h-6 w-6 text-${color}-600 ${loading ? 'animate-pulse' : ''}`} />\n        </div>\n      </div>\n    </div>\n  );\n\n  const ProgressBar = ({\n    value,\n    max,\n    label,\n    color = 'blue'\n  }: {\n    value: number;\n    max: number;\n    label: string;\n    color?: string;\n  }) => {\n    const percentage = max > 0 ? Math.min((value / max) * 100, 100) : 0;\n\n    return (\n      <div className=\"space-y-2\">\n        <div className=\"flex justify-between text-sm\">\n          <span className=\"text-gray-600\">{label}</span>\n          <span className=\"font-medium\">\n            {formatCurrency(value)} / {formatCurrency(max)}\n          </span>\n        </div>\n        <div className=\"w-full bg-gray-200 rounded-full h-2\">\n          <div\n            className={`bg-${color}-600 h-2 rounded-full transition-all duration-300`}\n            style={{ width: `${percentage}%` }}\n          ></div>\n        </div>\n        <div className=\"text-xs text-gray-500 text-right\">\n          {percentage.toFixed(1)}% utilized\n        </div>\n      </div>\n    );\n  };\n\n  const allocationMetrics = calculateAllocationMetrics();\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Page Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Capital Management</h1>\n          <p className=\"text-gray-600 mt-1\">Portfolio overview and risk management with real-time broker integration</p>\n        </div>\n        <div className=\"flex space-x-3\">\n          <button\n            onClick={refreshAllData}\n            disabled={loadingBalance || loadingAllocation || loadingPortfolio}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center space-x-2\"\n          >\n            <RefreshCw className={`h-4 w-4 ${(loadingBalance || loadingAllocation || loadingPortfolio) ? 'animate-spin' : ''}`} />\n            <span>Refresh Data</span>\n          </button>\n          <button\n            onClick={() => setShowAllocationSettings(!showAllocationSettings)}\n            className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2\"\n          >\n            <Settings className=\"h-4 w-4\" />\n            <span>Settings</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Broker Connection Status */}\n      <div className={`p-4 rounded-lg border ${\n        brokerConnected\n          ? 'bg-green-50 border-green-200'\n          : 'bg-red-50 border-red-200'\n      }`}>\n        <div className=\"flex items-center space-x-2\">\n          {brokerConnected ? (\n            <>\n              <CheckCircle className=\"h-5 w-5 text-green-600\" />\n              <span className=\"text-green-800 font-medium\">Broker Connected</span>\n              {brokerBalance && (\n                <span className=\"text-green-600 text-sm\">\n                  Last sync: {new Date(brokerBalance.lastSyncAt).toLocaleString()}\n                </span>\n              )}\n            </>\n          ) : (\n            <>\n              <XCircle className=\"h-5 w-5 text-red-600\" />\n              <span className=\"text-red-800 font-medium\">Broker Not Connected</span>\n              {balanceError && (\n                <span className=\"text-red-600 text-sm\">- {balanceError}</span>\n              )}\n            </>\n          )}\n        </div>\n      </div>\n\n      {/* Real-time Broker Balance Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <StatCard\n          title=\"Available Cash (Broker)\"\n          value={brokerBalance ? formatCurrency(brokerBalance.availableCash) : formatCurrency(portfolioSummary.availableCash)}\n          icon={DollarSign}\n          color=\"green\"\n          loading={loadingBalance}\n          error={!brokerConnected}\n        />\n        <StatCard\n          title=\"Margin Used\"\n          value={brokerBalance ? formatCurrency(brokerBalance.marginUsed) : formatCurrency(portfolioSummary.marginUsed)}\n          icon={PieChart}\n          color=\"orange\"\n          loading={loadingBalance}\n          error={!brokerConnected}\n        />\n        <StatCard\n          title=\"Margin Available\"\n          value={brokerBalance ? formatCurrency(brokerBalance.marginAvailable) : formatCurrency(portfolioSummary.marginAvailable)}\n          icon={Wallet}\n          color=\"blue\"\n          loading={loadingBalance}\n          error={!brokerConnected}\n        />\n        <StatCard\n          title=\"Total Balance\"\n          value={brokerBalance ? formatCurrency(brokerBalance.totalBalance) : formatCurrency(portfolioSummary.totalValue)}\n          change={formatPercentage(portfolioSummary.totalPnLPercent)}\n          icon={portfolioSummary.totalPnL >= 0 ? TrendingUp : TrendingDown}\n          color={portfolioSummary.totalPnL >= 0 ? \"green\" : \"red\"}\n          loading={loadingBalance}\n          error={!brokerConnected}\n        />\n      </div>\n\n      {/* Darvas Box Strategy Fund Allocation */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <div className=\"flex items-center space-x-3\">\n            <Target className=\"h-6 w-6 text-blue-600\" />\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900\">Darvas Box Strategy Fund Allocation</h3>\n              <p className=\"text-sm text-gray-600\">Dedicated fund allocation for systematic trading</p>\n            </div>\n          </div>\n          <button\n            onClick={() => setShowAllocationSettings(!showAllocationSettings)}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2\"\n          >\n            <Calculator className=\"h-4 w-4\" />\n            <span>Configure</span>\n          </button>\n        </div>\n\n        {loadingAllocation ? (\n          <div className=\"space-y-4\">\n            <div className=\"h-4 bg-gray-200 rounded animate-pulse\"></div>\n            <div className=\"h-4 bg-gray-200 rounded animate-pulse w-3/4\"></div>\n            <div className=\"h-4 bg-gray-200 rounded animate-pulse w-1/2\"></div>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className=\"space-y-4\">\n              <div className=\"text-center p-4 bg-blue-50 rounded-lg\">\n                <div className=\"text-2xl font-bold text-blue-600 mb-1\">\n                  {formatCurrency(fundAllocation?.totalAllocatedAmount || 0)}\n                </div>\n                <div className=\"text-sm text-blue-800\">Total Allocated</div>\n                {allocationMetrics && (\n                  <div className=\"text-xs text-blue-600 mt-1\">\n                    {allocationMetrics.allocationPercentage.toFixed(1)}% of available funds\n                  </div>\n                )}\n              </div>\n\n              <div className=\"text-center p-4 bg-green-50 rounded-lg\">\n                <div className=\"text-2xl font-bold text-green-600 mb-1\">\n                  {formatCurrency(fundAllocation?.maxPerStock || 10000)}\n                </div>\n                <div className=\"text-sm text-green-800\">Max Per Stock</div>\n                <div className=\"text-xs text-green-600 mt-1\">\n                  Individual stock limit\n                </div>\n              </div>\n            </div>\n\n            <div className=\"space-y-4\">\n              <div className=\"text-center p-4 bg-orange-50 rounded-lg\">\n                <div className=\"text-2xl font-bold text-orange-600 mb-1\">\n                  {formatCurrency(fundAllocation?.maxPerTrade || 2000)}\n                </div>\n                <div className=\"text-sm text-orange-800\">Max Per Trade</div>\n                <div className=\"text-xs text-orange-600 mt-1\">\n                  Single trade limit\n                </div>\n              </div>\n\n              <div className=\"text-center p-4 bg-purple-50 rounded-lg\">\n                <div className=\"text-2xl font-bold text-purple-600 mb-1\">\n                  {allocationMetrics?.maxTradesPerStock || 5}\n                </div>\n                <div className=\"text-sm text-purple-800\">Trades Per Stock</div>\n                <div className=\"text-xs text-purple-600 mt-1\">\n                  ₹{fundAllocation?.maxPerStock || 10000} ÷ ₹{fundAllocation?.maxPerTrade || 2000}\n                </div>\n              </div>\n            </div>\n\n            <div className=\"space-y-4\">\n              <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n                <div className=\"text-2xl font-bold text-gray-600 mb-1\">\n                  {allocationMetrics?.totalPossibleStocks || 0}\n                </div>\n                <div className=\"text-sm text-gray-800\">Possible Stocks</div>\n                <div className=\"text-xs text-gray-600 mt-1\">\n                  Based on allocation\n                </div>\n              </div>\n\n              <div className=\"text-center p-4 bg-indigo-50 rounded-lg\">\n                <div className=\"text-2xl font-bold text-indigo-600 mb-1\">\n                  {formatCurrency(allocationMetrics?.remainingAllocation || 0)}\n                </div>\n                <div className=\"text-sm text-indigo-800\">Remaining Funds</div>\n                <div className=\"text-xs text-indigo-600 mt-1\">\n                  Available for allocation\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Allocation Settings Modal */}\n      {showAllocationSettings && (\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between mb-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Fund Allocation Settings</h3>\n            <button\n              onClick={() => setShowAllocationSettings(false)}\n              className=\"text-gray-400 hover:text-gray-600\"\n            >\n              <XCircle className=\"h-6 w-6\" />\n            </button>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Total Strategy Allocation (₹)\n              </label>\n              <input\n                type=\"number\"\n                value={allocationSettings.totalAllocatedAmount}\n                onChange={(e) => setAllocationSettings(prev => ({\n                  ...prev,\n                  totalAllocatedAmount: Number(e.target.value)\n                }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"100000\"\n              />\n              <p className=\"text-xs text-gray-500 mt-1\">\n                Amount dedicated to Darvas Box strategy\n              </p>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Max Per Stock (₹)\n              </label>\n              <input\n                type=\"number\"\n                value={allocationSettings.maxPerStock}\n                onChange={(e) => setAllocationSettings(prev => ({\n                  ...prev,\n                  maxPerStock: Number(e.target.value)\n                }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"10000\"\n              />\n              <p className=\"text-xs text-gray-500 mt-1\">\n                Maximum allocation per stock symbol\n              </p>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Max Per Trade (₹)\n              </label>\n              <input\n                type=\"number\"\n                value={allocationSettings.maxPerTrade}\n                onChange={(e) => setAllocationSettings(prev => ({\n                  ...prev,\n                  maxPerTrade: Number(e.target.value)\n                }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"2000\"\n              />\n              <p className=\"text-xs text-gray-500 mt-1\">\n                Maximum amount per individual trade\n              </p>\n            </div>\n          </div>\n\n          <div className=\"mt-6 flex justify-end space-x-3\">\n            <button\n              onClick={() => setShowAllocationSettings(false)}\n              className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n            >\n              Cancel\n            </button>\n            <button\n              onClick={saveAllocationSettings}\n              disabled={savingSettings}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center space-x-2\"\n            >\n              {savingSettings ? (\n                <RefreshCw className=\"h-4 w-4 animate-spin\" />\n              ) : (\n                <Save className=\"h-4 w-4\" />\n              )}\n              <span>{savingSettings ? 'Saving...' : 'Save Settings'}</span>\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* Risk Management Section */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Position Sizing Rules */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Position Sizing Rules</h3>\n            <Shield className=\"h-5 w-5 text-gray-400\" />\n          </div>\n\n          <div className=\"space-y-6\">\n            {/* Per Stock Limit Visualization */}\n            <div>\n              <div className=\"flex justify-between items-center mb-2\">\n                <span className=\"text-sm font-medium text-gray-700\">Per Stock Limit</span>\n                <span className=\"text-sm text-gray-600\">\n                  {formatCurrency(fundAllocation?.maxPerStock || 10000)} max\n                </span>\n              </div>\n              <ProgressBar\n                value={8000} // Example current allocation\n                max={fundAllocation?.maxPerStock || 10000}\n                label=\"RELIANCE\"\n                color=\"blue\"\n              />\n            </div>\n\n            {/* Per Trade Limit Visualization */}\n            <div>\n              <div className=\"flex justify-between items-center mb-2\">\n                <span className=\"text-sm font-medium text-gray-700\">Per Trade Limit</span>\n                <span className=\"text-sm text-gray-600\">\n                  {formatCurrency(fundAllocation?.maxPerTrade || 2000)} max\n                </span>\n              </div>\n              <div className=\"grid grid-cols-5 gap-2\">\n                {Array.from({ length: 5 }, (_, i) => (\n                  <div\n                    key={i}\n                    className={`h-8 rounded flex items-center justify-center text-xs font-medium ${\n                      i < 3\n                        ? 'bg-green-100 text-green-800'\n                        : 'bg-gray-100 text-gray-500'\n                    }`}\n                  >\n                    Trade {i + 1}\n                  </div>\n                ))}\n              </div>\n              <div className=\"text-xs text-gray-500 mt-2 text-center\">\n                Up to 5 trades per stock (₹{fundAllocation?.maxPerStock || 10000} ÷ ₹{fundAllocation?.maxPerTrade || 2000} = 5 trades)\n              </div>\n            </div>\n\n            {/* Risk Metrics */}\n            <div className=\"pt-4 border-t border-gray-100\">\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-600\">Portfolio Risk</span>\n                  <span className=\"font-medium\">{riskMetrics.portfolioRisk}%</span>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-600\">Max Drawdown</span>\n                  <span className=\"font-medium text-red-600\">{riskMetrics.maxDrawdown}%</span>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-600\">Sharpe Ratio</span>\n                  <span className=\"font-medium\">{riskMetrics.sharpeRatio}</span>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-600\">Volatility</span>\n                  <span className=\"font-medium\">{riskMetrics.volatility}%</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Stock Allocation Tracking */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Stock Allocation Tracking</h3>\n            <BarChart3 className=\"h-5 w-5 text-gray-400\" />\n          </div>\n\n          {fundAllocation?.stockAllocations && fundAllocation.stockAllocations.length > 0 ? (\n            <div className=\"space-y-4\">\n              {fundAllocation.stockAllocations.map((stock) => (\n                <div key={stock.id} className=\"border border-gray-200 rounded-lg p-4\">\n                  <div className=\"flex justify-between items-center mb-2\">\n                    <span className=\"font-medium text-gray-900\">{stock.symbol}</span>\n                    <span className=\"text-sm text-gray-600\">\n                      {stock.tradesCount} / {Math.floor((fundAllocation?.maxPerStock || 10000) / (fundAllocation?.maxPerTrade || 2000))} trades\n                    </span>\n                  </div>\n\n                  <ProgressBar\n                    value={stock.allocatedAmount}\n                    max={fundAllocation?.maxPerStock || 10000}\n                    label={`Allocated: ${formatCurrency(stock.allocatedAmount)}`}\n                    color=\"green\"\n                  />\n\n                  <div className=\"mt-2 flex justify-between text-sm text-gray-600\">\n                    <span>Used: {formatCurrency(stock.usedAmount)}</span>\n                    <span>Available: {formatCurrency(stock.allocatedAmount - stock.usedAmount)}</span>\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-8 text-gray-500\">\n              <Target className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n              <p>No stock allocations yet.</p>\n              <p className=\"text-sm mt-1\">Allocations will appear here when you start trading.</p>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Fund Allocation Summary */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900\">Fund Allocation Summary</h3>\n          <button\n            onClick={refreshAllData}\n            disabled={loadingBalance || loadingAllocation || loadingPortfolio}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center space-x-2\"\n          >\n            <RefreshCw className={`h-4 w-4 ${(loadingBalance || loadingAllocation || loadingPortfolio) ? 'animate-spin' : ''}`} />\n            <span>Refresh</span>\n          </button>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n          <div className=\"text-center\">\n            <div className=\"w-20 h-20 mx-auto bg-blue-100 rounded-full flex items-center justify-center mb-3\">\n              <span className=\"text-lg font-bold text-blue-600\">\n                {allocationMetrics ? `${allocationMetrics.allocationPercentage.toFixed(0)}%` : '0%'}\n              </span>\n            </div>\n            <h4 className=\"font-medium text-gray-900\">Strategy Allocation</h4>\n            <p className=\"text-sm text-gray-600\">{formatCurrency(fundAllocation?.totalAllocatedAmount || 0)}</p>\n          </div>\n\n          <div className=\"text-center\">\n            <div className=\"w-20 h-20 mx-auto bg-green-100 rounded-full flex items-center justify-center mb-3\">\n              <span className=\"text-lg font-bold text-green-600\">\n                {allocationMetrics?.totalPossibleStocks || 0}\n              </span>\n            </div>\n            <h4 className=\"font-medium text-gray-900\">Possible Stocks</h4>\n            <p className=\"text-sm text-gray-600\">Based on limits</p>\n          </div>\n\n          <div className=\"text-center\">\n            <div className=\"w-20 h-20 mx-auto bg-orange-100 rounded-full flex items-center justify-center mb-3\">\n              <span className=\"text-lg font-bold text-orange-600\">\n                {allocationMetrics?.maxTradesPerStock || 5}\n              </span>\n            </div>\n            <h4 className=\"font-medium text-gray-900\">Trades Per Stock</h4>\n            <p className=\"text-sm text-gray-600\">Maximum allowed</p>\n          </div>\n\n          <div className=\"text-center\">\n            <div className=\"w-20 h-20 mx-auto bg-purple-100 rounded-full flex items-center justify-center mb-3\">\n              <span className=\"text-lg font-bold text-purple-600\">\n                {formatCurrency(allocationMetrics?.remainingAllocation || 0).replace('₹', '₹')}\n              </span>\n            </div>\n            <h4 className=\"font-medium text-gray-900\">Available</h4>\n            <p className=\"text-sm text-gray-600\">For allocation</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Alerts and Notifications */}\n      <div className=\"space-y-4\">\n        {!brokerConnected && (\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n            <div className=\"flex items-start\">\n              <Plug className=\"h-5 w-5 text-red-600 mt-0.5 mr-3\" />\n              <div>\n                <h4 className=\"text-sm font-medium text-red-800\">Broker Connection Required</h4>\n                <p className=\"text-sm text-red-700 mt-1\">\n                  Connect your broker account to access real-time balance and enable automated trading features.\n                </p>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {allocationMetrics && allocationMetrics.allocationPercentage > 80 && (\n          <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n            <div className=\"flex items-start\">\n              <AlertTriangle className=\"h-5 w-5 text-yellow-600 mt-0.5 mr-3\" />\n              <div>\n                <h4 className=\"text-sm font-medium text-yellow-800\">High Allocation Warning</h4>\n                <p className=\"text-sm text-yellow-700 mt-1\">\n                  You have allocated {allocationMetrics.allocationPercentage.toFixed(1)}% of your available funds to the Darvas Box strategy.\n                  Consider maintaining some cash reserves for opportunities.\n                </p>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {fundAllocation && fundAllocation.maxPerTrade > fundAllocation.maxPerStock / 3 && (\n          <div className=\"bg-orange-50 border border-orange-200 rounded-lg p-4\">\n            <div className=\"flex items-start\">\n              <AlertTriangle className=\"h-5 w-5 text-orange-600 mt-0.5 mr-3\" />\n              <div>\n                <h4 className=\"text-sm font-medium text-orange-800\">Position Sizing Alert</h4>\n                <p className=\"text-sm text-orange-700 mt-1\">\n                  Your per-trade limit is high relative to per-stock limit. This reduces diversification opportunities.\n                </p>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA;AACA;AAtBA;;;;;;AAoEe,SAAS;IACtB,2BAA2B;IAC3B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACzE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,wBAAwB;IACxB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAC5E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3D,sBAAsB;QACtB,aAAa;QACb,aAAa;IACf;IAEA,WAAW;IACX,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;QACzE,YAAY;QACZ,eAAe;QACf,UAAU;QACV,iBAAiB;QACjB,eAAe;QACf,YAAY;QACZ,iBAAiB;IACnB;IACA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;QAC1D,eAAe;QACf,aAAa,CAAC;QACd,aAAa;QACb,YAAY;IACd;IAEA,oCAAoC;IACpC,MAAM,qBAAqB,OAAO,eAAe,KAAK;QACpD,kBAAkB;QAClB,gBAAgB;QAEhB,IAAI;YACF,MAAM,WAAW,8HAAA,CAAA,YAAS,CAAC,aAAa,CAAC;YAEzC,yCAAyC;YACzC,IAAI,CAAC,cAAc;gBACjB,MAAM,SAAS,8HAAA,CAAA,eAAY,CAAC,GAAG,CAAM;gBACrC,IAAI,QAAQ;oBACV,iBAAiB,OAAO,IAAI;oBAC5B,mBAAmB,OAAO,OAAO;oBAEjC,IAAI,OAAO,OAAO,EAAE;wBAClB,oBAAoB,CAAA,OAAQ,CAAC;gCAC3B,GAAG,IAAI;gCACP,eAAe,OAAO,IAAI,CAAC,aAAa;gCACxC,YAAY,OAAO,IAAI,CAAC,UAAU;gCAClC,iBAAiB,OAAO,IAAI,CAAC,eAAe;4BAC9C,CAAC;oBACH;oBAEA,kBAAkB;oBAClB;gBACF;YACF;YAEA,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,qBAAqB;YACrB,8HAAA,CAAA,eAAY,CAAC,GAAG,CAAC,UAAU;YAE3B,IAAI,KAAK,OAAO,EAAE;gBAChB,iBAAiB,KAAK,IAAI;gBAC1B,mBAAmB;gBACnB,gBAAgB;gBAEhB,iDAAiD;gBACjD,oBAAoB,CAAA,OAAQ,CAAC;wBAC3B,GAAG,IAAI;wBACP,eAAe,KAAK,IAAI,CAAC,aAAa;wBACtC,YAAY,KAAK,IAAI,CAAC,UAAU;wBAChC,iBAAiB,KAAK,IAAI,CAAC,eAAe;oBAC5C,CAAC;YACH,OAAO;gBACL,gBAAgB,KAAK,KAAK;gBAC1B,mBAAmB;gBAEnB,mDAAmD;gBACnD,IAAI,KAAK,IAAI,EAAE;oBACb,iBAAiB,KAAK,IAAI;oBAC1B,oBAAoB,CAAA,OAAQ,CAAC;4BAC3B,GAAG,IAAI;4BACP,eAAe,KAAK,IAAI,CAAC,aAAa;4BACtC,YAAY,KAAK,IAAI,CAAC,UAAU;4BAChC,iBAAiB,KAAK,IAAI,CAAC,eAAe;wBAC5C,CAAC;gBACH;YACF;QACF,EAAE,OAAO,OAAO;YACd,gBAAgB;YAChB,mBAAmB;QACrB,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,uBAAuB;IACvB,MAAM,qBAAqB;QACzB,oBAAoB;QAEpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,YAAY,KAAK,IAAI;gBAC3B,oBAAoB,CAAA,OAAQ,CAAC;wBAC3B,GAAG,IAAI;wBACP,YAAY,UAAU,UAAU;wBAChC,eAAe,UAAU,aAAa;wBACtC,UAAU,UAAU,QAAQ;wBAC5B,iBAAiB,UAAU,eAAe;oBAC5C,CAAC;YACH;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,wBAAwB;IACxB,MAAM,sBAAsB;QAC1B,qBAAqB;QAErB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,kBAAkB,KAAK,IAAI;gBAC3B,sBAAsB;oBACpB,sBAAsB,KAAK,IAAI,CAAC,oBAAoB;oBACpD,aAAa,KAAK,IAAI,CAAC,WAAW;oBAClC,aAAa,KAAK,IAAI,CAAC,WAAW;gBACpC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,2BAA2B;IAC3B,MAAM,yBAAyB;QAC7B,kBAAkB;QAElB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,cAAc;oBACd,GAAG,kBAAkB;gBACvB;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,kBAAkB,KAAK,IAAI;gBAC3B,0BAA0B;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;QACvD,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,+BAA+B;IAC/B,MAAM,6BAA6B;QACjC,IAAI,CAAC,kBAAkB,CAAC,eAAe,OAAO;QAE9C,MAAM,iBAAiB,eAAe,oBAAoB;QAC1D,MAAM,iBAAiB,cAAc,aAAa;QAClD,MAAM,uBAAuB,iBAAiB,IAAI,AAAC,iBAAiB,iBAAkB,MAAM;QAC5F,MAAM,oBAAoB,KAAK,KAAK,CAAC,eAAe,WAAW,GAAG,eAAe,WAAW;QAC5F,MAAM,sBAAsB,iBAAiB,IAAI,KAAK,KAAK,CAAC,iBAAiB,eAAe,WAAW,IAAI;QAE3G,OAAO;YACL;YACA;YACA;YACA;YACA;YACA,qBAAqB,KAAK,GAAG,CAAC,GAAG,iBAAiB;QACpD;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAiB;QACrB,MAAM,QAAQ,GAAG,CAAC;YAChB;YACA;YACA;SACD;IACH;IAEA,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,WAAW,CAAC,EAChB,KAAK,EACL,KAAK,EACL,MAAM,EACN,MAAM,IAAI,EACV,QAAQ,MAAM,EACd,UAAU,KAAK,EACf,QAAQ,KAAK,EASd,iBACC,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;4BACjD,wBACC,8OAAC;gCAAI,WAAU;;;;;uCACb,sBACF,8OAAC;gCAAE,WAAU;0CAAuC;;;;;qDAEpD,8OAAC;gCAAE,WAAU;0CAAyC;;;;;;4BAEvD,UAAU,CAAC,WAAW,CAAC,uBACtB,8OAAC;gCAAE,WAAW,CAAC,aAAa,EAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,UAAU;;oCAC/D,OAAO,UAAU,CAAC,OAAO,KAAK;oCAAK;;;;;;;;;;;;;kCAI1C,8OAAC;wBAAI,WAAW,CAAC,oBAAoB,EAAE,MAAM,IAAI,CAAC;kCAChD,cAAA,8OAAC;4BAAK,WAAW,CAAC,aAAa,EAAE,MAAM,KAAK,EAAE,UAAU,kBAAkB,IAAI;;;;;;;;;;;;;;;;;;;;;;IAMtF,MAAM,cAAc,CAAC,EACnB,KAAK,EACL,GAAG,EACH,KAAK,EACL,QAAQ,MAAM,EAMf;QACC,MAAM,aAAa,MAAM,IAAI,KAAK,GAAG,CAAC,AAAC,QAAQ,MAAO,KAAK,OAAO;QAElE,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAAiB;;;;;;sCACjC,8OAAC;4BAAK,WAAU;;gCACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;gCAAO;gCAAI,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;;8BAG9C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,WAAW,CAAC,GAAG,EAAE,MAAM,iDAAiD,CAAC;wBACzE,OAAO;4BAAE,OAAO,GAAG,WAAW,CAAC,CAAC;wBAAC;;;;;;;;;;;8BAGrC,8OAAC;oBAAI,WAAU;;wBACZ,WAAW,OAAO,CAAC;wBAAG;;;;;;;;;;;;;IAI/B;IAEA,MAAM,oBAAoB;IAE1B,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAEpC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,UAAU,kBAAkB,qBAAqB;gCACjD,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAW,CAAC,QAAQ,EAAE,AAAC,kBAAkB,qBAAqB,mBAAoB,iBAAiB,IAAI;;;;;;kDAClH,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCACC,SAAS,IAAM,0BAA0B,CAAC;gCAC1C,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAI,WAAW,CAAC,sBAAsB,EACrC,kBACI,iCACA,4BACJ;0BACA,cAAA,8OAAC;oBAAI,WAAU;8BACZ,gCACC;;0CACE,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC;gCAAK,WAAU;0CAA6B;;;;;;4BAC5C,+BACC,8OAAC;gCAAK,WAAU;;oCAAyB;oCAC3B,IAAI,KAAK,cAAc,UAAU,EAAE,cAAc;;;;;;;;qDAKnE;;0CACE,8OAAC,4MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,8OAAC;gCAAK,WAAU;0CAA2B;;;;;;4BAC1C,8BACC,8OAAC;gCAAK,WAAU;;oCAAuB;oCAAG;;;;;;;;;;;;;;;;;;;0BAQpD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,OAAM;wBACN,OAAO,gBAAgB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,cAAc,aAAa,IAAI,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,iBAAiB,aAAa;wBAClH,MAAM,kNAAA,CAAA,aAAU;wBAChB,OAAM;wBACN,SAAS;wBACT,OAAO,CAAC;;;;;;kCAEV,8OAAC;wBACC,OAAM;wBACN,OAAO,gBAAgB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,cAAc,UAAU,IAAI,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,iBAAiB,UAAU;wBAC5G,MAAM,8MAAA,CAAA,WAAQ;wBACd,OAAM;wBACN,SAAS;wBACT,OAAO,CAAC;;;;;;kCAEV,8OAAC;wBACC,OAAM;wBACN,OAAO,gBAAgB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,cAAc,eAAe,IAAI,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,iBAAiB,eAAe;wBACtH,MAAM,sMAAA,CAAA,SAAM;wBACZ,OAAM;wBACN,SAAS;wBACT,OAAO,CAAC;;;;;;kCAEV,8OAAC;wBACC,OAAM;wBACN,OAAO,gBAAgB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,cAAc,YAAY,IAAI,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,iBAAiB,UAAU;wBAC9G,QAAQ,CAAA,GAAA,mHAAA,CAAA,mBAAgB,AAAD,EAAE,iBAAiB,eAAe;wBACzD,MAAM,iBAAiB,QAAQ,IAAI,IAAI,kNAAA,CAAA,aAAU,GAAG,sNAAA,CAAA,eAAY;wBAChE,OAAO,iBAAiB,QAAQ,IAAI,IAAI,UAAU;wBAClD,SAAS;wBACT,OAAO,CAAC;;;;;;;;;;;;0BAKZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAGzC,8OAAC;gCACC,SAAS,IAAM,0BAA0B,CAAC;gCAC1C,WAAU;;kDAEV,8OAAC,8MAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;oBAIT,kCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;6CAGjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB,wBAAwB;;;;;;0DAE1D,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;4CACtC,mCACC,8OAAC;gDAAI,WAAU;;oDACZ,kBAAkB,oBAAoB,CAAC,OAAO,CAAC;oDAAG;;;;;;;;;;;;;kDAKzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB,eAAe;;;;;;0DAEjD,8OAAC;gDAAI,WAAU;0DAAyB;;;;;;0DACxC,8OAAC;gDAAI,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;0CAMjD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB,eAAe;;;;;;0DAEjD,8OAAC;gDAAI,WAAU;0DAA0B;;;;;;0DACzC,8OAAC;gDAAI,WAAU;0DAA+B;;;;;;;;;;;;kDAKhD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,mBAAmB,qBAAqB;;;;;;0DAE3C,8OAAC;gDAAI,WAAU;0DAA0B;;;;;;0DACzC,8OAAC;gDAAI,WAAU;;oDAA+B;oDAC1C,gBAAgB,eAAe;oDAAM;oDAAK,gBAAgB,eAAe;;;;;;;;;;;;;;;;;;;0CAKjF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,mBAAmB,uBAAuB;;;;;;0DAE7C,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;0DACvC,8OAAC;gDAAI,WAAU;0DAA6B;;;;;;;;;;;;kDAK9C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,mBAAmB,uBAAuB;;;;;;0DAE5D,8OAAC;gDAAI,WAAU;0DAA0B;;;;;;0DACzC,8OAAC;gDAAI,WAAU;0DAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUvD,wCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,8OAAC;gCACC,SAAS,IAAM,0BAA0B;gCACzC,WAAU;0CAEV,cAAA,8OAAC,4MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIvB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,MAAK;wCACL,OAAO,mBAAmB,oBAAoB;wCAC9C,UAAU,CAAC,IAAM,sBAAsB,CAAA,OAAQ,CAAC;oDAC9C,GAAG,IAAI;oDACP,sBAAsB,OAAO,EAAE,MAAM,CAAC,KAAK;gDAC7C,CAAC;wCACD,WAAU;wCACV,aAAY;;;;;;kDAEd,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;0CAK5C,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,MAAK;wCACL,OAAO,mBAAmB,WAAW;wCACrC,UAAU,CAAC,IAAM,sBAAsB,CAAA,OAAQ,CAAC;oDAC9C,GAAG,IAAI;oDACP,aAAa,OAAO,EAAE,MAAM,CAAC,KAAK;gDACpC,CAAC;wCACD,WAAU;wCACV,aAAY;;;;;;kDAEd,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;0CAK5C,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,MAAK;wCACL,OAAO,mBAAmB,WAAW;wCACrC,UAAU,CAAC,IAAM,sBAAsB,CAAA,OAAQ,CAAC;oDAC9C,GAAG,IAAI;oDACP,aAAa,OAAO,EAAE,MAAM,CAAC,KAAK;gDACpC,CAAC;wCACD,WAAU;wCACV,aAAY;;;;;;kDAEd,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;kCAM9C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,0BAA0B;gCACzC,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;;oCAET,+BACC,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAElB,8OAAC;kDAAM,iBAAiB,cAAc;;;;;;;;;;;;;;;;;;;;;;;;0BAO9C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;kDACpD,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;0CAGpB,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAoC;;;;;;kEACpD,8OAAC;wDAAK,WAAU;;4DACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB,eAAe;4DAAO;;;;;;;;;;;;;0DAG1D,8OAAC;gDACC,OAAO;gDACP,KAAK,gBAAgB,eAAe;gDACpC,OAAM;gDACN,OAAM;;;;;;;;;;;;kDAKV,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAoC;;;;;;kEACpD,8OAAC;wDAAK,WAAU;;4DACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB,eAAe;4DAAM;;;;;;;;;;;;;0DAGzD,8OAAC;gDAAI,WAAU;0DACZ,MAAM,IAAI,CAAC;oDAAE,QAAQ;gDAAE,GAAG,CAAC,GAAG,kBAC7B,8OAAC;wDAEC,WAAW,CAAC,iEAAiE,EAC3E,IAAI,IACA,gCACA,6BACJ;;4DACH;4DACQ,IAAI;;uDAPN;;;;;;;;;;0DAWX,8OAAC;gDAAI,WAAU;;oDAAyC;oDAC1B,gBAAgB,eAAe;oDAAM;oDAAK,gBAAgB,eAAe;oDAAK;;;;;;;;;;;;;kDAK9G,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;;gEAAe,YAAY,aAAa;gEAAC;;;;;;;;;;;;;8DAE3D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;;gEAA4B,YAAY,WAAW;gEAAC;;;;;;;;;;;;;8DAEtE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;sEAAe,YAAY,WAAW;;;;;;;;;;;;8DAExD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;;gEAAe,YAAY,UAAU;gEAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;kDACpD,8OAAC,kNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;4BAGtB,gBAAgB,oBAAoB,eAAe,gBAAgB,CAAC,MAAM,GAAG,kBAC5E,8OAAC;gCAAI,WAAU;0CACZ,eAAe,gBAAgB,CAAC,GAAG,CAAC,CAAC,sBACpC,8OAAC;wCAAmB,WAAU;;0DAC5B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAA6B,MAAM,MAAM;;;;;;kEACzD,8OAAC;wDAAK,WAAU;;4DACb,MAAM,WAAW;4DAAC;4DAAI,KAAK,KAAK,CAAC,CAAC,gBAAgB,eAAe,KAAK,IAAI,CAAC,gBAAgB,eAAe,IAAI;4DAAG;;;;;;;;;;;;;0DAItH,8OAAC;gDACC,OAAO,MAAM,eAAe;gDAC5B,KAAK,gBAAgB,eAAe;gDACpC,OAAO,CAAC,WAAW,EAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,eAAe,GAAG;gDAC5D,OAAM;;;;;;0DAGR,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;4DAAK;4DAAO,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,UAAU;;;;;;;kEAC5C,8OAAC;;4DAAK;4DAAY,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,eAAe,GAAG,MAAM,UAAU;;;;;;;;;;;;;;uCAjBnE,MAAM,EAAE;;;;;;;;;qDAuBtB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;kDAAE;;;;;;kDACH,8OAAC;wCAAE,WAAU;kDAAe;;;;;;;;;;;;;;;;;;;;;;;;0BAOpC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,8OAAC;gCACC,SAAS;gCACT,UAAU,kBAAkB,qBAAqB;gCACjD,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAW,CAAC,QAAQ,EAAE,AAAC,kBAAkB,qBAAqB,mBAAoB,iBAAiB,IAAI;;;;;;kDAClH,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;kCAIV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDACb,oBAAoB,GAAG,kBAAkB,oBAAoB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG;;;;;;;;;;;kDAGnF,8OAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,8OAAC;wCAAE,WAAU;kDAAyB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB,wBAAwB;;;;;;;;;;;;0CAG/F,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDACb,mBAAmB,uBAAuB;;;;;;;;;;;kDAG/C,8OAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAGvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDACb,mBAAmB,qBAAqB;;;;;;;;;;;kDAG7C,8OAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAGvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,mBAAmB,uBAAuB,GAAG,OAAO,CAAC,KAAK;;;;;;;;;;;kDAG9E,8OAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;0BAM3C,8OAAC;gBAAI,WAAU;;oBACZ,CAAC,iCACA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAA4B;;;;;;;;;;;;;;;;;;;;;;;oBAQhD,qBAAqB,kBAAkB,oBAAoB,GAAG,oBAC7D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAsC;;;;;;sDACpD,8OAAC;4CAAE,WAAU;;gDAA+B;gDACtB,kBAAkB,oBAAoB,CAAC,OAAO,CAAC;gDAAG;;;;;;;;;;;;;;;;;;;;;;;;oBAQ/E,kBAAkB,eAAe,WAAW,GAAG,eAAe,WAAW,GAAG,mBAC3E,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAsC;;;;;;sDACpD,8OAAC;4CAAE,WAAU;sDAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU5D", "debugId": null}}]}