import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { BackgroundDataProvider } from '@/components/providers/BackgroundDataProvider';
import { AppInitializer } from '@/components/AppInitializer';
import { PerformanceMonitor } from '@/components/PerformanceMonitor';

export default function Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <AppInitializer>
      <BackgroundDataProvider>
        <DashboardLayout>{children}</DashboardLayout>
        <PerformanceMonitor />
      </BackgroundDataProvider>
    </AppInitializer>
  );
}
