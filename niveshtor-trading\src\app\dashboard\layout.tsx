import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { BackgroundDataProvider } from '@/components/providers/BackgroundDataProvider';
import { AppInitializer } from '@/components/AppInitializer';
import { PerformanceMonitor } from '@/components/PerformanceMonitor';
// Import service initializer to ensure all background services start
import '@/lib/service-initializer';

export default function Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <AppInitializer>
      <BackgroundDataProvider>
        <DashboardLayout>{children}</DashboardLayout>
        <PerformanceMonitor />
      </BackgroundDataProvider>
    </AppInitializer>
  );
}
