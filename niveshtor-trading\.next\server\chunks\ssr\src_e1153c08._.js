module.exports = {

"[project]/src/components/ui/RealTimeIndicator.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Real-time update status indicator
__turbopack_context__.s({
    "CompactRealTimeIndicator": ()=>CompactRealTimeIndicator,
    "RealTimeIndicator": ()=>RealTimeIndicator,
    "useRealTimeIndicator": ()=>useRealTimeIndicator
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wifi$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Wifi$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/wifi.js [app-ssr] (ecmascript) <export default as Wifi>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wifi$2d$off$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__WifiOff$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/wifi-off.js [app-ssr] (ecmascript) <export default as WifiOff>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$activity$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Activity$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/activity.js [app-ssr] (ecmascript) <export default as Activity>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-ssr] (ecmascript) <export default as Clock>");
;
;
;
function RealTimeIndicator({ isActive, lastUpdate, updateCount, error, className = '' }) {
    const formatLastUpdate = (date)=>{
        if (!date) return 'Never';
        const now = new Date();
        const diff = now.getTime() - date.getTime();
        const seconds = Math.floor(diff / 1000);
        if (seconds < 60) return `${seconds}s ago`;
        if (seconds < 3600) return `${Math.floor(seconds / 60)}m ago`;
        return date.toLocaleTimeString();
    };
    const getStatusColor = ()=>{
        if (error) return 'text-red-500';
        if (isActive) return 'text-green-500';
        return 'text-gray-400';
    };
    const getStatusIcon = ()=>{
        if (error) return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wifi$2d$off$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__WifiOff$3e$__["WifiOff"], {
            className: "h-4 w-4"
        }, void 0, false, {
            fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
            lineNumber: 39,
            columnNumber: 23
        }, this);
        if (isActive) return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wifi$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Wifi$3e$__["Wifi"], {
            className: "h-4 w-4"
        }, void 0, false, {
            fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
            lineNumber: 40,
            columnNumber: 26
        }, this);
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wifi$2d$off$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__WifiOff$3e$__["WifiOff"], {
            className: "h-4 w-4"
        }, void 0, false, {
            fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
            lineNumber: 41,
            columnNumber: 12
        }, this);
    };
    const getStatusText = ()=>{
        if (error) return 'Connection Error';
        if (isActive) return 'Live Updates';
        return 'Offline';
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `flex items-center space-x-2 text-sm ${className}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `flex items-center space-x-1 ${getStatusColor()}`,
                children: [
                    getStatusIcon(),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "font-medium",
                        children: getStatusText()
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
                        lineNumber: 55,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
                lineNumber: 53,
                columnNumber: 7
            }, this),
            isActive && updateCount > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center space-x-1 text-gray-500",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$activity$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Activity$3e$__["Activity"], {
                        className: "h-3 w-3"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
                        lineNumber: 61,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: [
                            updateCount,
                            " updates"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
                        lineNumber: 62,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
                lineNumber: 60,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center space-x-1 text-gray-500",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                        className: "h-3 w-3"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
                        lineNumber: 68,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: formatLastUpdate(lastUpdate)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
                        lineNumber: 69,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
                lineNumber: 67,
                columnNumber: 7
            }, this),
            error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-red-500 text-xs",
                children: error.message
            }, void 0, false, {
                fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
                lineNumber: 74,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
        lineNumber: 51,
        columnNumber: 5
    }, this);
}
function CompactRealTimeIndicator({ isActive, lastUpdate, error, className = '' }) {
    const getStatusColor = ()=>{
        if (error) return 'text-red-500';
        if (isActive) return 'text-green-500';
        return 'text-gray-400';
    };
    const getStatusIcon = ()=>{
        if (error) return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wifi$2d$off$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__WifiOff$3e$__["WifiOff"], {
            className: "h-3 w-3"
        }, void 0, false, {
            fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
            lineNumber: 96,
            columnNumber: 23
        }, this);
        if (isActive) return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wifi$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Wifi$3e$__["Wifi"], {
            className: "h-3 w-3"
        }, void 0, false, {
            fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
            lineNumber: 97,
            columnNumber: 26
        }, this);
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wifi$2d$off$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__WifiOff$3e$__["WifiOff"], {
            className: "h-3 w-3"
        }, void 0, false, {
            fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
            lineNumber: 98,
            columnNumber: 12
        }, this);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `flex items-center space-x-1 ${getStatusColor()} ${className}`,
        children: [
            getStatusIcon(),
            isActive && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-2 h-2 bg-green-500 rounded-full animate-pulse"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
                lineNumber: 105,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
        lineNumber: 102,
        columnNumber: 5
    }, this);
}
function useRealTimeIndicator() {
    const [isActive, setIsActive] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useState(false);
    const [lastUpdate, setLastUpdate] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useState(null);
    const [updateCount, setUpdateCount] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useState(0);
    const [error, setError] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useState(null);
    // Listen for real-time updates
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        const handleRealTimeUpdate = (event)=>{
            setLastUpdate(new Date());
            setUpdateCount((prev)=>prev + 1);
            setError(null);
            setIsActive(true);
        };
        const handleError = (event)=>{
            setError(event.detail.error);
        };
        window.addEventListener('realTimePriceUpdate', handleRealTimeUpdate);
        window.addEventListener('realTimeError', handleError);
        return ()=>{
            window.removeEventListener('realTimePriceUpdate', handleRealTimeUpdate);
            window.removeEventListener('realTimeError', handleError);
        };
    }, []);
    return {
        isActive,
        lastUpdate,
        updateCount,
        error,
        clearError: ()=>setError(null)
    };
}
}),
"[project]/src/hooks/useBackgroundData.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// React hook for seamless background data integration
__turbopack_context__.s({
    "useBackgroundData": ()=>useBackgroundData,
    "useBackgroundNameUpdates": ()=>useBackgroundNameUpdates,
    "useBackgroundPriceUpdates": ()=>useBackgroundPriceUpdates
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$background$2d$data$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/background-data-service.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stock$2d$names$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stock-names-service.ts [app-ssr] (ecmascript)");
;
;
;
function useBackgroundData(options = {}) {
    const { enablePriceUpdates = true, enableNameUpdates = true, enableRealTimeUpdates = false, onError, onRealTimeUpdate } = options;
    // State
    const [isNamesReady, setIsNamesReady] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isUpdating, setIsUpdating] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [lastUpdate, setLastUpdate] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [stockNames, setStockNames] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(new Map());
    // Refs
    const listenerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const realTimeListenerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const mountedRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(true);
    // Create listener
    const createListener = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>({
            onPriceUpdate: (data)=>{
                if (!mountedRef.current || !enablePriceUpdates) return;
                console.log(`🔄 Received background price update: ${data.length} stocks`);
                setLastUpdate(new Date());
                setIsUpdating(false);
                // Trigger custom event for other components to listen
                window.dispatchEvent(new CustomEvent('backgroundPriceUpdate', {
                    detail: {
                        data,
                        timestamp: new Date()
                    }
                }));
            },
            onNamesUpdate: (namesMap)=>{
                if (!mountedRef.current || !enableNameUpdates) return;
                console.log(`📝 Received background names update: ${namesMap.size} names`);
                setStockNames(new Map(namesMap));
                setIsNamesReady(true);
                setError(null);
                // Trigger custom event for other components to listen
                window.dispatchEvent(new CustomEvent('backgroundNamesUpdate', {
                    detail: {
                        namesMap,
                        timestamp: new Date()
                    }
                }));
            },
            onError: (err)=>{
                if (!mountedRef.current) return;
                console.error('❌ Background data error:', err);
                setError(err);
                setIsUpdating(false);
                if (onError) {
                    onError(err);
                }
            }
        }), [
        enablePriceUpdates,
        enableNameUpdates,
        onError
    ]);
    // Create real-time listener
    const createRealTimeListener = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>({
            onRealTimeUpdate: (quotes)=>{
                if (!mountedRef.current || !enableRealTimeUpdates) return;
                console.log(`⚡ Received real-time update: ${quotes.length} quotes`);
                setLastUpdate(new Date());
                // Call custom callback if provided
                if (onRealTimeUpdate) {
                    onRealTimeUpdate(quotes);
                }
                // Trigger custom event for components to listen
                window.dispatchEvent(new CustomEvent('realTimePriceUpdate', {
                    detail: {
                        quotes,
                        timestamp: new Date()
                    }
                }));
            },
            onError: (err)=>{
                if (!mountedRef.current) return;
                console.error('❌ Real-time update error:', err);
                setError(err);
                if (onError) {
                    onError(err);
                }
            }
        }), [
        enableRealTimeUpdates,
        onRealTimeUpdate,
        onError
    ]);
    // Initialize and setup listener
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        mountedRef.current = true;
        const initializeData = async ()=>{
            try {
                // Check if names are already ready
                const namesReady = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$background$2d$data$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["backgroundDataService"].areNamesReady();
                setIsNamesReady(namesReady);
                if (namesReady) {
                    // Load existing cached names
                    const cachedNames = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stock$2d$names$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["stockNamesService"].getCachedStockNames();
                    setStockNames(cachedNames);
                }
                // Create and register listener
                const listener = createListener();
                listenerRef.current = listener;
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$background$2d$data$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["backgroundDataService"].addListener(listener);
                // Create and register real-time listener if enabled
                if (enableRealTimeUpdates) {
                    const realTimeListener = createRealTimeListener();
                    realTimeListenerRef.current = realTimeListener;
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$background$2d$data$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["backgroundDataService"].addRealTimeListener(realTimeListener);
                }
                // Initialize background service if not already done
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$background$2d$data$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["backgroundDataService"].initialize();
            } catch (err) {
                console.error('❌ Failed to initialize background data hook:', err);
                setError(err);
                // Set names ready to true to allow pages to work even if background service fails
                setIsNamesReady(true);
            }
        };
        initializeData();
        // Cleanup
        return ()=>{
            mountedRef.current = false;
            if (listenerRef.current) {
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$background$2d$data$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["backgroundDataService"].removeListener(listenerRef.current);
            }
            if (realTimeListenerRef.current) {
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$background$2d$data$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["backgroundDataService"].removeRealTimeListener(realTimeListenerRef.current);
            }
        };
    }, [
        createListener,
        createRealTimeListener,
        enableRealTimeUpdates
    ]);
    // Get stock name with fallback
    const getStockName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((symbol)=>{
        // First try from local state
        const name = stockNames.get(symbol);
        if (name) return name;
        // Fallback to sync method
        try {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stock$2d$names$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["stockNamesService"].getStockNameSync(symbol);
        } catch (error) {
            // Ultimate fallback - just return symbol without .NS
            return symbol.replace('.NS', '');
        }
    }, [
        stockNames
    ]);
    // Force update
    const forceUpdate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        setIsUpdating(true);
        setError(null);
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$background$2d$data$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["backgroundDataService"].forceUpdate();
        } catch (err) {
            setError(err);
            throw err;
        }
    }, []);
    // Clear error
    const clearError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        setError(null);
    }, []);
    return {
        // Status
        isNamesReady,
        isUpdating,
        lastUpdate,
        error,
        // Data
        stockNames,
        // Methods
        getStockName,
        forceUpdate,
        clearError
    };
}
function useBackgroundPriceUpdates(callback) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handlePriceUpdate = (event)=>{
            callback(event.detail.data);
        };
        window.addEventListener('backgroundPriceUpdate', handlePriceUpdate);
        return ()=>{
            window.removeEventListener('backgroundPriceUpdate', handlePriceUpdate);
        };
    }, [
        callback
    ]);
}
function useBackgroundNameUpdates(callback) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleNameUpdate = (event)=>{
            callback(event.detail.namesMap);
        };
        window.addEventListener('backgroundNamesUpdate', handleNameUpdate);
        return ()=>{
            window.removeEventListener('backgroundNamesUpdate', handleNameUpdate);
        };
    }, [
        callback
    ]);
}
}),
"[project]/src/hooks/useRealTimeStocks.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// React hook for real-time stock data updates
__turbopack_context__.s({
    "useRealTimeStocks": ()=>useRealTimeStocks,
    "useRealTimeUpdates": ()=>useRealTimeUpdates,
    "useSeamlessStockData": ()=>useSeamlessStockData
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useBackgroundData$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useBackgroundData.ts [app-ssr] (ecmascript)");
;
;
function useRealTimeStocks(options = {}) {
    const { onUpdate, onError } = options;
    // State
    const [isActive, setIsActive] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [lastUpdate, setLastUpdate] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [updateCount, setUpdateCount] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    // Refs
    const mountedRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(true);
    const onUpdateRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(onUpdate);
    const onErrorRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(onError);
    // Update refs when callbacks change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        onUpdateRef.current = onUpdate;
        onErrorRef.current = onError;
    }, [
        onUpdate,
        onError
    ]);
    // Real-time update handler
    const handleRealTimeUpdate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((quotes)=>{
        if (!mountedRef.current) return;
        setLastUpdate(new Date());
        setUpdateCount((prev)=>prev + 1);
        setError(null);
        // Call custom callback if provided
        if (onUpdateRef.current) {
            onUpdateRef.current(quotes);
        }
    }, []);
    // Error handler
    const handleError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((err)=>{
        if (!mountedRef.current) return;
        setError(err);
        if (onErrorRef.current) {
            onErrorRef.current(err);
        }
    }, []);
    // Background data hook with real-time enabled
    const { forceUpdate: forceBackgroundUpdate } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useBackgroundData$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBackgroundData"])({
        enableRealTimeUpdates: isActive,
        onRealTimeUpdate: handleRealTimeUpdate,
        onError: handleError
    });
    // Start real-time updates
    const start = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        console.log('🚀 Starting real-time stock updates...');
        setIsActive(true);
        setError(null);
    }, []);
    // Stop real-time updates
    const stop = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        console.log('⏹️ Stopping real-time stock updates...');
        setIsActive(false);
    }, []);
    // Force immediate update
    const forceUpdate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        try {
            await forceBackgroundUpdate();
        } catch (err) {
            handleError(err);
        }
    }, [
        forceBackgroundUpdate,
        handleError
    ]);
    // Clear error
    const clearError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        setError(null);
    }, []);
    // Cleanup on unmount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        mountedRef.current = true;
        return ()=>{
            mountedRef.current = false;
        };
    }, []);
    return {
        isActive,
        lastUpdate,
        updateCount,
        error,
        start,
        stop,
        forceUpdate,
        clearError
    };
}
function useRealTimeUpdates(callback) {
    const callbackRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(callback);
    // Update callback ref
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        callbackRef.current = callback;
    }, [
        callback
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleRealTimeUpdate = (event)=>{
            const { quotes } = event.detail;
            callbackRef.current(quotes);
        };
        window.addEventListener('realTimePriceUpdate', handleRealTimeUpdate);
        return ()=>{
            window.removeEventListener('realTimePriceUpdate', handleRealTimeUpdate);
        };
    }, []);
}
function useSeamlessStockData() {
    const [stocks, setStocks] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    // Background data for names
    const { isNamesReady, getStockName } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useBackgroundData$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBackgroundData"])();
    // Real-time updates for prices
    const realTimeStocks = useRealTimeStocks({
        onUpdate: (quotes)=>{
            // Update stocks with new price data
            setStocks((prevStocks)=>{
                const updatedStocks = [
                    ...prevStocks
                ];
                quotes.forEach((quote)=>{
                    const index = updatedStocks.findIndex((stock)=>stock.symbol === quote.symbol);
                    if (index >= 0) {
                        updatedStocks[index] = {
                            ...updatedStocks[index],
                            ...quote
                        };
                    } else {
                        updatedStocks.push({
                            ...quote,
                            name: getStockName(quote.symbol)
                        });
                    }
                });
                return updatedStocks;
            });
            setIsLoading(false);
        }
    });
    // Start real-time updates when names are ready
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (isNamesReady && !realTimeStocks.isActive) {
            realTimeStocks.start();
        }
    }, [
        isNamesReady,
        realTimeStocks
    ]);
    return {
        stocks,
        isLoading: isLoading && !isNamesReady,
        isRealTimeActive: realTimeStocks.isActive,
        lastUpdate: realTimeStocks.lastUpdate,
        updateCount: realTimeStocks.updateCount,
        error: realTimeStocks.error,
        forceUpdate: realTimeStocks.forceUpdate,
        getStockName
    };
}
}),
"[project]/src/lib/holdings-service.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Holdings service to manage current holdings across strategies
__turbopack_context__.s({
    "holdingsService": ()=>holdingsService
});
class HoldingsService {
    holdings = [
        // Sample holdings for demonstration - in real app, this would come from database
        {
            symbol: 'RELIANCE',
            strategy: 'DARVAS_BOX',
            quantity: 50,
            avgPrice: 2200.00,
            currentPrice: 2456.75,
            purchaseDate: new Date('2024-01-15')
        },
        {
            symbol: 'TCS',
            strategy: 'DARVAS_BOX',
            quantity: 25,
            avgPrice: 3400.00,
            currentPrice: 3234.50,
            purchaseDate: new Date('2024-01-20')
        },
        {
            symbol: 'HDFC',
            strategy: 'WEEKLY_HIGH',
            quantity: 40,
            avgPrice: 1600.00,
            currentPrice: 1678.90,
            purchaseDate: new Date('2024-02-01')
        },
        {
            symbol: 'INFY',
            strategy: 'BOH_FILTER',
            quantity: 60,
            avgPrice: 1500.00,
            currentPrice: 1456.80,
            purchaseDate: new Date('2024-02-10')
        }
    ];
    // Get all current holdings
    getAllHoldings() {
        return [
            ...this.holdings
        ];
    }
    // Get holdings for a specific strategy
    getHoldingsByStrategy(strategy) {
        return this.holdings.filter((holding)=>holding.strategy === strategy);
    }
    // Check if a stock is currently held in any strategy
    isStockInHoldings(symbol) {
        return this.holdings.some((holding)=>holding.symbol === symbol);
    }
    // Get all unique symbols in holdings
    getHoldingSymbols() {
        return [
            ...new Set(this.holdings.map((holding)=>holding.symbol))
        ];
    }
    // Add a new holding
    addHolding(holding) {
        const existingIndex = this.holdings.findIndex((h)=>h.symbol === holding.symbol && h.strategy === holding.strategy);
        if (existingIndex >= 0) {
            // Update existing holding (average price calculation)
            const existing = this.holdings[existingIndex];
            const totalQuantity = existing.quantity + holding.quantity;
            const totalValue = existing.quantity * existing.avgPrice + holding.quantity * holding.avgPrice;
            this.holdings[existingIndex] = {
                ...existing,
                quantity: totalQuantity,
                avgPrice: totalValue / totalQuantity,
                currentPrice: holding.currentPrice
            };
        } else {
            // Add new holding
            this.holdings.push({
                ...holding,
                purchaseDate: new Date()
            });
        }
    }
    // Remove a holding
    removeHolding(symbol, strategy) {
        this.holdings = this.holdings.filter((holding)=>!(holding.symbol === symbol && holding.strategy === strategy));
    }
    // Update current price for a holding
    updateCurrentPrice(symbol, currentPrice) {
        this.holdings.forEach((holding)=>{
            if (holding.symbol === symbol) {
                holding.currentPrice = currentPrice;
            }
        });
    }
    // Get stocks that were bought above ₹2000 and are still in holdings
    getStocksAbove2000InHoldings() {
        return this.holdings.filter((holding)=>holding.avgPrice > 2000 || holding.currentPrice > 2000).map((holding)=>holding.symbol);
    }
    // Check if a stock should be eligible for trading
    // (CMP < 2000 OR currently in holdings)
    isStockEligibleForTrading(symbol, currentPrice) {
        return currentPrice < 2000 || this.isStockInHoldings(symbol);
    }
}
const holdingsService = new HoldingsService();
}),
"[project]/src/lib/weekly-high-signal-detector.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Weekly High Signal Detection Service
// Monitors for new Weekly High Signals and triggers automatic GTT order creation
__turbopack_context__.s({
    "weeklyHighSignalDetector": ()=>weeklyHighSignalDetector
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$yahoo$2d$finance$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/yahoo-finance.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/nifty-stocks.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$holdings$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/holdings-service.ts [app-ssr] (ecmascript)");
;
;
;
class WeeklyHighSignalDetector {
    config = {
        enabled: true,
        pollingIntervalMinutes: 5,
        marketStartHour: 9,
        marketEndHour: 15,
        strongSignalThreshold: 2.0,
        moderateSignalThreshold: 5.0,
        minVolumeRatio: 1.2,
        maxInvestmentPerStock: 10000,
        investmentPerOrder: 2000
    };
    isRunning = false;
    pollingInterval = null;
    lastSignals = new Map();
    signalListeners = new Set();
    newSignalListeners = new Set();
    constructor(){
        console.log('📡 Weekly High Signal Detector initialized');
    }
    // Configuration management
    updateConfig(newConfig) {
        this.config = {
            ...this.config,
            ...newConfig
        };
        console.log('⚙️ Signal detector config updated:', this.config);
        if (this.isRunning) {
            this.stop();
            this.start();
        }
    }
    getConfig() {
        return {
            ...this.config
        };
    }
    // Check if market is currently open
    isMarketOpen() {
        const now = new Date();
        const currentHour = now.getHours();
        const currentMinute = now.getMinutes();
        // Market hours: 9:15 AM to 3:30 PM (Monday to Friday)
        const isWeekday = now.getDay() >= 1 && now.getDay() <= 5;
        const isAfterStart = currentHour > this.config.marketStartHour || currentHour === this.config.marketStartHour && currentMinute >= 15;
        const isBeforeEnd = currentHour < this.config.marketEndHour || currentHour === this.config.marketEndHour && currentMinute <= 30;
        return isWeekday && isAfterStart && isBeforeEnd;
    }
    // Generate mock OHLC data for weekly high calculation
    generateOHLCData(currentPrice) {
        const data = [];
        let price = currentPrice * 0.95; // Start 5% below current price
        for(let i = 0; i < 7; i++){
            const open = price;
            const high = price * (1 + Math.random() * 0.08); // Up to 8% higher
            const low = price * (1 - Math.random() * 0.05); // Up to 5% lower
            const close = low + Math.random() * (high - low);
            data.push({
                open,
                high,
                low,
                close
            });
            price = close;
        }
        return data;
    }
    // Calculate signal strength based on proximity to weekly high and volume
    calculateSignalStrength(currentPrice, weeklyHigh, volumeRatio) {
        const percentFromHigh = Math.abs((currentPrice - weeklyHigh) / weeklyHigh * 100);
        if (percentFromHigh <= this.config.strongSignalThreshold && volumeRatio >= this.config.minVolumeRatio) {
            return 'STRONG';
        } else if (percentFromHigh <= this.config.moderateSignalThreshold) {
            return 'MODERATE';
        } else {
            return 'WEAK';
        }
    }
    // Scan for Weekly High Signals
    async scanForSignals() {
        try {
            console.log('🔍 Scanning for Weekly High Signals...');
            // Fetch all Nifty 200 stocks with current prices
            const yahooSymbols = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NIFTY_200_SYMBOLS"].map(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getYahooSymbol"]);
            const quotes = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$yahoo$2d$finance$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["yahooFinanceService"].getMultipleQuotesWithCachedNames(yahooSymbols);
            console.log(`📊 Got quotes for ${quotes.length}/${yahooSymbols.length} symbols`);
            // Get current holdings
            const holdingSymbols = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$holdings$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["holdingsService"].getAllHoldings().map((h)=>h.symbol);
            // Process each stock for signal detection
            const signals = [];
            for(let i = 0; i < __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NIFTY_200_SYMBOLS"].length; i++){
                const nseSymbol = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NIFTY_200_SYMBOLS"][i];
                const yahooSymbol = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getYahooSymbol"])(nseSymbol);
                const quote = quotes.find((q)=>q.symbol === yahooSymbol);
                if (!quote || quote.price <= 0) continue;
                const price = quote.price;
                const inHoldings = holdingSymbols.includes(nseSymbol);
                const isEligible = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$holdings$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["holdingsService"].isStockEligibleForTrading(nseSymbol, price);
                // Create stock object for BOH eligibility check
                const stock = {
                    symbol: nseSymbol,
                    name: quote.name,
                    price,
                    change: quote.change || 0,
                    changePercent: quote.changePercent || 0,
                    volume: quote.volume || 0,
                    marketCap: quote.marketCap,
                    high52Week: quote.high52Week,
                    low52Week: quote.low52Week,
                    high52WeekDate: quote.high52WeekDate,
                    low52WeekDate: quote.low52WeekDate,
                    isEligible,
                    inHoldings
                };
                const stockWithBOH = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["addBOHEligibility"])(stock);
                // Only process BOH eligible stocks
                if (!stockWithBOH.isBOHEligible) continue;
                // Calculate weekly high data
                const ohlcData = this.generateOHLCData(price);
                const lastWeekHighest = Math.max(...ohlcData.map((d)=>d.high));
                const suggestedBuyPrice = lastWeekHighest + 0.05;
                const percentDifference = (price - suggestedBuyPrice) / suggestedBuyPrice * 100;
                const suggestedGTTQuantity = Math.floor(this.config.investmentPerOrder / suggestedBuyPrice);
                // Calculate volume metrics
                const avgVolume = quote.avgVolume || quote.volume || 1;
                const volumeRatio = quote.volume / avgVolume;
                // Calculate signal strength
                const signalStrength = this.calculateSignalStrength(price, lastWeekHighest, volumeRatio);
                // Only include signals that are MODERATE or STRONG
                if (signalStrength === 'WEAK') continue;
                const signal = {
                    symbol: nseSymbol,
                    name: quote.name,
                    currentPrice: price,
                    lastWeekHighest,
                    suggestedBuyPrice,
                    percentDifference,
                    suggestedGTTQuantity,
                    isBOHEligible: true,
                    inHoldings,
                    signalStrength,
                    detectedAt: new Date(),
                    volume: quote.volume || 0,
                    avgVolume,
                    volumeRatio
                };
                signals.push(signal);
            }
            console.log(`✅ Found ${signals.length} Weekly High Signals`);
            return signals;
        } catch (error) {
            console.error('❌ Error scanning for signals:', error);
            return [];
        }
    }
    // Detect new signals by comparing with previous scan
    detectNewSignals(currentSignals) {
        const newSignals = [];
        for (const signal of currentSignals){
            const previousSignal = this.lastSignals.get(signal.symbol);
            // Consider it a new signal if:
            // 1. Stock wasn't in previous signals, OR
            // 2. Signal strength improved (MODERATE -> STRONG), OR
            // 3. Price moved significantly closer to weekly high
            const isNewSignal = !previousSignal || signal.signalStrength === 'STRONG' && previousSignal.signalStrength !== 'STRONG' || Math.abs(signal.percentDifference) < Math.abs(previousSignal.percentDifference) - 1;
            if (isNewSignal) {
                newSignals.push(signal);
                console.log(`🆕 New signal detected: ${signal.symbol} (${signal.signalStrength})`);
            }
        }
        return newSignals;
    }
    // Main polling function
    async poll() {
        if (!this.config.enabled) {
            console.log('⏸️ Signal detection is disabled');
            return;
        }
        if (!this.isMarketOpen()) {
            console.log('🕐 Market is closed, skipping signal detection');
            return;
        }
        try {
            console.log('🔄 Polling for Weekly High Signals...');
            const currentSignals = await this.scanForSignals();
            const newSignals = this.detectNewSignals(currentSignals);
            // Update last signals cache
            this.lastSignals.clear();
            currentSignals.forEach((signal)=>{
                this.lastSignals.set(signal.symbol, signal);
            });
            // Notify listeners about all current signals
            this.signalListeners.forEach((listener)=>{
                try {
                    listener(currentSignals);
                } catch (error) {
                    console.error('❌ Error in signal listener:', error);
                }
            });
            // Notify listeners about new signals
            if (newSignals.length > 0) {
                console.log(`🚨 ${newSignals.length} new signals detected!`);
                newSignals.forEach((signal)=>{
                    this.newSignalListeners.forEach((listener)=>{
                        try {
                            listener(signal);
                        } catch (error) {
                            console.error('❌ Error in new signal listener:', error);
                        }
                    });
                });
            }
        } catch (error) {
            console.error('❌ Error in signal polling:', error);
        }
    }
    // Start the detection service
    start() {
        if (this.isRunning) {
            console.log('⚠️ Signal detector is already running');
            return;
        }
        console.log(`🚀 Starting Weekly High Signal Detector (polling every ${this.config.pollingIntervalMinutes} minutes)`);
        this.isRunning = true;
        // Initial scan
        this.poll();
        // Set up polling interval
        this.pollingInterval = setInterval(()=>{
            this.poll();
        }, this.config.pollingIntervalMinutes * 60 * 1000);
    }
    // Stop the detection service
    stop() {
        if (!this.isRunning) {
            console.log('⚠️ Signal detector is not running');
            return;
        }
        console.log('⏹️ Stopping Weekly High Signal Detector');
        this.isRunning = false;
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
            this.pollingInterval = null;
        }
    }
    // Add listener for all signals
    addSignalListener(listener) {
        this.signalListeners.add(listener);
    }
    // Remove listener for all signals
    removeSignalListener(listener) {
        this.signalListeners.delete(listener);
    }
    // Add listener for new signals only
    addNewSignalListener(listener) {
        this.newSignalListeners.add(listener);
    }
    // Remove listener for new signals
    removeNewSignalListener(listener) {
        this.newSignalListeners.delete(listener);
    }
    // Get current status
    getStatus() {
        return {
            isRunning: this.isRunning,
            isMarketOpen: this.isMarketOpen(),
            config: this.config,
            lastSignalCount: this.lastSignals.size,
            listenerCount: this.signalListeners.size + this.newSignalListeners.size
        };
    }
    // Manual trigger for testing
    async triggerManualScan() {
        console.log('🔧 Manual signal scan triggered');
        return await this.scanForSignals();
    }
}
const weeklyHighSignalDetector = new WeeklyHighSignalDetector();
// Auto-start the service when imported
if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
;
}),
"[project]/src/lib/automatic-gtt-service.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Automatic GTT Order Creation Service
// Creates GTT buy orders automatically when new Weekly High Signals are detected
__turbopack_context__.s({
    "automaticGTTService": ()=>automaticGTTService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$weekly$2d$high$2d$signal$2d$detector$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/weekly-high-signal-detector.ts [app-ssr] (ecmascript)");
;
class AutomaticGTTService {
    config = {
        enabled: true,
        maxOrdersPerDay: 20,
        maxInvestmentPerStock: 10000,
        investmentPerOrder: 2000,
        minSignalStrength: 'MODERATE',
        requireVolumeConfirmation: true,
        onlyDuringMarketHours: true
    };
    orders = [];
    dailyOrderCount = 0;
    lastResetDate = new Date().toDateString();
    orderListeners = new Set();
    isInitialized = false;
    constructor(){
        console.log('🤖 Automatic GTT Service initialized');
    }
    // Initialize the service and start listening for signals
    async initialize() {
        if (this.isInitialized) {
            console.log('⚠️ Automatic GTT Service already initialized');
            return;
        }
        console.log('🚀 Initializing Automatic GTT Service...');
        // Load existing orders from localStorage or API
        await this.loadExistingOrders();
        // Start listening for new signals
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$weekly$2d$high$2d$signal$2d$detector$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["weeklyHighSignalDetector"].addNewSignalListener(this.handleNewSignal.bind(this));
        // Reset daily counter if it's a new day
        this.resetDailyCounterIfNeeded();
        this.isInitialized = true;
        console.log('✅ Automatic GTT Service initialized successfully');
    }
    // Configuration management
    updateConfig(newConfig) {
        this.config = {
            ...this.config,
            ...newConfig
        };
        console.log('⚙️ Auto GTT config updated:', this.config);
    }
    getConfig() {
        return {
            ...this.config
        };
    }
    // Load existing orders (from localStorage for now, can be replaced with API)
    async loadExistingOrders() {
        try {
            const stored = localStorage.getItem('autoGTTOrders');
            if (stored) {
                const parsedOrders = JSON.parse(stored);
                this.orders = parsedOrders.map((order)=>({
                        ...order,
                        createdAt: new Date(order.createdAt)
                    }));
                console.log(`📂 Loaded ${this.orders.length} existing auto GTT orders`);
            }
            // Load daily counter
            const storedCounter = localStorage.getItem('autoGTTDailyCount');
            const storedDate = localStorage.getItem('autoGTTLastResetDate');
            if (storedCounter && storedDate === new Date().toDateString()) {
                this.dailyOrderCount = parseInt(storedCounter, 10);
            }
        } catch (error) {
            console.error('❌ Error loading existing orders:', error);
        }
    }
    // Save orders to localStorage
    saveOrders() {
        try {
            localStorage.setItem('autoGTTOrders', JSON.stringify(this.orders));
            localStorage.setItem('autoGTTDailyCount', this.dailyOrderCount.toString());
            localStorage.setItem('autoGTTLastResetDate', this.lastResetDate);
        } catch (error) {
            console.error('❌ Error saving orders:', error);
        }
    }
    // Reset daily counter if it's a new day
    resetDailyCounterIfNeeded() {
        const today = new Date().toDateString();
        if (this.lastResetDate !== today) {
            this.dailyOrderCount = 0;
            this.lastResetDate = today;
            console.log('🔄 Daily order counter reset for new day');
        }
    }
    // Check if we can create a new order
    canCreateOrder(signal) {
        // Check if service is enabled
        if (!this.config.enabled) {
            return {
                canCreate: false,
                reason: 'Automatic GTT service is disabled'
            };
        }
        // Check market hours if required
        if (this.config.onlyDuringMarketHours) {
            const now = new Date();
            const currentHour = now.getHours();
            const currentMinute = now.getMinutes();
            const isWeekday = now.getDay() >= 1 && now.getDay() <= 5;
            const isMarketOpen = isWeekday && (currentHour > 9 || currentHour === 9 && currentMinute >= 15) && (currentHour < 15 || currentHour === 15 && currentMinute <= 30);
            if (!isMarketOpen) {
                return {
                    canCreate: false,
                    reason: 'Market is closed'
                };
            }
        }
        // Check daily limit
        this.resetDailyCounterIfNeeded();
        if (this.dailyOrderCount >= this.config.maxOrdersPerDay) {
            return {
                canCreate: false,
                reason: 'Daily order limit reached'
            };
        }
        // Check signal strength
        const strengthOrder = {
            'WEAK': 1,
            'MODERATE': 2,
            'STRONG': 3
        };
        if (strengthOrder[signal.signalStrength] < strengthOrder[this.config.minSignalStrength]) {
            return {
                canCreate: false,
                reason: `Signal strength ${signal.signalStrength} below minimum ${this.config.minSignalStrength}`
            };
        }
        // Check volume confirmation if required
        if (this.config.requireVolumeConfirmation && signal.volumeRatio < 1.2) {
            return {
                canCreate: false,
                reason: 'Insufficient volume confirmation'
            };
        }
        // Check for existing pending orders for this stock
        const existingOrder = this.orders.find((order)=>order.symbol === signal.symbol && order.status === 'PENDING' && order.source === 'SIGNAL');
        if (existingOrder) {
            return {
                canCreate: false,
                reason: 'Pending order already exists for this stock'
            };
        }
        // Check investment limits
        const existingInvestment = this.orders.filter((order)=>order.symbol === signal.symbol && order.status === 'PENDING').reduce((sum, order)=>sum + order.triggerPrice * order.quantity, 0);
        const newInvestment = signal.suggestedBuyPrice * signal.suggestedGTTQuantity;
        if (existingInvestment + newInvestment > this.config.maxInvestmentPerStock) {
            return {
                canCreate: false,
                reason: 'Would exceed maximum investment per stock'
            };
        }
        // Check if quantity is valid
        if (signal.suggestedGTTQuantity <= 0) {
            return {
                canCreate: false,
                reason: 'Invalid quantity calculated'
            };
        }
        // Check if trigger price is reasonable
        if (signal.suggestedBuyPrice <= 0 || signal.suggestedBuyPrice > 5000) {
            return {
                canCreate: false,
                reason: 'Invalid trigger price'
            };
        }
        return {
            canCreate: true
        };
    }
    // Handle new signal detection
    async handleNewSignal(signal) {
        console.log(`🔔 New signal received: ${signal.symbol} (${signal.signalStrength})`);
        const validation = this.canCreateOrder(signal);
        if (!validation.canCreate) {
            console.log(`⏭️ Skipping order creation for ${signal.symbol}: ${validation.reason}`);
            return;
        }
        try {
            await this.createAutoGTTOrder(signal);
        } catch (error) {
            console.error(`❌ Failed to create auto GTT order for ${signal.symbol}:`, error);
        }
    }
    // Create automatic GTT order
    async createAutoGTTOrder(signal) {
        console.log(`🤖 Creating automatic GTT order for ${signal.symbol}...`);
        const order = {
            id: `auto_gtt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            symbol: signal.symbol,
            name: signal.name,
            orderType: 'BUY',
            triggerPrice: signal.suggestedBuyPrice,
            quantity: signal.suggestedGTTQuantity,
            status: 'PENDING',
            createdAt: new Date(),
            source: 'SIGNAL',
            signalStrength: signal.signalStrength,
            autoCreated: true,
            originalSignal: signal
        };
        // Add to orders list
        this.orders.push(order);
        this.dailyOrderCount++;
        // Save to storage
        this.saveOrders();
        // Log the creation
        console.log(`✅ Auto GTT order created: ${order.symbol} - Trigger: ₹${order.triggerPrice.toFixed(2)}, Qty: ${order.quantity}`);
        console.log(`📊 Daily orders: ${this.dailyOrderCount}/${this.config.maxOrdersPerDay}`);
        // Notify listeners
        this.orderListeners.forEach((listener)=>{
            try {
                listener(order);
            } catch (error) {
                console.error('❌ Error in order listener:', error);
            }
        });
        return order;
    }
    // Get all orders
    getAllOrders() {
        return [
            ...this.orders
        ];
    }
    // Get orders by status
    getOrdersByStatus(status) {
        return this.orders.filter((order)=>order.status === status);
    }
    // Get orders by source
    getOrdersBySource(source) {
        return this.orders.filter((order)=>order.source === source);
    }
    // Cancel an order
    cancelOrder(orderId) {
        const order = this.orders.find((o)=>o.id === orderId);
        if (order && order.status === 'PENDING') {
            order.status = 'CANCELLED';
            this.saveOrders();
            console.log(`❌ Order cancelled: ${order.symbol} (${orderId})`);
            return true;
        }
        return false;
    }
    // Update order status (for external triggers)
    updateOrderStatus(orderId, status) {
        const order = this.orders.find((o)=>o.id === orderId);
        if (order) {
            order.status = status;
            this.saveOrders();
            console.log(`🔄 Order status updated: ${order.symbol} -> ${status}`);
            return true;
        }
        return false;
    }
    // Add order listener
    addOrderListener(listener) {
        this.orderListeners.add(listener);
    }
    // Remove order listener
    removeOrderListener(listener) {
        this.orderListeners.delete(listener);
    }
    // Get service statistics
    getStatistics() {
        const today = new Date().toDateString();
        const todayOrders = this.orders.filter((order)=>order.createdAt.toDateString() === today && order.autoCreated);
        return {
            totalOrders: this.orders.length,
            autoCreatedOrders: this.orders.filter((o)=>o.autoCreated).length,
            todayOrders: todayOrders.length,
            dailyLimit: this.config.maxOrdersPerDay,
            pendingOrders: this.orders.filter((o)=>o.status === 'PENDING').length,
            triggeredOrders: this.orders.filter((o)=>o.status === 'TRIGGERED').length,
            cancelledOrders: this.orders.filter((o)=>o.status === 'CANCELLED').length,
            isEnabled: this.config.enabled,
            isInitialized: this.isInitialized
        };
    }
    // Manual trigger for testing
    async testCreateOrder(symbol) {
        console.log(`🧪 Test order creation for ${symbol}`);
        // Get current signals
        const signals = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$weekly$2d$high$2d$signal$2d$detector$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["weeklyHighSignalDetector"].triggerManualScan();
        const signal = signals.find((s)=>s.symbol === symbol);
        if (!signal) {
            console.log(`❌ No signal found for ${symbol}`);
            return null;
        }
        return await this.createAutoGTTOrder(signal);
    }
    // Start the service
    async start() {
        if (!this.isInitialized) {
            await this.initialize();
        }
        // Start the signal detector if not already running
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$weekly$2d$high$2d$signal$2d$detector$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["weeklyHighSignalDetector"].start();
        console.log('🚀 Automatic GTT Service started');
    }
    // Stop the service
    stop() {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$weekly$2d$high$2d$signal$2d$detector$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["weeklyHighSignalDetector"].stop();
        console.log('⏹️ Automatic GTT Service stopped');
    }
}
const automaticGTTService = new AutomaticGTTService();
// Auto-start the service when imported
if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
;
}),
"[project]/src/lib/central-data-manager.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Central Data Management Service
// Manages all stock data, caching, and real-time synchronization across pages
__turbopack_context__.s({
    "centralDataManager": ()=>centralDataManager
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$yahoo$2d$finance$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/yahoo-finance.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/nifty-stocks.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$holdings$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/holdings-service.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$weekly$2d$high$2d$signal$2d$detector$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/weekly-high-signal-detector.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$automatic$2d$gtt$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/automatic-gtt-service.ts [app-ssr] (ecmascript)");
;
;
;
;
;
class CentralDataManager {
    cache = {
        nifty200Stocks: [],
        bohEligibleStocks: [],
        weeklyHighSignals: [],
        gttOrders: [],
        lastUpdated: {
            nifty200: null,
            bohEligible: null,
            weeklyHighSignals: null,
            gttOrders: null
        },
        isLoading: {
            nifty200: false,
            bohEligible: false,
            weeklyHighSignals: false,
            gttOrders: false
        }
    };
    config = {
        nifty200UpdateInterval: 30,
        bohEligibleUpdateInterval: 60,
        weeklyHighSignalsUpdateInterval: 300,
        gttOrdersUpdateInterval: 30,
        marketStartHour: 9,
        marketEndHour: 15,
        enableRealTimeUpdates: true
    };
    intervals = new Map();
    listeners = new Map();
    isInitialized = false;
    isRunning = false;
    constructor(){
        console.log('📊 Central Data Manager initialized');
        this.initializeListeners();
    }
    initializeListeners() {
        this.listeners.set('nifty200', new Set());
        this.listeners.set('bohEligible', new Set());
        this.listeners.set('weeklyHighSignals', new Set());
        this.listeners.set('gttOrders', new Set());
    }
    // Configuration management
    updateConfig(newConfig) {
        this.config = {
            ...this.config,
            ...newConfig
        };
        console.log('⚙️ Central Data Manager config updated:', this.config);
        if (this.isRunning) {
            this.stop();
            this.start();
        }
    }
    getConfig() {
        return {
            ...this.config
        };
    }
    // Market hours detection
    isMarketOpen() {
        const now = new Date();
        const currentHour = now.getHours();
        const currentMinute = now.getMinutes();
        const isWeekday = now.getDay() >= 1 && now.getDay() <= 5;
        const isAfterStart = currentHour > this.config.marketStartHour || currentHour === this.config.marketStartHour && currentMinute >= 15;
        const isBeforeEnd = currentHour < this.config.marketEndHour || currentHour === this.config.marketEndHour && currentMinute <= 30;
        return isWeekday && isAfterStart && isBeforeEnd;
    }
    // Data loading methods
    async loadNifty200Stocks() {
        if (this.cache.isLoading.nifty200) return;
        this.cache.isLoading.nifty200 = true;
        console.log('📈 Loading Nifty 200 stocks...');
        try {
            const yahooSymbols = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NIFTY_200_SYMBOLS"].map(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getYahooSymbol"]);
            const quotes = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$yahoo$2d$finance$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["yahooFinanceService"].getMultipleQuotesWithCachedNames(yahooSymbols);
            const holdingSymbols = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$holdings$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["holdingsService"].getAllHoldings().map((h)=>h.symbol);
            const processedStocks = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NIFTY_200_SYMBOLS"].map((nseSymbol)=>{
                const yahooSymbol = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getYahooSymbol"])(nseSymbol);
                const quote = quotes.find((q)=>q.symbol === yahooSymbol);
                const price = quote?.price || 0;
                const inHoldings = holdingSymbols.includes(nseSymbol);
                const isEligible = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$holdings$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["holdingsService"].isStockEligibleForTrading(nseSymbol, price);
                const stock = {
                    symbol: nseSymbol,
                    name: quote?.name || nseSymbol,
                    price,
                    change: quote?.change || 0,
                    changePercent: quote?.changePercent || 0,
                    volume: quote?.volume || 0,
                    marketCap: quote?.marketCap,
                    high52Week: quote?.high52Week,
                    low52Week: quote?.low52Week,
                    high52WeekDate: quote?.high52WeekDate,
                    low52WeekDate: quote?.low52WeekDate,
                    isEligible,
                    inHoldings
                };
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["addBOHEligibility"])(stock);
            });
            this.cache.nifty200Stocks = processedStocks;
            this.cache.lastUpdated.nifty200 = new Date();
            console.log(`✅ Loaded ${processedStocks.length} Nifty 200 stocks`);
            this.notifyListeners('nifty200', processedStocks);
        } catch (error) {
            console.error('❌ Error loading Nifty 200 stocks:', error);
        } finally{
            this.cache.isLoading.nifty200 = false;
        }
    }
    async loadBOHEligibleStocks() {
        if (this.cache.isLoading.bohEligible) return;
        this.cache.isLoading.bohEligible = true;
        console.log('🔍 Loading BOH eligible stocks...');
        try {
            // Use cached Nifty 200 data if available and recent
            let stocks = this.cache.nifty200Stocks;
            if (stocks.length === 0 || !this.cache.lastUpdated.nifty200 || Date.now() - this.cache.lastUpdated.nifty200.getTime() > 60000) {
                await this.loadNifty200Stocks();
                stocks = this.cache.nifty200Stocks;
            }
            const bohEligibleStocks = stocks.filter((stock)=>stock.isBOHEligible);
            this.cache.bohEligibleStocks = bohEligibleStocks;
            this.cache.lastUpdated.bohEligible = new Date();
            console.log(`✅ Loaded ${bohEligibleStocks.length} BOH eligible stocks`);
            this.notifyListeners('bohEligible', bohEligibleStocks);
        } catch (error) {
            console.error('❌ Error loading BOH eligible stocks:', error);
        } finally{
            this.cache.isLoading.bohEligible = false;
        }
    }
    async loadWeeklyHighSignals() {
        if (this.cache.isLoading.weeklyHighSignals) return;
        this.cache.isLoading.weeklyHighSignals = true;
        console.log('📊 Loading Weekly High Signals...');
        try {
            const signals = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$weekly$2d$high$2d$signal$2d$detector$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["weeklyHighSignalDetector"].triggerManualScan();
            this.cache.weeklyHighSignals = signals;
            this.cache.lastUpdated.weeklyHighSignals = new Date();
            console.log(`✅ Loaded ${signals.length} Weekly High Signals`);
            this.notifyListeners('weeklyHighSignals', signals);
        } catch (error) {
            console.error('❌ Error loading Weekly High Signals:', error);
        } finally{
            this.cache.isLoading.weeklyHighSignals = false;
        }
    }
    async loadGTTOrders() {
        if (this.cache.isLoading.gttOrders) return;
        this.cache.isLoading.gttOrders = true;
        console.log('📋 Loading GTT orders...');
        try {
            const orders = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$automatic$2d$gtt$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["automaticGTTService"].getAllOrders();
            this.cache.gttOrders = orders;
            this.cache.lastUpdated.gttOrders = new Date();
            console.log(`✅ Loaded ${orders.length} GTT orders`);
            this.notifyListeners('gttOrders', orders);
        } catch (error) {
            console.error('❌ Error loading GTT orders:', error);
        } finally{
            this.cache.isLoading.gttOrders = false;
        }
    }
    // Listener management
    addListener(dataType, listener) {
        const listeners = this.listeners.get(dataType);
        if (listeners) {
            listeners.add(listener);
            console.log(`👂 Added listener for ${dataType}`);
        }
    }
    removeListener(dataType, listener) {
        const listeners = this.listeners.get(dataType);
        if (listeners) {
            listeners.delete(listener);
            console.log(`🔇 Removed listener for ${dataType}`);
        }
    }
    notifyListeners(dataType, data) {
        const listeners = this.listeners.get(dataType);
        if (listeners) {
            const timestamp = new Date();
            listeners.forEach((listener)=>{
                try {
                    listener(data, timestamp);
                } catch (error) {
                    console.error(`❌ Error in ${dataType} listener:`, error);
                }
            });
        }
    }
    // Public data access methods
    getNifty200Stocks() {
        return [
            ...this.cache.nifty200Stocks
        ];
    }
    getBOHEligibleStocks() {
        return [
            ...this.cache.bohEligibleStocks
        ];
    }
    getWeeklyHighSignals() {
        return [
            ...this.cache.weeklyHighSignals
        ];
    }
    getGTTOrders() {
        return [
            ...this.cache.gttOrders
        ];
    }
    getLastUpdated(dataType) {
        return this.cache.lastUpdated[dataType];
    }
    isDataLoading(dataType) {
        return this.cache.isLoading[dataType];
    }
    // Cache management
    getCacheStatus() {
        return {
            nifty200Count: this.cache.nifty200Stocks.length,
            bohEligibleCount: this.cache.bohEligibleStocks.length,
            weeklyHighSignalsCount: this.cache.weeklyHighSignals.length,
            gttOrdersCount: this.cache.gttOrders.length,
            lastUpdated: {
                ...this.cache.lastUpdated
            },
            isLoading: {
                ...this.cache.isLoading
            },
            listenerCounts: {
                nifty200: this.listeners.get('nifty200')?.size || 0,
                bohEligible: this.listeners.get('bohEligible')?.size || 0,
                weeklyHighSignals: this.listeners.get('weeklyHighSignals')?.size || 0,
                gttOrders: this.listeners.get('gttOrders')?.size || 0
            }
        };
    }
    // Force refresh methods
    async refreshNifty200() {
        await this.loadNifty200Stocks();
    }
    async refreshBOHEligible() {
        await this.loadBOHEligibleStocks();
    }
    async refreshWeeklyHighSignals() {
        await this.loadWeeklyHighSignals();
    }
    async refreshGTTOrders() {
        await this.loadGTTOrders();
    }
    async refreshAll() {
        console.log('🔄 Refreshing all data...');
        await Promise.all([
            this.loadNifty200Stocks(),
            this.loadBOHEligibleStocks(),
            this.loadWeeklyHighSignals(),
            this.loadGTTOrders()
        ]);
        console.log('✅ All data refreshed');
    }
    // Service lifecycle
    async initialize() {
        if (this.isInitialized) {
            console.log('⚠️ Central Data Manager already initialized');
            return;
        }
        console.log('🚀 Initializing Central Data Manager...');
        // Load initial data
        await this.refreshAll();
        this.isInitialized = true;
        console.log('✅ Central Data Manager initialized successfully');
    }
    start() {
        if (this.isRunning) {
            console.log('⚠️ Central Data Manager already running');
            return;
        }
        console.log('🚀 Starting Central Data Manager background updates...');
        // Set up periodic updates
        this.intervals.set('nifty200', setInterval(()=>{
            if (this.isMarketOpen() || !this.config.enableRealTimeUpdates) {
                this.loadNifty200Stocks();
            }
        }, this.config.nifty200UpdateInterval * 1000));
        this.intervals.set('bohEligible', setInterval(()=>{
            this.loadBOHEligibleStocks();
        }, this.config.bohEligibleUpdateInterval * 1000));
        this.intervals.set('weeklyHighSignals', setInterval(()=>{
            if (this.isMarketOpen() || !this.config.enableRealTimeUpdates) {
                this.loadWeeklyHighSignals();
            }
        }, this.config.weeklyHighSignalsUpdateInterval * 1000));
        this.intervals.set('gttOrders', setInterval(()=>{
            this.loadGTTOrders();
        }, this.config.gttOrdersUpdateInterval * 1000));
        this.isRunning = true;
        console.log('✅ Central Data Manager background updates started');
    }
    stop() {
        if (!this.isRunning) {
            console.log('⚠️ Central Data Manager not running');
            return;
        }
        console.log('⏹️ Stopping Central Data Manager background updates...');
        this.intervals.forEach((interval, dataType)=>{
            clearInterval(interval);
            console.log(`⏹️ Stopped ${dataType} updates`);
        });
        this.intervals.clear();
        this.isRunning = false;
        console.log('✅ Central Data Manager background updates stopped');
    }
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            isRunning: this.isRunning,
            isMarketOpen: this.isMarketOpen(),
            config: this.config,
            cache: this.getCacheStatus()
        };
    }
}
const centralDataManager = new CentralDataManager();
// Auto-initialize and start the service when imported
if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
;
}),
"[project]/src/lib/service-initializer.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

// Service Initializer
// Ensures all background services are imported and auto-started when the app loads
__turbopack_context__.s({
    "initializeAllServices": ()=>initializeAllServices
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/central-data-manager.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$automatic$2d$gtt$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/automatic-gtt-service.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$weekly$2d$high$2d$signal$2d$detector$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/weekly-high-signal-detector.ts [app-ssr] (ecmascript)");
;
;
;
console.log('🚀 Service Initializer: Importing all background services...');
const initializeAllServices = async ()=>{
    console.log('🔧 Initializing all background services...');
    try {
        // Wait a moment for auto-start to complete
        await new Promise((resolve)=>setTimeout(resolve, 1000));
        // Check service status
        const centralStatus = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["centralDataManager"].getStatus();
        const gttStats = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$automatic$2d$gtt$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["automaticGTTService"].getStatistics();
        const signalStatus = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$weekly$2d$high$2d$signal$2d$detector$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["weeklyHighSignalDetector"].getStatus();
        console.log('📊 Service Status Check:');
        console.log('- Central Data Manager:', centralStatus.isInitialized ? '✅ Running' : '❌ Not Running');
        console.log('- Automatic GTT Service:', gttStats.isInitialized ? '✅ Running' : '❌ Not Running');
        console.log('- Weekly High Signal Detector:', signalStatus.isRunning ? '✅ Running' : '❌ Not Running');
        return {
            centralDataManager: centralStatus.isInitialized,
            automaticGTTService: gttStats.isInitialized,
            weeklyHighSignalDetector: signalStatus.isRunning
        };
    } catch (error) {
        console.error('❌ Service initialization check failed:', error);
        return {
            centralDataManager: false,
            automaticGTTService: false,
            weeklyHighSignalDetector: false
        };
    }
};
;
}),
"[project]/src/lib/service-initializer.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/central-data-manager.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$automatic$2d$gtt$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/automatic-gtt-service.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$weekly$2d$high$2d$signal$2d$detector$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/weekly-high-signal-detector.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$service$2d$initializer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/service-initializer.ts [app-ssr] (ecmascript) <locals>");
}),
"[project]/src/hooks/useCentralData.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// React Hook for Central Data Manager Integration
// Provides real-time data access and automatic updates for all stock-related pages
__turbopack_context__.s({
    "useBOHEligibleStocks": ()=>useBOHEligibleStocks,
    "useCentralData": ()=>useCentralData,
    "useGTTOrders": ()=>useGTTOrders,
    "useNifty200Stocks": ()=>useNifty200Stocks,
    "useWeeklyHighSignals": ()=>useWeeklyHighSignals
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$service$2d$initializer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/service-initializer.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/central-data-manager.ts [app-ssr] (ecmascript)");
;
;
function useCentralData() {
    const [nifty200, setNifty200] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        data: [],
        lastUpdated: null,
        isLoading: false,
        error: null
    });
    const [bohEligible, setBohEligible] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        data: [],
        lastUpdated: null,
        isLoading: false,
        error: null
    });
    const [weeklyHighSignals, setWeeklyHighSignals] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        data: [],
        lastUpdated: null,
        isLoading: false,
        error: null
    });
    const [gttOrders, setGttOrders] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        data: [],
        lastUpdated: null,
        isLoading: false,
        error: null
    });
    const [isInitialized, setIsInitialized] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isServiceRunning, setIsServiceRunning] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const pollingIntervals = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(new Map());
    // Fetch data directly from services
    const fetchData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (dataType)=>{
        try {
            let data = [];
            let lastUpdated = null;
            switch(dataType){
                case 'nifty200':
                    data = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["centralDataManager"].getNifty200Stocks();
                    lastUpdated = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["centralDataManager"].getLastUpdated('nifty200');
                    setNifty200({
                        data,
                        lastUpdated,
                        isLoading: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["centralDataManager"].isDataLoading('nifty200'),
                        error: null
                    });
                    break;
                case 'bohEligible':
                    data = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["centralDataManager"].getBOHEligibleStocks();
                    lastUpdated = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["centralDataManager"].getLastUpdated('bohEligible');
                    setBohEligible({
                        data,
                        lastUpdated,
                        isLoading: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["centralDataManager"].isDataLoading('bohEligible'),
                        error: null
                    });
                    break;
                case 'weeklyHighSignals':
                    data = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["centralDataManager"].getWeeklyHighSignals();
                    lastUpdated = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["centralDataManager"].getLastUpdated('weeklyHighSignals');
                    setWeeklyHighSignals({
                        data,
                        lastUpdated,
                        isLoading: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["centralDataManager"].isDataLoading('weeklyHighSignals'),
                        error: null
                    });
                    break;
                case 'gttOrders':
                    data = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["centralDataManager"].getGTTOrders();
                    lastUpdated = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["centralDataManager"].getLastUpdated('gttOrders');
                    setGttOrders({
                        data,
                        lastUpdated,
                        isLoading: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["centralDataManager"].isDataLoading('gttOrders'),
                        error: null
                    });
                    break;
            }
            console.log(`📊 Updated ${dataType}: ${data.length} items`);
        } catch (error) {
            console.error(`❌ Error fetching ${dataType}:`, error);
            const errorState = {
                data: [],
                lastUpdated: null,
                isLoading: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
            switch(dataType){
                case 'nifty200':
                    setNifty200((prev)=>({
                            ...prev,
                            ...errorState
                        }));
                    break;
                case 'bohEligible':
                    setBohEligible((prev)=>({
                            ...prev,
                            ...errorState
                        }));
                    break;
                case 'weeklyHighSignals':
                    setWeeklyHighSignals((prev)=>({
                            ...prev,
                            ...errorState
                        }));
                    break;
                case 'gttOrders':
                    setGttOrders((prev)=>({
                            ...prev,
                            ...errorState
                        }));
                    break;
            }
        }
    }, []);
    // Check service status directly
    const checkServiceStatus = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        try {
            const status = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["centralDataManager"].getStatus();
            setIsInitialized(status.isInitialized);
            setIsServiceRunning(status.isRunning);
            console.log('📊 Service status:', {
                initialized: status.isInitialized,
                running: status.isRunning,
                marketOpen: status.isMarketOpen
            });
        } catch (error) {
            console.error('❌ Error checking service status:', error);
            setIsInitialized(false);
            setIsServiceRunning(false);
        }
    }, []);
    // Initialize service directly if not already initialized
    const initializeService = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        try {
            console.log('🚀 Initializing Central Data Manager directly...');
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["centralDataManager"].getStatus().isInitialized) {
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["centralDataManager"].initialize();
                console.log('✅ Central Data Manager initialized');
            }
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["centralDataManager"].getStatus().isRunning) {
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["centralDataManager"].start();
                console.log('✅ Central Data Manager started');
            }
            setIsInitialized(true);
            setIsServiceRunning(true);
        } catch (error) {
            console.error('❌ Error initializing service:', error);
            setIsInitialized(false);
            setIsServiceRunning(false);
        }
    }, []);
    // Refresh specific data type
    const refreshData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (dataType)=>{
        if (dataType) {
            await fetchData(dataType);
        } else {
            // Refresh all data
            await Promise.all([
                fetchData('nifty200'),
                fetchData('bohEligible'),
                fetchData('weeklyHighSignals'),
                fetchData('gttOrders')
            ]);
        }
    }, [
        fetchData
    ]);
    // Set up polling for real-time updates
    const setupPolling = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        // Clear existing intervals
        pollingIntervals.current.forEach((interval)=>clearInterval(interval));
        pollingIntervals.current.clear();
        // Set up new intervals
        const intervals = {
            nifty200: 30000,
            bohEligible: 60000,
            weeklyHighSignals: 300000,
            gttOrders: 30000 // 30 seconds
        };
        Object.entries(intervals).forEach(([dataType, interval])=>{
            const intervalId = setInterval(()=>{
                fetchData(dataType);
            }, interval);
            pollingIntervals.current.set(dataType, intervalId);
        });
        console.log('⏰ Polling intervals set up for real-time updates');
    }, [
        fetchData
    ]);
    // Set up data listeners for real-time updates
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        console.log('🔗 Setting up Central Data Manager listeners...');
        // Add listeners for each data type
        const nifty200Listener = (data, timestamp)=>{
            setNifty200({
                data,
                lastUpdated: timestamp,
                isLoading: false,
                error: null
            });
            console.log(`📊 Nifty200 data updated: ${data.length} stocks`);
        };
        const bohEligibleListener = (data, timestamp)=>{
            setBohEligible({
                data,
                lastUpdated: timestamp,
                isLoading: false,
                error: null
            });
            console.log(`📊 BOH Eligible data updated: ${data.length} stocks`);
        };
        const weeklyHighSignalsListener = (data, timestamp)=>{
            setWeeklyHighSignals({
                data,
                lastUpdated: timestamp,
                isLoading: false,
                error: null
            });
            console.log(`📊 Weekly High Signals updated: ${data.length} signals`);
        };
        const gttOrdersListener = (data, timestamp)=>{
            setGttOrders({
                data,
                lastUpdated: timestamp,
                isLoading: false,
                error: null
            });
            console.log(`📊 GTT Orders updated: ${data.length} orders`);
        };
        // Add listeners to Central Data Manager
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["centralDataManager"].addListener('nifty200', nifty200Listener);
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["centralDataManager"].addListener('bohEligible', bohEligibleListener);
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["centralDataManager"].addListener('weeklyHighSignals', weeklyHighSignalsListener);
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["centralDataManager"].addListener('gttOrders', gttOrdersListener);
        // Initialize and load initial data
        const initialize = async ()=>{
            checkServiceStatus();
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["centralDataManager"].getStatus().isInitialized) {
                await initializeService();
            }
            // Load initial data
            await refreshData();
        };
        initialize();
        // Cleanup listeners on unmount
        return ()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["centralDataManager"].removeListener('nifty200', nifty200Listener);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["centralDataManager"].removeListener('bohEligible', bohEligibleListener);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["centralDataManager"].removeListener('weeklyHighSignals', weeklyHighSignalsListener);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["centralDataManager"].removeListener('gttOrders', gttOrdersListener);
            pollingIntervals.current.forEach((interval)=>clearInterval(interval));
            pollingIntervals.current.clear();
        };
    }, []);
    // Re-setup polling when service status changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (isServiceRunning) {
            setupPolling();
        }
    }, [
        isServiceRunning,
        setupPolling
    ]);
    return {
        nifty200,
        bohEligible,
        weeklyHighSignals,
        gttOrders,
        refreshData,
        isInitialized,
        isServiceRunning
    };
}
function useNifty200Stocks() {
    const { nifty200, refreshData } = useCentralData();
    return {
        stocks: nifty200.data,
        lastUpdated: nifty200.lastUpdated,
        isLoading: nifty200.isLoading,
        error: nifty200.error,
        refresh: ()=>refreshData('nifty200')
    };
}
function useBOHEligibleStocks() {
    const { bohEligible, refreshData } = useCentralData();
    return {
        stocks: bohEligible.data,
        lastUpdated: bohEligible.lastUpdated,
        isLoading: bohEligible.isLoading,
        error: bohEligible.error,
        refresh: ()=>refreshData('bohEligible')
    };
}
function useWeeklyHighSignals() {
    const { weeklyHighSignals, refreshData } = useCentralData();
    return {
        signals: weeklyHighSignals.data,
        lastUpdated: weeklyHighSignals.lastUpdated,
        isLoading: weeklyHighSignals.isLoading,
        error: weeklyHighSignals.error,
        refresh: ()=>refreshData('weeklyHighSignals')
    };
}
function useGTTOrders() {
    const { gttOrders, refreshData } = useCentralData();
    return {
        orders: gttOrders.data,
        lastUpdated: gttOrders.lastUpdated,
        isLoading: gttOrders.isLoading,
        error: gttOrders.error,
        refresh: ()=>refreshData('gttOrders')
    };
}
}),
"[project]/src/app/dashboard/boh-eligible/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>BOHEligiblePage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-ssr] (ecmascript) <export default as Search>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/refresh-cw.js [app-ssr] (ecmascript) <export default as RefreshCw>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$funnel$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Filter$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/funnel.js [app-ssr] (ecmascript) <export default as Filter>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-alert.js [app-ssr] (ecmascript) <export default as AlertCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader.js [app-ssr] (ecmascript) <export default as Loader>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/cache-service.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$LoadingStates$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/LoadingStates.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$RealTimeIndicator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/RealTimeIndicator.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useBackgroundData$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useBackgroundData.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useRealTimeStocks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useRealTimeStocks.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useCentralData$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useCentralData.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
;
function BOHEligiblePage() {
    // Central Data Manager for instant BOH eligible stocks
    const { stocks: bohStocks, lastUpdated: bohLastUpdated, isLoading: bohIsLoading, error: bohError, refresh: refreshBOHData } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useCentralData$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBOHEligibleStocks"])();
    // Background data hook for stock names (fallback)
    const { isNamesReady, getStockName, forceUpdate: forceBackgroundUpdate, error: backgroundError } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useBackgroundData$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBackgroundData"])();
    // Search state
    const [searchQuery, setSearchQuery] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [searchResults, setSearchResults] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isSearching, setIsSearching] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Nifty 200 stocks state - now loads instantly with cached names
    const [niftyStocks, setNiftyStocks] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [loadingPriceData, setLoadingPriceData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [niftyError, setNiftyError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    // Filter state
    const [showFilters, setShowFilters] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [priceFilter, setPriceFilter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        min: 0,
        max: 10000
    });
    const [showOnlyHoldings, setShowOnlyHoldings] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Load price data only (names are handled by background service)
    const loadPriceData = async (forceRefresh = false)=>{
        setLoadingPriceData(true);
        setNiftyError(null);
        try {
            if (forceRefresh) {
                // Force background update for both names and prices
                await forceBackgroundUpdate();
            }
            const batchSize = 25;
            const totalBatches = Math.ceil(200 / batchSize);
            const allStocks = [];
            console.log(`🚀 Loading price data for ${totalBatches} batches${forceRefresh ? ' (force refresh)' : ''}...`);
            for(let batchIndex = 0; batchIndex < totalBatches; batchIndex++){
                try {
                    let batchData;
                    if (!forceRefresh) {
                        const cacheKey = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CacheKeys"].niftyStocks(batchIndex);
                        const cached = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cacheService"].get(cacheKey);
                        if (cached) {
                            batchData = cached;
                        }
                    }
                    if (!batchData) {
                        try {
                            // Pass forceRefresh parameter to API to control name caching
                            const url = `/api/stocks/nifty200?batchIndex=${batchIndex}&batchSize=${batchSize}${forceRefresh ? '&forceRefresh=true' : ''}`;
                            console.log(`🔄 Fetching batch ${batchIndex + 1}/${totalBatches}: ${url}`);
                            const response = await fetch(url, {
                                method: 'GET',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                // Add timeout to prevent hanging
                                signal: AbortSignal.timeout(30000) // 30 second timeout
                            });
                            if (!response.ok) {
                                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                            }
                            const data = await response.json();
                            if (data.success) {
                                batchData = data.data;
                                // Cache the batch data (but names are cached separately in the service)
                                if (!forceRefresh) {
                                    const cacheKey = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CacheKeys"].niftyStocks(batchIndex);
                                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cacheService"].set(cacheKey, batchData);
                                }
                                console.log(`✅ Successfully fetched batch ${batchIndex + 1}: ${batchData.stocks?.length || 0} stocks`);
                            } else {
                                console.error(`❌ API returned error for batch ${batchIndex}:`, data.error);
                                continue;
                            }
                        } catch (fetchError) {
                            console.error(`❌ Network error fetching batch ${batchIndex}:`, fetchError);
                            continue;
                        }
                    }
                    if (batchData && batchData.stocks) {
                        // Use cached names from background service for instant display
                        const stocksWithCachedNames = batchData.stocks.map((stock)=>({
                                ...stock,
                                name: getStockName(stock.symbol) || stock.name
                            }));
                        allStocks.push(...stocksWithCachedNames);
                        setNiftyStocks([
                            ...allStocks
                        ]);
                        console.log(`✅ Loaded batch ${batchIndex + 1}/${totalBatches}: ${stocksWithCachedNames.length} stocks (Total: ${allStocks.length})`);
                    }
                    if (batchIndex < totalBatches - 1) {
                        await new Promise((resolve)=>setTimeout(resolve, 200));
                    }
                } catch (batchError) {
                    console.error(`Error loading batch ${batchIndex}:`, batchError);
                }
            }
            setNiftyStocks(allStocks);
            console.log(`🎉 Loaded all price data: ${allStocks.length} total stocks`);
        } catch (error) {
            console.error('❌ Error loading price data:', error);
            // If we have no stocks at all, create fallback entries with cached names
            if (niftyStocks.length === 0 && isNamesReady) {
                console.log('🔄 Creating fallback stock entries with cached names...');
                // Import NIFTY_200_SYMBOLS and create basic entries
                __turbopack_context__.r("[project]/src/lib/nifty-stocks.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i).then(({ NIFTY_200_SYMBOLS, getYahooSymbol })=>{
                    const fallbackStocks = NIFTY_200_SYMBOLS.slice(0, 50).map((symbol)=>{
                        const yahooSymbol = getYahooSymbol(symbol);
                        return {
                            symbol: yahooSymbol || symbol,
                            name: getStockName(yahooSymbol || symbol),
                            price: 0,
                            change: 0,
                            changePercent: 0,
                            volume: 0,
                            high52Week: 0,
                            low52Week: 0,
                            high52WeekDate: undefined,
                            low52WeekDate: undefined,
                            isBOHEligible: false,
                            avgVolume: 0,
                            isEligible: true,
                            inHoldings: false
                        };
                    });
                    setNiftyStocks(fallbackStocks);
                    console.log(`✅ Created ${fallbackStocks.length} fallback stock entries`);
                });
            }
            setNiftyError('Some data may be unavailable. Showing cached information.');
        } finally{
            setLoadingPriceData(false);
        }
    };
    // Search within Nifty 200 stocks
    const handleSearch = async (query)=>{
        if (!query.trim()) {
            setSearchResults([]);
            return;
        }
        setIsSearching(true);
        try {
            const response = await fetch('/api/stocks/nifty200', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    searchQuery: query
                })
            });
            const data = await response.json();
            if (data.success) {
                setSearchResults(data.data.stocks);
            } else {
                setSearchResults([]);
            }
        } catch (error) {
            console.error('Search error:', error);
            setSearchResults([]);
        } finally{
            setIsSearching(false);
        }
    };
    // Filter stocks based on current filters
    const getFilteredStocks = (stocks)=>{
        return stocks.filter((stock)=>{
            // Only show BOH eligible stocks
            if (!stock.isBOHEligible) return false;
            // General filters
            if (stock.price < priceFilter.min || stock.price > priceFilter.max) return false;
            if (showOnlyHoldings && !stock.inHoldings) return false;
            return true;
        });
    };
    // Get BOH eligible stocks
    const getBOHEligibleStocks = (stocks)=>{
        return stocks.filter((stock)=>stock.isBOHEligible);
    };
    // Load initial data when names are ready
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (isNamesReady) {
            loadPriceData(false);
        }
    }, [
        isNamesReady
    ]);
    // Handle search with debouncing
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const debounceTimer = setTimeout(()=>{
            handleSearch(searchQuery);
        }, 300);
        return ()=>clearTimeout(debounceTimer);
    }, [
        searchQuery
    ]);
    // Real-time stock updates for BOH page
    const realTimeStocks = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useRealTimeStocks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRealTimeStocks"])({
        onUpdate: (quotes)=>{
            console.log(`⚡ BOH page received real-time update: ${quotes.length} quotes`);
            // Update existing stocks with new price data
            setNiftyStocks((currentStocks)=>{
                const updatedStocks = currentStocks.map((stock)=>{
                    const updatedQuote = quotes.find((quote)=>quote.symbol === stock.symbol);
                    if (updatedQuote) {
                        return {
                            ...stock,
                            price: updatedQuote.price || stock.price,
                            change: updatedQuote.change || stock.change,
                            changePercent: updatedQuote.changePercent || stock.changePercent,
                            volume: updatedQuote.volume || stock.volume,
                            high52Week: updatedQuote.high52Week || stock.high52Week,
                            low52Week: updatedQuote.low52Week || stock.low52Week,
                            high52WeekDate: updatedQuote.high52WeekDate || stock.high52WeekDate,
                            low52WeekDate: updatedQuote.low52WeekDate || stock.low52WeekDate,
                            isBOHEligible: updatedQuote.isBOHEligible || stock.isBOHEligible
                        };
                    }
                    return stock;
                });
                return updatedStocks;
            });
        },
        onError: (error)=>{
            console.error('❌ BOH real-time update error:', error);
            setNiftyError('Real-time updates temporarily unavailable');
        }
    });
    // Start real-time updates when names are ready
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (isNamesReady && !realTimeStocks.isActive) {
            console.log('🚀 Starting real-time updates for BOH Eligible page');
            realTimeStocks.start();
        }
    }, [
        isNamesReady,
        realTimeStocks
    ]);
    // BOH Stock row component with required columns
    const BOHStockRow = ({ stock })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-between p-4 hover:bg-gray-50 border-b border-gray-100",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex-1 grid grid-cols-5 gap-4 items-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                        className: "font-medium text-gray-900",
                                        children: stock.symbol
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                        lineNumber: 299,
                                        columnNumber: 13
                                    }, this),
                                    stock.isBOHEligible && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full",
                                        children: "BOH"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                        lineNumber: 301,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 298,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-sm text-gray-600 truncate",
                                children: stock.name
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 306,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                        lineNumber: 297,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "font-semibold text-gray-900",
                                children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatCurrency"])(stock.price)
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 311,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `text-sm ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getChangeColor"])(stock.change)}`,
                                children: [
                                    stock.change >= 0 ? '+' : '',
                                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatPercentage"])(stock.changePercent)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 314,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                        lineNumber: 310,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-sm text-gray-900",
                                children: stock.low52WeekDate || 'N/A'
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 321,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-xs text-gray-500",
                                children: [
                                    "₹",
                                    stock.low52Week?.toFixed(2) || 'N/A'
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 324,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                        lineNumber: 320,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-sm text-gray-900",
                                children: stock.high52WeekDate || 'N/A'
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 331,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-xs text-gray-500",
                                children: [
                                    "₹",
                                    stock.high52Week?.toFixed(2) || 'N/A'
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 334,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                        lineNumber: 330,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: `px-3 py-1 rounded-full text-sm font-medium ${stock.isBOHEligible ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,
                            children: stock.isBOHEligible ? 'Yes' : 'No'
                        }, void 0, false, {
                            fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                            lineNumber: 341,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                        lineNumber: 340,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                lineNumber: 295,
                columnNumber: 7
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
            lineNumber: 294,
            columnNumber: 5
        }, this);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-between",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                className: "text-2xl font-bold text-gray-900",
                                children: "BOH Eligible Stocks"
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 358,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-gray-600 mt-1",
                                children: [
                                    "Stocks with Boom-Bust-Recovery pattern (52-week low after 52-week high) • ",
                                    getBOHEligibleStocks(niftyStocks).length,
                                    " eligible stocks",
                                    loadingPriceData && niftyStocks.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-blue-600",
                                        children: [
                                            " (Loading ",
                                            niftyStocks.length,
                                            "/200...)"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                        lineNumber: 362,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 359,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mt-2",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$RealTimeIndicator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RealTimeIndicator"], {
                                    isActive: realTimeStocks.isActive,
                                    lastUpdate: realTimeStocks.lastUpdate,
                                    updateCount: realTimeStocks.updateCount,
                                    error: realTimeStocks.error
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                    lineNumber: 366,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 365,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                        lineNumber: 357,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex space-x-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>setShowFilters(!showFilters),
                                className: "px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$funnel$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Filter$3e$__["Filter"], {
                                        className: "h-4 w-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                        lineNumber: 379,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "Filters"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                        lineNumber: 380,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 375,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>loadPriceData(true),
                                disabled: loadingPriceData,
                                className: "px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center space-x-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__["RefreshCw"], {
                                        className: `h-4 w-4 ${loadingPriceData ? 'animate-spin' : ''}`
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                        lineNumber: 387,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "Refresh All"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                        lineNumber: 388,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 382,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                        lineNumber: 374,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                lineNumber: 356,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__["Search"], {
                        className: "absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                        lineNumber: 395,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                        type: "text",
                        placeholder: "Search stocks by symbol or name...",
                        value: searchQuery,
                        onChange: (e)=>setSearchQuery(e.target.value),
                        className: "w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                        lineNumber: 396,
                        columnNumber: 9
                    }, this),
                    isSearching && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute right-3 top-1/2 transform -translate-y-1/2",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__["RefreshCw"], {
                            className: "h-5 w-5 text-gray-400 animate-spin"
                        }, void 0, false, {
                            fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                            lineNumber: 405,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                        lineNumber: 404,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                lineNumber: 394,
                columnNumber: 7
            }, this),
            showFilters && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white rounded-lg shadow-sm border border-gray-200 p-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-semibold text-gray-900 mb-4",
                        children: "Filter Options"
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                        lineNumber: 413,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "grid grid-cols-1 md:grid-cols-3 gap-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                        className: "block text-sm font-medium text-gray-700 mb-2",
                                        children: "Price Range"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                        lineNumber: 416,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex space-x-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                type: "number",
                                                placeholder: "Min",
                                                value: priceFilter.min,
                                                onChange: (e)=>setPriceFilter((prev)=>({
                                                            ...prev,
                                                            min: Number(e.target.value)
                                                        })),
                                                className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                                lineNumber: 418,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                type: "number",
                                                placeholder: "Max",
                                                value: priceFilter.max,
                                                onChange: (e)=>setPriceFilter((prev)=>({
                                                            ...prev,
                                                            max: Number(e.target.value)
                                                        })),
                                                className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                                lineNumber: 425,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                        lineNumber: 417,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 415,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                        className: "block text-sm font-medium text-gray-700 mb-2",
                                        children: "Stock Filters"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                        lineNumber: 436,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "space-y-2",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                            className: "flex items-center",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                    type: "checkbox",
                                                    checked: showOnlyHoldings,
                                                    onChange: (e)=>setShowOnlyHoldings(e.target.checked),
                                                    className: "rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                                    lineNumber: 439,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "ml-2 text-sm text-gray-600",
                                                    children: "Show only stocks in holdings"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                                    lineNumber: 445,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                            lineNumber: 438,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                        lineNumber: 437,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 435,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-end",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>{
                                        setPriceFilter({
                                            min: 0,
                                            max: 10000
                                        });
                                        setShowOnlyHoldings(false);
                                    },
                                    className: "px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",
                                    children: "Reset Filters"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                    lineNumber: 451,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 450,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                        lineNumber: 414,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                lineNumber: 412,
                columnNumber: 9
            }, this),
            niftyError && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-red-50 border border-red-200 rounded-lg p-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__["AlertCircle"], {
                            className: "h-5 w-5 text-red-600 mr-2"
                        }, void 0, false, {
                            fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                            lineNumber: 469,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-red-800",
                            children: niftyError
                        }, void 0, false, {
                            fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                            lineNumber: 470,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                    lineNumber: 468,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                lineNumber: 467,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white rounded-lg shadow-sm border border-gray-200",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-gray-50 px-4 py-3 border-b border-gray-200",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-5 gap-4 text-sm font-medium text-gray-700",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: "Stock Name"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                    lineNumber: 480,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center",
                                    children: "CMP"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                    lineNumber: 481,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center",
                                    children: "52-Week Low Date"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                    lineNumber: 482,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center",
                                    children: "52-Week High Date"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                    lineNumber: 483,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center",
                                    children: "BOH Eligible"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                    lineNumber: 484,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                            lineNumber: 479,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                        lineNumber: 478,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: !isNamesReady ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "p-8 text-center text-gray-500",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader$3e$__["Loader"], {
                                    className: "h-8 w-8 mx-auto mb-4 animate-spin text-blue-600"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                    lineNumber: 491,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    children: "Initializing stock data..."
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                    lineNumber: 492,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-sm mt-1",
                                    children: "Loading stock names for the first time"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                    lineNumber: 493,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                            lineNumber: 490,
                            columnNumber: 13
                        }, this) : loadingPriceData && niftyStocks.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$LoadingStates$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["StockListSkeleton"], {
                            count: 10
                        }, void 0, false, {
                            fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                            lineNumber: 496,
                            columnNumber: 13
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                            children: [
                                getFilteredStocks(niftyStocks).map((stock, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(BOHStockRow, {
                                        stock: stock
                                    }, `boh-${stock.symbol}-${index}`, false, {
                                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                        lineNumber: 500,
                                        columnNumber: 17
                                    }, this)),
                                loadingPriceData && niftyStocks.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "p-4 border-t border-gray-100 bg-blue-50",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center justify-center space-x-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader$3e$__["Loader"], {
                                                className: "h-4 w-4 animate-spin text-blue-600"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                                lineNumber: 506,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-blue-700 text-sm",
                                                children: [
                                                    "Updating price data... (",
                                                    niftyStocks.length,
                                                    "/200)"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                                lineNumber: 507,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                        lineNumber: 505,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                    lineNumber: 504,
                                    columnNumber: 17
                                }, this),
                                niftyStocks.length === 0 && !loadingPriceData && isNamesReady && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "p-8 text-center text-gray-500",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__["AlertCircle"], {
                                            className: "h-12 w-12 mx-auto mb-4 text-gray-300"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                            lineNumber: 516,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            children: "No stocks loaded."
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                            lineNumber: 517,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm mt-1",
                                            children: "Try refreshing the data."
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                            lineNumber: 518,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                    lineNumber: 515,
                                    columnNumber: 17
                                }, this),
                                getFilteredStocks(niftyStocks).length === 0 && niftyStocks.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "p-8 text-center text-gray-500",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__["AlertCircle"], {
                                            className: "h-12 w-12 mx-auto mb-4 text-gray-300"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                            lineNumber: 524,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            children: "No BOH eligible stocks found."
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                            lineNumber: 525,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm mt-1",
                                            children: "BOH stocks have 52-week low after 52-week high (Boom → Bust → Recovery pattern)."
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                            lineNumber: 526,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                    lineNumber: 523,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                        lineNumber: 488,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                lineNumber: 476,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
        lineNumber: 354,
        columnNumber: 5
    }, this);
}
}),

};

//# sourceMappingURL=src_e1153c08._.js.map