{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/app/dashboard/holdings/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  Briefcase, \n  TrendingUp, \n  TrendingDown, \n  DollarSign,\n  <PERSON><PERSON>hart,\n  MoreVertical,\n  Eye,\n  ArrowUpRight,\n  ArrowDownRight\n} from 'lucide-react';\nimport { formatCurrency, formatPercentage, getChangeColor } from '@/lib/utils';\n\ninterface Holding {\n  symbol: string;\n  name: string;\n  quantity: number;\n  avgPrice: number;\n  currentPrice: number;\n  pnl: number;\n  pnlPercent: number;\n  marketValue: number;\n  dayChange: number;\n  dayChangePercent: number;\n  sector: string;\n}\n\nexport default function CurrentHoldingPage() {\n  const [holdings, setHoldings] = useState<Holding[]>([\n    {\n      symbol: 'RELIANCE',\n      name: 'Reliance Industries Ltd',\n      quantity: 50,\n      avgPrice: 2200.00,\n      currentPrice: 2456.75,\n      pnl: 12837.50,\n      pnlPercent: 11.67,\n      marketValue: 122837.50,\n      dayChange: 45.20,\n      dayChangePercent: 1.87,\n      sector: 'Energy'\n    },\n    {\n      symbol: 'TCS',\n      name: 'Tata Consultancy Services',\n      quantity: 25,\n      avgPrice: 3400.00,\n      currentPrice: 3234.50,\n      pnl: -4137.50,\n      pnlPercent: -4.87,\n      marketValue: 80862.50,\n      dayChange: -23.45,\n      dayChangePercent: -0.72,\n      sector: 'IT'\n    },\n    {\n      symbol: 'HDFC',\n      name: 'HDFC Bank Limited',\n      quantity: 40,\n      avgPrice: 1600.00,\n      currentPrice: 1678.90,\n      pnl: 3156.00,\n      pnlPercent: 4.93,\n      marketValue: 67156.00,\n      dayChange: 28.50,\n      dayChangePercent: 1.73,\n      sector: 'Banking'\n    },\n    {\n      symbol: 'INFY',\n      name: 'Infosys Limited',\n      quantity: 60,\n      avgPrice: 1500.00,\n      currentPrice: 1456.80,\n      pnl: -2592.00,\n      pnlPercent: -2.88,\n      marketValue: 87408.00,\n      dayChange: 12.30,\n      dayChangePercent: 0.85,\n      sector: 'IT'\n    }\n  ]);\n\n  const totalInvestment = holdings.reduce((sum, holding) => sum + (holding.avgPrice * holding.quantity), 0);\n  const totalMarketValue = holdings.reduce((sum, holding) => sum + holding.marketValue, 0);\n  const totalPnL = totalMarketValue - totalInvestment;\n  const totalPnLPercent = (totalPnL / totalInvestment) * 100;\n  const totalDayChange = holdings.reduce((sum, holding) => sum + (holding.dayChange * holding.quantity), 0);\n\n  const HoldingRow = ({ holding }: { holding: Holding }) => (\n    <div className=\"flex items-center justify-between p-4 hover:bg-gray-50 border-b border-gray-100\">\n      <div className=\"flex-1\">\n        <div className=\"flex items-center space-x-3\">\n          <div>\n            <h4 className=\"font-medium text-gray-900\">{holding.symbol}</h4>\n            <p className=\"text-sm text-gray-600 truncate max-w-xs\">{holding.name}</p>\n            <p className=\"text-xs text-gray-500\">{holding.sector}</p>\n          </div>\n        </div>\n      </div>\n      \n      <div className=\"flex items-center space-x-6\">\n        <div className=\"text-right\">\n          <p className=\"text-sm text-gray-600\">Qty: {holding.quantity}</p>\n          <p className=\"text-sm text-gray-600\">Avg: {formatCurrency(holding.avgPrice)}</p>\n        </div>\n        \n        <div className=\"text-right\">\n          <p className=\"font-medium text-gray-900\">{formatCurrency(holding.currentPrice)}</p>\n          <p className={`text-sm ${getChangeColor(holding.dayChange)}`}>\n            {holding.dayChange >= 0 ? '+' : ''}{holding.dayChange.toFixed(2)} ({formatPercentage(holding.dayChangePercent)})\n          </p>\n        </div>\n        \n        <div className=\"text-right\">\n          <p className=\"font-medium text-gray-900\">{formatCurrency(holding.marketValue)}</p>\n          <p className={`text-sm font-medium ${getChangeColor(holding.pnl)}`}>\n            {holding.pnl >= 0 ? '+' : ''}{formatCurrency(Math.abs(holding.pnl))} ({formatPercentage(holding.pnlPercent)})\n          </p>\n        </div>\n        \n        <div className=\"flex items-center space-x-2\">\n          <button className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors\">\n            <Eye className=\"h-4 w-4\" />\n          </button>\n          <button className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors\">\n            <MoreVertical className=\"h-4 w-4\" />\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n\n  const sectorAllocation = holdings.reduce((acc, holding) => {\n    const sector = holding.sector;\n    if (!acc[sector]) {\n      acc[sector] = { value: 0, percentage: 0 };\n    }\n    acc[sector].value += holding.marketValue;\n    return acc;\n  }, {} as Record<string, { value: number; percentage: number }>);\n\n  // Calculate percentages\n  Object.keys(sectorAllocation).forEach(sector => {\n    sectorAllocation[sector].percentage = (sectorAllocation[sector].value / totalMarketValue) * 100;\n  });\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Page Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Current Holdings</h1>\n          <p className=\"text-gray-600 mt-1\">Active positions and P&L tracking</p>\n        </div>\n        <div className=\"flex space-x-3\">\n          <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n            Export Report\n          </button>\n          <button className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\">\n            Refresh Prices\n          </button>\n        </div>\n      </div>\n\n      {/* Portfolio Summary */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Total Investment</p>\n              <p className=\"text-2xl font-bold text-gray-900 mt-1\">{formatCurrency(totalInvestment)}</p>\n            </div>\n            <DollarSign className=\"h-8 w-8 text-blue-600\" />\n          </div>\n        </div>\n        \n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Current Value</p>\n              <p className=\"text-2xl font-bold text-gray-900 mt-1\">{formatCurrency(totalMarketValue)}</p>\n            </div>\n            <Briefcase className=\"h-8 w-8 text-gray-600\" />\n          </div>\n        </div>\n        \n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Total P&L</p>\n              <p className={`text-2xl font-bold mt-1 ${getChangeColor(totalPnL)}`}>\n                {totalPnL >= 0 ? '+' : ''}{formatCurrency(Math.abs(totalPnL))}\n              </p>\n              <p className={`text-sm ${getChangeColor(totalPnL)}`}>\n                {formatPercentage(totalPnLPercent)}\n              </p>\n            </div>\n            {totalPnL >= 0 ? (\n              <TrendingUp className=\"h-8 w-8 text-green-600\" />\n            ) : (\n              <TrendingDown className=\"h-8 w-8 text-red-600\" />\n            )}\n          </div>\n        </div>\n        \n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Day&apos;s P&L</p>\n              <p className={`text-2xl font-bold mt-1 ${getChangeColor(totalDayChange)}`}>\n                {totalDayChange >= 0 ? '+' : ''}{formatCurrency(Math.abs(totalDayChange))}\n              </p>\n            </div>\n            {totalDayChange >= 0 ? (\n              <ArrowUpRight className=\"h-8 w-8 text-green-600\" />\n            ) : (\n              <ArrowDownRight className=\"h-8 w-8 text-red-600\" />\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Holdings and Sector Allocation */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Holdings List */}\n        <div className=\"lg:col-span-2 bg-white rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"p-6 border-b border-gray-200\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Holdings ({holdings.length})</h3>\n          </div>\n          \n          <div>\n            {holdings.map((holding) => (\n              <HoldingRow key={holding.symbol} holding={holding} />\n            ))}\n          </div>\n        </div>\n\n        {/* Sector Allocation */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between mb-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Sector Allocation</h3>\n            <PieChart className=\"h-5 w-5 text-gray-400\" />\n          </div>\n          \n          <div className=\"space-y-4\">\n            {Object.entries(sectorAllocation).map(([sector, data]) => (\n              <div key={sector} className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className={`w-3 h-3 rounded-full ${\n                    sector === 'IT' ? 'bg-blue-500' :\n                    sector === 'Banking' ? 'bg-green-500' :\n                    sector === 'Energy' ? 'bg-orange-500' :\n                    'bg-gray-500'\n                  }`}></div>\n                  <span className=\"text-sm font-medium text-gray-900\">{sector}</span>\n                </div>\n                <div className=\"text-right\">\n                  <p className=\"text-sm font-medium text-gray-900\">{data.percentage.toFixed(1)}%</p>\n                  <p className=\"text-xs text-gray-600\">{formatCurrency(data.value)}</p>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Performance Metrics */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-6\">Performance Metrics</h3>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-green-600 mb-2\">\n              {holdings.filter(h => h.pnl > 0).length}\n            </div>\n            <p className=\"text-sm text-gray-600\">Profitable Positions</p>\n          </div>\n          \n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-red-600 mb-2\">\n              {holdings.filter(h => h.pnl < 0).length}\n            </div>\n            <p className=\"text-sm text-gray-600\">Loss Making Positions</p>\n          </div>\n          \n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-gray-900 mb-2\">\n              {((holdings.filter(h => h.pnl > 0).length / holdings.length) * 100).toFixed(1)}%\n            </div>\n            <p className=\"text-sm text-gray-600\">Win Rate</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;AAdA;;;;AA8Be,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QAClD;YACE,QAAQ;YACR,MAAM;YACN,UAAU;YACV,UAAU;YACV,cAAc;YACd,KAAK;YACL,YAAY;YACZ,aAAa;YACb,WAAW;YACX,kBAAkB;YAClB,QAAQ;QACV;QACA;YACE,QAAQ;YACR,MAAM;YACN,UAAU;YACV,UAAU;YACV,cAAc;YACd,KAAK,CAAC;YACN,YAAY,CAAC;YACb,aAAa;YACb,WAAW,CAAC;YACZ,kBAAkB,CAAC;YACnB,QAAQ;QACV;QACA;YACE,QAAQ;YAC<PERSON>,MAAM;YACN,UAAU;YACV,UAAU;YACV,cAAc;YACd,KAAK;YACL,YAAY;YACZ,aAAa;YACb,WAAW;YACX,kBAAkB;YAClB,QAAQ;QACV;QACA;YACE,QAAQ;YACR,MAAM;YACN,UAAU;YACV,UAAU;YACV,cAAc;YACd,KAAK,CAAC;YACN,YAAY,CAAC;YACb,aAAa;YACb,WAAW;YACX,kBAAkB;YAClB,QAAQ;QACV;KACD;IAED,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,MAAO,QAAQ,QAAQ,GAAG,QAAQ,QAAQ,EAAG;IACvG,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,WAAW,EAAE;IACtF,MAAM,WAAW,mBAAmB;IACpC,MAAM,kBAAkB,AAAC,WAAW,kBAAmB;IACvD,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,MAAO,QAAQ,SAAS,GAAG,QAAQ,QAAQ,EAAG;IAEvG,MAAM,aAAa;YAAC,EAAE,OAAO,EAAwB;6BACnD,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B,QAAQ,MAAM;;;;;;8CACzD,6LAAC;oCAAE,WAAU;8CAA2C,QAAQ,IAAI;;;;;;8CACpE,6LAAC;oCAAE,WAAU;8CAAyB,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;8BAK1D,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;;wCAAwB;wCAAM,QAAQ,QAAQ;;;;;;;8CAC3D,6LAAC;oCAAE,WAAU;;wCAAwB;wCAAM,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,QAAQ;;;;;;;;;;;;;sCAG5E,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAA6B,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,YAAY;;;;;;8CAC7E,6LAAC;oCAAE,WAAW,AAAC,WAA4C,OAAlC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,SAAS;;wCACtD,QAAQ,SAAS,IAAI,IAAI,MAAM;wCAAI,QAAQ,SAAS,CAAC,OAAO,CAAC;wCAAG;wCAAG,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,gBAAgB;wCAAE;;;;;;;;;;;;;sCAInH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAA6B,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,WAAW;;;;;;8CAC5E,6LAAC;oCAAE,WAAW,AAAC,uBAAkD,OAA5B,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,GAAG;;wCAC5D,QAAQ,GAAG,IAAI,IAAI,MAAM;wCAAI,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,GAAG,CAAC,QAAQ,GAAG;wCAAG;wCAAG,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,UAAU;wCAAE;;;;;;;;;;;;;sCAIhH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAO,WAAU;8CAChB,cAAA,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;8CAEjB,6LAAC;oCAAO,WAAU;8CAChB,cAAA,6LAAC,6NAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOlC,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAC,KAAK;QAC7C,MAAM,SAAS,QAAQ,MAAM;QAC7B,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE;YAChB,GAAG,CAAC,OAAO,GAAG;gBAAE,OAAO;gBAAG,YAAY;YAAE;QAC1C;QACA,GAAG,CAAC,OAAO,CAAC,KAAK,IAAI,QAAQ,WAAW;QACxC,OAAO;IACT,GAAG,CAAC;IAEJ,wBAAwB;IACxB,OAAO,IAAI,CAAC,kBAAkB,OAAO,CAAC,CAAA;QACpC,gBAAgB,CAAC,OAAO,CAAC,UAAU,GAAG,AAAC,gBAAgB,CAAC,OAAO,CAAC,KAAK,GAAG,mBAAoB;IAC9F;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAEpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,WAAU;0CAAkF;;;;;;0CAGpG,6LAAC;gCAAO,WAAU;0CAA+F;;;;;;;;;;;;;;;;;;0BAOrH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAyC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;8CAEvE,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAI1B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAyC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;8CAEvE,6LAAC,+MAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIzB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAW,AAAC,2BAAmD,OAAzB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;;gDACrD,YAAY,IAAI,MAAM;gDAAI,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,GAAG,CAAC;;;;;;;sDAErD,6LAAC;4CAAE,WAAW,AAAC,WAAmC,OAAzB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;sDACrC,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE;;;;;;;;;;;;gCAGrB,YAAY,kBACX,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;yDAEtB,6LAAC,yNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAK9B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAW,AAAC,2BAAyD,OAA/B,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;;gDACrD,kBAAkB,IAAI,MAAM;gDAAI,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,GAAG,CAAC;;;;;;;;;;;;;gCAG5D,kBAAkB,kBACjB,6LAAC,6NAAA,CAAA,eAAY;oCAAC,WAAU;;;;;yDAExB,6LAAC,iOAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAOlC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;;wCAAsC;wCAAW,SAAS,MAAM;wCAAC;;;;;;;;;;;;0CAGjF,6LAAC;0CACE,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;wCAAgC,SAAS;uCAAzB,QAAQ,MAAM;;;;;;;;;;;;;;;;kCAMrC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;kDACpD,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAGtB,6LAAC;gCAAI,WAAU;0CACZ,OAAO,OAAO,CAAC,kBAAkB,GAAG,CAAC;wCAAC,CAAC,QAAQ,KAAK;yDACnD,6LAAC;wCAAiB,WAAU;;0DAC1B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,AAAC,wBAKhB,OAJC,WAAW,OAAO,gBAClB,WAAW,YAAY,iBACvB,WAAW,WAAW,kBACtB;;;;;;kEAEF,6LAAC;wDAAK,WAAU;kEAAqC;;;;;;;;;;;;0DAEvD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;;4DAAqC,KAAK,UAAU,CAAC,OAAO,CAAC;4DAAG;;;;;;;kEAC7E,6LAAC;wDAAE,WAAU;kEAAyB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,KAAK;;;;;;;;;;;;;uCAZzD;;;;;;;;;;;;;;;;;;;;;;;0BAqBlB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAEzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,GAAG,GAAG,GAAG,MAAM;;;;;;kDAEzC,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAGvC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,GAAG,GAAG,GAAG,MAAM;;;;;;kDAEzC,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAGvC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CACZ,CAAC,AAAC,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,GAAG,GAAG,GAAG,MAAM,GAAG,SAAS,MAAM,GAAI,GAAG,EAAE,OAAO,CAAC;4CAAG;;;;;;;kDAEjF,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjD;GA7QwB;KAAA", "debugId": null}}, {"offset": {"line": 861, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/trending-down.js", "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/node_modules/lucide-react/src/icons/trending-down.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 17h6v-6', key: 't6n2it' }],\n  ['path', { d: 'm22 17-8.5-8.5-5 5L2 7', key: 'x473p' }],\n];\n\n/**\n * @component @name TrendingDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMTdoNnYtNiIgLz4KICA8cGF0aCBkPSJtMjIgMTctOC41LTguNS01IDVMMiA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/trending-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrendingDown = createLucideIcon('trending-down', __iconNode);\n\nexport default TrendingDown;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC5C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAS,CAAA;KAAA;CACxD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 903, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/dollar-sign.js", "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/node_modules/lucide-react/src/icons/dollar-sign.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['line', { x1: '12', x2: '12', y1: '2', y2: '22', key: '7eqyqh' }],\n  ['path', { d: 'M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6', key: '1b0p4s' }],\n];\n\n/**\n * @component @name DollarSign\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjIiIHkyPSIyMiIgLz4KICA8cGF0aCBkPSJNMTcgNUg5LjVhMy41IDMuNSAwIDAgMCAwIDdoNWEzLjUgMy41IDAgMCAxIDAgN0g2IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/dollar-sign\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst DollarSign = createLucideIcon('dollar-sign', __iconNode);\n\nexport default DollarSign;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjE;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqD,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACpF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 948, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/chart-pie.js", "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/node_modules/lucide-react/src/icons/chart-pie.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z',\n      key: 'pzmjnu',\n    },\n  ],\n  ['path', { d: 'M21.21 15.89A10 10 0 1 1 8 2.83', key: 'k2fpak' }],\n];\n\n/**\n * @component @name ChartPie\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTJjLjU1MiAwIDEuMDA1LS40NDkuOTUtLjk5OGExMCAxMCAwIDAgMC04Ljk1My04Ljk1MWMtLjU1LS4wNTUtLjk5OC4zOTgtLjk5OC45NXY4YTEgMSAwIDAgMCAxIDF6IiAvPgogIDxwYXRoIGQ9Ik0yMS4yMSAxNS44OUExMCAxMCAwIDEgMSA4IDIuODMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/chart-pie\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChartPie = createLucideIcon('chart-pie', __iconNode);\n\nexport default ChartPie;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAClE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAa,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 990, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js", "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/node_modules/lucide-react/src/icons/ellipsis-vertical.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '1', key: '41hilf' }],\n  ['circle', { cx: '12', cy: '5', r: '1', key: 'gxeob9' }],\n  ['circle', { cx: '12', cy: '19', r: '1', key: 'lyex9k' }],\n];\n\n/**\n * @component @name EllipsisVertical\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxIiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iNSIgcj0iMSIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjE5IiByPSIxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/ellipsis-vertical\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst EllipsisVertical = createLucideIcon('ellipsis-vertical', __iconNode);\n\nexport default EllipsisVertical;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACxD;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACvD;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,CAAmB,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAqB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 1045, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/eye.js", "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/node_modules/lucide-react/src/icons/eye.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0',\n      key: '1nclc0',\n    },\n  ],\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n];\n\n/**\n * @component @name Eye\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMi4wNjIgMTIuMzQ4YTEgMSAwIDAgMSAwLS42OTYgMTAuNzUgMTAuNzUgMCAwIDEgMTkuODc2IDAgMSAxIDAgMCAxIDAgLjY5NiAxMC43NSAxMC43NSAwIDAgMS0xOS44NzYgMCIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/eye\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Eye = createLucideIcon('eye', __iconNode);\n\nexport default Eye;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAM,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,EAAO,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 1089, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/arrow-up-right.js", "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/node_modules/lucide-react/src/icons/arrow-up-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M7 7h10v10', key: '1tivn9' }],\n  ['path', { d: 'M7 17 17 7', key: '1vkiza' }],\n];\n\n/**\n * @component @name ArrowUpRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNyA3aDEwdjEwIiAvPgogIDxwYXRoIGQ9Ik03IDE3IDE3IDciIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/arrow-up-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowUpRight = createLucideIcon('arrow-up-right', __iconNode);\n\nexport default ArrowUpRight;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC7C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAkB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 1131, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/arrow-down-right.js", "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/node_modules/lucide-react/src/icons/arrow-down-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm7 7 10 10', key: '1fmybs' }],\n  ['path', { d: 'M17 7v10H7', key: '6fjiku' }],\n];\n\n/**\n * @component @name ArrowDownRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtNyA3IDEwIDEwIiAvPgogIDxwYXRoIGQ9Ik0xNyA3djEwSDciIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/arrow-down-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowDownRight = createLucideIcon('arrow-down-right', __iconNode);\n\nexport default ArrowDownRight;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC7C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,cAAA,CAAiB,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAoB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}]}