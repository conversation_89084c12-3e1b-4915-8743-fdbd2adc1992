import { NextRequest, NextResponse } from 'next/server';
import { automaticGTTService } from '@/lib/automatic-gtt-service';
import { weeklyHighSignalDetector } from '@/lib/weekly-high-signal-detector';

export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Testing automatic GTT order creation...');

    const testResults = {
      timestamp: new Date().toISOString(),
      steps: [] as any[]
    };

    // Step 1: Get current orders count
    const initialOrders = automaticGTTService.getAllOrders();
    const initialCount = initialOrders.length;
    const initialPendingSignalOrders = initialOrders.filter(o => o.source === 'SIGNAL' && o.status === 'PENDING');

    testResults.steps.push({
      step: 1,
      name: 'Initial Order Count',
      data: {
        totalOrders: initialCount,
        pendingSignalOrders: initialPendingSignalOrders.length,
        existingSymbols: initialPendingSignalOrders.map(o => o.symbol)
      }
    });

    // Step 2: Get current signals
    console.log('📡 Getting current signals...');
    const currentSignals = await weeklyHighSignalDetector.triggerManualScan();

    testResults.steps.push({
      step: 2,
      name: 'Signal Detection',
      data: {
        totalSignals: currentSignals.length,
        strongSignals: currentSignals.filter(s => s.signalStrength === 'STRONG').length,
        moderateSignals: currentSignals.filter(s => s.signalStrength === 'MODERATE').length
      }
    });

    // Step 3: Find signals without existing orders
    const existingSymbols = new Set(initialPendingSignalOrders.map(o => o.symbol));
    const signalsWithoutOrders = currentSignals.filter(signal => !existingSymbols.has(signal.symbol));

    testResults.steps.push({
      step: 3,
      name: 'Eligible Signals Analysis',
      data: {
        totalSignals: currentSignals.length,
        existingOrderSymbols: existingSymbols.size,
        signalsWithoutOrders: signalsWithoutOrders.length,
        eligibleSymbols: signalsWithoutOrders.slice(0, 5).map(s => s.symbol)
      }
    });

    // Step 4: Test automatic order creation by simulating the handleAllSignals method
    console.log('🤖 Testing automatic order creation logic...');
    let ordersCreated = 0;

    if (signalsWithoutOrders.length > 0) {
      // Test with first 3 signals without orders
      const signalsToTest = signalsWithoutOrders.slice(0, 3);
      
      for (const signal of signalsToTest) {
        try {
          // Use the test method to create an order
          const order = await automaticGTTService.testCreateOrder(signal.symbol);
          if (order) {
            ordersCreated++;
            console.log(`✅ Created automatic order for ${signal.symbol}`);
          }
        } catch (error) {
          console.error(`❌ Failed to create order for ${signal.symbol}:`, error);
        }
      }
    }

    testResults.steps.push({
      step: 4,
      name: 'Automatic Order Creation Test',
      data: {
        signalsWithoutOrders: signalsWithoutOrders.length,
        signalsTested: Math.min(3, signalsWithoutOrders.length),
        ordersCreated,
        success: ordersCreated > 0
      }
    });

    // Step 5: Verify final order count
    const finalOrders = automaticGTTService.getAllOrders();
    const finalCount = finalOrders.length;
    const orderIncrease = finalCount - initialCount;

    testResults.steps.push({
      step: 5,
      name: 'Final Order Count Verification',
      data: {
        initialCount,
        finalCount,
        orderIncrease,
        expectedIncrease: ordersCreated,
        success: orderIncrease === ordersCreated
      }
    });

    // Step 6: Test the handleAllSignals method directly
    console.log('🔄 Testing handleAllSignals method directly...');
    
    // We'll simulate this by checking if the service would create orders for current signals
    const serviceStats = automaticGTTService.getStatistics();
    
    testResults.steps.push({
      step: 6,
      name: 'Service Integration Test',
      data: {
        serviceInitialized: serviceStats.isInitialized,
        totalOrdersInService: serviceStats.totalOrders,
        todayOrdersInService: serviceStats.todayOrders,
        listenerCount: weeklyHighSignalDetector.getStatus().listenerCount
      }
    });

    // Calculate overall results
    const successfulSteps = testResults.steps.filter(s => s.data.success !== false).length;
    const totalSteps = testResults.steps.length;
    const overallSuccess = ordersCreated > 0 || signalsWithoutOrders.length === 0;

    const summary = {
      overallSuccess,
      successfulSteps,
      totalSteps,
      initialOrderCount: initialCount,
      finalOrderCount: finalCount,
      newOrdersCreated: ordersCreated,
      signalsWithoutOrders: signalsWithoutOrders.length,
      automaticCreationWorking: ordersCreated > 0,
      allSignalsHaveOrders: signalsWithoutOrders.length === 0,
      message: signalsWithoutOrders.length === 0 
        ? 'All signals already have orders - automatic creation working correctly'
        : ordersCreated > 0 
          ? 'Automatic order creation working - new orders created'
          : 'Automatic order creation needs investigation'
    };

    console.log(`🎯 Automatic creation test: ${summary.message}`);

    return NextResponse.json({
      success: true,
      message: 'Automatic GTT order creation test completed',
      summary,
      testResults
    });

  } catch (error) {
    console.error('❌ Automatic creation test failed:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Test failed',
        message: 'Automatic creation test failed'
      },
      { status: 500 }
    );
  }
}
