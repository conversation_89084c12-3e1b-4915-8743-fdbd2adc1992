const CHUNK_PUBLIC_PATH = "server/app/api/debug-boh/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/src_lib_yahoo-finance_ts_39b50dc8._.js");
runtime.loadChunk("server/chunks/node_modules_ea2faa10._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__9fa7ec8c._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/debug-boh/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/debug-boh/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/debug-boh/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
