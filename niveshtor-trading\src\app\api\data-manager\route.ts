import { NextRequest, NextResponse } from 'next/server';
import { centralDataManager } from '@/lib/central-data-manager';

// GET - Get data manager status and cached data
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const action = url.searchParams.get('action');

    switch (action) {
      case 'status':
        const status = centralDataManager.getStatus();
        return NextResponse.json({
          success: true,
          data: status
        });

      case 'nifty200':
        const nifty200Stocks = centralDataManager.getNifty200Stocks();
        return NextResponse.json({
          success: true,
          data: nifty200Stocks,
          lastUpdated: centralDataManager.getLastUpdated('nifty200'),
          isLoading: centralDataManager.isDataLoading('nifty200')
        });

      case 'boh-eligible':
        const bohEligibleStocks = centralDataManager.getBOHEligibleStocks();
        return NextResponse.json({
          success: true,
          data: bohEligibleStocks,
          lastUpdated: centralDataManager.getLastUpdated('bohEligible'),
          isLoading: centralDataManager.isDataLoading('bohEligible')
        });

      case 'weekly-high-signals':
        const weeklyHighSignals = centralDataManager.getWeeklyHighSignals();
        return NextResponse.json({
          success: true,
          data: weeklyHighSignals,
          lastUpdated: centralDataManager.getLastUpdated('weeklyHighSignals'),
          isLoading: centralDataManager.isDataLoading('weeklyHighSignals')
        });

      case 'gtt-orders':
        const gttOrders = centralDataManager.getGTTOrders();
        return NextResponse.json({
          success: true,
          data: gttOrders,
          lastUpdated: centralDataManager.getLastUpdated('gttOrders'),
          isLoading: centralDataManager.isDataLoading('gttOrders')
        });

      case 'cache-status':
        const cacheStatus = centralDataManager.getCacheStatus();
        return NextResponse.json({
          success: true,
          data: cacheStatus
        });

      default:
        return NextResponse.json({
          success: true,
          message: 'Central Data Manager API',
          endpoints: {
            'GET ?action=status': 'Get service status',
            'GET ?action=nifty200': 'Get Nifty 200 stocks',
            'GET ?action=boh-eligible': 'Get BOH eligible stocks',
            'GET ?action=weekly-high-signals': 'Get Weekly High Signals',
            'GET ?action=gtt-orders': 'Get GTT orders',
            'GET ?action=cache-status': 'Get cache status',
            'POST': 'Control service (initialize/start/stop/refresh)'
          }
        });
    }

  } catch (error) {
    console.error('❌ Data Manager API GET error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'API error' 
      },
      { status: 500 }
    );
  }
}

// POST - Control the data manager service
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, config } = body;

    switch (action) {
      case 'initialize':
        await centralDataManager.initialize();
        return NextResponse.json({
          success: true,
          message: 'Central Data Manager initialized',
          data: centralDataManager.getStatus()
        });

      case 'start':
        centralDataManager.start();
        return NextResponse.json({
          success: true,
          message: 'Central Data Manager started',
          data: centralDataManager.getStatus()
        });

      case 'stop':
        centralDataManager.stop();
        return NextResponse.json({
          success: true,
          message: 'Central Data Manager stopped',
          data: centralDataManager.getStatus()
        });

      case 'configure':
        if (config) {
          centralDataManager.updateConfig(config);
        }
        return NextResponse.json({
          success: true,
          message: 'Configuration updated',
          data: centralDataManager.getConfig()
        });

      case 'refresh-all':
        await centralDataManager.refreshAll();
        return NextResponse.json({
          success: true,
          message: 'All data refreshed',
          data: centralDataManager.getCacheStatus()
        });

      case 'refresh-nifty200':
        await centralDataManager.refreshNifty200();
        return NextResponse.json({
          success: true,
          message: 'Nifty 200 data refreshed',
          data: {
            count: centralDataManager.getNifty200Stocks().length,
            lastUpdated: centralDataManager.getLastUpdated('nifty200')
          }
        });

      case 'refresh-boh-eligible':
        await centralDataManager.refreshBOHEligible();
        return NextResponse.json({
          success: true,
          message: 'BOH eligible data refreshed',
          data: {
            count: centralDataManager.getBOHEligibleStocks().length,
            lastUpdated: centralDataManager.getLastUpdated('bohEligible')
          }
        });

      case 'refresh-weekly-high-signals':
        await centralDataManager.refreshWeeklyHighSignals();
        return NextResponse.json({
          success: true,
          message: 'Weekly High Signals refreshed',
          data: {
            count: centralDataManager.getWeeklyHighSignals().length,
            lastUpdated: centralDataManager.getLastUpdated('weeklyHighSignals')
          }
        });

      case 'refresh-gtt-orders':
        await centralDataManager.refreshGTTOrders();
        return NextResponse.json({
          success: true,
          message: 'GTT orders refreshed',
          data: {
            count: centralDataManager.getGTTOrders().length,
            lastUpdated: centralDataManager.getLastUpdated('gttOrders')
          }
        });

      default:
        throw new Error('Invalid action. Use: initialize, start, stop, configure, refresh-all, refresh-nifty200, refresh-boh-eligible, refresh-weekly-high-signals, refresh-gtt-orders');
    }

  } catch (error) {
    console.error('❌ Data Manager API POST error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'API error' 
      },
      { status: 500 }
    );
  }
}
