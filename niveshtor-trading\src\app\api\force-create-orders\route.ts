import { NextRequest, NextResponse } from 'next/server';
import { automaticGTTService } from '@/lib/automatic-gtt-service';
import { weeklyHighSignalDetector } from '@/lib/weekly-high-signal-detector';

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Force creating multiple GTT orders...');

    // Get current signals
    const signals = await weeklyHighSignalDetector.triggerManualScan();
    console.log(`📊 Found ${signals.length} signals`);

    if (signals.length === 0) {
      return NextResponse.json({
        success: false,
        message: 'No signals found to create orders from'
      });
    }

    // Get existing orders to avoid duplicates
    const existingOrders = automaticGTTService.getAllOrders();
    const existingSymbols = new Set(
      existingOrders
        .filter(order => order.source === 'SIGNAL' && order.status === 'PENDING')
        .map(order => order.symbol)
    );

    console.log(`📋 Found ${existingOrders.length} existing orders, ${existingSymbols.size} pending signal orders`);

    // Filter signals that don't have existing orders
    const eligibleSignals = signals.filter(signal => !existingSymbols.has(signal.symbol));
    console.log(`✅ ${eligibleSignals.length} signals eligible for new orders`);

    // Create orders for first 10 eligible signals (to avoid overwhelming)
    const signalsToProcess = eligibleSignals.slice(0, 10);
    const results = [];

    for (const signal of signalsToProcess) {
      try {
        console.log(`🤖 Creating order for ${signal.symbol}...`);
        
        const order = await automaticGTTService.testCreateOrder(signal.symbol);
        
        if (order) {
          results.push({
            symbol: signal.symbol,
            status: 'SUCCESS',
            orderId: order.id,
            triggerPrice: order.triggerPrice,
            quantity: order.quantity
          });
          console.log(`✅ Order created for ${signal.symbol}: ${order.id}`);
        } else {
          results.push({
            symbol: signal.symbol,
            status: 'FAILED',
            error: 'No order returned from service'
          });
          console.log(`❌ Failed to create order for ${signal.symbol}`);
        }
      } catch (error) {
        results.push({
          symbol: signal.symbol,
          status: 'ERROR',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        console.log(`❌ Error creating order for ${signal.symbol}:`, error);
      }
    }

    // Get updated order count
    const updatedOrders = automaticGTTService.getAllOrders();
    const newOrderCount = updatedOrders.length - existingOrders.length;

    console.log(`🎉 Force creation completed: ${results.filter(r => r.status === 'SUCCESS').length} orders created`);

    return NextResponse.json({
      success: true,
      message: `Force created ${results.filter(r => r.status === 'SUCCESS').length} GTT orders`,
      data: {
        totalSignals: signals.length,
        eligibleSignals: eligibleSignals.length,
        processedSignals: signalsToProcess.length,
        successfulOrders: results.filter(r => r.status === 'SUCCESS').length,
        failedOrders: results.filter(r => r.status !== 'SUCCESS').length,
        newOrderCount,
        results,
        updatedOrderCount: updatedOrders.length
      }
    });

  } catch (error) {
    console.error('❌ Force order creation failed:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Force creation failed',
        message: 'Force order creation failed'
      },
      { status: 500 }
    );
  }
}
