(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/ui/RealTimeIndicator.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Real-time update status indicator
__turbopack_context__.s({
    "CompactRealTimeIndicator": ()=>CompactRealTimeIndicator,
    "RealTimeIndicator": ()=>RealTimeIndicator,
    "useRealTimeIndicator": ()=>useRealTimeIndicator
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wifi$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wifi$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/wifi.js [app-client] (ecmascript) <export default as Wifi>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wifi$2d$off$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__WifiOff$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/wifi-off.js [app-client] (ecmascript) <export default as WifiOff>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$activity$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Activity$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/activity.js [app-client] (ecmascript) <export default as Activity>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-client] (ecmascript) <export default as Clock>");
;
var _s = __turbopack_context__.k.signature();
;
;
function RealTimeIndicator(param) {
    let { isActive, lastUpdate, updateCount, error, className = '' } = param;
    const formatLastUpdate = (date)=>{
        if (!date) return 'Never';
        const now = new Date();
        const diff = now.getTime() - date.getTime();
        const seconds = Math.floor(diff / 1000);
        if (seconds < 60) return "".concat(seconds, "s ago");
        if (seconds < 3600) return "".concat(Math.floor(seconds / 60), "m ago");
        return date.toLocaleTimeString();
    };
    const getStatusColor = ()=>{
        if (error) return 'text-red-500';
        if (isActive) return 'text-green-500';
        return 'text-gray-400';
    };
    const getStatusIcon = ()=>{
        if (error) return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wifi$2d$off$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__WifiOff$3e$__["WifiOff"], {
            className: "h-4 w-4"
        }, void 0, false, {
            fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
            lineNumber: 39,
            columnNumber: 23
        }, this);
        if (isActive) return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wifi$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wifi$3e$__["Wifi"], {
            className: "h-4 w-4"
        }, void 0, false, {
            fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
            lineNumber: 40,
            columnNumber: 26
        }, this);
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wifi$2d$off$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__WifiOff$3e$__["WifiOff"], {
            className: "h-4 w-4"
        }, void 0, false, {
            fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
            lineNumber: 41,
            columnNumber: 12
        }, this);
    };
    const getStatusText = ()=>{
        if (error) return 'Connection Error';
        if (isActive) return 'Live Updates';
        return 'Offline';
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex items-center space-x-2 text-sm ".concat(className),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center space-x-1 ".concat(getStatusColor()),
                children: [
                    getStatusIcon(),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "font-medium",
                        children: getStatusText()
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
                        lineNumber: 55,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
                lineNumber: 53,
                columnNumber: 7
            }, this),
            isActive && updateCount > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center space-x-1 text-gray-500",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$activity$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Activity$3e$__["Activity"], {
                        className: "h-3 w-3"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
                        lineNumber: 61,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: [
                            updateCount,
                            " updates"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
                        lineNumber: 62,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
                lineNumber: 60,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center space-x-1 text-gray-500",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                        className: "h-3 w-3"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
                        lineNumber: 68,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: formatLastUpdate(lastUpdate)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
                        lineNumber: 69,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
                lineNumber: 67,
                columnNumber: 7
            }, this),
            error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-red-500 text-xs",
                children: error.message
            }, void 0, false, {
                fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
                lineNumber: 74,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
        lineNumber: 51,
        columnNumber: 5
    }, this);
}
_c = RealTimeIndicator;
function CompactRealTimeIndicator(param) {
    let { isActive, lastUpdate, error, className = '' } = param;
    const getStatusColor = ()=>{
        if (error) return 'text-red-500';
        if (isActive) return 'text-green-500';
        return 'text-gray-400';
    };
    const getStatusIcon = ()=>{
        if (error) return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wifi$2d$off$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__WifiOff$3e$__["WifiOff"], {
            className: "h-3 w-3"
        }, void 0, false, {
            fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
            lineNumber: 96,
            columnNumber: 23
        }, this);
        if (isActive) return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wifi$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wifi$3e$__["Wifi"], {
            className: "h-3 w-3"
        }, void 0, false, {
            fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
            lineNumber: 97,
            columnNumber: 26
        }, this);
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wifi$2d$off$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__WifiOff$3e$__["WifiOff"], {
            className: "h-3 w-3"
        }, void 0, false, {
            fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
            lineNumber: 98,
            columnNumber: 12
        }, this);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex items-center space-x-1 ".concat(getStatusColor(), " ").concat(className),
        children: [
            getStatusIcon(),
            isActive && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-2 h-2 bg-green-500 rounded-full animate-pulse"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
                lineNumber: 105,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/RealTimeIndicator.tsx",
        lineNumber: 102,
        columnNumber: 5
    }, this);
}
_c1 = CompactRealTimeIndicator;
function useRealTimeIndicator() {
    _s();
    const [isActive, setIsActive] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useState(false);
    const [lastUpdate, setLastUpdate] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useState(null);
    const [updateCount, setUpdateCount] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useState(0);
    const [error, setError] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useState(null);
    // Listen for real-time updates
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useEffect({
        "useRealTimeIndicator.useEffect": ()=>{
            const handleRealTimeUpdate = {
                "useRealTimeIndicator.useEffect.handleRealTimeUpdate": (event)=>{
                    setLastUpdate(new Date());
                    setUpdateCount({
                        "useRealTimeIndicator.useEffect.handleRealTimeUpdate": (prev)=>prev + 1
                    }["useRealTimeIndicator.useEffect.handleRealTimeUpdate"]);
                    setError(null);
                    setIsActive(true);
                }
            }["useRealTimeIndicator.useEffect.handleRealTimeUpdate"];
            const handleError = {
                "useRealTimeIndicator.useEffect.handleError": (event)=>{
                    setError(event.detail.error);
                }
            }["useRealTimeIndicator.useEffect.handleError"];
            window.addEventListener('realTimePriceUpdate', handleRealTimeUpdate);
            window.addEventListener('realTimeError', handleError);
            return ({
                "useRealTimeIndicator.useEffect": ()=>{
                    window.removeEventListener('realTimePriceUpdate', handleRealTimeUpdate);
                    window.removeEventListener('realTimeError', handleError);
                }
            })["useRealTimeIndicator.useEffect"];
        }
    }["useRealTimeIndicator.useEffect"], []);
    return {
        isActive,
        lastUpdate,
        updateCount,
        error,
        clearError: ()=>setError(null)
    };
}
_s(useRealTimeIndicator, "S7IuDzIwX63rvPGotUGKWOFF/AI=");
var _c, _c1;
__turbopack_context__.k.register(_c, "RealTimeIndicator");
__turbopack_context__.k.register(_c1, "CompactRealTimeIndicator");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/useBackgroundData.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// React hook for seamless background data integration
__turbopack_context__.s({
    "useBackgroundData": ()=>useBackgroundData,
    "useBackgroundNameUpdates": ()=>useBackgroundNameUpdates,
    "useBackgroundPriceUpdates": ()=>useBackgroundPriceUpdates
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$background$2d$data$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/background-data-service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stock$2d$names$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stock-names-service.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature();
;
;
;
function useBackgroundData() {
    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    _s();
    const { enablePriceUpdates = true, enableNameUpdates = true, enableRealTimeUpdates = false, onError, onRealTimeUpdate } = options;
    // State
    const [isNamesReady, setIsNamesReady] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isUpdating, setIsUpdating] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [lastUpdate, setLastUpdate] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [stockNames, setStockNames] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(new Map());
    // Refs
    const listenerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const realTimeListenerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const mountedRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(true);
    // Create listener
    const createListener = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useBackgroundData.useCallback[createListener]": ()=>({
                onPriceUpdate: ({
                    "useBackgroundData.useCallback[createListener]": (data)=>{
                        if (!mountedRef.current || !enablePriceUpdates) return;
                        console.log("🔄 Received background price update: ".concat(data.length, " stocks"));
                        setLastUpdate(new Date());
                        setIsUpdating(false);
                        // Trigger custom event for other components to listen
                        window.dispatchEvent(new CustomEvent('backgroundPriceUpdate', {
                            detail: {
                                data,
                                timestamp: new Date()
                            }
                        }));
                    }
                })["useBackgroundData.useCallback[createListener]"],
                onNamesUpdate: ({
                    "useBackgroundData.useCallback[createListener]": (namesMap)=>{
                        if (!mountedRef.current || !enableNameUpdates) return;
                        console.log("📝 Received background names update: ".concat(namesMap.size, " names"));
                        setStockNames(new Map(namesMap));
                        setIsNamesReady(true);
                        setError(null);
                        // Trigger custom event for other components to listen
                        window.dispatchEvent(new CustomEvent('backgroundNamesUpdate', {
                            detail: {
                                namesMap,
                                timestamp: new Date()
                            }
                        }));
                    }
                })["useBackgroundData.useCallback[createListener]"],
                onError: ({
                    "useBackgroundData.useCallback[createListener]": (err)=>{
                        if (!mountedRef.current) return;
                        console.error('❌ Background data error:', err);
                        setError(err);
                        setIsUpdating(false);
                        if (onError) {
                            onError(err);
                        }
                    }
                })["useBackgroundData.useCallback[createListener]"]
            })
    }["useBackgroundData.useCallback[createListener]"], [
        enablePriceUpdates,
        enableNameUpdates,
        onError
    ]);
    // Create real-time listener
    const createRealTimeListener = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useBackgroundData.useCallback[createRealTimeListener]": ()=>({
                onRealTimeUpdate: ({
                    "useBackgroundData.useCallback[createRealTimeListener]": (quotes)=>{
                        if (!mountedRef.current || !enableRealTimeUpdates) return;
                        console.log("⚡ Received real-time update: ".concat(quotes.length, " quotes"));
                        setLastUpdate(new Date());
                        // Call custom callback if provided
                        if (onRealTimeUpdate) {
                            onRealTimeUpdate(quotes);
                        }
                        // Trigger custom event for components to listen
                        window.dispatchEvent(new CustomEvent('realTimePriceUpdate', {
                            detail: {
                                quotes,
                                timestamp: new Date()
                            }
                        }));
                    }
                })["useBackgroundData.useCallback[createRealTimeListener]"],
                onError: ({
                    "useBackgroundData.useCallback[createRealTimeListener]": (err)=>{
                        if (!mountedRef.current) return;
                        console.error('❌ Real-time update error:', err);
                        setError(err);
                        if (onError) {
                            onError(err);
                        }
                    }
                })["useBackgroundData.useCallback[createRealTimeListener]"]
            })
    }["useBackgroundData.useCallback[createRealTimeListener]"], [
        enableRealTimeUpdates,
        onRealTimeUpdate,
        onError
    ]);
    // Initialize and setup listener
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useBackgroundData.useEffect": ()=>{
            mountedRef.current = true;
            const initializeData = {
                "useBackgroundData.useEffect.initializeData": async ()=>{
                    try {
                        // Check if names are already ready
                        const namesReady = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$background$2d$data$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["backgroundDataService"].areNamesReady();
                        setIsNamesReady(namesReady);
                        if (namesReady) {
                            // Load existing cached names
                            const cachedNames = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stock$2d$names$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["stockNamesService"].getCachedStockNames();
                            setStockNames(cachedNames);
                        }
                        // Create and register listener
                        const listener = createListener();
                        listenerRef.current = listener;
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$background$2d$data$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["backgroundDataService"].addListener(listener);
                        // Create and register real-time listener if enabled
                        if (enableRealTimeUpdates) {
                            const realTimeListener = createRealTimeListener();
                            realTimeListenerRef.current = realTimeListener;
                            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$background$2d$data$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["backgroundDataService"].addRealTimeListener(realTimeListener);
                        }
                        // Initialize background service if not already done
                        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$background$2d$data$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["backgroundDataService"].initialize();
                    } catch (err) {
                        console.error('❌ Failed to initialize background data hook:', err);
                        setError(err);
                        // Set names ready to true to allow pages to work even if background service fails
                        setIsNamesReady(true);
                    }
                }
            }["useBackgroundData.useEffect.initializeData"];
            initializeData();
            // Cleanup
            return ({
                "useBackgroundData.useEffect": ()=>{
                    mountedRef.current = false;
                    if (listenerRef.current) {
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$background$2d$data$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["backgroundDataService"].removeListener(listenerRef.current);
                    }
                    if (realTimeListenerRef.current) {
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$background$2d$data$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["backgroundDataService"].removeRealTimeListener(realTimeListenerRef.current);
                    }
                }
            })["useBackgroundData.useEffect"];
        }
    }["useBackgroundData.useEffect"], [
        createListener,
        createRealTimeListener,
        enableRealTimeUpdates
    ]);
    // Get stock name with fallback
    const getStockName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useBackgroundData.useCallback[getStockName]": (symbol)=>{
            // First try from local state
            const name = stockNames.get(symbol);
            if (name) return name;
            // Fallback to sync method
            try {
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stock$2d$names$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["stockNamesService"].getStockNameSync(symbol);
            } catch (error) {
                // Ultimate fallback - just return symbol without .NS
                return symbol.replace('.NS', '');
            }
        }
    }["useBackgroundData.useCallback[getStockName]"], [
        stockNames
    ]);
    // Force update
    const forceUpdate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useBackgroundData.useCallback[forceUpdate]": async ()=>{
            setIsUpdating(true);
            setError(null);
            try {
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$background$2d$data$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["backgroundDataService"].forceUpdate();
            } catch (err) {
                setError(err);
                throw err;
            }
        }
    }["useBackgroundData.useCallback[forceUpdate]"], []);
    // Clear error
    const clearError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useBackgroundData.useCallback[clearError]": ()=>{
            setError(null);
        }
    }["useBackgroundData.useCallback[clearError]"], []);
    return {
        // Status
        isNamesReady,
        isUpdating,
        lastUpdate,
        error,
        // Data
        stockNames,
        // Methods
        getStockName,
        forceUpdate,
        clearError
    };
}
_s(useBackgroundData, "mtc/5ycqv5D0/jqVLqysEM2eH1g=");
function useBackgroundPriceUpdates(callback) {
    _s1();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useBackgroundPriceUpdates.useEffect": ()=>{
            const handlePriceUpdate = {
                "useBackgroundPriceUpdates.useEffect.handlePriceUpdate": (event)=>{
                    callback(event.detail.data);
                }
            }["useBackgroundPriceUpdates.useEffect.handlePriceUpdate"];
            window.addEventListener('backgroundPriceUpdate', handlePriceUpdate);
            return ({
                "useBackgroundPriceUpdates.useEffect": ()=>{
                    window.removeEventListener('backgroundPriceUpdate', handlePriceUpdate);
                }
            })["useBackgroundPriceUpdates.useEffect"];
        }
    }["useBackgroundPriceUpdates.useEffect"], [
        callback
    ]);
}
_s1(useBackgroundPriceUpdates, "OD7bBpZva5O2jO+Puf00hKivP7c=");
function useBackgroundNameUpdates(callback) {
    _s2();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useBackgroundNameUpdates.useEffect": ()=>{
            const handleNameUpdate = {
                "useBackgroundNameUpdates.useEffect.handleNameUpdate": (event)=>{
                    callback(event.detail.namesMap);
                }
            }["useBackgroundNameUpdates.useEffect.handleNameUpdate"];
            window.addEventListener('backgroundNamesUpdate', handleNameUpdate);
            return ({
                "useBackgroundNameUpdates.useEffect": ()=>{
                    window.removeEventListener('backgroundNamesUpdate', handleNameUpdate);
                }
            })["useBackgroundNameUpdates.useEffect"];
        }
    }["useBackgroundNameUpdates.useEffect"], [
        callback
    ]);
}
_s2(useBackgroundNameUpdates, "OD7bBpZva5O2jO+Puf00hKivP7c=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/useRealTimeStocks.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// React hook for real-time stock data updates
__turbopack_context__.s({
    "useRealTimeStocks": ()=>useRealTimeStocks,
    "useRealTimeUpdates": ()=>useRealTimeUpdates,
    "useSeamlessStockData": ()=>useSeamlessStockData
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useBackgroundData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useBackgroundData.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature();
;
;
function useRealTimeStocks() {
    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    _s();
    const { onUpdate, onError } = options;
    // State
    const [isActive, setIsActive] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [lastUpdate, setLastUpdate] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [updateCount, setUpdateCount] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Refs
    const mountedRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(true);
    const onUpdateRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(onUpdate);
    const onErrorRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(onError);
    // Update refs when callbacks change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useRealTimeStocks.useEffect": ()=>{
            onUpdateRef.current = onUpdate;
            onErrorRef.current = onError;
        }
    }["useRealTimeStocks.useEffect"], [
        onUpdate,
        onError
    ]);
    // Real-time update handler
    const handleRealTimeUpdate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useRealTimeStocks.useCallback[handleRealTimeUpdate]": (quotes)=>{
            if (!mountedRef.current) return;
            setLastUpdate(new Date());
            setUpdateCount({
                "useRealTimeStocks.useCallback[handleRealTimeUpdate]": (prev)=>prev + 1
            }["useRealTimeStocks.useCallback[handleRealTimeUpdate]"]);
            setError(null);
            // Call custom callback if provided
            if (onUpdateRef.current) {
                onUpdateRef.current(quotes);
            }
        }
    }["useRealTimeStocks.useCallback[handleRealTimeUpdate]"], []);
    // Error handler
    const handleError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useRealTimeStocks.useCallback[handleError]": (err)=>{
            if (!mountedRef.current) return;
            setError(err);
            if (onErrorRef.current) {
                onErrorRef.current(err);
            }
        }
    }["useRealTimeStocks.useCallback[handleError]"], []);
    // Background data hook with real-time enabled
    const { forceUpdate: forceBackgroundUpdate } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useBackgroundData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBackgroundData"])({
        enableRealTimeUpdates: isActive,
        onRealTimeUpdate: handleRealTimeUpdate,
        onError: handleError
    });
    // Start real-time updates
    const start = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useRealTimeStocks.useCallback[start]": ()=>{
            console.log('🚀 Starting real-time stock updates...');
            setIsActive(true);
            setError(null);
        }
    }["useRealTimeStocks.useCallback[start]"], []);
    // Stop real-time updates
    const stop = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useRealTimeStocks.useCallback[stop]": ()=>{
            console.log('⏹️ Stopping real-time stock updates...');
            setIsActive(false);
        }
    }["useRealTimeStocks.useCallback[stop]"], []);
    // Force immediate update
    const forceUpdate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useRealTimeStocks.useCallback[forceUpdate]": async ()=>{
            try {
                await forceBackgroundUpdate();
            } catch (err) {
                handleError(err);
            }
        }
    }["useRealTimeStocks.useCallback[forceUpdate]"], [
        forceBackgroundUpdate,
        handleError
    ]);
    // Clear error
    const clearError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useRealTimeStocks.useCallback[clearError]": ()=>{
            setError(null);
        }
    }["useRealTimeStocks.useCallback[clearError]"], []);
    // Cleanup on unmount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useRealTimeStocks.useEffect": ()=>{
            mountedRef.current = true;
            return ({
                "useRealTimeStocks.useEffect": ()=>{
                    mountedRef.current = false;
                }
            })["useRealTimeStocks.useEffect"];
        }
    }["useRealTimeStocks.useEffect"], []);
    return {
        isActive,
        lastUpdate,
        updateCount,
        error,
        start,
        stop,
        forceUpdate,
        clearError
    };
}
_s(useRealTimeStocks, "bGoH4+xUwNYEE2YUUx36szSRYXY=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useBackgroundData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBackgroundData"]
    ];
});
function useRealTimeUpdates(callback) {
    _s1();
    const callbackRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(callback);
    // Update callback ref
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useRealTimeUpdates.useEffect": ()=>{
            callbackRef.current = callback;
        }
    }["useRealTimeUpdates.useEffect"], [
        callback
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useRealTimeUpdates.useEffect": ()=>{
            const handleRealTimeUpdate = {
                "useRealTimeUpdates.useEffect.handleRealTimeUpdate": (event)=>{
                    const { quotes } = event.detail;
                    callbackRef.current(quotes);
                }
            }["useRealTimeUpdates.useEffect.handleRealTimeUpdate"];
            window.addEventListener('realTimePriceUpdate', handleRealTimeUpdate);
            return ({
                "useRealTimeUpdates.useEffect": ()=>{
                    window.removeEventListener('realTimePriceUpdate', handleRealTimeUpdate);
                }
            })["useRealTimeUpdates.useEffect"];
        }
    }["useRealTimeUpdates.useEffect"], []);
}
_s1(useRealTimeUpdates, "K/h/7jNaRWXLP/aFqUkOqhPUWKE=");
function useSeamlessStockData() {
    _s2();
    const [stocks, setStocks] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    // Background data for names
    const { isNamesReady, getStockName } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useBackgroundData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBackgroundData"])();
    // Real-time updates for prices
    const realTimeStocks = useRealTimeStocks({
        onUpdate: {
            "useSeamlessStockData.useRealTimeStocks[realTimeStocks]": (quotes)=>{
                // Update stocks with new price data
                setStocks({
                    "useSeamlessStockData.useRealTimeStocks[realTimeStocks]": (prevStocks)=>{
                        const updatedStocks = [
                            ...prevStocks
                        ];
                        quotes.forEach({
                            "useSeamlessStockData.useRealTimeStocks[realTimeStocks]": (quote)=>{
                                const index = updatedStocks.findIndex({
                                    "useSeamlessStockData.useRealTimeStocks[realTimeStocks].index": (stock)=>stock.symbol === quote.symbol
                                }["useSeamlessStockData.useRealTimeStocks[realTimeStocks].index"]);
                                if (index >= 0) {
                                    updatedStocks[index] = {
                                        ...updatedStocks[index],
                                        ...quote
                                    };
                                } else {
                                    updatedStocks.push({
                                        ...quote,
                                        name: getStockName(quote.symbol)
                                    });
                                }
                            }
                        }["useSeamlessStockData.useRealTimeStocks[realTimeStocks]"]);
                        return updatedStocks;
                    }
                }["useSeamlessStockData.useRealTimeStocks[realTimeStocks]"]);
                setIsLoading(false);
            }
        }["useSeamlessStockData.useRealTimeStocks[realTimeStocks]"]
    });
    // Start real-time updates when names are ready
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useSeamlessStockData.useEffect": ()=>{
            if (isNamesReady && !realTimeStocks.isActive) {
                realTimeStocks.start();
            }
        }
    }["useSeamlessStockData.useEffect"], [
        isNamesReady,
        realTimeStocks
    ]);
    return {
        stocks,
        isLoading: isLoading && !isNamesReady,
        isRealTimeActive: realTimeStocks.isActive,
        lastUpdate: realTimeStocks.lastUpdate,
        updateCount: realTimeStocks.updateCount,
        error: realTimeStocks.error,
        forceUpdate: realTimeStocks.forceUpdate,
        getStockName
    };
}
_s2(useSeamlessStockData, "wNSKS/zwVYz54XDK+PAe+t5OtHk=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useBackgroundData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBackgroundData"],
        useRealTimeStocks
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/dashboard/boh-eligible/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>BOHEligiblePage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-client] (ecmascript) <export default as Search>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/refresh-cw.js [app-client] (ecmascript) <export default as RefreshCw>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$funnel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Filter$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/funnel.js [app-client] (ecmascript) <export default as Filter>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-alert.js [app-client] (ecmascript) <export default as AlertCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader.js [app-client] (ecmascript) <export default as Loader>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/cache-service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$LoadingStates$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/LoadingStates.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$RealTimeIndicator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/RealTimeIndicator.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useBackgroundData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useBackgroundData.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useRealTimeStocks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useRealTimeStocks.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
function BOHEligiblePage() {
    _s();
    // Background data hook for instant loading
    const { isNamesReady, getStockName, forceUpdate: forceBackgroundUpdate, error: backgroundError } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useBackgroundData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBackgroundData"])();
    // Search state
    const [searchQuery, setSearchQuery] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [searchResults, setSearchResults] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isSearching, setIsSearching] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Nifty 200 stocks state - now loads instantly with cached names
    const [niftyStocks, setNiftyStocks] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [loadingPriceData, setLoadingPriceData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [niftyError, setNiftyError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Filter state
    const [showFilters, setShowFilters] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [priceFilter, setPriceFilter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        min: 0,
        max: 10000
    });
    const [showOnlyHoldings, setShowOnlyHoldings] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Load price data only (names are handled by background service)
    const loadPriceData = async function() {
        let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;
        setLoadingPriceData(true);
        setNiftyError(null);
        try {
            if (forceRefresh) {
                // Force background update for both names and prices
                await forceBackgroundUpdate();
            }
            const batchSize = 25;
            const totalBatches = Math.ceil(200 / batchSize);
            const allStocks = [];
            console.log("🚀 Loading price data for ".concat(totalBatches, " batches").concat(forceRefresh ? ' (force refresh)' : '', "..."));
            for(let batchIndex = 0; batchIndex < totalBatches; batchIndex++){
                try {
                    let batchData;
                    if (!forceRefresh) {
                        const cacheKey = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CacheKeys"].niftyStocks(batchIndex);
                        const cached = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheService"].get(cacheKey);
                        if (cached) {
                            batchData = cached;
                        }
                    }
                    if (!batchData) {
                        try {
                            // Pass forceRefresh parameter to API to control name caching
                            const url = "/api/stocks/nifty200?batchIndex=".concat(batchIndex, "&batchSize=").concat(batchSize).concat(forceRefresh ? '&forceRefresh=true' : '');
                            console.log("🔄 Fetching batch ".concat(batchIndex + 1, "/").concat(totalBatches, ": ").concat(url));
                            const response = await fetch(url, {
                                method: 'GET',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                // Add timeout to prevent hanging
                                signal: AbortSignal.timeout(30000) // 30 second timeout
                            });
                            if (!response.ok) {
                                throw new Error("HTTP ".concat(response.status, ": ").concat(response.statusText));
                            }
                            const data = await response.json();
                            if (data.success) {
                                var _batchData_stocks;
                                batchData = data.data;
                                // Cache the batch data (but names are cached separately in the service)
                                if (!forceRefresh) {
                                    const cacheKey = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CacheKeys"].niftyStocks(batchIndex);
                                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheService"].set(cacheKey, batchData);
                                }
                                console.log("✅ Successfully fetched batch ".concat(batchIndex + 1, ": ").concat(((_batchData_stocks = batchData.stocks) === null || _batchData_stocks === void 0 ? void 0 : _batchData_stocks.length) || 0, " stocks"));
                            } else {
                                console.error("❌ API returned error for batch ".concat(batchIndex, ":"), data.error);
                                continue;
                            }
                        } catch (fetchError) {
                            console.error("❌ Network error fetching batch ".concat(batchIndex, ":"), fetchError);
                            continue;
                        }
                    }
                    if (batchData && batchData.stocks) {
                        // Use cached names from background service for instant display
                        const stocksWithCachedNames = batchData.stocks.map((stock)=>({
                                ...stock,
                                name: getStockName(stock.symbol) || stock.name
                            }));
                        allStocks.push(...stocksWithCachedNames);
                        setNiftyStocks([
                            ...allStocks
                        ]);
                        console.log("✅ Loaded batch ".concat(batchIndex + 1, "/").concat(totalBatches, ": ").concat(stocksWithCachedNames.length, " stocks (Total: ").concat(allStocks.length, ")"));
                    }
                    if (batchIndex < totalBatches - 1) {
                        await new Promise((resolve)=>setTimeout(resolve, 200));
                    }
                } catch (batchError) {
                    console.error("Error loading batch ".concat(batchIndex, ":"), batchError);
                }
            }
            setNiftyStocks(allStocks);
            console.log("🎉 Loaded all price data: ".concat(allStocks.length, " total stocks"));
        } catch (error) {
            console.error('❌ Error loading price data:', error);
            // If we have no stocks at all, create fallback entries with cached names
            if (niftyStocks.length === 0 && isNamesReady) {
                console.log('🔄 Creating fallback stock entries with cached names...');
                // Import NIFTY_200_SYMBOLS and create basic entries
                __turbopack_context__.r("[project]/src/lib/nifty-stocks.ts [app-client] (ecmascript, async loader)")(__turbopack_context__.i).then((param)=>{
                    let { NIFTY_200_SYMBOLS, getYahooSymbol } = param;
                    const fallbackStocks = NIFTY_200_SYMBOLS.slice(0, 50).map((symbol)=>{
                        const yahooSymbol = getYahooSymbol(symbol);
                        return {
                            symbol: yahooSymbol || symbol,
                            name: getStockName(yahooSymbol || symbol),
                            price: 0,
                            change: 0,
                            changePercent: 0,
                            volume: 0,
                            high52Week: 0,
                            low52Week: 0,
                            high52WeekDate: undefined,
                            low52WeekDate: undefined,
                            isBOHEligible: false,
                            avgVolume: 0,
                            isEligible: true,
                            inHoldings: false
                        };
                    });
                    setNiftyStocks(fallbackStocks);
                    console.log("✅ Created ".concat(fallbackStocks.length, " fallback stock entries"));
                });
            }
            setNiftyError('Some data may be unavailable. Showing cached information.');
        } finally{
            setLoadingPriceData(false);
        }
    };
    // Search within Nifty 200 stocks
    const handleSearch = async (query)=>{
        if (!query.trim()) {
            setSearchResults([]);
            return;
        }
        setIsSearching(true);
        try {
            const response = await fetch('/api/stocks/nifty200', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    searchQuery: query
                })
            });
            const data = await response.json();
            if (data.success) {
                setSearchResults(data.data.stocks);
            } else {
                setSearchResults([]);
            }
        } catch (error) {
            console.error('Search error:', error);
            setSearchResults([]);
        } finally{
            setIsSearching(false);
        }
    };
    // Filter stocks based on current filters
    const getFilteredStocks = (stocks)=>{
        return stocks.filter((stock)=>{
            // Only show BOH eligible stocks
            if (!stock.isBOHEligible) return false;
            // General filters
            if (stock.price < priceFilter.min || stock.price > priceFilter.max) return false;
            if (showOnlyHoldings && !stock.inHoldings) return false;
            return true;
        });
    };
    // Get BOH eligible stocks
    const getBOHEligibleStocks = (stocks)=>{
        return stocks.filter((stock)=>stock.isBOHEligible);
    };
    // Load initial data when names are ready
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BOHEligiblePage.useEffect": ()=>{
            if (isNamesReady) {
                loadPriceData(false);
            }
        }
    }["BOHEligiblePage.useEffect"], [
        isNamesReady
    ]);
    // Handle search with debouncing
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BOHEligiblePage.useEffect": ()=>{
            const debounceTimer = setTimeout({
                "BOHEligiblePage.useEffect.debounceTimer": ()=>{
                    handleSearch(searchQuery);
                }
            }["BOHEligiblePage.useEffect.debounceTimer"], 300);
            return ({
                "BOHEligiblePage.useEffect": ()=>clearTimeout(debounceTimer)
            })["BOHEligiblePage.useEffect"];
        }
    }["BOHEligiblePage.useEffect"], [
        searchQuery
    ]);
    // Real-time stock updates for BOH page
    const realTimeStocks = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useRealTimeStocks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRealTimeStocks"])({
        onUpdate: {
            "BOHEligiblePage.useRealTimeStocks[realTimeStocks]": (quotes)=>{
                console.log("⚡ BOH page received real-time update: ".concat(quotes.length, " quotes"));
                // Update existing stocks with new price data
                setNiftyStocks({
                    "BOHEligiblePage.useRealTimeStocks[realTimeStocks]": (currentStocks)=>{
                        const updatedStocks = currentStocks.map({
                            "BOHEligiblePage.useRealTimeStocks[realTimeStocks].updatedStocks": (stock)=>{
                                const updatedQuote = quotes.find({
                                    "BOHEligiblePage.useRealTimeStocks[realTimeStocks].updatedStocks.updatedQuote": (quote)=>quote.symbol === stock.symbol
                                }["BOHEligiblePage.useRealTimeStocks[realTimeStocks].updatedStocks.updatedQuote"]);
                                if (updatedQuote) {
                                    return {
                                        ...stock,
                                        price: updatedQuote.price || stock.price,
                                        change: updatedQuote.change || stock.change,
                                        changePercent: updatedQuote.changePercent || stock.changePercent,
                                        volume: updatedQuote.volume || stock.volume,
                                        high52Week: updatedQuote.high52Week || stock.high52Week,
                                        low52Week: updatedQuote.low52Week || stock.low52Week,
                                        high52WeekDate: updatedQuote.high52WeekDate || stock.high52WeekDate,
                                        low52WeekDate: updatedQuote.low52WeekDate || stock.low52WeekDate,
                                        isBOHEligible: updatedQuote.isBOHEligible || stock.isBOHEligible
                                    };
                                }
                                return stock;
                            }
                        }["BOHEligiblePage.useRealTimeStocks[realTimeStocks].updatedStocks"]);
                        return updatedStocks;
                    }
                }["BOHEligiblePage.useRealTimeStocks[realTimeStocks]"]);
            }
        }["BOHEligiblePage.useRealTimeStocks[realTimeStocks]"],
        onError: {
            "BOHEligiblePage.useRealTimeStocks[realTimeStocks]": (error)=>{
                console.error('❌ BOH real-time update error:', error);
                setNiftyError('Real-time updates temporarily unavailable');
            }
        }["BOHEligiblePage.useRealTimeStocks[realTimeStocks]"]
    });
    // Start real-time updates when names are ready
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BOHEligiblePage.useEffect": ()=>{
            if (isNamesReady && !realTimeStocks.isActive) {
                console.log('🚀 Starting real-time updates for BOH Eligible page');
                realTimeStocks.start();
            }
        }
    }["BOHEligiblePage.useEffect"], [
        isNamesReady,
        realTimeStocks
    ]);
    // BOH Stock row component with required columns
    const BOHStockRow = (param)=>{
        let { stock } = param;
        var _stock_low52Week, _stock_high52Week;
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-between p-4 hover:bg-gray-50 border-b border-gray-100",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex-1 grid grid-cols-5 gap-4 items-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                        className: "font-medium text-gray-900",
                                        children: stock.symbol
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                        lineNumber: 289,
                                        columnNumber: 13
                                    }, this),
                                    stock.isBOHEligible && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full",
                                        children: "BOH"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                        lineNumber: 291,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 288,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-sm text-gray-600 truncate",
                                children: stock.name
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 296,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                        lineNumber: 287,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "font-semibold text-gray-900",
                                children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatCurrency"])(stock.price)
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 301,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-sm ".concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getChangeColor"])(stock.change)),
                                children: [
                                    stock.change >= 0 ? '+' : '',
                                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatPercentage"])(stock.changePercent)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 304,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                        lineNumber: 300,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-sm text-gray-900",
                                children: stock.low52WeekDate || 'N/A'
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 311,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-xs text-gray-500",
                                children: [
                                    "₹",
                                    ((_stock_low52Week = stock.low52Week) === null || _stock_low52Week === void 0 ? void 0 : _stock_low52Week.toFixed(2)) || 'N/A'
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 314,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                        lineNumber: 310,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-sm text-gray-900",
                                children: stock.high52WeekDate || 'N/A'
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 321,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-xs text-gray-500",
                                children: [
                                    "₹",
                                    ((_stock_high52Week = stock.high52Week) === null || _stock_high52Week === void 0 ? void 0 : _stock_high52Week.toFixed(2)) || 'N/A'
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 324,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                        lineNumber: 320,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "px-3 py-1 rounded-full text-sm font-medium ".concat(stock.isBOHEligible ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),
                            children: stock.isBOHEligible ? 'Yes' : 'No'
                        }, void 0, false, {
                            fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                            lineNumber: 331,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                        lineNumber: 330,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                lineNumber: 285,
                columnNumber: 7
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
            lineNumber: 284,
            columnNumber: 5
        }, this);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-between",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                className: "text-2xl font-bold text-gray-900",
                                children: "BOH Eligible Stocks"
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 348,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-gray-600 mt-1",
                                children: [
                                    "Stocks with Boom-Bust-Recovery pattern (52-week low after 52-week high) • ",
                                    getBOHEligibleStocks(niftyStocks).length,
                                    " eligible stocks",
                                    loadingPriceData && niftyStocks.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-blue-600",
                                        children: [
                                            " (Loading ",
                                            niftyStocks.length,
                                            "/200...)"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                        lineNumber: 352,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 349,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mt-2",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$RealTimeIndicator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RealTimeIndicator"], {
                                    isActive: realTimeStocks.isActive,
                                    lastUpdate: realTimeStocks.lastUpdate,
                                    updateCount: realTimeStocks.updateCount,
                                    error: realTimeStocks.error
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                    lineNumber: 356,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 355,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                        lineNumber: 347,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex space-x-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>setShowFilters(!showFilters),
                                className: "px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$funnel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Filter$3e$__["Filter"], {
                                        className: "h-4 w-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                        lineNumber: 369,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "Filters"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                        lineNumber: 370,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 365,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>loadPriceData(true),
                                disabled: loadingPriceData,
                                className: "px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center space-x-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__["RefreshCw"], {
                                        className: "h-4 w-4 ".concat(loadingPriceData ? 'animate-spin' : '')
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                        lineNumber: 377,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "Refresh All"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                        lineNumber: 378,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 372,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                        lineNumber: 364,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                lineNumber: 346,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__["Search"], {
                        className: "absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                        lineNumber: 385,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                        type: "text",
                        placeholder: "Search stocks by symbol or name...",
                        value: searchQuery,
                        onChange: (e)=>setSearchQuery(e.target.value),
                        className: "w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                        lineNumber: 386,
                        columnNumber: 9
                    }, this),
                    isSearching && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute right-3 top-1/2 transform -translate-y-1/2",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__["RefreshCw"], {
                            className: "h-5 w-5 text-gray-400 animate-spin"
                        }, void 0, false, {
                            fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                            lineNumber: 395,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                        lineNumber: 394,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                lineNumber: 384,
                columnNumber: 7
            }, this),
            showFilters && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white rounded-lg shadow-sm border border-gray-200 p-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-semibold text-gray-900 mb-4",
                        children: "Filter Options"
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                        lineNumber: 403,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "grid grid-cols-1 md:grid-cols-3 gap-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                        className: "block text-sm font-medium text-gray-700 mb-2",
                                        children: "Price Range"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                        lineNumber: 406,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex space-x-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                type: "number",
                                                placeholder: "Min",
                                                value: priceFilter.min,
                                                onChange: (e)=>setPriceFilter((prev)=>({
                                                            ...prev,
                                                            min: Number(e.target.value)
                                                        })),
                                                className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                                lineNumber: 408,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                type: "number",
                                                placeholder: "Max",
                                                value: priceFilter.max,
                                                onChange: (e)=>setPriceFilter((prev)=>({
                                                            ...prev,
                                                            max: Number(e.target.value)
                                                        })),
                                                className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                                lineNumber: 415,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                        lineNumber: 407,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 405,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                        className: "block text-sm font-medium text-gray-700 mb-2",
                                        children: "Stock Filters"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                        lineNumber: 426,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "space-y-2",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                            className: "flex items-center",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                    type: "checkbox",
                                                    checked: showOnlyHoldings,
                                                    onChange: (e)=>setShowOnlyHoldings(e.target.checked),
                                                    className: "rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                                    lineNumber: 429,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "ml-2 text-sm text-gray-600",
                                                    children: "Show only stocks in holdings"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                                    lineNumber: 435,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                            lineNumber: 428,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                        lineNumber: 427,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 425,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-end",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>{
                                        setPriceFilter({
                                            min: 0,
                                            max: 10000
                                        });
                                        setShowOnlyHoldings(false);
                                    },
                                    className: "px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",
                                    children: "Reset Filters"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                    lineNumber: 441,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                lineNumber: 440,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                        lineNumber: 404,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                lineNumber: 402,
                columnNumber: 9
            }, this),
            niftyError && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-red-50 border border-red-200 rounded-lg p-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__["AlertCircle"], {
                            className: "h-5 w-5 text-red-600 mr-2"
                        }, void 0, false, {
                            fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                            lineNumber: 459,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-red-800",
                            children: niftyError
                        }, void 0, false, {
                            fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                            lineNumber: 460,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                    lineNumber: 458,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                lineNumber: 457,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white rounded-lg shadow-sm border border-gray-200",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-gray-50 px-4 py-3 border-b border-gray-200",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-5 gap-4 text-sm font-medium text-gray-700",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: "Stock Name"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                    lineNumber: 470,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center",
                                    children: "CMP"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                    lineNumber: 471,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center",
                                    children: "52-Week Low Date"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                    lineNumber: 472,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center",
                                    children: "52-Week High Date"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                    lineNumber: 473,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center",
                                    children: "BOH Eligible"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                    lineNumber: 474,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                            lineNumber: 469,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                        lineNumber: 468,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: !isNamesReady ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "p-8 text-center text-gray-500",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader$3e$__["Loader"], {
                                    className: "h-8 w-8 mx-auto mb-4 animate-spin text-blue-600"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                    lineNumber: 481,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    children: "Initializing stock data..."
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                    lineNumber: 482,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-sm mt-1",
                                    children: "Loading stock names for the first time"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                    lineNumber: 483,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                            lineNumber: 480,
                            columnNumber: 13
                        }, this) : loadingPriceData && niftyStocks.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$LoadingStates$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["StockListSkeleton"], {
                            count: 10
                        }, void 0, false, {
                            fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                            lineNumber: 486,
                            columnNumber: 13
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                            children: [
                                getFilteredStocks(niftyStocks).map((stock, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(BOHStockRow, {
                                        stock: stock
                                    }, "boh-".concat(stock.symbol, "-").concat(index), false, {
                                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                        lineNumber: 490,
                                        columnNumber: 17
                                    }, this)),
                                loadingPriceData && niftyStocks.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "p-4 border-t border-gray-100 bg-blue-50",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center justify-center space-x-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader$3e$__["Loader"], {
                                                className: "h-4 w-4 animate-spin text-blue-600"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                                lineNumber: 496,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-blue-700 text-sm",
                                                children: [
                                                    "Updating price data... (",
                                                    niftyStocks.length,
                                                    "/200)"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                                lineNumber: 497,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                        lineNumber: 495,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                    lineNumber: 494,
                                    columnNumber: 17
                                }, this),
                                niftyStocks.length === 0 && !loadingPriceData && isNamesReady && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "p-8 text-center text-gray-500",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__["AlertCircle"], {
                                            className: "h-12 w-12 mx-auto mb-4 text-gray-300"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                            lineNumber: 506,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            children: "No stocks loaded."
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                            lineNumber: 507,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm mt-1",
                                            children: "Try refreshing the data."
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                            lineNumber: 508,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                    lineNumber: 505,
                                    columnNumber: 17
                                }, this),
                                getFilteredStocks(niftyStocks).length === 0 && niftyStocks.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "p-8 text-center text-gray-500",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__["AlertCircle"], {
                                            className: "h-12 w-12 mx-auto mb-4 text-gray-300"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                            lineNumber: 514,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            children: "No BOH eligible stocks found."
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                            lineNumber: 515,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm mt-1",
                                            children: "BOH stocks have 52-week low after 52-week high (Boom → Bust → Recovery pattern)."
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                            lineNumber: 516,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                                    lineNumber: 513,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                        lineNumber: 478,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
                lineNumber: 466,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/dashboard/boh-eligible/page.tsx",
        lineNumber: 344,
        columnNumber: 5
    }, this);
}
_s(BOHEligiblePage, "i6Q/t5dLrb0iOXg8+0OY/jd08Dw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useBackgroundData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBackgroundData"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useRealTimeStocks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRealTimeStocks"]
    ];
});
_c = BOHEligiblePage;
var _c;
__turbopack_context__.k.register(_c, "BOHEligiblePage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_5f61d61a._.js.map