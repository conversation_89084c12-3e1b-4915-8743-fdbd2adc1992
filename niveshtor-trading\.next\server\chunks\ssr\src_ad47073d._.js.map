{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/hooks/useCentralData.ts"], "sourcesContent": ["// React Hook for Central Data Manager Integration\n// Provides real-time data access and automatic updates for all stock-related pages\n\nimport { useState, useEffect, useCallback, useRef } from 'react';\nimport { NiftyStock } from '@/lib/nifty-stocks';\nimport { WeeklyHighSignal } from '@/lib/weekly-high-signal-detector';\nimport { AutoGTTOrder } from '@/lib/automatic-gtt-service';\n\ntype DataType = 'nifty200' | 'bohEligible' | 'weeklyHighSignals' | 'gttOrders';\n\ninterface DataState<T> {\n  data: T[];\n  lastUpdated: Date | null;\n  isLoading: boolean;\n  error: string | null;\n}\n\ninterface CentralDataHook {\n  nifty200: DataState<NiftyStock>;\n  bohEligible: DataState<NiftyStock>;\n  weeklyHighSignals: DataState<WeeklyHighSignal>;\n  gttOrders: DataState<AutoGTTOrder>;\n  refreshData: (dataType?: DataType) => Promise<void>;\n  isInitialized: boolean;\n  isServiceRunning: boolean;\n}\n\nexport function useCentralData(): CentralDataHook {\n  const [nifty200, setNifty200] = useState<DataState<NiftyStock>>({\n    data: [],\n    lastUpdated: null,\n    isLoading: false,\n    error: null\n  });\n\n  const [bohEligible, setBohEligible] = useState<DataState<NiftyStock>>({\n    data: [],\n    lastUpdated: null,\n    isLoading: false,\n    error: null\n  });\n\n  const [weeklyHighSignals, setWeeklyHighSignals] = useState<DataState<WeeklyHighSignal>>({\n    data: [],\n    lastUpdated: null,\n    isLoading: false,\n    error: null\n  });\n\n  const [gttOrders, setGttOrders] = useState<DataState<AutoGTTOrder>>({\n    data: [],\n    lastUpdated: null,\n    isLoading: false,\n    error: null\n  });\n\n  const [isInitialized, setIsInitialized] = useState(false);\n  const [isServiceRunning, setIsServiceRunning] = useState(false);\n\n  const pollingIntervals = useRef<Map<DataType, NodeJS.Timeout>>(new Map());\n\n  // Fetch data from API\n  const fetchData = useCallback(async (dataType: DataType) => {\n    try {\n      const response = await fetch(`/api/data-manager?action=${dataType === 'bohEligible' ? 'boh-eligible' : dataType === 'weeklyHighSignals' ? 'weekly-high-signals' : dataType === 'gttOrders' ? 'gtt-orders' : dataType}`);\n      const result = await response.json();\n\n      if (result.success) {\n        const newState = {\n          data: result.data || [],\n          lastUpdated: result.lastUpdated ? new Date(result.lastUpdated) : new Date(),\n          isLoading: result.isLoading || false,\n          error: null\n        };\n\n        switch (dataType) {\n          case 'nifty200':\n            setNifty200(newState);\n            break;\n          case 'bohEligible':\n            setBohEligible(newState);\n            break;\n          case 'weeklyHighSignals':\n            setWeeklyHighSignals(newState);\n            break;\n          case 'gttOrders':\n            setGttOrders(newState);\n            break;\n        }\n\n        console.log(`📊 Updated ${dataType}: ${result.data?.length || 0} items`);\n      } else {\n        throw new Error(result.error || 'Failed to fetch data');\n      }\n    } catch (error) {\n      console.error(`❌ Error fetching ${dataType}:`, error);\n      \n      const errorState = {\n        data: [],\n        lastUpdated: null,\n        isLoading: false,\n        error: error instanceof Error ? error.message : 'Unknown error'\n      };\n\n      switch (dataType) {\n        case 'nifty200':\n          setNifty200(prev => ({ ...prev, ...errorState }));\n          break;\n        case 'bohEligible':\n          setBohEligible(prev => ({ ...prev, ...errorState }));\n          break;\n        case 'weeklyHighSignals':\n          setWeeklyHighSignals(prev => ({ ...prev, ...errorState }));\n          break;\n        case 'gttOrders':\n          setGttOrders(prev => ({ ...prev, ...errorState }));\n          break;\n      }\n    }\n  }, []);\n\n  // Check service status\n  const checkServiceStatus = useCallback(async () => {\n    try {\n      const response = await fetch('/api/data-manager?action=status');\n      const result = await response.json();\n\n      if (result.success) {\n        setIsInitialized(result.data.isInitialized);\n        setIsServiceRunning(result.data.isRunning);\n      }\n    } catch (error) {\n      console.error('❌ Error checking service status:', error);\n    }\n  }, []);\n\n  // Initialize service if not already initialized\n  const initializeService = useCallback(async () => {\n    try {\n      console.log('🚀 Initializing Central Data Manager...');\n      \n      const response = await fetch('/api/data-manager', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ action: 'initialize' })\n      });\n\n      const result = await response.json();\n      \n      if (result.success) {\n        console.log('✅ Central Data Manager initialized');\n        setIsInitialized(true);\n        \n        // Start the service\n        const startResponse = await fetch('/api/data-manager', {\n          method: 'POST',\n          headers: { 'Content-Type': 'application/json' },\n          body: JSON.stringify({ action: 'start' })\n        });\n\n        const startResult = await startResponse.json();\n        if (startResult.success) {\n          setIsServiceRunning(true);\n          console.log('✅ Central Data Manager started');\n        }\n      }\n    } catch (error) {\n      console.error('❌ Error initializing service:', error);\n    }\n  }, []);\n\n  // Refresh specific data type\n  const refreshData = useCallback(async (dataType?: DataType) => {\n    if (dataType) {\n      await fetchData(dataType);\n    } else {\n      // Refresh all data\n      await Promise.all([\n        fetchData('nifty200'),\n        fetchData('bohEligible'),\n        fetchData('weeklyHighSignals'),\n        fetchData('gttOrders')\n      ]);\n    }\n  }, [fetchData]);\n\n  // Set up polling for real-time updates\n  const setupPolling = useCallback(() => {\n    // Clear existing intervals\n    pollingIntervals.current.forEach(interval => clearInterval(interval));\n    pollingIntervals.current.clear();\n\n    // Set up new intervals\n    const intervals = {\n      nifty200: 30000, // 30 seconds\n      bohEligible: 60000, // 1 minute\n      weeklyHighSignals: 300000, // 5 minutes\n      gttOrders: 30000 // 30 seconds\n    };\n\n    Object.entries(intervals).forEach(([dataType, interval]) => {\n      const intervalId = setInterval(() => {\n        fetchData(dataType as DataType);\n      }, interval);\n      \n      pollingIntervals.current.set(dataType as DataType, intervalId);\n    });\n\n    console.log('⏰ Polling intervals set up for real-time updates');\n  }, [fetchData]);\n\n  // Initialize on mount\n  useEffect(() => {\n    const initialize = async () => {\n      // Check if service is already running\n      await checkServiceStatus();\n      \n      // Initialize service if needed\n      if (!isInitialized) {\n        await initializeService();\n      }\n\n      // Load initial data\n      await refreshData();\n\n      // Set up polling for real-time updates\n      setupPolling();\n    };\n\n    initialize();\n\n    // Cleanup on unmount\n    return () => {\n      pollingIntervals.current.forEach(interval => clearInterval(interval));\n      pollingIntervals.current.clear();\n    };\n  }, []);\n\n  // Re-setup polling when service status changes\n  useEffect(() => {\n    if (isServiceRunning) {\n      setupPolling();\n    }\n  }, [isServiceRunning, setupPolling]);\n\n  return {\n    nifty200,\n    bohEligible,\n    weeklyHighSignals,\n    gttOrders,\n    refreshData,\n    isInitialized,\n    isServiceRunning\n  };\n}\n\n// Specialized hooks for individual data types\nexport function useNifty200Stocks() {\n  const { nifty200, refreshData } = useCentralData();\n  \n  return {\n    stocks: nifty200.data,\n    lastUpdated: nifty200.lastUpdated,\n    isLoading: nifty200.isLoading,\n    error: nifty200.error,\n    refresh: () => refreshData('nifty200')\n  };\n}\n\nexport function useBOHEligibleStocks() {\n  const { bohEligible, refreshData } = useCentralData();\n  \n  return {\n    stocks: bohEligible.data,\n    lastUpdated: bohEligible.lastUpdated,\n    isLoading: bohEligible.isLoading,\n    error: bohEligible.error,\n    refresh: () => refreshData('bohEligible')\n  };\n}\n\nexport function useWeeklyHighSignals() {\n  const { weeklyHighSignals, refreshData } = useCentralData();\n  \n  return {\n    signals: weeklyHighSignals.data,\n    lastUpdated: weeklyHighSignals.lastUpdated,\n    isLoading: weeklyHighSignals.isLoading,\n    error: weeklyHighSignals.error,\n    refresh: () => refreshData('weeklyHighSignals')\n  };\n}\n\nexport function useGTTOrders() {\n  const { gttOrders, refreshData } = useCentralData();\n  \n  return {\n    orders: gttOrders.data,\n    lastUpdated: gttOrders.lastUpdated,\n    isLoading: gttOrders.isLoading,\n    error: gttOrders.error,\n    refresh: () => refreshData('gttOrders')\n  };\n}\n"], "names": [], "mappings": "AAAA,kDAAkD;AAClD,mFAAmF;;;;;;;;AAEnF;;AAwBO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;QAC9D,MAAM,EAAE;QACR,aAAa;QACb,WAAW;QACX,OAAO;IACT;IAEA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;QACpE,MAAM,EAAE;QACR,aAAa;QACb,WAAW;QACX,OAAO;IACT;IAEA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+B;QACtF,MAAM,EAAE;QACR,aAAa;QACb,WAAW;QACX,OAAO;IACT;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;QAClE,MAAM,EAAE;QACR,aAAa;QACb,WAAW;QACX,OAAO;IACT;IAEA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAiC,IAAI;IAEnE,sBAAsB;IACtB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACnC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,yBAAyB,EAAE,aAAa,gBAAgB,iBAAiB,aAAa,sBAAsB,wBAAwB,aAAa,cAAc,eAAe,UAAU;YACtN,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,WAAW;oBACf,MAAM,OAAO,IAAI,IAAI,EAAE;oBACvB,aAAa,OAAO,WAAW,GAAG,IAAI,KAAK,OAAO,WAAW,IAAI,IAAI;oBACrE,WAAW,OAAO,SAAS,IAAI;oBAC/B,OAAO;gBACT;gBAEA,OAAQ;oBACN,KAAK;wBACH,YAAY;wBACZ;oBACF,KAAK;wBACH,eAAe;wBACf;oBACF,KAAK;wBACH,qBAAqB;wBACrB;oBACF,KAAK;wBACH,aAAa;wBACb;gBACJ;gBAEA,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE,OAAO,IAAI,EAAE,UAAU,EAAE,MAAM,CAAC;YACzE,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,iBAAiB,EAAE,SAAS,CAAC,CAAC,EAAE;YAE/C,MAAM,aAAa;gBACjB,MAAM,EAAE;gBACR,aAAa;gBACb,WAAW;gBACX,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;YAEA,OAAQ;gBACN,KAAK;oBACH,YAAY,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,GAAG,UAAU;wBAAC,CAAC;oBAC/C;gBACF,KAAK;oBACH,eAAe,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,GAAG,UAAU;wBAAC,CAAC;oBAClD;gBACF,KAAK;oBACH,qBAAqB,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,GAAG,UAAU;wBAAC,CAAC;oBACxD;gBACF,KAAK;oBACH,aAAa,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,GAAG,UAAU;wBAAC,CAAC;oBAChD;YACJ;QACF;IACF,GAAG,EAAE;IAEL,uBAAuB;IACvB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,iBAAiB,OAAO,IAAI,CAAC,aAAa;gBAC1C,oBAAoB,OAAO,IAAI,CAAC,SAAS;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF,GAAG,EAAE;IAEL,gDAAgD;IAChD,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,QAAQ;gBAAa;YAC9C;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,QAAQ,GAAG,CAAC;gBACZ,iBAAiB;gBAEjB,oBAAoB;gBACpB,MAAM,gBAAgB,MAAM,MAAM,qBAAqB;oBACrD,QAAQ;oBACR,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,MAAM,KAAK,SAAS,CAAC;wBAAE,QAAQ;oBAAQ;gBACzC;gBAEA,MAAM,cAAc,MAAM,cAAc,IAAI;gBAC5C,IAAI,YAAY,OAAO,EAAE;oBACvB,oBAAoB;oBACpB,QAAQ,GAAG,CAAC;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD;IACF,GAAG,EAAE;IAEL,6BAA6B;IAC7B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrC,IAAI,UAAU;YACZ,MAAM,UAAU;QAClB,OAAO;YACL,mBAAmB;YACnB,MAAM,QAAQ,GAAG,CAAC;gBAChB,UAAU;gBACV,UAAU;gBACV,UAAU;gBACV,UAAU;aACX;QACH;IACF,GAAG;QAAC;KAAU;IAEd,uCAAuC;IACvC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,2BAA2B;QAC3B,iBAAiB,OAAO,CAAC,OAAO,CAAC,CAAA,WAAY,cAAc;QAC3D,iBAAiB,OAAO,CAAC,KAAK;QAE9B,uBAAuB;QACvB,MAAM,YAAY;YAChB,UAAU;YACV,aAAa;YACb,mBAAmB;YACnB,WAAW,MAAM,aAAa;QAChC;QAEA,OAAO,OAAO,CAAC,WAAW,OAAO,CAAC,CAAC,CAAC,UAAU,SAAS;YACrD,MAAM,aAAa,YAAY;gBAC7B,UAAU;YACZ,GAAG;YAEH,iBAAiB,OAAO,CAAC,GAAG,CAAC,UAAsB;QACrD;QAEA,QAAQ,GAAG,CAAC;IACd,GAAG;QAAC;KAAU;IAEd,sBAAsB;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa;YACjB,sCAAsC;YACtC,MAAM;YAEN,+BAA+B;YAC/B,IAAI,CAAC,eAAe;gBAClB,MAAM;YACR;YAEA,oBAAoB;YACpB,MAAM;YAEN,uCAAuC;YACvC;QACF;QAEA;QAEA,qBAAqB;QACrB,OAAO;YACL,iBAAiB,OAAO,CAAC,OAAO,CAAC,CAAA,WAAY,cAAc;YAC3D,iBAAiB,OAAO,CAAC,KAAK;QAChC;IACF,GAAG,EAAE;IAEL,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,kBAAkB;YACpB;QACF;IACF,GAAG;QAAC;QAAkB;KAAa;IAEnC,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAGO,SAAS;IACd,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG;IAElC,OAAO;QACL,QAAQ,SAAS,IAAI;QACrB,aAAa,SAAS,WAAW;QACjC,WAAW,SAAS,SAAS;QAC7B,OAAO,SAAS,KAAK;QACrB,SAAS,IAAM,YAAY;IAC7B;AACF;AAEO,SAAS;IACd,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG;IAErC,OAAO;QACL,QAAQ,YAAY,IAAI;QACxB,aAAa,YAAY,WAAW;QACpC,WAAW,YAAY,SAAS;QAChC,OAAO,YAAY,KAAK;QACxB,SAAS,IAAM,YAAY;IAC7B;AACF;AAEO,SAAS;IACd,MAAM,EAAE,iBAAiB,EAAE,WAAW,EAAE,GAAG;IAE3C,OAAO;QACL,SAAS,kBAAkB,IAAI;QAC/B,aAAa,kBAAkB,WAAW;QAC1C,WAAW,kBAAkB,SAAS;QACtC,OAAO,kBAAkB,KAAK;QAC9B,SAAS,IAAM,YAAY;IAC7B;AACF;AAEO,SAAS;IACd,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG;IAEnC,OAAO;QACL,QAAQ,UAAU,IAAI;QACtB,aAAa,UAAU,WAAW;QAClC,WAAW,UAAU,SAAS;QAC9B,OAAO,UAAU,KAAK;QACtB,SAAS,IAAM,YAAY;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 282, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/app/dashboard/gtt-orders/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport {\n  Clock,\n  TrendingUp,\n  TrendingDown,\n  CheckCircle,\n  XCircle,\n  AlertCircle,\n  Wifi,\n  WifiOff,\n  RefreshCw,\n  Calendar,\n  Target,\n  Activity,\n  Plus,\n  Settings,\n  Play,\n  Pause,\n  BellRing\n} from 'lucide-react';\nimport { formatCurrency, formatDateTime } from '@/lib/utils';\nimport { useGTTOrders } from '@/hooks/useCentralData';\n\ninterface GTTOrder {\n  id: string;\n  gttId?: string; // Angel One GTT ID\n  symbol: string;\n  name: string;\n  orderType: 'BUY' | 'SELL';\n  triggerPrice: number;\n  quantity: number;\n  status: 'PENDING' | 'TRIGGERED' | 'CANCELLED' | 'EXPIRED';\n  createdAt: Date;\n  source: 'SIGNAL' | 'HOLDING' | 'SALE'; // Which tab this order belongs to\n}\n\ntype TabType = 'SIGNAL' | 'HOLDING' | 'SALE';\n\nexport default function GTTOrdersPage() {\n  const [activeTab, setActiveTab] = useState<TabType>('SIGNAL');\n\n  // Central Data Manager for real-time GTT orders\n  const {\n    orders: centralGTTOrders,\n    lastUpdated: gttLastUpdated,\n    isLoading: gttIsLoading,\n    error: gttError,\n    refresh: refreshGTTOrders\n  } = useGTTOrders();\n\n  const [orders, setOrders] = useState<GTTOrder[]>([\n    // Sample GTT Buy on Signal orders\n    {\n      id: '1',\n      symbol: 'RELIANCE',\n      name: 'Reliance Industries Ltd',\n      orderType: 'BUY',\n      triggerPrice: 2400.05,\n      quantity: 8,\n      status: 'PENDING',\n      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),\n      source: 'SIGNAL'\n    },\n    {\n      id: '2',\n      symbol: 'TCS',\n      name: 'Tata Consultancy Services',\n      orderType: 'BUY',\n      triggerPrice: 3200.05,\n      quantity: 6,\n      status: 'TRIGGERED',\n      createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),\n      source: 'SIGNAL'\n    },\n    // Sample GTT Buy on Holding orders\n    {\n      id: '3',\n      symbol: 'INFY',\n      name: 'Infosys Limited',\n      orderType: 'BUY',\n      triggerPrice: 1350.00,\n      quantity: 14,\n      status: 'PENDING',\n      createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),\n      source: 'HOLDING'\n    },\n    // Sample GTT Sale orders\n    {\n      id: '4',\n      symbol: 'WIPRO',\n      name: 'Wipro Limited',\n      orderType: 'SELL',\n      triggerPrice: 450.00,\n      quantity: 44,\n      status: 'PENDING',\n      createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),\n      source: 'SALE'\n    }\n  ]);\n\n  const [isAngelOneConnected, setIsAngelOneConnected] = useState(false);\n  const [lastSync, setLastSync] = useState<Date | null>(null);\n  const [isCreatingOrders, setIsCreatingOrders] = useState(false);\n  const [createOrdersError, setCreateOrdersError] = useState<string | null>(null);\n\n  // Automatic GTT Service state\n  const [autoGTTEnabled, setAutoGTTEnabled] = useState(false);\n  const [autoGTTStatus, setAutoGTTStatus] = useState<any>(null);\n  const [showAutoGTTSettings, setShowAutoGTTSettings] = useState(false);\n  const [autoGTTNotifications, setAutoGTTNotifications] = useState<string[]>([]);\n\n  // Filter orders by active tab\n  const filteredOrders = orders.filter(order => order.source === activeTab);\n\n  // Interface for Weekly High Signal data\n  interface WeeklyHighStock {\n    symbol: string;\n    name: string;\n    currentPrice: number;\n    lastWeekHighest: number;\n    suggestedBuyPrice: number;\n    percentDifference: number;\n    suggestedGTTQuantity: number;\n    isBOHEligible: boolean;\n    inHoldings: boolean;\n  }\n\n  // Function to fetch BOH eligible stocks and create GTT orders using the API\n  const fetchAndCreateGTTOrders = async () => {\n    try {\n      console.log('🔍 Fetching BOH eligible stocks and creating GTT orders via API...');\n\n      // Use the dedicated API endpoint for creating signal orders\n      const response = await fetch('/api/gtt/create-signal-orders', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      console.log(`📡 API Response status: ${response.status}`);\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error(`❌ API request failed: ${response.status} - ${errorText}`);\n        throw new Error(`API request failed: ${response.status}`);\n      }\n\n      const data = await response.json();\n      console.log('📊 API Response data:', data);\n\n      if (!data.success) {\n        console.error('❌ API returned error:', data.error);\n        throw new Error(data.error || 'Failed to fetch GTT order data');\n      }\n\n      console.log(`✅ API returned ${data.data.orders.length} GTT orders to create`);\n      console.log(`📊 BOH Stats: Total BOH stocks: ${data.data.totalBOHStocks}, Valid for GTT: ${data.data.validForGTT}`);\n\n      if (data.data.stats.avgTriggerPrice > 0) {\n        console.log(`💰 Price Stats: Avg Trigger: ₹${data.data.stats.avgTriggerPrice.toFixed(2)}, Total Value: ₹${data.data.stats.totalValue.toFixed(2)}`);\n      }\n\n      return data.data.orders;\n    } catch (error) {\n      console.error('❌ Error fetching GTT orders from API:', error);\n      throw error;\n    }\n  };\n\n  // Function to create GTT orders for all BOH eligible stocks\n  const createAllSignalOrders = async () => {\n    setIsCreatingOrders(true);\n    setCreateOrdersError(null);\n\n    try {\n      console.log('🚀 Starting to create GTT orders for all BOH eligible stocks...');\n\n      // Fetch GTT order data from API\n      const gttOrderRequests = await fetchAndCreateGTTOrders();\n\n      console.log(`🔍 Received ${gttOrderRequests.length} GTT order requests from API`);\n\n      if (gttOrderRequests.length === 0) {\n        const errorMsg = 'No BOH eligible stocks found for GTT orders. This could be due to:\\n' +\n                        '• No stocks marked as BOH eligible\\n' +\n                        '• All BOH stocks filtered out due to price/quantity constraints\\n' +\n                        '• API data fetching issues';\n        console.warn('⚠️ ' + errorMsg);\n        setCreateOrdersError(errorMsg);\n        return;\n      }\n\n      // Filter out stocks that already have pending signal orders\n      const existingSignalSymbols = orders\n        .filter(order => order.source === 'SIGNAL' && order.status === 'PENDING')\n        .map(order => order.symbol);\n\n      console.log(`🔍 Existing signal orders: ${existingSignalSymbols.length} symbols:`, existingSignalSymbols);\n\n      const newOrderRequests = gttOrderRequests.filter((orderReq: any) =>\n        !existingSignalSymbols.includes(orderReq.symbol)\n      );\n\n      console.log(`📊 Creating orders for ${newOrderRequests.length} new stocks (${existingSignalSymbols.length} already have orders)`);\n\n      if (newOrderRequests.length === 0) {\n        const errorMsg = `All ${gttOrderRequests.length} BOH eligible stocks already have pending GTT orders`;\n        console.warn('⚠️ ' + errorMsg);\n        setCreateOrdersError(errorMsg);\n        return;\n      }\n\n      // Create GTT orders for each stock\n      const newOrders: GTTOrder[] = [];\n      let successCount = 0;\n      let errorCount = 0;\n\n      for (const orderReq of newOrderRequests) {\n        try {\n          const newOrder: GTTOrder = {\n            id: `signal_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,\n            symbol: orderReq.symbol,\n            name: orderReq.name,\n            orderType: orderReq.orderType,\n            triggerPrice: orderReq.triggerPrice,\n            quantity: orderReq.quantity,\n            status: 'PENDING',\n            createdAt: new Date(),\n            source: orderReq.source\n          };\n\n          newOrders.push(newOrder);\n          successCount++;\n\n          console.log(`✅ Created GTT order for ${orderReq.symbol}: Trigger=₹${orderReq.triggerPrice.toFixed(2)}, Qty=${orderReq.quantity}`);\n        } catch (error) {\n          console.error(`❌ Failed to create order for ${orderReq.symbol}:`, error);\n          errorCount++;\n        }\n      }\n\n      // Add new orders to the existing orders\n      setOrders(prevOrders => [...prevOrders, ...newOrders]);\n      setLastSync(new Date());\n\n      console.log(`🎉 Successfully created ${successCount} GTT orders (${errorCount} errors)`);\n\n      // Show success message\n      if (successCount > 0) {\n        const totalValue = newOrders.reduce((sum, order) => sum + (order.triggerPrice * order.quantity), 0);\n        const message = `🎉 Successfully created ${successCount} GTT orders for BOH eligible stocks!\\n\\n` +\n                       `📊 Summary:\\n` +\n                       `• Total Orders: ${successCount}\\n` +\n                       `• Total Investment: ₹${totalValue.toLocaleString()}\\n` +\n                       `• Average Trigger Price: ₹${(totalValue / newOrders.reduce((sum, order) => sum + order.quantity, 0)).toFixed(2)}\\n\\n` +\n                       `✅ All orders are now visible in the GTT Buy on Signal tab.`;\n\n        alert(message);\n        console.log('🎉 GTT Orders Created Successfully:', {\n          successCount,\n          totalValue,\n          orders: newOrders.map(o => ({ symbol: o.symbol, trigger: o.triggerPrice, qty: o.quantity }))\n        });\n      }\n\n      if (errorCount > 0) {\n        const errorMsg = `Created ${successCount} orders successfully, but ${errorCount} failed`;\n        console.warn('⚠️ Some GTT orders failed:', errorMsg);\n        setCreateOrdersError(errorMsg);\n      }\n\n    } catch (error) {\n      console.error('❌ Error creating signal orders:', error);\n      setCreateOrdersError(error instanceof Error ? error.message : 'Failed to create orders');\n    } finally {\n      setIsCreatingOrders(false);\n    }\n  };\n\n  // Function to clear all pending signal orders (for weekly cleanup)\n  const clearPendingSignalOrders = () => {\n    setOrders(prevOrders =>\n      prevOrders.filter(order =>\n        !(order.source === 'SIGNAL' && order.status === 'PENDING')\n      )\n    );\n    console.log('🧹 Cleared all pending signal orders');\n  };\n\n  // Function to get signal orders statistics\n  const getSignalOrdersStats = () => {\n    const signalOrders = orders.filter(order => order.source === 'SIGNAL');\n    return {\n      total: signalOrders.length,\n      pending: signalOrders.filter(order => order.status === 'PENDING').length,\n      triggered: signalOrders.filter(order => order.status === 'TRIGGERED').length,\n      cancelled: signalOrders.filter(order => order.status === 'CANCELLED').length,\n      expired: signalOrders.filter(order => order.status === 'EXPIRED').length\n    };\n  };\n\n  // Automatic GTT Service functions\n  const initializeAutoGTTService = async () => {\n    try {\n      const response = await fetch('/api/auto-gtt/initialize', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' }\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        console.log('✅ Auto GTT Service initialized');\n        setAutoGTTEnabled(data.data.service.isEnabled);\n        addAutoGTTNotification('🚀 Automatic GTT order detection started');\n      } else {\n        console.error('❌ Failed to initialize Auto GTT Service:', data.error);\n      }\n    } catch (error) {\n      console.error('❌ Error initializing auto GTT service:', error);\n    }\n  };\n\n  const fetchAutoGTTStatus = async () => {\n    try {\n      const response = await fetch('/api/auto-gtt?action=status');\n      const data = await response.json();\n\n      if (data.success) {\n        setAutoGTTStatus(data.data);\n        setAutoGTTEnabled(data.data.service.isEnabled);\n      }\n    } catch (error) {\n      console.error('❌ Error fetching auto GTT status:', error);\n    }\n  };\n\n  const toggleAutoGTTService = async () => {\n    try {\n      const action = autoGTTEnabled ? 'stop' : 'start';\n      const response = await fetch('/api/auto-gtt', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ action })\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        setAutoGTTEnabled(!autoGTTEnabled);\n        await fetchAutoGTTStatus();\n\n        const message = autoGTTEnabled ?\n          '⏹️ Automatic GTT order creation stopped' :\n          '🚀 Automatic GTT order creation started';\n\n        addAutoGTTNotification(message);\n      }\n    } catch (error) {\n      console.error('❌ Error toggling auto GTT service:', error);\n    }\n  };\n\n  const addAutoGTTNotification = (message: string) => {\n    setAutoGTTNotifications(prev => [message, ...prev.slice(0, 4)]); // Keep last 5 notifications\n\n    // Auto-remove notification after 5 seconds\n    setTimeout(() => {\n      setAutoGTTNotifications(prev => prev.filter(n => n !== message));\n    }, 5000);\n  };\n\n  // Use Central Data Manager for real-time GTT orders\n  useEffect(() => {\n    if (centralGTTOrders.length > 0) {\n      // Convert central GTT orders to display format\n      const displayOrders: GTTOrder[] = centralGTTOrders.map((centralOrder: any) => ({\n        id: centralOrder.id,\n        symbol: centralOrder.symbol,\n        name: centralOrder.name,\n        orderType: centralOrder.orderType,\n        triggerPrice: centralOrder.triggerPrice,\n        quantity: centralOrder.quantity,\n        status: centralOrder.status,\n        createdAt: new Date(centralOrder.createdAt),\n        source: centralOrder.source,\n        autoCreated: centralOrder.autoCreated,\n        signalStrength: centralOrder.signalStrength\n      }));\n\n      setOrders(displayOrders);\n      console.log(`📊 Updated orders from Central Data Manager: ${displayOrders.length} orders`);\n    }\n  }, [centralGTTOrders]);\n\n  // Legacy load auto GTT orders function (now uses Central Data Manager)\n  const loadAutoGTTOrders = async () => {\n    console.log('🔄 Refreshing GTT orders via Central Data Manager...');\n    await refreshGTTOrders();\n  };\n\n  // Initialize data on component mount\n  useEffect(() => {\n    const initializeData = async () => {\n      console.log('🚀 Initializing GTT Orders page...');\n\n      // Initialize with empty orders\n      setOrders([]);\n      setLastSync(new Date());\n\n      // Initialize automatic GTT service first\n      await initializeAutoGTTService();\n\n      // Then load orders and status\n      await Promise.all([\n        loadAutoGTTOrders(),\n        fetchAutoGTTStatus()\n      ]);\n\n      console.log('✅ GTT Orders page initialization complete');\n    };\n\n    initializeData();\n\n    // Set up periodic status updates\n    const statusInterval = setInterval(fetchAutoGTTStatus, 30000); // Every 30 seconds\n\n    // Set up periodic order refresh\n    const orderInterval = setInterval(loadAutoGTTOrders, 60000); // Every 60 seconds\n\n    return () => {\n      clearInterval(statusInterval);\n      clearInterval(orderInterval);\n    };\n  }, []);\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'PENDING': return 'text-yellow-600 bg-yellow-100';\n      case 'TRIGGERED': return 'text-green-600 bg-green-100';\n      case 'CANCELLED': return 'text-red-600 bg-red-100';\n      case 'EXPIRED': return 'text-gray-600 bg-gray-100';\n      default: return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'PENDING': return <Clock className=\"h-4 w-4\" />;\n      case 'TRIGGERED': return <CheckCircle className=\"h-4 w-4\" />;\n      case 'CANCELLED': return <XCircle className=\"h-4 w-4\" />;\n      case 'EXPIRED': return <AlertCircle className=\"h-4 w-4\" />;\n      default: return <Clock className=\"h-4 w-4\" />;\n    }\n  };\n\n  const getTabInfo = (tab: TabType) => {\n    switch (tab) {\n      case 'SIGNAL':\n        return {\n          title: 'GTT Buy on Signal',\n          description: 'Automated buy orders based on Weekly High Signal data',\n          icon: <Activity className=\"h-5 w-5\" />,\n          automation: 'Auto-created every Friday 8:00 PM'\n        };\n      case 'HOLDING':\n        return {\n          title: 'GTT Buy on Holding',\n          description: 'Additional buy orders at lower support levels for existing holdings',\n          icon: <TrendingDown className=\"h-5 w-5\" />,\n          automation: 'Auto-created when Ignorable Lower Price is calculated'\n        };\n      case 'SALE':\n        return {\n          title: 'GTT Sale',\n          description: 'Sell orders triggered when target prices are reached',\n          icon: <Target className=\"h-5 w-5\" />,\n          automation: 'Auto-created when Target Price is set for holdings'\n        };\n    }\n  };\n\n  const OrdersTable = () => (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n      <div className=\"overflow-x-auto\">\n        <table className=\"min-w-full divide-y divide-gray-200\">\n          <thead className=\"bg-gray-50\">\n            <tr>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Stock\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Order Type\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Trigger Price\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Quantity\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Status\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Created Date\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Actions\n              </th>\n            </tr>\n          </thead>\n          <tbody className=\"bg-white divide-y divide-gray-200\">\n            {filteredOrders.length > 0 ? (\n              filteredOrders.map((order) => (\n                <tr key={order.id} className=\"hover:bg-gray-50\">\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div>\n                      <div className=\"text-sm font-medium text-gray-900\">{order.symbol}</div>\n                      <div className=\"text-sm text-gray-500 truncate max-w-xs\">{order.name}</div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      <div className={`p-1 rounded-full mr-2 ${order.orderType === 'BUY' ? 'bg-green-100' : 'bg-red-100'}`}>\n                        {order.orderType === 'BUY' ? (\n                          <TrendingUp className=\"h-3 w-3 text-green-600\" />\n                        ) : (\n                          <TrendingDown className=\"h-3 w-3 text-red-600\" />\n                        )}\n                      </div>\n                      <span className={`text-sm font-medium ${order.orderType === 'BUY' ? 'text-green-600' : 'text-red-600'}`}>\n                        {order.orderType}\n                      </span>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {formatCurrency(order.triggerPrice)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {order.quantity}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>\n                      {getStatusIcon(order.status)}\n                      <span className=\"ml-1\">{order.status}</span>\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    {formatDateTime(order.createdAt)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <button className=\"text-red-600 hover:text-red-900 mr-3\">\n                      Cancel\n                    </button>\n                    <button className=\"text-blue-600 hover:text-blue-900\">\n                      View\n                    </button>\n                  </td>\n                </tr>\n              ))\n            ) : (\n              <tr>\n                <td colSpan={7} className=\"px-6 py-12 text-center\">\n                  <div className=\"text-gray-500\">\n                    <Clock className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n                    <p>No GTT orders found for {getTabInfo(activeTab).title}.</p>\n                    <p className=\"text-sm mt-1\">{getTabInfo(activeTab).automation}</p>\n                  </div>\n                </td>\n              </tr>\n            )}\n          </tbody>\n        </table>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Page Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">GTT Orders</h1>\n          <p className=\"text-gray-600 mt-1\">Automated Good Till Triggered order management</p>\n        </div>\n        <div className=\"flex items-center space-x-4\">\n          {/* Angel One Connection Status */}\n          <div className=\"flex items-center space-x-2\">\n            {isAngelOneConnected ? (\n              <div className=\"flex items-center space-x-2 text-green-600\">\n                <Wifi className=\"h-4 w-4\" />\n                <span className=\"text-sm font-medium\">Angel One Connected</span>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-2 text-red-600\">\n                <WifiOff className=\"h-4 w-4\" />\n                <span className=\"text-sm font-medium\">Angel One Disconnected</span>\n              </div>\n            )}\n          </div>\n          <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2\">\n            <RefreshCw className=\"h-4 w-4\" />\n            <span>Sync Orders</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Automation Status */}\n      <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-2\">\n            <Calendar className=\"h-5 w-5 text-blue-600\" />\n            <div>\n              <p className=\"text-sm font-medium text-blue-900\">Next Automation: Friday 8:00 PM</p>\n              <p className=\"text-xs text-blue-700\">\n                Weekly High Signal orders will be automatically created/updated\n                {lastSync && ` • Last sync: ${formatDateTime(lastSync)}`}\n              </p>\n            </div>\n          </div>\n\n          {/* Real-time Auto GTT Controls */}\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"flex items-center space-x-2\">\n              <div className={`w-2 h-2 rounded-full ${autoGTTEnabled ? 'bg-green-500' : 'bg-gray-400'}`}></div>\n              <span className=\"text-xs text-gray-600\">\n                Real-time Auto GTT {autoGTTEnabled ? 'ON' : 'OFF'}\n              </span>\n            </div>\n\n            <button\n              onClick={toggleAutoGTTService}\n              className={`px-3 py-1 rounded-md text-xs font-medium flex items-center space-x-1 transition-colors ${\n                autoGTTEnabled\n                  ? 'bg-red-100 text-red-700 hover:bg-red-200'\n                  : 'bg-green-100 text-green-700 hover:bg-green-200'\n              }`}\n            >\n              {autoGTTEnabled ? <Pause className=\"h-3 w-3\" /> : <Play className=\"h-3 w-3\" />}\n              <span>{autoGTTEnabled ? 'Stop' : 'Start'}</span>\n            </button>\n\n            <button\n              onClick={() => setShowAutoGTTSettings(!showAutoGTTSettings)}\n              className=\"px-3 py-1 bg-gray-100 text-gray-700 rounded-md text-xs font-medium hover:bg-gray-200 transition-colors flex items-center space-x-1\"\n            >\n              <Settings className=\"h-3 w-3\" />\n              <span>Settings</span>\n            </button>\n          </div>\n        </div>\n\n        {/* Auto GTT Status Details */}\n        {autoGTTStatus && (\n          <div className=\"mt-3 pt-3 border-t border-blue-200\">\n            <div className=\"grid grid-cols-4 gap-4 text-xs\">\n              <div>\n                <span className=\"text-blue-600 font-medium\">Market Status:</span>\n                <span className={`ml-1 ${autoGTTStatus.detector.isMarketOpen ? 'text-green-600' : 'text-gray-600'}`}>\n                  {autoGTTStatus.detector.isMarketOpen ? 'Open' : 'Closed'}\n                </span>\n              </div>\n              <div>\n                <span className=\"text-blue-600 font-medium\">Signals:</span>\n                <span className=\"ml-1 text-gray-700\">{autoGTTStatus.detector.lastSignalCount || 0}</span>\n              </div>\n              <div>\n                <span className=\"text-blue-600 font-medium\">Auto Orders:</span>\n                <span className=\"ml-1 text-gray-700\">{autoGTTStatus.service.autoCreatedOrders || 0}</span>\n              </div>\n              <div>\n                <span className=\"text-blue-600 font-medium\">Today:</span>\n                <span className=\"ml-1 text-gray-700\">\n                  {autoGTTStatus.service.todayOrders || 0}/{autoGTTStatus.service.dailyLimit || 20}\n                </span>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Auto GTT Notifications */}\n      {autoGTTNotifications.length > 0 && (\n        <div className=\"space-y-2\">\n          {autoGTTNotifications.map((notification, index) => (\n            <div key={index} className=\"bg-green-50 border border-green-200 rounded-lg p-3 flex items-center space-x-2\">\n              <BellRing className=\"h-4 w-4 text-green-600\" />\n              <span className=\"text-green-800 text-sm\">{notification}</span>\n            </div>\n          ))}\n        </div>\n      )}\n\n      {/* Auto GTT Settings Modal */}\n      {showAutoGTTSettings && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-lg p-6 w-full max-w-md\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h3 className=\"text-lg font-semibold text-gray-900\">Auto GTT Settings</h3>\n              <button\n                onClick={() => setShowAutoGTTSettings(false)}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <XCircle className=\"h-5 w-5\" />\n              </button>\n            </div>\n\n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Polling Interval (minutes)\n                </label>\n                <input\n                  type=\"number\"\n                  min=\"1\"\n                  max=\"60\"\n                  defaultValue=\"5\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Minimum Signal Strength\n                </label>\n                <select className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                  <option value=\"MODERATE\">Moderate</option>\n                  <option value=\"STRONG\">Strong Only</option>\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Max Orders Per Day\n                </label>\n                <input\n                  type=\"number\"\n                  min=\"1\"\n                  max=\"50\"\n                  defaultValue=\"20\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\"\n                />\n              </div>\n\n              <div className=\"flex items-center space-x-2\">\n                <input\n                  type=\"checkbox\"\n                  id=\"marketHoursOnly\"\n                  defaultChecked\n                  className=\"rounded border-gray-300\"\n                />\n                <label htmlFor=\"marketHoursOnly\" className=\"text-sm text-gray-700\">\n                  Only during market hours (9:15 AM - 3:30 PM)\n                </label>\n              </div>\n\n              <div className=\"flex items-center space-x-2\">\n                <input\n                  type=\"checkbox\"\n                  id=\"volumeConfirmation\"\n                  defaultChecked\n                  className=\"rounded border-gray-300\"\n                />\n                <label htmlFor=\"volumeConfirmation\" className=\"text-sm text-gray-700\">\n                  Require volume confirmation (20% above average)\n                </label>\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3 mt-6\">\n              <button\n                onClick={() => setShowAutoGTTSettings(false)}\n                className=\"px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors\"\n              >\n                Cancel\n              </button>\n              <button\n                onClick={() => {\n                  // TODO: Save settings\n                  setShowAutoGTTSettings(false);\n                  addAutoGTTNotification('⚙️ Auto GTT settings updated');\n                }}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\"\n              >\n                Save Settings\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Tab Navigation */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n        <div className=\"border-b border-gray-200\">\n          <nav className=\"flex space-x-8 px-6\">\n            {(['SIGNAL', 'HOLDING', 'SALE'] as const).map((tab) => {\n              const tabInfo = getTabInfo(tab);\n              const tabOrders = orders.filter(o => o.source === tab);\n              return (\n                <button\n                  key={tab}\n                  onClick={() => setActiveTab(tab)}\n                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors flex items-center space-x-2 ${\n                    activeTab === tab\n                      ? 'border-blue-500 text-blue-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  {tabInfo.icon}\n                  <span>{tabInfo.title}</span>\n                  <span className=\"bg-gray-100 text-gray-600 px-2 py-0.5 rounded-full text-xs\">\n                    {tabOrders.length}\n                  </span>\n                </button>\n              );\n            })}\n          </nav>\n        </div>\n\n        {/* Tab Content */}\n        <div className=\"p-6\">\n          <div className=\"mb-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                  {getTabInfo(activeTab).title}\n                </h3>\n                <p className=\"text-gray-600 text-sm mb-1\">\n                  {getTabInfo(activeTab).description}\n                </p>\n                <p className=\"text-blue-600 text-xs font-medium\">\n                  {getTabInfo(activeTab).automation}\n                </p>\n              </div>\n\n              {/* Create All Signal Orders Button - Only show for SIGNAL tab */}\n              {activeTab === 'SIGNAL' && (\n                <div className=\"flex flex-col items-end space-y-2\">\n                  <button\n                    onClick={createAllSignalOrders}\n                    disabled={isCreatingOrders}\n                    className={`px-4 py-2 rounded-lg font-medium text-sm transition-colors flex items-center space-x-2 ${\n                      isCreatingOrders\n                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                        : 'bg-green-600 text-white hover:bg-green-700'\n                    }`}\n                  >\n                    {isCreatingOrders ? (\n                      <>\n                        <RefreshCw className=\"h-4 w-4 animate-spin\" />\n                        <span>Creating Orders...</span>\n                      </>\n                    ) : (\n                      <>\n                        <Plus className=\"h-4 w-4\" />\n                        <span>Create All Signal Orders</span>\n                      </>\n                    )}\n                  </button>\n\n                  {createOrdersError && (\n                    <p className=\"text-red-600 text-xs max-w-xs text-right\">\n                      {createOrdersError}\n                    </p>\n                  )}\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Signal Orders Info - Only show for SIGNAL tab */}\n          {activeTab === 'SIGNAL' && (\n            <div className=\"mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h4 className=\"text-sm font-medium text-blue-900 mb-1\">\n                    Weekly High Signal Orders\n                  </h4>\n                  <p className=\"text-xs text-blue-700\">\n                    Orders are automatically created for BOH eligible stocks from the Weekly High Signal page.\n                    Trigger Price = Last Week's High + ₹0.05, Quantity = ₹2,000 ÷ Trigger Price\n                  </p>\n                </div>\n                <div className=\"text-right\">\n                  <div className=\"text-sm font-medium text-blue-900\">\n                    {getSignalOrdersStats().pending} Pending\n                  </div>\n                  <div className=\"text-xs text-blue-700\">\n                    {getSignalOrdersStats().triggered} Triggered\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Orders Table */}\n          <OrdersTable />\n        </div>\n      </div>\n\n      {/* Summary Statistics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Total Orders</p>\n              <p className=\"text-2xl font-bold text-gray-900 mt-1\">{orders.length}</p>\n            </div>\n            <Activity className=\"h-8 w-8 text-gray-600\" />\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Pending Orders</p>\n              <p className=\"text-2xl font-bold text-yellow-600 mt-1\">\n                {orders.filter(o => o.status === 'PENDING').length}\n              </p>\n            </div>\n            <Clock className=\"h-8 w-8 text-yellow-600\" />\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Triggered Today</p>\n              <p className=\"text-2xl font-bold text-green-600 mt-1\">\n                {orders.filter(o => o.status === 'TRIGGERED').length}\n              </p>\n            </div>\n            <CheckCircle className=\"h-8 w-8 text-green-600\" />\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Success Rate</p>\n              <p className=\"text-2xl font-bold text-gray-900 mt-1\">\n                {orders.length > 0 ? Math.round((orders.filter(o => o.status === 'TRIGGERED').length / orders.length) * 100) : 0}%\n              </p>\n            </div>\n            <TrendingUp className=\"h-8 w-8 text-gray-600\" />\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmBA;AACA;AAvBA;;;;;;AAwCe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAEpD,gDAAgD;IAChD,MAAM,EACJ,QAAQ,gBAAgB,EACxB,aAAa,cAAc,EAC3B,WAAW,YAAY,EACvB,OAAO,QAAQ,EACf,SAAS,gBAAgB,EAC1B,GAAG,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD;IAEf,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;QAC/C,kCAAkC;QAClC;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,WAAW;YACX,cAAc;YACd,UAAU;YACV,QAAQ;YACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;YACpD,QAAQ;QACV;QACA;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,WAAW;YACX,cAAc;YACd,UAAU;YACV,QAAQ;YACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;YACpD,QAAQ;QACV;QACA,mCAAmC;QACnC;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,WAAW;YACX,cAAc;YACd,UAAU;YACV,QAAQ;YACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;YACpD,QAAQ;QACV;QACA,yBAAyB;QACzB;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,WAAW;YACX,cAAc;YACd,UAAU;YACV,QAAQ;YACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;YACpD,QAAQ;QACV;KACD;IAED,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1E,8BAA8B;IAC9B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACxD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE7E,8BAA8B;IAC9B,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;IAe/D,4EAA4E;IAC5E,MAAM,0BAA0B;QAC9B,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,4DAA4D;YAC5D,MAAM,WAAW,MAAM,MAAM,iCAAiC;gBAC5D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,SAAS,MAAM,EAAE;YAExD,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,WAAW;gBACvE,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,yBAAyB;YAErC,IAAI,CAAC,KAAK,OAAO,EAAE;gBACjB,QAAQ,KAAK,CAAC,yBAAyB,KAAK,KAAK;gBACjD,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,qBAAqB,CAAC;YAC5E,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,KAAK,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,KAAK,IAAI,CAAC,WAAW,EAAE;YAElH,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,GAAG;gBACvC,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,gBAAgB,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI;YACnJ;YAEA,OAAO,KAAK,IAAI,CAAC,MAAM;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM;QACR;IACF;IAEA,4DAA4D;IAC5D,MAAM,wBAAwB;QAC5B,oBAAoB;QACpB,qBAAqB;QAErB,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,gCAAgC;YAChC,MAAM,mBAAmB,MAAM;YAE/B,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,iBAAiB,MAAM,CAAC,4BAA4B,CAAC;YAEhF,IAAI,iBAAiB,MAAM,KAAK,GAAG;gBACjC,MAAM,WAAW,yEACD,yCACA,sEACA;gBAChB,QAAQ,IAAI,CAAC,QAAQ;gBACrB,qBAAqB;gBACrB;YACF;YAEA,4DAA4D;YAC5D,MAAM,wBAAwB,OAC3B,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK,YAAY,MAAM,MAAM,KAAK,WAC9D,GAAG,CAAC,CAAA,QAAS,MAAM,MAAM;YAE5B,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,sBAAsB,MAAM,CAAC,SAAS,CAAC,EAAE;YAEnF,MAAM,mBAAmB,iBAAiB,MAAM,CAAC,CAAC,WAChD,CAAC,sBAAsB,QAAQ,CAAC,SAAS,MAAM;YAGjD,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,iBAAiB,MAAM,CAAC,aAAa,EAAE,sBAAsB,MAAM,CAAC,qBAAqB,CAAC;YAEhI,IAAI,iBAAiB,MAAM,KAAK,GAAG;gBACjC,MAAM,WAAW,CAAC,IAAI,EAAE,iBAAiB,MAAM,CAAC,oDAAoD,CAAC;gBACrG,QAAQ,IAAI,CAAC,QAAQ;gBACrB,qBAAqB;gBACrB;YACF;YAEA,mCAAmC;YACnC,MAAM,YAAwB,EAAE;YAChC,IAAI,eAAe;YACnB,IAAI,aAAa;YAEjB,KAAK,MAAM,YAAY,iBAAkB;gBACvC,IAAI;oBACF,MAAM,WAAqB;wBACzB,IAAI,CAAC,OAAO,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK;wBACzE,QAAQ,SAAS,MAAM;wBACvB,MAAM,SAAS,IAAI;wBACnB,WAAW,SAAS,SAAS;wBAC7B,cAAc,SAAS,YAAY;wBACnC,UAAU,SAAS,QAAQ;wBAC3B,QAAQ;wBACR,WAAW,IAAI;wBACf,QAAQ,SAAS,MAAM;oBACzB;oBAEA,UAAU,IAAI,CAAC;oBACf;oBAEA,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,SAAS,YAAY,CAAC,OAAO,CAAC,GAAG,MAAM,EAAE,SAAS,QAAQ,EAAE;gBAClI,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,SAAS,MAAM,CAAC,CAAC,CAAC,EAAE;oBAClE;gBACF;YACF;YAEA,wCAAwC;YACxC,UAAU,CAAA,aAAc;uBAAI;uBAAe;iBAAU;YACrD,YAAY,IAAI;YAEhB,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,aAAa,aAAa,EAAE,WAAW,QAAQ,CAAC;YAEvF,uBAAuB;YACvB,IAAI,eAAe,GAAG;gBACpB,MAAM,aAAa,UAAU,MAAM,CAAC,CAAC,KAAK,QAAU,MAAO,MAAM,YAAY,GAAG,MAAM,QAAQ,EAAG;gBACjG,MAAM,UAAU,CAAC,wBAAwB,EAAE,aAAa,wCAAwC,CAAC,GAClF,CAAC,aAAa,CAAC,GACf,CAAC,gBAAgB,EAAE,aAAa,EAAE,CAAC,GACnC,CAAC,qBAAqB,EAAE,WAAW,cAAc,GAAG,EAAE,CAAC,GACvD,CAAC,0BAA0B,EAAE,CAAC,aAAa,UAAU,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,QAAQ,EAAE,EAAE,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC,GACtH,CAAC,0DAA0D,CAAC;gBAE3E,MAAM;gBACN,QAAQ,GAAG,CAAC,uCAAuC;oBACjD;oBACA;oBACA,QAAQ,UAAU,GAAG,CAAC,CAAA,IAAK,CAAC;4BAAE,QAAQ,EAAE,MAAM;4BAAE,SAAS,EAAE,YAAY;4BAAE,KAAK,EAAE,QAAQ;wBAAC,CAAC;gBAC5F;YACF;YAEA,IAAI,aAAa,GAAG;gBAClB,MAAM,WAAW,CAAC,QAAQ,EAAE,aAAa,0BAA0B,EAAE,WAAW,OAAO,CAAC;gBACxF,QAAQ,IAAI,CAAC,8BAA8B;gBAC3C,qBAAqB;YACvB;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,qBAAqB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAChE,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,mEAAmE;IACnE,MAAM,2BAA2B;QAC/B,UAAU,CAAA,aACR,WAAW,MAAM,CAAC,CAAA,QAChB,CAAC,CAAC,MAAM,MAAM,KAAK,YAAY,MAAM,MAAM,KAAK,SAAS;QAG7D,QAAQ,GAAG,CAAC;IACd;IAEA,2CAA2C;IAC3C,MAAM,uBAAuB;QAC3B,MAAM,eAAe,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;QAC7D,OAAO;YACL,OAAO,aAAa,MAAM;YAC1B,SAAS,aAAa,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK,WAAW,MAAM;YACxE,WAAW,aAAa,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK,aAAa,MAAM;YAC5E,WAAW,aAAa,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK,aAAa,MAAM;YAC5E,SAAS,aAAa,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK,WAAW,MAAM;QAC1E;IACF;IAEA,kCAAkC;IAClC,MAAM,2BAA2B;QAC/B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,4BAA4B;gBACvD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,QAAQ,GAAG,CAAC;gBACZ,kBAAkB,KAAK,IAAI,CAAC,OAAO,CAAC,SAAS;gBAC7C,uBAAuB;YACzB,OAAO;gBACL,QAAQ,KAAK,CAAC,4CAA4C,KAAK,KAAK;YACtE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;QAC1D;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,iBAAiB,KAAK,IAAI;gBAC1B,kBAAkB,KAAK,IAAI,CAAC,OAAO,CAAC,SAAS;YAC/C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,SAAS,iBAAiB,SAAS;YACzC,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAO;YAChC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,kBAAkB,CAAC;gBACnB,MAAM;gBAEN,MAAM,UAAU,iBACd,4CACA;gBAEF,uBAAuB;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;QACtD;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,wBAAwB,CAAA,OAAQ;gBAAC;mBAAY,KAAK,KAAK,CAAC,GAAG;aAAG,GAAG,4BAA4B;QAE7F,2CAA2C;QAC3C,WAAW;YACT,wBAAwB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,MAAM;QACzD,GAAG;IACL;IAEA,oDAAoD;IACpD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB,MAAM,GAAG,GAAG;YAC/B,+CAA+C;YAC/C,MAAM,gBAA4B,iBAAiB,GAAG,CAAC,CAAC,eAAsB,CAAC;oBAC7E,IAAI,aAAa,EAAE;oBACnB,QAAQ,aAAa,MAAM;oBAC3B,MAAM,aAAa,IAAI;oBACvB,WAAW,aAAa,SAAS;oBACjC,cAAc,aAAa,YAAY;oBACvC,UAAU,aAAa,QAAQ;oBAC/B,QAAQ,aAAa,MAAM;oBAC3B,WAAW,IAAI,KAAK,aAAa,SAAS;oBAC1C,QAAQ,aAAa,MAAM;oBAC3B,aAAa,aAAa,WAAW;oBACrC,gBAAgB,aAAa,cAAc;gBAC7C,CAAC;YAED,UAAU;YACV,QAAQ,GAAG,CAAC,CAAC,6CAA6C,EAAE,cAAc,MAAM,CAAC,OAAO,CAAC;QAC3F;IACF,GAAG;QAAC;KAAiB;IAErB,uEAAuE;IACvE,MAAM,oBAAoB;QACxB,QAAQ,GAAG,CAAC;QACZ,MAAM;IACR;IAEA,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB,QAAQ,GAAG,CAAC;YAEZ,+BAA+B;YAC/B,UAAU,EAAE;YACZ,YAAY,IAAI;YAEhB,yCAAyC;YACzC,MAAM;YAEN,8BAA8B;YAC9B,MAAM,QAAQ,GAAG,CAAC;gBAChB;gBACA;aACD;YAED,QAAQ,GAAG,CAAC;QACd;QAEA;QAEA,iCAAiC;QACjC,MAAM,iBAAiB,YAAY,oBAAoB,QAAQ,mBAAmB;QAElF,gCAAgC;QAChC,MAAM,gBAAgB,YAAY,mBAAmB,QAAQ,mBAAmB;QAEhF,OAAO;YACL,cAAc;YACd,cAAc;QAChB;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAW,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YACxC,KAAK;gBAAa,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChD,KAAK;gBAAa,qBAAO,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5C,KAAK;gBAAW,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAC9C;gBAAS,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QACnC;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBAC1B,YAAY;gBACd;YACF,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,oBAAM,8OAAC,sNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;oBAC9B,YAAY;gBACd;YACF,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,oBAAM,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;oBACxB,YAAY;gBACd;QACJ;IACF;IAEA,MAAM,cAAc,kBAClB,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAM,WAAU;;sCACf,8OAAC;4BAAM,WAAU;sCACf,cAAA,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;;;;;;;;;;;;sCAKnG,8OAAC;4BAAM,WAAU;sCACd,eAAe,MAAM,GAAG,IACvB,eAAe,GAAG,CAAC,CAAC,sBAClB,8OAAC;oCAAkB,WAAU;;sDAC3B,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAqC,MAAM,MAAM;;;;;;kEAChE,8OAAC;wDAAI,WAAU;kEAA2C,MAAM,IAAI;;;;;;;;;;;;;;;;;sDAGxE,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,sBAAsB,EAAE,MAAM,SAAS,KAAK,QAAQ,iBAAiB,cAAc;kEACjG,MAAM,SAAS,KAAK,sBACnB,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;iFAEtB,8OAAC,sNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;kEAG5B,8OAAC;wDAAK,WAAW,CAAC,oBAAoB,EAAE,MAAM,SAAS,KAAK,QAAQ,mBAAmB,gBAAgB;kEACpG,MAAM,SAAS;;;;;;;;;;;;;;;;;sDAItB,8OAAC;4CAAG,WAAU;sDACX,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,YAAY;;;;;;sDAEpC,8OAAC;4CAAG,WAAU;sDACX,MAAM,QAAQ;;;;;;sDAEjB,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAK,WAAW,CAAC,wEAAwE,EAAE,eAAe,MAAM,MAAM,GAAG;;oDACvH,cAAc,MAAM,MAAM;kEAC3B,8OAAC;wDAAK,WAAU;kEAAQ,MAAM,MAAM;;;;;;;;;;;;;;;;;sDAGxC,8OAAC;4CAAG,WAAU;sDACX,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,SAAS;;;;;;sDAEjC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAO,WAAU;8DAAuC;;;;;;8DAGzD,8OAAC;oDAAO,WAAU;8DAAoC;;;;;;;;;;;;;mCAxCjD,MAAM,EAAE;;;;0DA+CnB,8OAAC;0CACC,cAAA,8OAAC;oCAAG,SAAS;oCAAG,WAAU;8CACxB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;;oDAAE;oDAAyB,WAAW,WAAW,KAAK;oDAAC;;;;;;;0DACxD,8OAAC;gDAAE,WAAU;0DAAgB,WAAW,WAAW,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAW/E,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAEpC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACZ,oCACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;yDAGxC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;;;;;;0CAI5C,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;;oDAAwB;oDAElC,YAAY,CAAC,cAAc,EAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW;;;;;;;;;;;;;;;;;;;0CAM9D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,qBAAqB,EAAE,iBAAiB,iBAAiB,eAAe;;;;;;0DACzF,8OAAC;gDAAK,WAAU;;oDAAwB;oDAClB,iBAAiB,OAAO;;;;;;;;;;;;;kDAIhD,8OAAC;wCACC,SAAS;wCACT,WAAW,CAAC,uFAAuF,EACjG,iBACI,6CACA,kDACJ;;4CAED,+BAAiB,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;qEAAe,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAClE,8OAAC;0DAAM,iBAAiB,SAAS;;;;;;;;;;;;kDAGnC,8OAAC;wCACC,SAAS,IAAM,uBAAuB,CAAC;wCACvC,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;oBAMX,+BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAK,WAAU;sDAA4B;;;;;;sDAC5C,8OAAC;4CAAK,WAAW,CAAC,KAAK,EAAE,cAAc,QAAQ,CAAC,YAAY,GAAG,mBAAmB,iBAAiB;sDAChG,cAAc,QAAQ,CAAC,YAAY,GAAG,SAAS;;;;;;;;;;;;8CAGpD,8OAAC;;sDACC,8OAAC;4CAAK,WAAU;sDAA4B;;;;;;sDAC5C,8OAAC;4CAAK,WAAU;sDAAsB,cAAc,QAAQ,CAAC,eAAe,IAAI;;;;;;;;;;;;8CAElF,8OAAC;;sDACC,8OAAC;4CAAK,WAAU;sDAA4B;;;;;;sDAC5C,8OAAC;4CAAK,WAAU;sDAAsB,cAAc,OAAO,CAAC,iBAAiB,IAAI;;;;;;;;;;;;8CAEnF,8OAAC;;sDACC,8OAAC;4CAAK,WAAU;sDAA4B;;;;;;sDAC5C,8OAAC;4CAAK,WAAU;;gDACb,cAAc,OAAO,CAAC,WAAW,IAAI;gDAAE;gDAAE,cAAc,OAAO,CAAC,UAAU,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASzF,qBAAqB,MAAM,GAAG,mBAC7B,8OAAC;gBAAI,WAAU;0BACZ,qBAAqB,GAAG,CAAC,CAAC,cAAc,sBACvC,8OAAC;wBAAgB,WAAU;;0CACzB,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;gCAAK,WAAU;0CAA0B;;;;;;;uBAFlC;;;;;;;;;;YASf,qCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCACC,SAAS,IAAM,uBAAuB;oCACtC,WAAU;8CAEV,cAAA,8OAAC,4MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAIvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,KAAI;4CACJ,KAAI;4CACJ,cAAa;4CACb,WAAU;;;;;;;;;;;;8CAId,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CAAO,WAAU;;8DAChB,8OAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,8OAAC;oDAAO,OAAM;8DAAS;;;;;;;;;;;;;;;;;;8CAI3B,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,KAAI;4CACJ,KAAI;4CACJ,cAAa;4CACb,WAAU;;;;;;;;;;;;8CAId,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACH,cAAc;4CACd,WAAU;;;;;;sDAEZ,8OAAC;4CAAM,SAAQ;4CAAkB,WAAU;sDAAwB;;;;;;;;;;;;8CAKrE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACH,cAAc;4CACd,WAAU;;;;;;sDAEZ,8OAAC;4CAAM,SAAQ;4CAAqB,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAM1E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,uBAAuB;oCACtC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS;wCACP,sBAAsB;wCACtB,uBAAuB;wCACvB,uBAAuB;oCACzB;oCACA,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,AAAC;gCAAC;gCAAU;gCAAW;6BAAO,CAAW,GAAG,CAAC,CAAC;gCAC7C,MAAM,UAAU,WAAW;gCAC3B,MAAM,YAAY,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;gCAClD,qBACE,8OAAC;oCAEC,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAC,uFAAuF,EACjG,cAAc,MACV,kCACA,8EACJ;;wCAED,QAAQ,IAAI;sDACb,8OAAC;sDAAM,QAAQ,KAAK;;;;;;sDACpB,8OAAC;4CAAK,WAAU;sDACb,UAAU,MAAM;;;;;;;mCAXd;;;;;4BAeX;;;;;;;;;;;kCAKJ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DACX,WAAW,WAAW,KAAK;;;;;;8DAE9B,8OAAC;oDAAE,WAAU;8DACV,WAAW,WAAW,WAAW;;;;;;8DAEpC,8OAAC;oDAAE,WAAU;8DACV,WAAW,WAAW,UAAU;;;;;;;;;;;;wCAKpC,cAAc,0BACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS;oDACT,UAAU;oDACV,WAAW,CAAC,uFAAuF,EACjG,mBACI,iDACA,8CACJ;8DAED,iCACC;;0EACE,8OAAC,gNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;0EACrB,8OAAC;0EAAK;;;;;;;qFAGR;;0EACE,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;0EAAK;;;;;;;;;;;;;gDAKX,mCACC,8OAAC;oDAAE,WAAU;8DACV;;;;;;;;;;;;;;;;;;;;;;;4BASZ,cAAc,0BACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAyC;;;;;;8DAGvD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAKvC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;wDACZ,uBAAuB,OAAO;wDAAC;;;;;;;8DAElC,8OAAC;oDAAI,WAAU;;wDACZ,uBAAuB,SAAS;wDAAC;;;;;;;;;;;;;;;;;;;;;;;;0CAQ5C,8OAAC;;;;;;;;;;;;;;;;;0BAKL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAyC,OAAO,MAAM;;;;;;;;;;;;8CAErE,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIxB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDACV,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;;;;;;;;;;;;8CAGtD,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIrB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDACV,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;;;;;;;;;;;;8CAGxD,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAI3B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;;gDACV,OAAO,MAAM,GAAG,IAAI,KAAK,KAAK,CAAC,AAAC,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM,GAAG,OAAO,MAAM,GAAI,OAAO;gDAAE;;;;;;;;;;;;;8CAGrH,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC", "debugId": null}}]}