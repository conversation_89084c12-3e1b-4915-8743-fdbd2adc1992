(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "cn": ()=>cn,
    "formatCurrency": ()=>formatCurrency,
    "formatDate": ()=>formatDate,
    "formatDateTime": ()=>formatDateTime,
    "formatNumber": ()=>formatNumber,
    "formatPercentage": ()=>formatPercentage,
    "getChangeColor": ()=>getChangeColor
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-client] (ecmascript)");
;
;
function cn() {
    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){
        inputs[_key] = arguments[_key];
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function formatCurrency(amount) {
    let currency = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : '₹';
    return "".concat(currency).concat(amount.toLocaleString('en-IN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }));
}
function formatNumber(num) {
    if (num >= 10000000) {
        return (num / 10000000).toFixed(2) + 'Cr';
    } else if (num >= 100000) {
        return (num / 100000).toFixed(2) + 'L';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(2) + 'K';
    }
    return num.toString();
}
function formatPercentage(value) {
    return "".concat(value >= 0 ? '+' : '').concat(value.toFixed(2), "%");
}
function getChangeColor(value) {
    if (value > 0) return 'text-green-600';
    if (value < 0) return 'text-red-600';
    return 'text-gray-600';
}
function formatDate(date) {
    return date.toLocaleDateString('en-IN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}
function formatDateTime(date) {
    return date.toLocaleString('en-IN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/layout/OptimizedSidebar.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "OptimizedSidebar": ()=>OptimizedSidebar,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chart-column.js [app-client] (ecmascript) <export default as BarChart3>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wallet$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wallet$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/wallet.js [app-client] (ecmascript) <export default as Wallet>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-client] (ecmascript) <export default as Search>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$funnel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Filter$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/funnel.js [app-client] (ecmascript) <export default as Filter>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trending-up.js [app-client] (ecmascript) <export default as TrendingUp>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-client] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$briefcase$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Briefcase$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/briefcase.js [app-client] (ecmascript) <export default as Briefcase>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$test$2d$tube$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TestTube$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/test-tube.js [app-client] (ecmascript) <export default as TestTube>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plug$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plug$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/plug.js [app-client] (ecmascript) <export default as Plug>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$menu$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Menu$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/menu.js [app-client] (ecmascript) <export default as Menu>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-client] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
const navigation = [
    {
        name: 'Capital Management',
        href: '/dashboard/capital',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wallet$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wallet$3e$__["Wallet"],
        description: 'Portfolio overview and risk management'
    },
    {
        name: 'Stock Universal',
        href: '/dashboard/stocks',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__["Search"],
        description: 'Stock search and watchlist management'
    },
    {
        name: 'BOH Eligible',
        href: '/dashboard/boh-eligible',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$funnel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Filter$3e$__["Filter"],
        description: 'Boom-Bust-Recovery pattern stocks'
    },
    {
        name: 'Weekly High Signal',
        href: '/dashboard/weekly-high',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__["TrendingUp"],
        description: 'Weekly high breakout signals'
    },
    {
        name: 'GTT Order',
        href: '/dashboard/gtt-orders',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"],
        description: 'Good Till Triggered order management'
    },
    {
        name: 'Current Holdings',
        href: '/dashboard/holdings',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$briefcase$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Briefcase$3e$__["Briefcase"],
        description: 'Current portfolio positions and performance'
    },
    {
        name: 'Back Testing',
        href: '/dashboard/backtesting',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$test$2d$tube$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TestTube$3e$__["TestTube"],
        description: 'Strategy backtesting and analysis'
    },
    {
        name: 'Connect Broker',
        href: '/dashboard/broker',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plug$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plug$3e$__["Plug"],
        description: 'Broker connection and authentication'
    }
];
function OptimizedSidebar(param) {
    let { className } = param;
    _s();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isPending, startTransition] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTransition"])();
    const [activeHref, setActiveHref] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(pathname);
    // Optimized navigation handler with immediate feedback
    const handleNavigation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "OptimizedSidebar.useCallback[handleNavigation]": (href, e)=>{
            e.preventDefault();
            // Skip if already on the same page
            if (href === pathname) {
                setIsMobileMenuOpen(false);
                return;
            }
            // Immediate visual feedback
            setActiveHref(href);
            setIsMobileMenuOpen(false);
            // Use transition for smooth navigation
            startTransition({
                "OptimizedSidebar.useCallback[handleNavigation]": ()=>{
                    router.push(href);
                }
            }["OptimizedSidebar.useCallback[handleNavigation]"]);
        }
    }["OptimizedSidebar.useCallback[handleNavigation]"], [
        router,
        pathname
    ]);
    // Memoize navigation items to prevent re-renders
    const navigationItems = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "OptimizedSidebar.useMemo[navigationItems]": ()=>navigation
    }["OptimizedSidebar.useMemo[navigationItems]"], []);
    // Prefetch likely next pages on hover
    const handleMouseEnter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "OptimizedSidebar.useCallback[handleMouseEnter]": (href)=>{
            if (href !== pathname) {
                router.prefetch(href);
            }
        }
    }["OptimizedSidebar.useCallback[handleMouseEnter]"], [
        router,
        pathname
    ]);
    // Close mobile menu on escape key
    const handleKeyDown = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "OptimizedSidebar.useCallback[handleKeyDown]": (e)=>{
            if (e.key === 'Escape') {
                setIsMobileMenuOpen(false);
            }
        }
    }["OptimizedSidebar.useCallback[handleKeyDown]"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "lg:hidden fixed top-4 left-4 z-50",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),
                    className: "p-2 rounded-md bg-white shadow-md border border-gray-200 hover:bg-gray-50 transition-colors duration-150",
                    "aria-label": "Toggle navigation menu",
                    children: isMobileMenuOpen ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                        className: "h-6 w-6"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/OptimizedSidebar.tsx",
                        lineNumber: 130,
                        columnNumber: 13
                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$menu$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Menu$3e$__["Menu"], {
                        className: "h-6 w-6"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/OptimizedSidebar.tsx",
                        lineNumber: 132,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/OptimizedSidebar.tsx",
                    lineNumber: 124,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/layout/OptimizedSidebar.tsx",
                lineNumber: 123,
                columnNumber: 7
            }, this),
            isMobileMenuOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "lg:hidden fixed inset-0 z-40 bg-black bg-opacity-50 transition-opacity duration-300",
                onClick: ()=>setIsMobileMenuOpen(false)
            }, void 0, false, {
                fileName: "[project]/src/components/layout/OptimizedSidebar.tsx",
                lineNumber: 139,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('fixed inset-y-0 left-0 z-40 w-72 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0', isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full', className),
                onKeyDown: handleKeyDown,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex flex-col h-full",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-center h-16 px-4 border-b border-gray-200 bg-white",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__["BarChart3"], {
                                        className: "h-8 w-8 text-blue-600"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/layout/OptimizedSidebar.tsx",
                                        lineNumber: 158,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-xl font-bold text-gray-900",
                                        children: "Niveshtor"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/layout/OptimizedSidebar.tsx",
                                        lineNumber: 159,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/layout/OptimizedSidebar.tsx",
                                lineNumber: 157,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/OptimizedSidebar.tsx",
                            lineNumber: 156,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                            className: "flex-1 px-4 py-6 space-y-2 overflow-y-auto",
                            children: navigationItems.map((item)=>{
                                const isActive = (activeHref || pathname) === item.href;
                                const isLoading = isPending && isActive;
                                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    href: item.href,
                                    onClick: (e)=>handleNavigation(item.href, e),
                                    onMouseEnter: ()=>handleMouseEnter(item.href),
                                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('group flex items-start p-3 rounded-lg text-sm font-medium transition-all duration-150 hover:scale-[1.02]', isActive ? 'bg-blue-50 text-blue-700 border border-blue-200 shadow-sm' : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900 hover:shadow-sm', isLoading && 'opacity-75 cursor-wait'),
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(item.icon, {
                                            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('flex-shrink-0 h-5 w-5 mt-0.5 mr-3 transition-colors duration-150', isActive ? 'text-blue-600' : 'text-gray-400 group-hover:text-gray-500')
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/OptimizedSidebar.tsx",
                                            lineNumber: 183,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex-1 min-w-0",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "font-medium",
                                                    children: item.name
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/layout/OptimizedSidebar.tsx",
                                                    lineNumber: 190,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-xs text-gray-500 mt-1 leading-tight",
                                                    children: item.description
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/layout/OptimizedSidebar.tsx",
                                                    lineNumber: 191,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/layout/OptimizedSidebar.tsx",
                                            lineNumber: 189,
                                            columnNumber: 19
                                        }, this),
                                        isLoading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex-shrink-0 ml-2",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/layout/OptimizedSidebar.tsx",
                                                lineNumber: 197,
                                                columnNumber: 23
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/OptimizedSidebar.tsx",
                                            lineNumber: 196,
                                            columnNumber: 21
                                        }, this)
                                    ]
                                }, item.name, true, {
                                    fileName: "[project]/src/components/layout/OptimizedSidebar.tsx",
                                    lineNumber: 170,
                                    columnNumber: 17
                                }, this);
                            })
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/OptimizedSidebar.tsx",
                            lineNumber: 164,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "p-4 border-t border-gray-200",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-xs text-gray-500 text-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: "Niveshtor Trading Platform"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/layout/OptimizedSidebar.tsx",
                                        lineNumber: 208,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mt-1",
                                        children: "v1.0.0"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/layout/OptimizedSidebar.tsx",
                                        lineNumber: 209,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/layout/OptimizedSidebar.tsx",
                                lineNumber: 207,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/OptimizedSidebar.tsx",
                            lineNumber: 206,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/layout/OptimizedSidebar.tsx",
                    lineNumber: 154,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/layout/OptimizedSidebar.tsx",
                lineNumber: 146,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
_s(OptimizedSidebar, "hJhqQ1/lRUuLoVQZjWYnlRnfhQ0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTransition"]
    ];
});
_c = OptimizedSidebar;
const __TURBOPACK__default__export__ = OptimizedSidebar;
var _c;
__turbopack_context__.k.register(_c, "OptimizedSidebar");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/layout/header.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Header": ()=>Header
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bell$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bell$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/bell.js [app-client] (ecmascript) <export default as Bell>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/settings.js [app-client] (ecmascript) <export default as Settings>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/user.js [app-client] (ecmascript) <export default as User>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$log$2d$out$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LogOut$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/log-out.js [app-client] (ecmascript) <export default as LogOut>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
function Header() {
    _s();
    const [showUserMenu, setShowUserMenu] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
        className: "bg-white border-b border-gray-200 px-6 py-4",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-between",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex-1",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        className: "text-2xl font-semibold text-gray-900",
                        children: "Dashboard"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/header.tsx",
                        lineNumber: 14,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/header.tsx",
                    lineNumber: 13,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center space-x-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center space-x-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "h-2 w-2 bg-green-500 rounded-full animate-pulse"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/header.tsx",
                                    lineNumber: 21,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-sm text-gray-600",
                                    children: "Market Open"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/header.tsx",
                                    lineNumber: 22,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/layout/header.tsx",
                            lineNumber: 20,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            className: "p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bell$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bell$3e$__["Bell"], {
                                className: "h-5 w-5"
                            }, void 0, false, {
                                fileName: "[project]/src/components/layout/header.tsx",
                                lineNumber: 27,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/header.tsx",
                            lineNumber: 26,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            className: "p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__["Settings"], {
                                className: "h-5 w-5"
                            }, void 0, false, {
                                fileName: "[project]/src/components/layout/header.tsx",
                                lineNumber: 32,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/header.tsx",
                            lineNumber: 31,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "relative",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>setShowUserMenu(!showUserMenu),
                                    className: "flex items-center space-x-2 p-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "h-8 w-8 bg-blue-600 rounded-full flex items-center justify-center",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"], {
                                                className: "h-4 w-4 text-white"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/layout/header.tsx",
                                                lineNumber: 42,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/header.tsx",
                                            lineNumber: 41,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-sm font-medium",
                                            children: "Trader"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/header.tsx",
                                            lineNumber: 44,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/layout/header.tsx",
                                    lineNumber: 37,
                                    columnNumber: 13
                                }, this),
                                showUserMenu && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                            href: "#",
                                            className: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"], {
                                                    className: "h-4 w-4 mr-2"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/layout/header.tsx",
                                                    lineNumber: 54,
                                                    columnNumber: 19
                                                }, this),
                                                "Profile"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/layout/header.tsx",
                                            lineNumber: 50,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                            href: "#",
                                            className: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__["Settings"], {
                                                    className: "h-4 w-4 mr-2"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/layout/header.tsx",
                                                    lineNumber: 61,
                                                    columnNumber: 19
                                                }, this),
                                                "Settings"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/layout/header.tsx",
                                            lineNumber: 57,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {
                                            className: "my-1"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/header.tsx",
                                            lineNumber: 64,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                            href: "#",
                                            className: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$log$2d$out$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LogOut$3e$__["LogOut"], {
                                                    className: "h-4 w-4 mr-2"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/layout/header.tsx",
                                                    lineNumber: 69,
                                                    columnNumber: 19
                                                }, this),
                                                "Sign out"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/layout/header.tsx",
                                            lineNumber: 65,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/layout/header.tsx",
                                    lineNumber: 49,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/layout/header.tsx",
                            lineNumber: 36,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/layout/header.tsx",
                    lineNumber: 18,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/layout/header.tsx",
            lineNumber: 11,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/layout/header.tsx",
        lineNumber: 10,
        columnNumber: 5
    }, this);
}
_s(Header, "jgB3rPvt2aGmulzofY3caF660uU=");
_c = Header;
var _c;
__turbopack_context__.k.register(_c, "Header");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/LoadingStates.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Optimized loading states and skeleton screens for better perceived performance
__turbopack_context__.s({
    "ButtonLoading": ()=>ButtonLoading,
    "CapitalCardSkeleton": ()=>CapitalCardSkeleton,
    "ChartLoadingSkeleton": ()=>ChartLoadingSkeleton,
    "DataTableLoading": ()=>DataTableLoading,
    "ErrorFallback": ()=>ErrorFallback,
    "InlineLoading": ()=>InlineLoading,
    "LoadingSpinner": ()=>LoadingSpinner,
    "NavigationLoading": ()=>NavigationLoading,
    "PageLoadingOverlay": ()=>PageLoadingOverlay,
    "PortfolioSummarySkeleton": ()=>PortfolioSummarySkeleton,
    "StockListSkeleton": ()=>StockListSkeleton,
    "StockRowSkeleton": ()=>StockRowSkeleton
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader.js [app-client] (ecmascript) <export default as Loader>");
;
;
function LoadingSpinner(param) {
    let { size = 'md', className = '' } = param;
    const sizeClasses = {
        sm: 'w-4 h-4',
        md: 'w-6 h-6',
        lg: 'w-8 h-8'
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader$3e$__["Loader"], {
        className: "animate-spin ".concat(sizeClasses[size], " ").concat(className)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/LoadingStates.tsx",
        lineNumber: 17,
        columnNumber: 5
    }, this);
}
_c = LoadingSpinner;
function StockRowSkeleton() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex items-center justify-between p-4 border-b border-gray-100 animate-pulse",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex-1",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center space-x-3",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "h-4 bg-gray-200 rounded w-20 mb-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/LoadingStates.tsx",
                                lineNumber: 28,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "h-3 bg-gray-200 rounded w-32"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/LoadingStates.tsx",
                                lineNumber: 29,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/LoadingStates.tsx",
                        lineNumber: 27,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/LoadingStates.tsx",
                    lineNumber: 26,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/LoadingStates.tsx",
                lineNumber: 25,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center space-x-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-right",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "h-4 bg-gray-200 rounded w-16 mb-1"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/LoadingStates.tsx",
                                lineNumber: 35,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "h-3 bg-gray-200 rounded w-12"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/LoadingStates.tsx",
                                lineNumber: 36,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/LoadingStates.tsx",
                        lineNumber: 34,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-right",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "h-4 bg-gray-200 rounded w-12 mb-1"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/LoadingStates.tsx",
                                lineNumber: 39,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "h-3 bg-gray-200 rounded w-8"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/LoadingStates.tsx",
                                lineNumber: 40,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/LoadingStates.tsx",
                        lineNumber: 38,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "h-8 w-8 bg-gray-200 rounded"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/LoadingStates.tsx",
                        lineNumber: 42,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/LoadingStates.tsx",
                lineNumber: 33,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/LoadingStates.tsx",
        lineNumber: 24,
        columnNumber: 5
    }, this);
}
_c1 = StockRowSkeleton;
function StockListSkeleton(param) {
    let { count = 5 } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-white rounded-lg shadow-sm border border-gray-200",
        children: Array.from({
            length: count
        }).map((_, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(StockRowSkeleton, {}, index, false, {
                fileName: "[project]/src/components/ui/LoadingStates.tsx",
                lineNumber: 53,
                columnNumber: 9
            }, this))
    }, void 0, false, {
        fileName: "[project]/src/components/ui/LoadingStates.tsx",
        lineNumber: 51,
        columnNumber: 5
    }, this);
}
_c2 = StockListSkeleton;
function CapitalCardSkeleton() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-between mb-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "h-5 bg-gray-200 rounded w-32"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/LoadingStates.tsx",
                        lineNumber: 64,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "h-8 w-8 bg-gray-200 rounded"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/LoadingStates.tsx",
                        lineNumber: 65,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/LoadingStates.tsx",
                lineNumber: 63,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "h-8 bg-gray-200 rounded w-24"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/LoadingStates.tsx",
                        lineNumber: 68,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "h-4 bg-gray-200 rounded w-20"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/LoadingStates.tsx",
                        lineNumber: 69,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/LoadingStates.tsx",
                lineNumber: 67,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/LoadingStates.tsx",
        lineNumber: 62,
        columnNumber: 5
    }, this);
}
_c3 = CapitalCardSkeleton;
function PortfolioSummarySkeleton() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",
        children: Array.from({
            length: 4
        }).map((_, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(CapitalCardSkeleton, {}, index, false, {
                fileName: "[project]/src/components/ui/LoadingStates.tsx",
                lineNumber: 80,
                columnNumber: 9
            }, this))
    }, void 0, false, {
        fileName: "[project]/src/components/ui/LoadingStates.tsx",
        lineNumber: 78,
        columnNumber: 5
    }, this);
}
_c4 = PortfolioSummarySkeleton;
function PageLoadingOverlay(param) {
    let { message = 'Loading...' } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed inset-0 bg-white bg-opacity-90 flex items-center justify-center z-50",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "text-center",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(LoadingSpinner, {
                    size: "lg",
                    className: "text-blue-600 mb-4"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/LoadingStates.tsx",
                    lineNumber: 91,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-gray-600 font-medium",
                    children: message
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/LoadingStates.tsx",
                    lineNumber: 92,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/ui/LoadingStates.tsx",
            lineNumber: 90,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/LoadingStates.tsx",
        lineNumber: 89,
        columnNumber: 5
    }, this);
}
_c5 = PageLoadingOverlay;
function InlineLoading(param) {
    let { message = 'Loading...' } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex items-center justify-center py-8",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(LoadingSpinner, {
                className: "text-blue-600 mr-3"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/LoadingStates.tsx",
                lineNumber: 102,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                className: "text-gray-600",
                children: message
            }, void 0, false, {
                fileName: "[project]/src/components/ui/LoadingStates.tsx",
                lineNumber: 103,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/LoadingStates.tsx",
        lineNumber: 101,
        columnNumber: 5
    }, this);
}
_c6 = InlineLoading;
function ButtonLoading(param) {
    let { children, isLoading, ...props } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
        ...props,
        disabled: isLoading || props.disabled,
        className: "".concat(props.className, " ").concat(isLoading ? 'cursor-wait opacity-75' : ''),
        children: isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(LoadingSpinner, {
                    size: "sm",
                    className: "mr-2"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/LoadingStates.tsx",
                    lineNumber: 122,
                    columnNumber: 11
                }, this),
                "Loading..."
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/ui/LoadingStates.tsx",
            lineNumber: 121,
            columnNumber: 9
        }, this) : children
    }, void 0, false, {
        fileName: "[project]/src/components/ui/LoadingStates.tsx",
        lineNumber: 115,
        columnNumber: 5
    }, this);
}
_c7 = ButtonLoading;
function DataTableLoading(param) {
    let { columns = 4, rows = 5 } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "border-b border-gray-200 p-4 animate-pulse",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "grid gap-4",
                    style: {
                        gridTemplateColumns: "repeat(".concat(columns, ", 1fr)")
                    },
                    children: Array.from({
                        length: columns
                    }).map((_, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "h-4 bg-gray-200 rounded"
                        }, index, false, {
                            fileName: "[project]/src/components/ui/LoadingStates.tsx",
                            lineNumber: 143,
                            columnNumber: 13
                        }, this))
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/LoadingStates.tsx",
                    lineNumber: 141,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/LoadingStates.tsx",
                lineNumber: 140,
                columnNumber: 7
            }, this),
            Array.from({
                length: rows
            }).map((_, rowIndex)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "border-b border-gray-100 p-4 animate-pulse",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "grid gap-4",
                        style: {
                            gridTemplateColumns: "repeat(".concat(columns, ", 1fr)")
                        },
                        children: Array.from({
                            length: columns
                        }).map((_, colIndex)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "h-4 bg-gray-200 rounded"
                            }, colIndex, false, {
                                fileName: "[project]/src/components/ui/LoadingStates.tsx",
                                lineNumber: 153,
                                columnNumber: 15
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/LoadingStates.tsx",
                        lineNumber: 151,
                        columnNumber: 11
                    }, this)
                }, rowIndex, false, {
                    fileName: "[project]/src/components/ui/LoadingStates.tsx",
                    lineNumber: 150,
                    columnNumber: 9
                }, this))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/LoadingStates.tsx",
        lineNumber: 138,
        columnNumber: 5
    }, this);
}
_c8 = DataTableLoading;
function ChartLoadingSkeleton() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "h-6 bg-gray-200 rounded w-48 mb-6"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/LoadingStates.tsx",
                lineNumber: 166,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "h-64 bg-gray-100 rounded flex items-end justify-between px-4 pb-4",
                children: Array.from({
                    length: 12
                }).map((_, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-gray-200 rounded-t",
                        style: {
                            height: "".concat(Math.random() * 80 + 20, "%"),
                            width: '6%'
                        }
                    }, index, false, {
                        fileName: "[project]/src/components/ui/LoadingStates.tsx",
                        lineNumber: 169,
                        columnNumber: 11
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/components/ui/LoadingStates.tsx",
                lineNumber: 167,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/LoadingStates.tsx",
        lineNumber: 165,
        columnNumber: 5
    }, this);
}
_c9 = ChartLoadingSkeleton;
function NavigationLoading() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed top-0 left-0 right-0 h-1 bg-blue-200 z-50",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "h-full bg-blue-600 animate-pulse"
        }, void 0, false, {
            fileName: "[project]/src/components/ui/LoadingStates.tsx",
            lineNumber: 187,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/LoadingStates.tsx",
        lineNumber: 186,
        columnNumber: 5
    }, this);
}
_c10 = NavigationLoading;
function ErrorFallback(param) {
    let { error, resetError } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen flex items-center justify-center bg-gray-50",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "max-w-md w-full bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-red-500 mb-4",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                        className: "w-12 h-12 mx-auto",
                        fill: "none",
                        stroke: "currentColor",
                        viewBox: "0 0 24 24",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            strokeLinecap: "round",
                            strokeLinejoin: "round",
                            strokeWidth: 2,
                            d: "M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/LoadingStates.tsx",
                            lineNumber: 202,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/LoadingStates.tsx",
                        lineNumber: 201,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/LoadingStates.tsx",
                    lineNumber: 200,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                    className: "text-lg font-semibold text-gray-900 mb-2",
                    children: "Something went wrong"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/LoadingStates.tsx",
                    lineNumber: 205,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-gray-600 mb-4 text-sm",
                    children: error.message
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/LoadingStates.tsx",
                    lineNumber: 206,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: resetError,
                    className: "px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",
                    children: "Try again"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/LoadingStates.tsx",
                    lineNumber: 207,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/ui/LoadingStates.tsx",
            lineNumber: 199,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/LoadingStates.tsx",
        lineNumber: 198,
        columnNumber: 5
    }, this);
}
_c11 = ErrorFallback;
var _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11;
__turbopack_context__.k.register(_c, "LoadingSpinner");
__turbopack_context__.k.register(_c1, "StockRowSkeleton");
__turbopack_context__.k.register(_c2, "StockListSkeleton");
__turbopack_context__.k.register(_c3, "CapitalCardSkeleton");
__turbopack_context__.k.register(_c4, "PortfolioSummarySkeleton");
__turbopack_context__.k.register(_c5, "PageLoadingOverlay");
__turbopack_context__.k.register(_c6, "InlineLoading");
__turbopack_context__.k.register(_c7, "ButtonLoading");
__turbopack_context__.k.register(_c8, "DataTableLoading");
__turbopack_context__.k.register(_c9, "ChartLoadingSkeleton");
__turbopack_context__.k.register(_c10, "NavigationLoading");
__turbopack_context__.k.register(_c11, "ErrorFallback");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/layout/dashboard-layout.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "DashboardLayout": ()=>DashboardLayout
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$OptimizedSidebar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/layout/OptimizedSidebar.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$header$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/layout/header.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$LoadingStates$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/LoadingStates.tsx [app-client] (ecmascript)");
'use client';
;
;
;
;
;
function DashboardLayout(param) {
    let { children } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex h-screen bg-gray-50",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$OptimizedSidebar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["OptimizedSidebar"], {}, void 0, false, {
                fileName: "[project]/src/components/layout/dashboard-layout.tsx",
                lineNumber: 16,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex-1 flex flex-col overflow-hidden lg:ml-0",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$header$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Header"], {}, void 0, false, {
                        fileName: "[project]/src/components/layout/dashboard-layout.tsx",
                        lineNumber: 21,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                        className: "flex-1 overflow-y-auto p-6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Suspense"], {
                            fallback: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$LoadingStates$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NavigationLoading"], {}, void 0, false, {
                                fileName: "[project]/src/components/layout/dashboard-layout.tsx",
                                lineNumber: 25,
                                columnNumber: 31
                            }, void 0),
                            children: children
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/dashboard-layout.tsx",
                            lineNumber: 25,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/dashboard-layout.tsx",
                        lineNumber: 24,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/layout/dashboard-layout.tsx",
                lineNumber: 19,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/layout/dashboard-layout.tsx",
        lineNumber: 14,
        columnNumber: 5
    }, this);
}
_c = DashboardLayout;
var _c;
__turbopack_context__.k.register(_c, "DashboardLayout");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/cache-service.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Cache service for optimizing data fetching and reducing API calls
__turbopack_context__.s({
    "CacheKeys": ()=>CacheKeys,
    "cacheService": ()=>cacheService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
;
class CacheService {
    // Get TTL based on data type
    getTTL(key) {
        if (key.includes('stock-name') || key.includes('names-map')) {
            return this.STOCK_NAMES_TTL;
        }
        if (key.includes('stock') || key.includes('nifty')) {
            return this.STOCK_DATA_TTL;
        }
        if (key.includes('broker') || key.includes('balance')) {
            return this.BROKER_DATA_TTL;
        }
        if (key.includes('portfolio') || key.includes('holdings')) {
            return this.PORTFOLIO_DATA_TTL;
        }
        return this.DEFAULT_TTL;
    }
    // Set cache entry
    set(key, data, customTTL) {
        const ttl = customTTL || this.getTTL(key);
        const now = Date.now();
        this.cache.set(key, {
            data,
            timestamp: now,
            expiresAt: now + ttl
        });
    }
    // Get cache entry
    get(key) {
        const entry = this.cache.get(key);
        if (!entry) {
            return null;
        }
        // Check if expired
        if (Date.now() > entry.expiresAt) {
            this.cache.delete(key);
            return null;
        }
        return entry.data;
    }
    // Check if key exists and is valid
    has(key) {
        const entry = this.cache.get(key);
        if (!entry) {
            return false;
        }
        // Check if expired
        if (Date.now() > entry.expiresAt) {
            this.cache.delete(key);
            return false;
        }
        return true;
    }
    // Clear specific key
    delete(key) {
        this.cache.delete(key);
    }
    // Clear all cache
    clear() {
        this.cache.clear();
    }
    // Clear expired entries
    cleanup() {
        const now = Date.now();
        for (const [key, entry] of this.cache.entries()){
            if (now > entry.expiresAt) {
                this.cache.delete(key);
            }
        }
    }
    // Get cache stats
    getStats() {
        return {
            size: this.cache.size,
            keys: Array.from(this.cache.keys()),
            hitRate: 0 // TODO: Implement hit rate tracking
        };
    }
    // Cached fetch wrapper
    async cachedFetch(key, fetchFn, customTTL) {
        // Try to get from cache first
        const cached = this.get(key);
        if (cached !== null) {
            return cached;
        }
        // Fetch fresh data
        try {
            const data = await fetchFn();
            this.set(key, data, customTTL);
            return data;
        } catch (error) {
            // If fetch fails, try to return stale data if available
            const staleEntry = this.cache.get(key);
            if (staleEntry) {
                console.warn("Using stale data for ".concat(key, " due to fetch error:"), error);
                return staleEntry.data;
            }
            throw error;
        }
    }
    // Prefetch data in background
    async prefetch(key, fetchFn, customTTL) {
        // Only prefetch if not already cached
        if (!this.has(key)) {
            try {
                const data = await fetchFn();
                this.set(key, data, customTTL);
            } catch (error) {
                console.warn("Prefetch failed for ".concat(key, ":"), error);
            }
        }
    }
    // Batch fetch with caching
    async batchFetch(requests) {
        const results = [];
        const fetchPromises = [];
        for (const request of requests){
            const cached = this.get(request.key);
            if (cached !== null) {
                results.push(cached);
            } else {
                fetchPromises.push(request.fetchFn().then((data)=>{
                    this.set(request.key, data, request.ttl);
                    return data;
                }));
            }
        }
        // Wait for all fetches to complete
        const fetchedResults = await Promise.all(fetchPromises);
        results.push(...fetchedResults);
        return results;
    }
    // Invalidate cache by pattern
    invalidatePattern(pattern) {
        const regex = new RegExp(pattern);
        for (const key of this.cache.keys()){
            if (regex.test(key)) {
                this.cache.delete(key);
            }
        }
    }
    // Auto cleanup interval
    startAutoCleanup() {
        let intervalMs = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 5 * 60 * 1000;
        setInterval(()=>{
            this.cleanup();
        }, intervalMs);
    }
    constructor(){
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "cache", new Map());
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "DEFAULT_TTL", 5 * 60 * 1000); // 5 minutes
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "STOCK_DATA_TTL", 30 * 1000); // 30 seconds for stock data
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "STOCK_NAMES_TTL", 24 * 60 * 60 * 1000); // 24 hours for stock names
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "BROKER_DATA_TTL", 10 * 1000); // 10 seconds for broker data
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "PORTFOLIO_DATA_TTL", 60 * 1000); // 1 minute for portfolio data
    }
}
const cacheService = new CacheService();
// Start auto cleanup
if ("TURBOPACK compile-time truthy", 1) {
    cacheService.startAutoCleanup();
}
const CacheKeys = {
    brokerBalance: (userId)=>"broker-balance-".concat(userId),
    fundAllocation: (userId, strategy)=>"fund-allocation-".concat(userId, "-").concat(strategy),
    portfolioSummary: (userId)=>"portfolio-summary-".concat(userId),
    niftyStocks: (batchIndex)=>"nifty-stocks-batch-".concat(batchIndex),
    stockQuote: (symbol)=>"stock-quote-".concat(symbol),
    stockName: (symbol)=>"stock-name-".concat(symbol),
    stockNamesMap: ()=>'stock-names-map',
    stockPriceData: (symbol)=>"stock-price-data-".concat(symbol),
    stockSearch: (query)=>"stock-search-".concat(query),
    yahooQuotes: (symbols)=>"yahoo-quotes-".concat(symbols.sort().join(','))
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/stock-names-service.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Stock names service for caching and managing stock company names
__turbopack_context__.s({
    "stockNamesService": ()=>stockNamesService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/cache-service.ts [app-client] (ecmascript)");
;
;
class StockNamesService {
    // Get stock name from cache or fetch if not available
    async getStockName(symbol) {
        // Try to get from cache first
        const cached = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheService"].get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CacheKeys"].stockName(symbol));
        if (cached) {
            return cached;
        }
        // If not in cache, fetch from Yahoo Finance
        try {
            const { yahooFinanceService } = await __turbopack_context__.r("[project]/src/lib/yahoo-finance.ts [app-client] (ecmascript, async loader)")(__turbopack_context__.i);
            const quote = await yahooFinanceService.getQuote(symbol);
            const name = (quote === null || quote === void 0 ? void 0 : quote.name) || symbol.replace('.NS', '');
            // Cache the name for 24 hours
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheService"].set(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CacheKeys"].stockName(symbol), name);
            return name;
        } catch (error) {
            console.warn("Failed to fetch name for ".concat(symbol, ":"), error);
            // Return symbol without .NS as fallback
            return symbol.replace('.NS', '');
        }
    }
    // Get multiple stock names efficiently
    async getStockNames(symbols) {
        const namesMap = new Map();
        const uncachedSymbols = [];
        // Check cache for each symbol
        for (const symbol of symbols){
            const cached = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheService"].get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CacheKeys"].stockName(symbol));
            if (cached) {
                namesMap.set(symbol, cached);
            } else {
                uncachedSymbols.push(symbol);
            }
        }
        // If all names are cached, return immediately
        if (uncachedSymbols.length === 0) {
            return namesMap;
        }
        console.log("📝 Fetching names for ".concat(uncachedSymbols.length, " uncached stocks"));
        // Fetch uncached names in batches
        const batches = this.chunkArray(uncachedSymbols, this.BATCH_SIZE);
        for (const batch of batches){
            try {
                const { yahooFinanceService } = await __turbopack_context__.r("[project]/src/lib/yahoo-finance.ts [app-client] (ecmascript, async loader)")(__turbopack_context__.i);
                const quotes = await yahooFinanceService.getMultipleQuotes(batch);
                for (const quote of quotes){
                    if (quote && quote.symbol) {
                        // Use the name from the quote, or fallback to symbol without .NS
                        const name = quote.name || quote.symbol.replace('.NS', '');
                        namesMap.set(quote.symbol, name);
                        // Cache the name for 24 hours
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheService"].set(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CacheKeys"].stockName(quote.symbol), name);
                    }
                }
                // Ensure all symbols in the batch have names (even if fallback)
                for (const symbol of batch){
                    if (!namesMap.has(symbol)) {
                        const fallbackName = symbol.replace('.NS', '');
                        namesMap.set(symbol, fallbackName);
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheService"].set(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CacheKeys"].stockName(symbol), fallbackName);
                        console.log("📝 Using fallback name for ".concat(symbol, ": ").concat(fallbackName));
                    }
                }
                // Add delay between batches to avoid rate limiting
                if (batches.indexOf(batch) < batches.length - 1) {
                    await new Promise((resolve)=>setTimeout(resolve, 100));
                }
            } catch (error) {
                console.warn("Failed to fetch names for batch:", error);
                // Add fallback names for failed batch
                for (const symbol of batch){
                    if (!namesMap.has(symbol)) {
                        const fallbackName = symbol.replace('.NS', '');
                        namesMap.set(symbol, fallbackName);
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheService"].set(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CacheKeys"].stockName(symbol), fallbackName);
                    }
                }
            }
        }
        return namesMap;
    }
    // Get all cached stock names
    getCachedStockNames() {
        const cachedMap = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheService"].get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CacheKeys"].stockNamesMap());
        return cachedMap || new Map();
    }
    // Cache stock names map for quick access
    cacheStockNamesMap(namesMap) {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheService"].set(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CacheKeys"].stockNamesMap(), namesMap);
    }
    // Preload stock names for given symbols
    async preloadStockNames(symbols) {
        console.log("🚀 Preloading names for ".concat(symbols.length, " stocks"));
        try {
            const namesMap = await this.getStockNames(symbols);
            this.cacheStockNamesMap(namesMap);
            console.log("✅ Preloaded ".concat(namesMap.size, " stock names"));
        } catch (error) {
            console.error('Failed to preload stock names:', error);
        }
    }
    // Check if stock name is cached
    isNameCached(symbol) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheService"].has(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CacheKeys"].stockName(symbol));
    }
    // Get cache statistics for stock names
    getNamesCacheStats() {
        const stats = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheService"].getStats();
        const nameKeys = stats.keys.filter((key)=>key.includes('stock-name'));
        return {
            cachedCount: nameKeys.length,
            totalRequested: nameKeys.length,
            hitRate: nameKeys.length > 0 ? 0.8 : 0 // Estimated hit rate
        };
    }
    // Clear all cached stock names
    clearNamesCache() {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheService"].invalidatePattern('stock-name');
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheService"].delete(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CacheKeys"].stockNamesMap());
        console.log('🗑️ Cleared all cached stock names');
    }
    // Refresh stock names (force re-fetch)
    async refreshStockNames(symbols) {
        // Clear existing cache for these symbols
        for (const symbol of symbols){
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheService"].delete(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CacheKeys"].stockName(symbol));
        }
        // Fetch fresh names
        return await this.getStockNames(symbols);
    }
    // Utility function to chunk array into smaller arrays
    chunkArray(array, chunkSize) {
        const chunks = [];
        for(let i = 0; i < array.length; i += chunkSize){
            chunks.push(array.slice(i, i + chunkSize));
        }
        return chunks;
    }
    // Get stock name with fallback
    getStockNameSync(symbol) {
        const cached = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheService"].get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CacheKeys"].stockName(symbol));
        return cached || symbol.replace('.NS', '');
    }
    // Batch update stock names from quotes
    updateStockNamesFromQuotes(quotes) {
        for (const quote of quotes){
            if (quote && quote.symbol && quote.name) {
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheService"].set(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CacheKeys"].stockName(quote.symbol), quote.name);
            }
        }
    }
    // Force refresh all stock names (clears cache and re-fetches)
    async forceRefreshAllNames(symbols) {
        console.log('🔄 Force refreshing all stock names...');
        this.clearNamesCache();
        await this.preloadStockNames(symbols);
    }
    constructor(){
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "BATCH_SIZE", 50);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "MAX_RETRIES", 3);
    }
}
const stockNamesService = new StockNamesService();
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/nifty-stocks.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Current Nifty 200 stock symbols - updated list as of 2025
__turbopack_context__.s({
    "NIFTY_200_SYMBOLS": ()=>NIFTY_200_SYMBOLS,
    "addBOHEligibility": ()=>addBOHEligibility,
    "calculateBOHEligibility": ()=>calculateBOHEligibility,
    "getDisplaySymbol": ()=>getDisplaySymbol,
    "getYahooSymbol": ()=>getYahooSymbol
});
const RAW_NIFTY_200_SYMBOLS = [
    'NYKAA',
    'MRF',
    'MANKIND',
    'CHOLAFIN',
    'CONCOR',
    'ICICIPRULI',
    'PREMIERENE',
    'BSE',
    'BANDHANBNK',
    'WAAREEENER',
    'SHRIRAMFIN',
    'SBICARD',
    'DABUR',
    'DIXON',
    'GMRAIRPORT',
    'VBL',
    'BAJFINANCE',
    'BHEL',
    'BIOCON',
    'INDHOTEL',
    'COALINDIA',
    'HYUNDAI',
    'GODREJCP',
    'HINDUNILVR',
    'ADANIENSOL',
    'PATANJALI',
    'SHREECEM',
    'VMM',
    'CUMMINSIND',
    'LODHA',
    'ABB',
    'COCHINSHIP',
    'BRITANNIA',
    'ULTRACEMCO',
    'AUBANK',
    'KALYANKJIL',
    'BDL',
    'DIVISLAB',
    'INDIGO',
    'POWERGRID',
    'OIL',
    'HEROMOTOCO',
    'ASTRAL',
    'ACC',
    'BANKBARODA',
    'BOSCHLTD',
    'MOTILALOFS',
    'TORNTPHARM',
    'TATATECH',
    'MAHABANK',
    'M&M',
    'ASIANPAINT',
    'UNITDSPR',
    'PIIND',
    'ITC',
    'ASHOKLEY',
    'NESTLEIND',
    'HDFCAMC',
    'ADANIGREEN',
    'MARICO',
    'APOLLOTYRE',
    'LTF',
    'HDFCBANK',
    'TVSMOTOR',
    'ADANIPOWER',
    'MARUTI',
    'MOTHERSON',
    'BAJAJHFL',
    'NTPCGREEN',
    'JIOFIN',
    'BAJAJFINSV',
    'JSWENERGY',
    'TORNTPOWER',
    'NTPC',
    'FEDERALBNK',
    'ALKEM',
    'NHPC',
    'BAJAJ-AUTO',
    'EICHERMOT',
    'M&MFIN',
    'ETERNAL',
    'MPHASIS',
    'HUDCO',
    'PETRONET',
    'SUPREMEIND',
    'HAL',
    'CIPLA',
    'IRCTC',
    'KOTAKBANK',
    'POLICYBZR',
    'INDIANB',
    'CANBK',
    'AXISBANK',
    'ONGC',
    'LICI',
    'SWIGGY',
    'TATAMOTORS',
    'IDEA',
    'SOLARINDS',
    'LICHSGFIN',
    'MAZDOCK',
    'TATAPOWER',
    'IREDA',
    'SRF',
    'BAJAJHLDNG',
    'SBIN',
    'BHARTIHEXA',
    'ZYDUSLIFE',
    'VOLTAS',
    'AMBUJACEM',
    'MUTHOOTFIN',
    'TITAN',
    'ADANIPORTS',
    'SBILIFE',
    'ATGL',
    'ADANIENT',
    'YESBANK',
    'INFY',
    'TATACONSUM',
    'EXIDEIND',
    'AUROPHARMA',
    'PAYTM',
    'PFC',
    'TATAELXSI',
    'TATACOMM',
    'SUNPHARMA',
    'INDUSTOWER',
    'JSWSTEEL',
    'ESCORTS',
    'IRFC',
    'BHARTIARTL',
    'LUPIN',
    'RVNL',
    'POLYCAB',
    'CGPOWER',
    'GLENMARK',
    'HAVELLS',
    'PIDILITIND',
    'TCS',
    'NMDC',
    'LTIM',
    'TRENT',
    'SUZLON',
    'DMART',
    'JUBLFOOD',
    'SAIL',
    'COLPAL',
    'LT',
    'MFSL',
    'SONACOMS',
    'PRESTIGE',
    'IDFCFIRSTB',
    'ICICIBANK',
    'SJVN',
    'BEL',
    'OFSS',
    'WIPRO',
    'ICICIGI',
    'ABCAPITAL',
    'COFORGE',
    'JINDALSTEL',
    'GRASIM',
    'BANKINDIA',
    'PAGEIND',
    'ABFRL',
    'TIINDIA',
    'INDUSINDBK',
    'PNB',
    'RECLTD',
    'KPITTECH',
    'HDFCLIFE',
    'RELIANCE',
    'PERSISTENT',
    'DRREDDY',
    'UPL',
    'OLAELEC',
    'TECHM',
    'OBEROIRLTY',
    'APOLLOHOSP',
    'BHARATFORG',
    'NAUKRI',
    'HINDPETRO',
    'DLF',
    'TATASTEEL',
    'BPCL',
    'HINDALCO',
    'IRB',
    'APLAPOLLO',
    'NATIONALUM',
    'HCLTECH',
    'SIEMENS',
    'IOC',
    'GODREJPROP',
    'IGL',
    'HINDZINC',
    'PHOENIXLTD',
    'VEDL',
    'UNIONBANK',
    'MAXHEALTH',
    'GAIL'
];
const NIFTY_200_SYMBOLS = [
    ...new Set(RAW_NIFTY_200_SYMBOLS)
];
// Validation: Log any duplicates found and final count
const duplicates = RAW_NIFTY_200_SYMBOLS.filter((symbol, index)=>RAW_NIFTY_200_SYMBOLS.indexOf(symbol) !== index);
if (duplicates.length > 0) {
    console.warn('Duplicate symbols found and removed:', duplicates);
}
console.log("Nifty 200 symbols loaded: ".concat(NIFTY_200_SYMBOLS.length, " unique symbols"));
function getYahooSymbol(nseSymbol) {
    // Handle special cases for Yahoo Finance symbol mapping
    const symbolMappings = {
        'M&M': 'MM.NS',
        'M&MFIN': 'MMFIN.NS',
        'BAJAJ-AUTO': 'BAJAJ_AUTO.NS',
        'L&T': 'LT.NS',
        'LTF': 'LTFH.NS',
        'BOSCHLTD': 'BOSCHLTD.NS',
        'BSOFT': 'BSOFT.NS' // Birlasoft
    };
    // Handle merged/renamed stocks - map to their current equivalent
    const renamedMappings = {
        'CADILAHC': 'ZYDUSLIFE.NS'
    };
    // Handle merged stocks (for backward compatibility)
    const delistedMappings = {
        'HDFC': 'HDFCBANK.NS',
        'MINDTREE': 'LTIM.NS',
        'PVR': 'PVRINOX.NS',
        ...renamedMappings
    };
    // Stocks that are completely delisted/suspended - these will be skipped
    const delistedStocks = new Set([]);
    // Stocks that might have issues - keep for now but monitor
    const problematicStocks = new Set([
        'WAAREEENER',
        'PREMIERENE',
        'GMRAIRPORT',
        'ADANIENSOL',
        'PATANJALI',
        'VMM',
        'KALYANKJIL',
        'NTPCGREEN',
        'JIOFIN',
        'BHARTIHEXA',
        'ATGL',
        'IREDA',
        'SWIGGY',
        'SOLARINDS',
        'OLAELEC',
        'PHOENIXLTD',
        'MAXHEALTH' // Check if available
    ]);
    // Check if stock is delisted/suspended - return null to skip
    if (delistedStocks.has(nseSymbol)) {
        console.log("🚫 Skipping delisted/suspended stock: ".concat(nseSymbol));
        return null; // This will be handled in the API to skip the stock
    }
    // Check for renamed/merged stocks first
    if (renamedMappings[nseSymbol]) {
        console.log("📝 Mapping renamed stock ".concat(nseSymbol, " to ").concat(renamedMappings[nseSymbol]));
        return renamedMappings[nseSymbol];
    }
    if (delistedMappings[nseSymbol]) {
        console.log("📝 Mapping merged stock ".concat(nseSymbol, " to ").concat(delistedMappings[nseSymbol]));
        return delistedMappings[nseSymbol];
    }
    if (symbolMappings[nseSymbol]) {
        return symbolMappings[nseSymbol];
    }
    // Log if this is a potentially problematic stock
    if (problematicStocks.has(nseSymbol)) {
        console.log("⚠️ Fetching potentially new/problematic stock: ".concat(nseSymbol));
    }
    return "".concat(nseSymbol, ".NS");
}
function getDisplaySymbol(nseSymbol) {
    return nseSymbol;
}
function calculateBOHEligibility(stock) {
    // BOH Eligible if 52-week low occurred AFTER 52-week high
    // This indicates: High (boom) → Low (bust) → Recovery pattern
    if (stock.high52WeekDate && stock.low52WeekDate) {
        const highDate = new Date(stock.high52WeekDate);
        const lowDate = new Date(stock.low52WeekDate);
        // Return true if low date is after high date (boom → bust → recovery pattern)
        return lowDate > highDate;
    }
    // Fallback: If we don't have dates, use a heuristic based on price and 52-week range
    // This is for testing purposes when Yahoo Finance doesn't provide dates
    if (stock.high52Week && stock.low52Week && stock.price > 0) {
        const priceRange = stock.high52Week - stock.low52Week;
        const currentFromLow = stock.price - stock.low52Week;
        const currentFromHigh = stock.high52Week - stock.price;
        // Consider BOH eligible if:
        // 1. Stock has a significant price range (> 20% of current price)
        // 2. Current price is closer to 52-week low than high (recovery phase)
        // 3. Stock is not at 52-week high (not in boom phase)
        const hasSignificantRange = priceRange > stock.price * 0.2;
        const isInRecoveryPhase = currentFromLow < currentFromHigh;
        const notAtHigh = stock.price < stock.high52Week * 0.95;
        // Make about 60% of eligible stocks BOH eligible for testing
        const randomFactor = (stock.symbol.charCodeAt(0) + stock.symbol.charCodeAt(stock.symbol.length - 1)) % 10;
        const shouldBeBOH = randomFactor < 6; // 60% chance
        return hasSignificantRange && isInRecoveryPhase && notAtHigh && shouldBeBOH;
    }
    return false;
}
function addBOHEligibility(stock) {
    return {
        ...stock,
        isBOHEligible: calculateBOHEligibility(stock)
    };
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/yahoo-finance.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "yahooFinanceService": ()=>yahooFinanceService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/cache-service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stock$2d$names$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stock-names-service.ts [app-client] (ecmascript)");
;
;
;
;
// Yahoo Finance API endpoints - using chart endpoint which is more reliable
const YAHOO_FINANCE_BASE_URL = 'https://query1.finance.yahoo.com/v8/finance/chart';
const YAHOO_SEARCH_URL = 'https://query1.finance.yahoo.com/v1/finance/search';
const YAHOO_QUOTE_URL = 'https://query1.finance.yahoo.com/v7/finance/quote';
const YAHOO_QUOTE_SUMMARY_URL = 'https://query2.finance.yahoo.com/v10/finance/quoteSummary';
// Alternative endpoints for better reliability
const YAHOO_CHART_URL = 'https://query1.finance.yahoo.com/v8/finance/chart';
// Known problematic stocks that often fail - handle with extra care
const PROBLEMATIC_STOCKS = new Set([
    'BOSCHLTD.NS',
    'BSOFT.NS',
    'MINDTREE.NS',
    'PVR.NS',
    'HDFC.NS' // Merged stock
]);
class YahooFinanceService {
    // Start real-time updates for given symbols
    startRealTimeUpdates(symbols, callback) {
        console.log("🔄 Starting real-time updates for ".concat(symbols.length, " symbols"));
        this.currentSymbols = symbols;
        this.updateListeners.add(callback);
        if (!this.isRealTimeActive) {
            this.isRealTimeActive = true;
            this.scheduleNextUpdate();
        }
    }
    // Stop real-time updates for a specific callback
    stopRealTimeUpdates(callback) {
        this.updateListeners.delete(callback);
        if (this.updateListeners.size === 0) {
            this.isRealTimeActive = false;
            if (this.realTimeInterval) {
                clearTimeout(this.realTimeInterval);
                this.realTimeInterval = null;
            }
            console.log('⏹️ Stopped real-time updates - no active listeners');
        }
    }
    // Schedule the next update
    scheduleNextUpdate() {
        if (!this.isRealTimeActive) return;
        const now = Date.now();
        const timeSinceLastUpdate = now - this.lastUpdateTime;
        const timeUntilNextUpdate = Math.max(0, 30000 - timeSinceLastUpdate); // 30 seconds
        this.realTimeInterval = setTimeout(()=>{
            this.performRealTimeUpdate();
        }, timeUntilNextUpdate);
    }
    // Perform the actual real-time update
    async performRealTimeUpdate() {
        if (!this.isRealTimeActive || this.currentSymbols.length === 0) return;
        try {
            console.log("🔄 Performing real-time update for ".concat(this.currentSymbols.length, " symbols"));
            const quotes = await this.getMultipleQuotesWithCachedNames(this.currentSymbols);
            this.lastUpdateTime = Date.now();
            // Notify all listeners
            this.updateListeners.forEach((callback)=>{
                try {
                    callback(quotes);
                } catch (error) {
                    console.error('❌ Error in update listener:', error);
                }
            });
            console.log("✅ Real-time update completed: ".concat(quotes.length, " quotes updated"));
        } catch (error) {
            console.error('❌ Real-time update failed:', error);
        }
        // Schedule next update
        this.scheduleNextUpdate();
    }
    async makeRequest(url) {
        let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
        try {
            console.log("🌐 Making request to: ".concat(url), params);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(url, {
                params,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept': 'application/json',
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache',
                    'Referer': 'https://finance.yahoo.com/'
                },
                timeout: 15000
            });
            console.log("✅ Request successful, status: ".concat(response.status));
            return response.data;
        } catch (error) {
            var _error_response, _error_response1;
            const errorDetails = {
                url,
                params,
                message: (error === null || error === void 0 ? void 0 : error.message) || 'Unknown error',
                status: (error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) || 'No status',
                data: (error === null || error === void 0 ? void 0 : (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data) || 'No data'
            };
            console.error('❌ Yahoo Finance API error:', errorDetails);
            // For historical data requests, return null instead of throwing
            if (url.includes('/v8/finance/chart/')) {
                console.warn("⚠️ Historical data request failed, returning null");
                return null;
            }
            throw new Error("Failed to fetch data from Yahoo Finance: ".concat((error === null || error === void 0 ? void 0 : error.message) || 'Unknown error'));
        }
    }
    // Get price data only (without fetching names from API) - optimized for frequent updates
    async getPriceDataOnly(symbol) {
        try {
            var _data_chart_result, _data_chart;
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("".concat(YAHOO_CHART_URL, "/").concat(symbol), {
                params: {
                    interval: '1d',
                    range: '1d',
                    includePrePost: false
                },
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Accept': 'application/json',
                    'Referer': 'https://finance.yahoo.com/'
                },
                timeout: 8000
            });
            const data = response.data;
            if ((_data_chart = data.chart) === null || _data_chart === void 0 ? void 0 : (_data_chart_result = _data_chart.result) === null || _data_chart_result === void 0 ? void 0 : _data_chart_result[0]) {
                var _result_indicators_quote, _result_indicators;
                const result = data.chart.result[0];
                const meta = result.meta;
                const quote = (_result_indicators = result.indicators) === null || _result_indicators === void 0 ? void 0 : (_result_indicators_quote = _result_indicators.quote) === null || _result_indicators_quote === void 0 ? void 0 : _result_indicators_quote[0];
                const currentPrice = meta.regularMarketPrice || (quote === null || quote === void 0 ? void 0 : quote.close) && quote.close[quote.close.length - 1] || meta.previousClose || 0;
                const previousClose = meta.previousClose || meta.chartPreviousClose || currentPrice;
                const change = meta.regularMarketChange || currentPrice - previousClose;
                const changePercent = meta.regularMarketChangePercent || (previousClose > 0 ? change / previousClose * 100 : 0);
                if (currentPrice > 0) {
                    var _quote_volume;
                    // Get 52-week high/low dates by analyzing historical data
                    let high52WeekDate;
                    let low52WeekDate;
                    try {
                        var _historicalData_chart_result, _historicalData_chart;
                        // Get 1-year historical data to find exact dates
                        const historicalResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("".concat(YAHOO_CHART_URL, "/").concat(symbol), {
                            params: {
                                interval: '1d',
                                range: '1y',
                                includePrePost: false
                            },
                            headers: {
                                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                                'Accept': 'application/json',
                                'Referer': 'https://finance.yahoo.com/'
                            },
                            timeout: 10000
                        });
                        const historicalData = historicalResponse.data;
                        if ((_historicalData_chart = historicalData.chart) === null || _historicalData_chart === void 0 ? void 0 : (_historicalData_chart_result = _historicalData_chart.result) === null || _historicalData_chart_result === void 0 ? void 0 : _historicalData_chart_result[0]) {
                            var _historicalResult_indicators_quote, _historicalResult_indicators;
                            const historicalResult = historicalData.chart.result[0];
                            const timestamps = historicalResult.timestamp;
                            const historicalQuote = (_historicalResult_indicators = historicalResult.indicators) === null || _historicalResult_indicators === void 0 ? void 0 : (_historicalResult_indicators_quote = _historicalResult_indicators.quote) === null || _historicalResult_indicators_quote === void 0 ? void 0 : _historicalResult_indicators_quote[0];
                            if (timestamps && (historicalQuote === null || historicalQuote === void 0 ? void 0 : historicalQuote.high) && (historicalQuote === null || historicalQuote === void 0 ? void 0 : historicalQuote.low)) {
                                const highs = historicalQuote.high;
                                const lows = historicalQuote.low;
                                // Find 52-week high and low with their dates
                                let maxHigh = -Infinity;
                                let minLow = Infinity;
                                let maxHighIndex = -1;
                                let minLowIndex = -1;
                                for(let i = 0; i < highs.length; i++){
                                    if (highs[i] && highs[i] > maxHigh) {
                                        maxHigh = highs[i];
                                        maxHighIndex = i;
                                    }
                                    if (lows[i] && lows[i] < minLow) {
                                        minLow = lows[i];
                                        minLowIndex = i;
                                    }
                                }
                                if (maxHighIndex >= 0 && minLowIndex >= 0) {
                                    high52WeekDate = new Date(timestamps[maxHighIndex] * 1000).toISOString().split('T')[0];
                                    low52WeekDate = new Date(timestamps[minLowIndex] * 1000).toISOString().split('T')[0];
                                }
                            }
                        }
                    } catch (historicalError) {
                        var _historicalError_response;
                        console.warn("⚠️ Could not fetch historical data for ".concat(symbol, ":"), {
                            error: historicalError.message,
                            status: (_historicalError_response = historicalError.response) === null || _historicalError_response === void 0 ? void 0 : _historicalError_response.status,
                            timeout: historicalError.code === 'ECONNABORTED'
                        });
                    }
                    return {
                        symbol: symbol,
                        price: parseFloat(currentPrice.toString()),
                        change: parseFloat(change.toString()),
                        changePercent: parseFloat(changePercent.toString()),
                        volume: parseInt((meta.regularMarketVolume || (quote === null || quote === void 0 ? void 0 : (_quote_volume = quote.volume) === null || _quote_volume === void 0 ? void 0 : _quote_volume[quote.volume.length - 1]) || 0).toString()),
                        marketCap: meta.marketCap,
                        high52Week: parseFloat((meta.fiftyTwoWeekHigh || 0).toString()),
                        low52Week: parseFloat((meta.fiftyTwoWeekLow || 0).toString()),
                        high52WeekDate,
                        low52WeekDate,
                        avgVolume: parseInt((meta.averageDailyVolume3Month || 0).toString())
                    };
                }
            }
            return null;
        } catch (error) {
            console.warn("Failed to get price data for ".concat(symbol, ":"), error);
            return null;
        }
    }
    // Get multiple price data only (batch operation)
    async getMultiplePriceDataOnly(symbols) {
        const results = [];
        const batchSize = 25;
        for(let i = 0; i < symbols.length; i += batchSize){
            const batch = symbols.slice(i, i + batchSize);
            const batchPromises = batch.map((symbol)=>this.getPriceDataOnly(symbol));
            try {
                const batchResults = await Promise.all(batchPromises);
                results.push(...batchResults.filter((result)=>result !== null));
                // Add delay between batches
                if (i + batchSize < symbols.length) {
                    await new Promise((resolve)=>setTimeout(resolve, 200));
                }
            } catch (error) {
                console.error("Batch error for symbols ".concat(batch.join(', '), ":"), error);
            }
        }
        return results;
    }
    // Get quote with cached name (optimized for frequent updates)
    async getQuoteWithCachedName(symbol) {
        try {
            // Get price data only
            const priceData = await this.getPriceDataOnly(symbol);
            if (!priceData) return null;
            // Get name from cache or use fallback
            const name = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stock$2d$names$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["stockNamesService"].getStockNameSync(symbol);
            return {
                ...priceData,
                name
            };
        } catch (error) {
            console.warn("Failed to get quote with cached name for ".concat(symbol, ":"), error);
            return null;
        }
    }
    // Get batch stock prices (alias for compatibility with static data cache)
    async getBatchStockPrices(symbols) {
        try {
            const quotes = await this.getMultipleQuotesWithCachedNames(symbols);
            const results = {};
            quotes.forEach((quote)=>{
                results[quote.symbol.replace('.NS', '')] = {
                    regularMarketPrice: quote.currentPrice,
                    regularMarketChange: quote.change,
                    regularMarketChangePercent: quote.changePercent
                };
            });
            return results;
        } catch (error) {
            console.error('❌ Failed to get batch stock prices:', error);
            return {};
        }
    }
    // Get multiple quotes with cached names (batch operation)
    async getMultipleQuotesWithCachedNames(symbols) {
        try {
            console.log("📊 Fetching quotes with cached names for ".concat(symbols.length, " symbols"));
            // Get price data for all symbols
            const priceDataList = await this.getMultiplePriceDataOnly(symbols);
            console.log("💰 Got price data for ".concat(priceDataList.length, "/").concat(symbols.length, " symbols"));
            // Get cached names for all symbols
            const namesMap = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stock$2d$names$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["stockNamesService"].getStockNames(symbols);
            console.log("📝 Got names for ".concat(namesMap.size, "/").concat(symbols.length, " symbols"));
            // Combine price data with cached names
            const quotes = [];
            for (const priceData of priceDataList){
                const name = namesMap.get(priceData.symbol) || priceData.symbol.replace('.NS', '');
                const quote = {
                    ...priceData,
                    name
                };
                quotes.push(quote);
                // Log BOH eligibility data for debugging
                if (priceData.high52WeekDate && priceData.low52WeekDate) {
                    const highDate = new Date(priceData.high52WeekDate);
                    const lowDate = new Date(priceData.low52WeekDate);
                    const isBOHEligible = lowDate > highDate;
                    console.log("🔍 ".concat(priceData.symbol, ": High=").concat(priceData.high52WeekDate, ", Low=").concat(priceData.low52WeekDate, ", BOH=").concat(isBOHEligible));
                }
            }
            console.log("✅ Combined ".concat(quotes.length, " quotes with cached names"));
            return quotes;
        } catch (error) {
            console.error('❌ Failed to get multiple quotes with cached names:', error);
            return [];
        }
    }
    async getQuote(symbol) {
        try {
            var _data_quoteResponse_result, _data_quoteResponse;
            // Add .NS suffix for NSE stocks if not present
            const formattedSymbol = symbol.includes('.') ? symbol : "".concat(symbol, ".NS");
            const data = await this.makeRequest(YAHOO_QUOTE_URL, {
                symbols: formattedSymbol
            });
            const result = (_data_quoteResponse = data.quoteResponse) === null || _data_quoteResponse === void 0 ? void 0 : (_data_quoteResponse_result = _data_quoteResponse.result) === null || _data_quoteResponse_result === void 0 ? void 0 : _data_quoteResponse_result[0];
            if (!result) return null;
            return {
                symbol: result.symbol,
                name: result.longName || result.shortName || symbol,
                price: result.regularMarketPrice || 0,
                change: result.regularMarketChange || 0,
                changePercent: result.regularMarketChangePercent || 0,
                volume: result.regularMarketVolume || 0,
                marketCap: result.marketCap,
                high52Week: result.fiftyTwoWeekHigh,
                low52Week: result.fiftyTwoWeekLow,
                avgVolume: result.averageDailyVolume3Month
            };
        } catch (error) {
            console.error("Error fetching quote for ".concat(symbol, ":"), error);
            return null;
        }
    }
    async getMultipleQuotes(symbols) {
        console.log("🔍 Yahoo Finance: Fetching quotes for ".concat(symbols.length, " symbols:"), symbols.slice(0, 5));
        try {
            // Format symbols for NSE - ensure .NS suffix
            const formattedSymbols = symbols.map((symbol)=>{
                const formatted = symbol.includes('.') ? symbol : "".concat(symbol, ".NS");
                return formatted;
            });
            console.log("📝 Formatted symbols:", formattedSymbols.slice(0, 5));
            const allResults = [];
            // Process each symbol individually using chart endpoint (more reliable)
            for(let i = 0; i < formattedSymbols.length; i++){
                const symbol = formattedSymbols[i];
                try {
                    console.log("📊 Fetching data for ".concat(symbol, " (").concat(i + 1, "/").concat(formattedSymbols.length, ")"));
                    let stockQuote = null;
                    // Try multiple approaches for better success rate
                    // Approach 1: Chart endpoint (most reliable)
                    try {
                        var _data_chart_result, _data_chart;
                        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("".concat(YAHOO_CHART_URL, "/").concat(symbol), {
                            params: {
                                interval: '1d',
                                range: '1d',
                                includePrePost: false
                            },
                            headers: {
                                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                                'Accept': 'application/json',
                                'Accept-Language': 'en-US,en;q=0.9',
                                'Referer': 'https://finance.yahoo.com/'
                            },
                            timeout: 8000
                        });
                        const data = response.data;
                        if ((_data_chart = data.chart) === null || _data_chart === void 0 ? void 0 : (_data_chart_result = _data_chart.result) === null || _data_chart_result === void 0 ? void 0 : _data_chart_result[0]) {
                            var _result_indicators_quote, _result_indicators;
                            const result = data.chart.result[0];
                            const meta = result.meta;
                            const quote = (_result_indicators = result.indicators) === null || _result_indicators === void 0 ? void 0 : (_result_indicators_quote = _result_indicators.quote) === null || _result_indicators_quote === void 0 ? void 0 : _result_indicators_quote[0];
                            // Extract current price from meta or latest quote data
                            const currentPrice = meta.regularMarketPrice || (quote === null || quote === void 0 ? void 0 : quote.close) && quote.close[quote.close.length - 1] || meta.previousClose || 0;
                            const previousClose = meta.previousClose || meta.chartPreviousClose || currentPrice;
                            // Calculate change and change percent
                            const change = meta.regularMarketChange || currentPrice - previousClose;
                            const changePercent = meta.regularMarketChangePercent || (previousClose > 0 ? change / previousClose * 100 : 0);
                            if (currentPrice > 0) {
                                var _quote_volume;
                                // Get 52-week high/low dates by analyzing historical data
                                let high52WeekDate;
                                let low52WeekDate;
                                try {
                                    var _historicalData_chart_result, _historicalData_chart;
                                    // Get 1-year historical data to find exact dates
                                    const historicalResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("".concat(YAHOO_CHART_URL, "/").concat(symbol), {
                                        params: {
                                            interval: '1d',
                                            range: '1y',
                                            includePrePost: false
                                        },
                                        headers: {
                                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                                            'Accept': 'application/json',
                                            'Referer': 'https://finance.yahoo.com/'
                                        },
                                        timeout: 5000
                                    });
                                    const historicalData = historicalResponse.data;
                                    if ((_historicalData_chart = historicalData.chart) === null || _historicalData_chart === void 0 ? void 0 : (_historicalData_chart_result = _historicalData_chart.result) === null || _historicalData_chart_result === void 0 ? void 0 : _historicalData_chart_result[0]) {
                                        var _historicalResult_indicators_quote, _historicalResult_indicators;
                                        const historicalResult = historicalData.chart.result[0];
                                        const timestamps = historicalResult.timestamp;
                                        const historicalQuote = (_historicalResult_indicators = historicalResult.indicators) === null || _historicalResult_indicators === void 0 ? void 0 : (_historicalResult_indicators_quote = _historicalResult_indicators.quote) === null || _historicalResult_indicators_quote === void 0 ? void 0 : _historicalResult_indicators_quote[0];
                                        if (timestamps && (historicalQuote === null || historicalQuote === void 0 ? void 0 : historicalQuote.high) && (historicalQuote === null || historicalQuote === void 0 ? void 0 : historicalQuote.low)) {
                                            const highs = historicalQuote.high;
                                            const lows = historicalQuote.low;
                                            // Find 52-week high and low with their dates
                                            let maxHigh = -Infinity;
                                            let minLow = Infinity;
                                            let maxHighIndex = -1;
                                            let minLowIndex = -1;
                                            for(let i = 0; i < highs.length; i++){
                                                if (highs[i] && highs[i] > maxHigh) {
                                                    maxHigh = highs[i];
                                                    maxHighIndex = i;
                                                }
                                                if (lows[i] && lows[i] < minLow) {
                                                    minLow = lows[i];
                                                    minLowIndex = i;
                                                }
                                            }
                                            if (maxHighIndex >= 0 && minLowIndex >= 0) {
                                                high52WeekDate = new Date(timestamps[maxHighIndex] * 1000).toISOString().split('T')[0];
                                                low52WeekDate = new Date(timestamps[minLowIndex] * 1000).toISOString().split('T')[0];
                                            }
                                        }
                                    }
                                } catch (historicalError) {
                                    console.log("⚠️ Could not fetch historical data for ".concat(symbol, " dates"));
                                }
                                stockQuote = {
                                    symbol: symbol,
                                    name: meta.longName || meta.shortName || symbol.replace('.NS', ''),
                                    price: parseFloat(currentPrice.toString()),
                                    change: parseFloat(change.toString()),
                                    changePercent: parseFloat(changePercent.toString()),
                                    volume: parseInt((meta.regularMarketVolume || (quote === null || quote === void 0 ? void 0 : (_quote_volume = quote.volume) === null || _quote_volume === void 0 ? void 0 : _quote_volume[quote.volume.length - 1]) || 0).toString()),
                                    marketCap: meta.marketCap,
                                    high52Week: parseFloat((meta.fiftyTwoWeekHigh || 0).toString()),
                                    low52Week: parseFloat((meta.fiftyTwoWeekLow || 0).toString()),
                                    high52WeekDate,
                                    low52WeekDate,
                                    avgVolume: parseInt((meta.averageDailyVolume3Month || 0).toString())
                                };
                                console.log("✅ Chart API success for ".concat(symbol, ": ₹").concat(stockQuote.price));
                            }
                        }
                    } catch (chartError) {
                        var _chartError_response_data_chart_error, _chartError_response_data_chart, _chartError_response_data, _chartError_response;
                        const errorMsg = ((_chartError_response = chartError.response) === null || _chartError_response === void 0 ? void 0 : (_chartError_response_data = _chartError_response.data) === null || _chartError_response_data === void 0 ? void 0 : (_chartError_response_data_chart = _chartError_response_data.chart) === null || _chartError_response_data_chart === void 0 ? void 0 : (_chartError_response_data_chart_error = _chartError_response_data_chart.error) === null || _chartError_response_data_chart_error === void 0 ? void 0 : _chartError_response_data_chart_error.description) || chartError.message;
                        if (errorMsg === null || errorMsg === void 0 ? void 0 : errorMsg.includes('delisted')) {
                            console.log("🚫 ".concat(symbol, " is delisted: ").concat(errorMsg));
                        } else {
                            console.log("⚠️ Chart API failed for ".concat(symbol, ": ").concat(errorMsg, ", trying quote API..."));
                        }
                    }
                    // Approach 2: Quote endpoint (fallback)
                    if (!stockQuote) {
                        try {
                            var _data_quoteResponse_result, _data_quoteResponse;
                            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(YAHOO_QUOTE_URL, {
                                params: {
                                    symbols: symbol,
                                    formatted: true
                                },
                                headers: {
                                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                                    'Accept': 'application/json',
                                    'Referer': 'https://finance.yahoo.com/'
                                },
                                timeout: 8000
                            });
                            const data = response.data;
                            const result = (_data_quoteResponse = data.quoteResponse) === null || _data_quoteResponse === void 0 ? void 0 : (_data_quoteResponse_result = _data_quoteResponse.result) === null || _data_quoteResponse_result === void 0 ? void 0 : _data_quoteResponse_result[0];
                            if (result && result.regularMarketPrice > 0) {
                                stockQuote = {
                                    symbol: symbol,
                                    name: result.longName || result.shortName || symbol.replace('.NS', ''),
                                    price: parseFloat(result.regularMarketPrice) || 0,
                                    change: parseFloat(result.regularMarketChange) || 0,
                                    changePercent: parseFloat(result.regularMarketChangePercent) || 0,
                                    volume: parseInt(result.regularMarketVolume) || 0,
                                    marketCap: result.marketCap,
                                    high52Week: parseFloat(result.fiftyTwoWeekHigh) || 0,
                                    low52Week: parseFloat(result.fiftyTwoWeekLow) || 0,
                                    avgVolume: parseInt(result.averageDailyVolume3Month) || 0
                                };
                                console.log("✅ Quote API success for ".concat(symbol, ": ₹").concat(stockQuote.price));
                            }
                        } catch (quoteError) {
                            console.log("⚠️ Quote API also failed for ".concat(symbol));
                        }
                    }
                    // If we got valid data, add it to results
                    if (stockQuote && stockQuote.price > 0) {
                        allResults.push(stockQuote);
                    } else {
                        console.warn("⚠️ All methods failed for ".concat(symbol, " - creating fallback entry"));
                        // Create a fallback entry instead of skipping
                        allResults.push({
                            symbol: symbol,
                            name: symbol.replace('.NS', ''),
                            price: 0,
                            change: 0,
                            changePercent: 0,
                            volume: 0,
                            high52Week: 0,
                            low52Week: 0,
                            avgVolume: 0
                        });
                    }
                    // Small delay to avoid rate limiting
                    if (i < formattedSymbols.length - 1) {
                        await new Promise((resolve)=>setTimeout(resolve, 150));
                    }
                } catch (symbolError) {
                    console.warn("⚠️ Critical error fetching ".concat(symbol, ":"), symbolError.message);
                    // Create a fallback entry instead of skipping
                    allResults.push({
                        symbol: symbol,
                        name: symbol.replace('.NS', ''),
                        price: 0,
                        change: 0,
                        changePercent: 0,
                        volume: 0,
                        high52Week: 0,
                        low52Week: 0,
                        avgVolume: 0
                    });
                }
            }
            // Check if we have a reasonable success rate
            const successRate = allResults.length / formattedSymbols.length;
            console.log("📊 Success rate: ".concat((successRate * 100).toFixed(1), "% (").concat(allResults.length, "/").concat(formattedSymbols.length, ")"));
            // If success rate is too low, try batch processing for remaining symbols
            if (successRate < 0.8 && allResults.length < formattedSymbols.length) {
                console.log("⚠️ Low success rate, trying batch processing for remaining symbols...");
                const fetchedSymbols = new Set(allResults.map((r)=>r.symbol));
                const remainingSymbols = formattedSymbols.filter((s)=>!fetchedSymbols.has(s));
                if (remainingSymbols.length > 0) {
                    try {
                        // Try batch processing with smaller batches
                        const batchSize = 5;
                        for(let i = 0; i < remainingSymbols.length; i += batchSize){
                            const batch = remainingSymbols.slice(i, i + batchSize);
                            try {
                                var _data_quoteResponse1;
                                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(YAHOO_QUOTE_URL, {
                                    params: {
                                        symbols: batch.join(','),
                                        formatted: true
                                    },
                                    headers: {
                                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                                        'Accept': 'application/json',
                                        'Referer': 'https://finance.yahoo.com/'
                                    },
                                    timeout: 10000
                                });
                                const data = response.data;
                                const results = ((_data_quoteResponse1 = data.quoteResponse) === null || _data_quoteResponse1 === void 0 ? void 0 : _data_quoteResponse1.result) || [];
                                for (const result of results){
                                    if (result && result.regularMarketPrice > 0) {
                                        const batchQuote = {
                                            symbol: result.symbol,
                                            name: result.longName || result.shortName || result.symbol.replace('.NS', ''),
                                            price: parseFloat(result.regularMarketPrice) || 0,
                                            change: parseFloat(result.regularMarketChange) || 0,
                                            changePercent: parseFloat(result.regularMarketChangePercent) || 0,
                                            volume: parseInt(result.regularMarketVolume) || 0,
                                            marketCap: result.marketCap,
                                            high52Week: parseFloat(result.fiftyTwoWeekHigh) || 0,
                                            low52Week: parseFloat(result.fiftyTwoWeekLow) || 0,
                                            avgVolume: parseInt(result.averageDailyVolume3Month) || 0
                                        };
                                        allResults.push(batchQuote);
                                        console.log("✅ Batch recovery success for ".concat(result.symbol, ": ₹").concat(batchQuote.price));
                                    }
                                }
                                // Delay between batches
                                await new Promise((resolve)=>setTimeout(resolve, 200));
                            } catch (batchError) {
                                console.error("❌ Batch processing failed for batch:", batch);
                            }
                        }
                    } catch (error) {
                        console.error("❌ Batch recovery failed:", error);
                    }
                }
            }
            console.log("🎉 Final results: ".concat(allResults.length, " quotes fetched out of ").concat(formattedSymbols.length, " requested"));
            console.log("📊 Sample results:", allResults.slice(0, 3).map((r)=>({
                    symbol: r.symbol,
                    price: r.price,
                    name: r.name
                })));
            // Cache the results
            const cacheKey = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CacheKeys"].yahooQuotes(symbols);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheService"].set(cacheKey, allResults);
            return allResults;
        } catch (error) {
            console.error('❌ Critical error in getMultipleQuotes:', error);
            // Return fallback quotes for all symbols
            return symbols.map((symbol)=>({
                    symbol: symbol.includes('.') ? symbol : "".concat(symbol, ".NS"),
                    name: symbol,
                    price: 0,
                    change: 0,
                    changePercent: 0,
                    volume: 0,
                    marketCap: undefined,
                    high52Week: 0,
                    low52Week: 0,
                    avgVolume: 0
                }));
        }
    }
    async searchStocks(query) {
        try {
            const data = await this.makeRequest(YAHOO_SEARCH_URL, {
                q: query,
                quotesCount: 10,
                newsCount: 0
            });
            const quotes = data.quotes || [];
            return quotes.filter((quote)=>quote.isYahooFinance && quote.symbol).map((quote)=>({
                    symbol: quote.symbol,
                    name: quote.longname || quote.shortname || quote.symbol,
                    exchange: quote.exchange || 'NSE',
                    type: quote.quoteType || 'EQUITY'
                }));
        } catch (error) {
            console.error('Error searching stocks:', error);
            return [];
        }
    }
    // Helper method to get Indian stock symbols
    getIndianStockSymbol(symbol) {
        return symbol.includes('.') ? symbol : "".concat(symbol, ".NS");
    }
    // Helper method to format Indian stock symbols for display
    formatSymbolForDisplay(symbol) {
        return symbol.replace('.NS', '').replace('.BO', '');
    }
    // Get historical data for a symbol using existing quote data
    async getHistoricalData(symbol) {
        let days = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 7;
        try {
            console.log("📊 Getting historical data for ".concat(symbol, " (").concat(days, " days)"));
            // For now, use current quote data and simulate historical data
            // This is a fallback approach since Yahoo Finance historical API is complex
            const currentQuote = await this.getQuoteWithCachedName(symbol);
            if (!currentQuote) {
                console.warn("No current quote found for ".concat(symbol));
                return null;
            }
            // Generate simulated historical data based on current price
            // This is a simplified approach for weekly high calculation
            const historicalData = [];
            const basePrice = currentQuote.price;
            const baseVolume = currentQuote.volume || 1000000;
            for(let i = days - 1; i >= 0; i--){
                const date = new Date();
                date.setDate(date.getDate() - i);
                // Simulate price variation (±5% from current price)
                const variation = (Math.random() - 0.5) * 0.1; // ±5%
                const dayPrice = basePrice * (1 + variation);
                // Simulate intraday high/low (±2% from day price)
                const intraVariation = Math.random() * 0.04; // 0-4%
                const high = dayPrice * (1 + intraVariation);
                const low = dayPrice * (1 - intraVariation);
                // Simulate volume variation
                const volumeVariation = (Math.random() - 0.5) * 0.4; // ±20%
                const volume = Math.floor(baseVolume * (1 + volumeVariation));
                historicalData.push({
                    date,
                    open: dayPrice,
                    high: Math.max(high, dayPrice),
                    low: Math.min(low, dayPrice),
                    close: dayPrice,
                    volume: Math.max(volume, 100000) // Minimum volume
                });
            }
            // Ensure the most recent day has the current price as high
            if (historicalData.length > 0) {
                const lastDay = historicalData[historicalData.length - 1];
                lastDay.high = Math.max(lastDay.high, currentQuote.price);
                lastDay.close = currentQuote.price;
                lastDay.volume = currentQuote.volume || lastDay.volume;
            }
            console.log("✅ Generated ".concat(historicalData.length, " days of historical data for ").concat(symbol));
            return historicalData;
        } catch (error) {
            console.error("❌ Error generating historical data for ".concat(symbol, ":"), error);
            return null;
        }
    }
    constructor(){
        // Real-time update system
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "updateListeners", new Set());
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "isRealTimeActive", false);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "realTimeInterval", null);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "lastUpdateTime", 0);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "currentSymbols", []);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "cache", new Map());
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "CACHE_DURATION", 30 * 1000); // 30 seconds
    }
}
const yahooFinanceService = new YahooFinanceService();
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/background-data-service.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Background data service for preloading and auto-updating stock data
__turbopack_context__.s({
    "backgroundDataService": ()=>backgroundDataService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stock$2d$names$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stock-names-service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/nifty-stocks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$yahoo$2d$finance$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/yahoo-finance.ts [app-client] (ecmascript)");
;
;
;
;
class BackgroundDataService {
    // Initialize background service
    async initialize() {
        if (this.isInitialized) return;
        console.log('🚀 Initializing background data service...');
        try {
            // Step 1: Preload all stock names immediately
            await this.preloadAllStockNames();
            // Step 2: Start automatic price updates
            this.startPriceUpdates();
            // Step 3: Start automatic name refresh (before 24h expiry)
            this.startNameRefresh();
            this.isInitialized = true;
            console.log('✅ Background data service initialized successfully');
        } catch (error) {
            console.error('❌ Failed to initialize background data service:', error);
            this.notifyError(error);
        }
    }
    // Preload all Nifty 200 stock names
    async preloadAllStockNames() {
        console.log('📝 Preloading all Nifty 200 stock names...');
        try {
            const yahooSymbols = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NIFTY_200_SYMBOLS"].map(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getYahooSymbol"]).filter((s)=>s !== null);
            // Check if names are already cached
            const cachedCount = yahooSymbols.filter((symbol)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stock$2d$names$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["stockNamesService"].isNameCached(symbol)).length;
            if (cachedCount === yahooSymbols.length) {
                console.log('✅ All stock names already cached');
                return;
            }
            console.log("📊 Found ".concat(cachedCount, "/").concat(yahooSymbols.length, " names in cache, fetching remaining..."));
            // Preload all names with better error handling
            try {
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stock$2d$names$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["stockNamesService"].preloadStockNames(yahooSymbols);
                // Get the names map and notify listeners
                const namesMap = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stock$2d$names$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["stockNamesService"].getStockNames(yahooSymbols);
                this.notifyNamesUpdate(namesMap);
                console.log("✅ Preloaded ".concat(namesMap.size, " stock names"));
            } catch (preloadError) {
                console.warn('⚠️ Some stocks failed during preloading, but continuing with available data:', preloadError);
                // Still try to get whatever names we have
                try {
                    const namesMap = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stock$2d$names$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["stockNamesService"].getStockNames(yahooSymbols);
                    this.notifyNamesUpdate(namesMap);
                    console.log("✅ Loaded ".concat(namesMap.size, " stock names (with some failures)"));
                } catch (fallbackError) {
                    console.error('❌ Failed to load any stock names:', fallbackError);
                    // Create a basic names map with fallback names
                    const fallbackNamesMap = new Map();
                    yahooSymbols.forEach((symbol)=>{
                        fallbackNamesMap.set(symbol, symbol.replace('.NS', ''));
                    });
                    this.notifyNamesUpdate(fallbackNamesMap);
                    console.log("✅ Using fallback names for ".concat(fallbackNamesMap.size, " stocks"));
                }
            }
        } catch (error) {
            console.error('❌ Failed to preload stock names:', error);
            throw error;
        }
    }
    // Start automatic price updates
    startPriceUpdates() {
        if (this.priceUpdateInterval) {
            clearInterval(this.priceUpdateInterval);
        }
        console.log("🔄 Starting automatic price updates every ".concat(this.PRICE_UPDATE_INTERVAL / 1000, "s"));
        // Initial update
        this.updatePriceData();
        // Set up recurring updates
        this.priceUpdateInterval = setInterval(()=>{
            this.updatePriceData();
        }, this.PRICE_UPDATE_INTERVAL);
    }
    // Update price data in background
    async updatePriceData() {
        if (this.isUpdating) {
            console.log('⏳ Price update already in progress, skipping...');
            return;
        }
        this.isUpdating = true;
        try {
            console.log('💰 Updating price data in background...');
            const yahooSymbols = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NIFTY_200_SYMBOLS"].map(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getYahooSymbol"]).filter((s)=>s !== null);
            const allPriceData = [];
            // Process in batches to avoid overwhelming the API
            for(let i = 0; i < yahooSymbols.length; i += this.BATCH_SIZE){
                const batch = yahooSymbols.slice(i, i + this.BATCH_SIZE);
                try {
                    const { yahooFinanceService } = await __turbopack_context__.r("[project]/src/lib/yahoo-finance.ts [app-client] (ecmascript, async loader)")(__turbopack_context__.i);
                    const batchData = await yahooFinanceService.getMultipleQuotesWithCachedNames(batch);
                    allPriceData.push(...batchData);
                    console.log("📊 Updated batch ".concat(Math.floor(i / this.BATCH_SIZE) + 1, "/").concat(Math.ceil(yahooSymbols.length / this.BATCH_SIZE), ": ").concat(batchData.length, " stocks"));
                    // Add delay between batches
                    if (i + this.BATCH_SIZE < yahooSymbols.length) {
                        await new Promise((resolve)=>setTimeout(resolve, this.BATCH_DELAY));
                    }
                } catch (batchError) {
                    console.warn("⚠️ Failed to update batch starting at index ".concat(i, ":"), batchError);
                }
            }
            // Notify all listeners of the price update
            this.notifyPriceUpdate(allPriceData);
            console.log("✅ Background price update completed: ".concat(allPriceData.length, " stocks updated"));
        } catch (error) {
            console.error('❌ Background price update failed:', error);
            this.notifyError(error);
        } finally{
            this.isUpdating = false;
        }
    }
    // Start automatic name refresh (before cache expiry)
    startNameRefresh() {
        if (this.nameRefreshInterval) {
            clearInterval(this.nameRefreshInterval);
        }
        console.log("🔄 Starting automatic name refresh every ".concat(this.NAME_REFRESH_INTERVAL / (60 * 60 * 1000), "h"));
        this.nameRefreshInterval = setInterval(async ()=>{
            console.log('🔄 Refreshing stock names before cache expiry...');
            try {
                await this.preloadAllStockNames();
            } catch (error) {
                console.error('❌ Failed to refresh stock names:', error);
                this.notifyError(error);
            }
        }, this.NAME_REFRESH_INTERVAL);
    }
    // Add listener for background updates
    addListener(listener) {
        this.listeners.push(listener);
    }
    // Remove listener
    removeListener(listener) {
        const index = this.listeners.indexOf(listener);
        if (index > -1) {
            this.listeners.splice(index, 1);
        }
    }
    // Notify listeners of price updates
    notifyPriceUpdate(data) {
        this.listeners.forEach((listener)=>{
            try {
                listener.onPriceUpdate(data);
            } catch (error) {
                console.error('❌ Error in price update listener:', error);
            }
        });
    }
    // Notify listeners of name updates
    notifyNamesUpdate(namesMap) {
        this.listeners.forEach((listener)=>{
            try {
                listener.onNamesUpdate(namesMap);
            } catch (error) {
                console.error('❌ Error in names update listener:', error);
            }
        });
    }
    // Notify listeners of errors
    notifyError(error) {
        this.listeners.forEach((listener)=>{
            try {
                listener.onError(error);
            } catch (listenerError) {
                console.error('❌ Error in error listener:', listenerError);
            }
        });
    }
    // Get current status
    getStatus() {
        return {
            initialized: this.isInitialized,
            updating: this.isUpdating,
            listenersCount: this.listeners.length,
            cacheStats: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stock$2d$names$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["stockNamesService"].getNamesCacheStats()
        };
    }
    // Force immediate update
    async forceUpdate() {
        console.log('🔄 Forcing immediate data update...');
        await this.preloadAllStockNames();
        await this.updatePriceData();
    }
    // Stop all background processes
    stop() {
        console.log('🛑 Stopping background data service...');
        if (this.priceUpdateInterval) {
            clearInterval(this.priceUpdateInterval);
            this.priceUpdateInterval = null;
        }
        if (this.nameRefreshInterval) {
            clearInterval(this.nameRefreshInterval);
            this.nameRefreshInterval = null;
        }
        this.listeners = [];
        this.isInitialized = false;
        this.isUpdating = false;
        console.log('✅ Background data service stopped');
    }
    // Check if stock names are ready
    areNamesReady() {
        const yahooSymbols = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NIFTY_200_SYMBOLS"].map(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getYahooSymbol"]).filter((s)=>s !== null);
        const cachedCount = yahooSymbols.filter((symbol)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stock$2d$names$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["stockNamesService"].isNameCached(symbol)).length;
        return cachedCount === yahooSymbols.length;
    }
    // Real-time update methods
    addRealTimeListener(listener) {
        this.realTimeListeners.push(listener);
        if (!this.realTimeActive) {
            this.startRealTimeUpdates();
        }
    }
    removeRealTimeListener(listener) {
        const index = this.realTimeListeners.indexOf(listener);
        if (index > -1) {
            this.realTimeListeners.splice(index, 1);
        }
        if (this.realTimeListeners.length === 0) {
            this.stopRealTimeUpdates();
        }
    }
    startRealTimeUpdates() {
        if (this.realTimeActive) return;
        console.log('🔄 Starting real-time price updates...');
        this.realTimeActive = true;
        // Get all Nifty 200 symbols
        const allSymbols = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NIFTY_200_SYMBOLS"].map((symbol)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getYahooSymbol"])(symbol));
        // Start Yahoo Finance real-time updates
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$yahoo$2d$finance$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["yahooFinanceService"].startRealTimeUpdates(allSymbols, (quotes)=>{
            // Notify all real-time listeners
            this.realTimeListeners.forEach((listener)=>{
                try {
                    listener.onRealTimeUpdate(quotes);
                } catch (error) {
                    console.error('❌ Error in real-time listener:', error);
                    listener.onError(error);
                }
            });
        });
    }
    stopRealTimeUpdates() {
        if (!this.realTimeActive) return;
        console.log('⏹️ Stopping real-time price updates...');
        this.realTimeActive = false;
        // Stop Yahoo Finance real-time updates
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$yahoo$2d$finance$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["yahooFinanceService"].stopRealTimeUpdates(()=>{});
    }
    // Force immediate update for all real-time listeners
    async forceRealTimeUpdate() {
        if (!this.realTimeActive || this.realTimeListeners.length === 0) return;
        try {
            console.log('🔄 Forcing immediate real-time update...');
            const allSymbols = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NIFTY_200_SYMBOLS"].map((symbol)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getYahooSymbol"])(symbol));
            const quotes = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$yahoo$2d$finance$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["yahooFinanceService"].getMultipleQuotesWithCachedNames(allSymbols);
            this.realTimeListeners.forEach((listener)=>{
                try {
                    listener.onRealTimeUpdate(quotes);
                } catch (error) {
                    console.error('❌ Error in forced real-time update listener:', error);
                    listener.onError(error);
                }
            });
            console.log("✅ Forced real-time update completed: ".concat(quotes.length, " quotes"));
        } catch (error) {
            console.error('❌ Failed to force real-time update:', error);
            this.realTimeListeners.forEach((listener)=>{
                listener.onError(error);
            });
        }
    }
    constructor(){
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "listeners", []);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "realTimeListeners", []);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "priceUpdateInterval", null);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "nameRefreshInterval", null);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "isInitialized", false);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "isUpdating", false);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "realTimeActive", false);
        // Configuration
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "PRICE_UPDATE_INTERVAL", 30 * 1000); // 30 seconds
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "NAME_REFRESH_INTERVAL", 23 * 60 * 60 * 1000); // 23 hours (refresh before 24h expiry)
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "BATCH_SIZE", 25);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "BATCH_DELAY", 200); // ms between batches
    }
}
const backgroundDataService = new BackgroundDataService();
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/providers/BackgroundDataProvider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "BackgroundDataProvider": ()=>BackgroundDataProvider,
    "useBackgroundDataContext": ()=>useBackgroundDataContext
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$background$2d$data$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/background-data-service.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
const BackgroundDataContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function BackgroundDataProvider(param) {
    let { children } = param;
    _s();
    const [isInitialized, setIsInitialized] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isUpdating, setIsUpdating] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BackgroundDataProvider.useEffect": ()=>{
            let mounted = true;
            const initializeBackgroundService = {
                "BackgroundDataProvider.useEffect.initializeBackgroundService": async ()=>{
                    try {
                        console.log('🚀 Initializing background data service from provider...');
                        // Initialize the background service
                        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$background$2d$data$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["backgroundDataService"].initialize();
                        if (mounted) {
                            setIsInitialized(true);
                            setError(null);
                            console.log('✅ Background data service initialized successfully');
                        }
                    } catch (err) {
                        console.error('❌ Failed to initialize background data service:', err);
                        if (mounted) {
                            setError(err);
                            // Still set as initialized to allow pages to work
                            setIsInitialized(true);
                        }
                    }
                }
            }["BackgroundDataProvider.useEffect.initializeBackgroundService"];
            // Start initialization after a short delay to avoid blocking initial render
            const initTimer = setTimeout({
                "BackgroundDataProvider.useEffect.initTimer": ()=>{
                    initializeBackgroundService();
                }
            }["BackgroundDataProvider.useEffect.initTimer"], 500);
            // Cleanup
            return ({
                "BackgroundDataProvider.useEffect": ()=>{
                    mounted = false;
                    clearTimeout(initTimer);
                }
            })["BackgroundDataProvider.useEffect"];
        }
    }["BackgroundDataProvider.useEffect"], []);
    const forceUpdate = async ()=>{
        setIsUpdating(true);
        setError(null);
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$background$2d$data$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["backgroundDataService"].forceUpdate();
        } catch (err) {
            setError(err);
            throw err;
        } finally{
            setIsUpdating(false);
        }
    };
    const contextValue = {
        isInitialized,
        isUpdating,
        error,
        forceUpdate
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(BackgroundDataContext.Provider, {
        value: contextValue,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/providers/BackgroundDataProvider.tsx",
        lineNumber: 83,
        columnNumber: 5
    }, this);
}
_s(BackgroundDataProvider, "3oUd0MzKJZ2hiRtwTHw5vVLoSE4=");
_c = BackgroundDataProvider;
function useBackgroundDataContext() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(BackgroundDataContext);
    if (context === undefined) {
        throw new Error('useBackgroundDataContext must be used within a BackgroundDataProvider');
    }
    return context;
}
_s1(useBackgroundDataContext, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "BackgroundDataProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/AppInitializer.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AppInitializer": ()=>AppInitializer,
    "useAppInitialization": ()=>useAppInitialization
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader.js [app-client] (ecmascript) <export default as Loader>");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
function AppInitializer(param) {
    let { children } = param;
    _s();
    const [isInitialized, setIsInitialized] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isInitializing, setIsInitializing] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const initializeApp = async ()=>{
        try {
            console.log('🚀 Simple initialization...');
            setIsInitializing(true);
            // Simple delay to simulate initialization
            await new Promise((resolve)=>setTimeout(resolve, 1000));
            setIsInitialized(true);
            setIsInitializing(false);
            console.log('✅ Simple initialization completed');
        } catch (error) {
            console.error('❌ Initialization failed:', error);
            setIsInitializing(false);
            setIsInitialized(false);
        }
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AppInitializer.useEffect": ()=>{
            // Check for bypass parameter
            const urlParams = new URLSearchParams(window.location.search);
            const bypassInit = urlParams.get('bypass') === 'true';
            if (bypassInit) {
                console.log('🔄 Bypassing initialization...');
                setIsInitialized(true);
                setIsInitializing(false);
                return;
            }
            // Start simple initialization
            initializeApp();
        }
    }["AppInitializer.useEffect"], []);
    // Show initialization screen while loading
    if (isInitializing) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gray-50 flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mb-6",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader$3e$__["Loader"], {
                                className: "h-12 w-12 animate-spin text-blue-600 mx-auto"
                            }, void 0, false, {
                                fileName: "[project]/src/components/AppInitializer.tsx",
                                lineNumber: 52,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/AppInitializer.tsx",
                            lineNumber: 51,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-xl font-semibold text-gray-900 mb-2",
                            children: "Initializing Niveshtor Trading"
                        }, void 0, false, {
                            fileName: "[project]/src/components/AppInitializer.tsx",
                            lineNumber: 55,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-600 mb-6",
                            children: "Setting up the application..."
                        }, void 0, false, {
                            fileName: "[project]/src/components/AppInitializer.tsx",
                            lineNumber: 58,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/AppInitializer.tsx",
                    lineNumber: 50,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/AppInitializer.tsx",
                lineNumber: 49,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/AppInitializer.tsx",
            lineNumber: 48,
            columnNumber: 7
        }, this);
    }
    // Show main app if initialized
    if (isInitialized) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
            children: children
        }, void 0, false);
    }
    // Fallback loading state
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gray-50 flex items-center justify-center",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "text-center",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader$3e$__["Loader"], {
                    className: "h-8 w-8 animate-spin text-blue-600 mx-auto mb-4"
                }, void 0, false, {
                    fileName: "[project]/src/components/AppInitializer.tsx",
                    lineNumber: 76,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-gray-600",
                    children: "Loading application..."
                }, void 0, false, {
                    fileName: "[project]/src/components/AppInitializer.tsx",
                    lineNumber: 77,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/AppInitializer.tsx",
            lineNumber: 75,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/AppInitializer.tsx",
        lineNumber: 74,
        columnNumber: 5
    }, this);
}
_s(AppInitializer, "/rB8aQTTs0cA6z5mjrwC3Bbj4Qo=");
_c = AppInitializer;
function useAppInitialization() {
    _s1();
    const [isInitialized, setIsInitialized] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isChecking, setIsChecking] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useAppInitialization.useEffect": ()=>{
            const checkInitialization = {
                "useAppInitialization.useEffect.checkInitialization": async ()=>{
                    try {
                        const response = await fetch('/api/initialize-app');
                        const result = await response.json();
                        setIsInitialized(result.success && result.isFullyInitialized);
                    } catch (error) {
                        console.error('Error checking initialization:', error);
                        setIsInitialized(false);
                    } finally{
                        setIsChecking(false);
                    }
                }
            }["useAppInitialization.useEffect.checkInitialization"];
            checkInitialization();
        }
    }["useAppInitialization.useEffect"], []);
    return {
        isInitialized,
        isChecking
    };
}
_s1(useAppInitialization, "VR2x2VghR+t4b3iaBmQRgF/HdDU=");
var _c;
__turbopack_context__.k.register(_c, "AppInitializer");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/PerformanceMonitor.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "PerformanceMonitor": ()=>PerformanceMonitor,
    "usePerformanceReporting": ()=>usePerformanceReporting
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-client] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$zap$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Zap$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/zap.js [app-client] (ecmascript) <export default as Zap>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/triangle-alert.js [app-client] (ecmascript) <export default as AlertTriangle>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
function PerformanceMonitor() {
    _s();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    const [metrics, setMetrics] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [currentMetric, setCurrentMetric] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [showMonitor, setShowMonitor] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [navigationStart, setNavigationStart] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    // Track navigation performance
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PerformanceMonitor.useEffect": ()=>{
            const startTime = performance.now();
            setNavigationStart(startTime);
            // Measure when page is fully loaded
            const measurePerformance = {
                "PerformanceMonitor.useEffect.measurePerformance": ()=>{
                    const endTime = performance.now();
                    const totalTime = endTime - startTime;
                    const metric = {
                        navigationTime: totalTime,
                        dataLoadTime: 0,
                        totalTime,
                        isInstant: totalTime < 200,
                        timestamp: new Date(),
                        page: pathname
                    };
                    setCurrentMetric(metric);
                    setMetrics({
                        "PerformanceMonitor.useEffect.measurePerformance": (prev)=>[
                                ...prev.slice(-9),
                                metric
                            ]
                    }["PerformanceMonitor.useEffect.measurePerformance"]); // Keep last 10 metrics
                    console.log("⚡ Navigation to ".concat(pathname, ": ").concat(totalTime.toFixed(2), "ms ").concat(totalTime < 200 ? '(INSTANT)' : '(SLOW)'));
                }
            }["PerformanceMonitor.useEffect.measurePerformance"];
            // Measure after a short delay to allow for data loading
            const timer = setTimeout(measurePerformance, 100);
            return ({
                "PerformanceMonitor.useEffect": ()=>clearTimeout(timer)
            })["PerformanceMonitor.useEffect"];
        }
    }["PerformanceMonitor.useEffect"], [
        pathname
    ]);
    // Calculate performance stats
    const getPerformanceStats = ()=>{
        var _sortedByTime_, _sortedByTime_1;
        if (metrics.length === 0) {
            return {
                averageNavigationTime: 0,
                instantNavigations: 0,
                totalNavigations: 0,
                slowestPage: '',
                fastestPage: ''
            };
        }
        const totalTime = metrics.reduce((sum, m)=>sum + m.navigationTime, 0);
        const instantCount = metrics.filter((m)=>m.isInstant).length;
        const sortedByTime = [
            ...metrics
        ].sort((a, b)=>a.navigationTime - b.navigationTime);
        return {
            averageNavigationTime: totalTime / metrics.length,
            instantNavigations: instantCount,
            totalNavigations: metrics.length,
            slowestPage: ((_sortedByTime_ = sortedByTime[sortedByTime.length - 1]) === null || _sortedByTime_ === void 0 ? void 0 : _sortedByTime_.page) || '',
            fastestPage: ((_sortedByTime_1 = sortedByTime[0]) === null || _sortedByTime_1 === void 0 ? void 0 : _sortedByTime_1.page) || ''
        };
    };
    const stats = getPerformanceStats();
    // Show/hide monitor with keyboard shortcut
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PerformanceMonitor.useEffect": ()=>{
            const handleKeyPress = {
                "PerformanceMonitor.useEffect.handleKeyPress": (e)=>{
                    if (e.ctrlKey && e.shiftKey && e.key === 'P') {
                        setShowMonitor({
                            "PerformanceMonitor.useEffect.handleKeyPress": (prev)=>!prev
                        }["PerformanceMonitor.useEffect.handleKeyPress"]);
                    }
                }
            }["PerformanceMonitor.useEffect.handleKeyPress"];
            window.addEventListener('keydown', handleKeyPress);
            return ({
                "PerformanceMonitor.useEffect": ()=>window.removeEventListener('keydown', handleKeyPress)
            })["PerformanceMonitor.useEffect"];
        }
    }["PerformanceMonitor.useEffect"], []);
    if (!showMonitor) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "fixed bottom-4 right-4 z-50",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                onClick: ()=>setShowMonitor(true),
                className: "bg-gray-800 text-white p-2 rounded-full shadow-lg hover:bg-gray-700 transition-colors",
                title: "Show Performance Monitor (Ctrl+Shift+P)",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$zap$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Zap$3e$__["Zap"], {
                    className: "h-4 w-4"
                }, void 0, false, {
                    fileName: "[project]/src/components/PerformanceMonitor.tsx",
                    lineNumber: 110,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                lineNumber: 105,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/PerformanceMonitor.tsx",
            lineNumber: 104,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed bottom-4 right-4 z-50 bg-white rounded-lg shadow-xl border border-gray-200 p-4 w-80",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-between mb-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center space-x-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$zap$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Zap$3e$__["Zap"], {
                                className: "h-4 w-4 text-blue-600"
                            }, void 0, false, {
                                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                lineNumber: 120,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-semibold text-gray-900",
                                children: "Performance Monitor"
                            }, void 0, false, {
                                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                lineNumber: 121,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/PerformanceMonitor.tsx",
                        lineNumber: 119,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>setShowMonitor(false),
                        className: "text-gray-400 hover:text-gray-600",
                        children: "×"
                    }, void 0, false, {
                        fileName: "[project]/src/components/PerformanceMonitor.tsx",
                        lineNumber: 123,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                lineNumber: 118,
                columnNumber: 7
            }, this),
            currentMetric && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-4 p-3 bg-gray-50 rounded-lg",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center space-x-2 mb-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                                className: "h-4 w-4 text-gray-600"
                            }, void 0, false, {
                                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                lineNumber: 135,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-sm font-medium text-gray-900",
                                children: "Current Page"
                            }, void 0, false, {
                                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                lineNumber: 136,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/PerformanceMonitor.tsx",
                        lineNumber: 134,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-xs text-gray-600 mb-1",
                        children: currentMetric.page
                    }, void 0, false, {
                        fileName: "[project]/src/components/PerformanceMonitor.tsx",
                        lineNumber: 138,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-lg font-bold ".concat(currentMetric.isInstant ? 'text-green-600' : 'text-orange-600'),
                        children: [
                            currentMetric.navigationTime.toFixed(0),
                            "ms",
                            currentMetric.isInstant ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded",
                                children: "INSTANT"
                            }, void 0, false, {
                                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                lineNumber: 142,
                                columnNumber: 15
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "ml-2 text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded",
                                children: "SLOW"
                            }, void 0, false, {
                                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                lineNumber: 144,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/PerformanceMonitor.tsx",
                        lineNumber: 139,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                lineNumber: 133,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-2 text-sm",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex justify-between",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-gray-600",
                                children: "Average Time:"
                            }, void 0, false, {
                                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                lineNumber: 153,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    stats.averageNavigationTime.toFixed(0),
                                    "ms"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                lineNumber: 154,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/PerformanceMonitor.tsx",
                        lineNumber: 152,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex justify-between",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-gray-600",
                                children: "Instant Navigation:"
                            }, void 0, false, {
                                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                lineNumber: 157,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    stats.instantNavigations,
                                    "/",
                                    stats.totalNavigations,
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "ml-1 text-xs text-gray-500",
                                        children: [
                                            "(",
                                            stats.totalNavigations > 0 ? Math.round(stats.instantNavigations / stats.totalNavigations * 100) : 0,
                                            "%)"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                        lineNumber: 160,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                lineNumber: 158,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/PerformanceMonitor.tsx",
                        lineNumber: 156,
                        columnNumber: 9
                    }, this),
                    stats.fastestPage && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex justify-between",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-gray-600",
                                children: "Fastest:"
                            }, void 0, false, {
                                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                lineNumber: 167,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium text-green-600 text-xs",
                                children: stats.fastestPage
                            }, void 0, false, {
                                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                lineNumber: 168,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/PerformanceMonitor.tsx",
                        lineNumber: 166,
                        columnNumber: 11
                    }, this),
                    stats.slowestPage && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex justify-between",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-gray-600",
                                children: "Slowest:"
                            }, void 0, false, {
                                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                lineNumber: 173,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium text-orange-600 text-xs",
                                children: stats.slowestPage
                            }, void 0, false, {
                                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                lineNumber: 174,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/PerformanceMonitor.tsx",
                        lineNumber: 172,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                lineNumber: 151,
                columnNumber: 7
            }, this),
            metrics.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-4 pt-3 border-t border-gray-200",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-xs font-medium text-gray-700 mb-2",
                        children: "Recent Navigations"
                    }, void 0, false, {
                        fileName: "[project]/src/components/PerformanceMonitor.tsx",
                        lineNumber: 182,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-1",
                        children: metrics.slice(-5).reverse().map((metric, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex justify-between items-center text-xs",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-gray-600 truncate flex-1 mr-2",
                                        children: metric.page.split('/').pop() || 'dashboard'
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                        lineNumber: 186,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium ".concat(metric.isInstant ? 'text-green-600' : 'text-orange-600'),
                                        children: [
                                            metric.navigationTime.toFixed(0),
                                            "ms"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                        lineNumber: 189,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, index, true, {
                                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                lineNumber: 185,
                                columnNumber: 15
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/components/PerformanceMonitor.tsx",
                        lineNumber: 183,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                lineNumber: 181,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-3 pt-3 border-t border-gray-200",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center space-x-2 text-xs",
                    children: stats.averageNavigationTime < 200 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$zap$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Zap$3e$__["Zap"], {
                                className: "h-3 w-3 text-green-500"
                            }, void 0, false, {
                                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                lineNumber: 203,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-green-700",
                                children: "Target: <200ms ✓"
                            }, void 0, false, {
                                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                lineNumber: 204,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__["AlertTriangle"], {
                                className: "h-3 w-3 text-orange-500"
                            }, void 0, false, {
                                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                lineNumber: 208,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-orange-700",
                                children: "Target: <200ms"
                            }, void 0, false, {
                                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                lineNumber: 209,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true)
                }, void 0, false, {
                    fileName: "[project]/src/components/PerformanceMonitor.tsx",
                    lineNumber: 200,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                lineNumber: 199,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-2 text-xs text-gray-500",
                children: "Press Ctrl+Shift+P to toggle"
            }, void 0, false, {
                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                lineNumber: 215,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/PerformanceMonitor.tsx",
        lineNumber: 117,
        columnNumber: 5
    }, this);
}
_s(PerformanceMonitor, "UvPvc6VDF1xUy24YAZulwtWsmrA=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"]
    ];
});
_c = PerformanceMonitor;
function usePerformanceReporting() {
    const reportDataLoadTime = (loadTime, dataType)=>{
        console.log("📊 Data loading time for ".concat(dataType, ": ").concat(loadTime.toFixed(2), "ms"));
    };
    return {
        reportDataLoadTime
    };
}
var _c;
__turbopack_context__.k.register(_c, "PerformanceMonitor");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_71869a0f._.js.map