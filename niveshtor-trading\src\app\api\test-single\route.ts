import { NextRequest, NextResponse } from 'next/server';
import { yahooFinanceService } from '@/lib/yahoo-finance';

export async function GET(request: NextRequest) {
  try {
    const symbol = 'RELIANCE.NS';
    
    console.log('🧪 Testing single stock:', symbol);
    
    // Test the price data only method
    const priceData = await yahooFinanceService.getPriceDataOnly(symbol);
    
    if (!priceData) {
      return NextResponse.json({
        success: false,
        error: 'No price data returned'
      });
    }
    
    return NextResponse.json({
      success: true,
      data: {
        symbol: priceData.symbol,
        price: priceData.price,
        high52Week: priceData.high52Week,
        low52Week: priceData.low52Week,
        high52WeekDate: priceData.high52WeekDate,
        low52WeekDate: priceData.low52WeekDate,
        hasDates: !!(priceData.high52WeekDate && priceData.low52WeekDate),
        isBOHEligible: priceData.high52WeekDate && priceData.low52WeekDate ? 
          new Date(priceData.low52WeekDate) > new Date(priceData.high52WeekDate) : false
      }
    });
    
  } catch (error) {
    console.error('❌ Test single error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
