module.exports = {

"[project]/.next-internal/server/app/api/test-gtt-workflow/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/api/test-gtt-workflow/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GET": ()=>GET
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
;
async function GET(request) {
    try {
        console.log('🧪 Testing complete GTT workflow...');
        const requestUrl = new URL(request.url);
        const baseUrl = `${requestUrl.protocol}//${requestUrl.host}`;
        // Step 1: Test Nifty 200 API
        console.log('📊 Step 1: Testing Nifty 200 API...');
        const niftyResponse = await fetch(`${baseUrl}/api/stocks/nifty200?batchIndex=0&batchSize=50`);
        const niftyData = await niftyResponse.json();
        if (!niftyData.success) {
            throw new Error('Nifty 200 API failed');
        }
        const bohEligibleCount = niftyData.data.stocks.filter((stock)=>stock.isBOHEligible).length;
        console.log(`✅ Nifty 200 API: ${niftyData.data.stocks.length} stocks, ${bohEligibleCount} BOH eligible`);
        // Step 2: Test GTT Create Signal Orders API
        console.log('🎯 Step 2: Testing GTT Create Signal Orders API...');
        const gttResponse = await fetch(`${baseUrl}/api/gtt/create-signal-orders`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        const gttData = await gttResponse.json();
        if (!gttData.success) {
            throw new Error('GTT Create Signal Orders API failed');
        }
        console.log(`✅ GTT API: ${gttData.data.totalBOHStocks} BOH stocks, ${gttData.data.validForGTT} valid for GTT`);
        // Step 3: Validate data consistency
        console.log('🔍 Step 3: Validating data consistency...');
        const sampleOrders = gttData.data.orders.slice(0, 5);
        const validationResults = sampleOrders.map((order)=>({
                symbol: order.symbol,
                triggerPrice: order.triggerPrice,
                quantity: order.quantity,
                isValid: order.triggerPrice > 0 && order.quantity > 0,
                estimatedValue: order.triggerPrice * order.quantity
            }));
        const allValid = validationResults.every((r)=>r.isValid);
        console.log(`✅ Validation: ${allValid ? 'All orders valid' : 'Some orders invalid'}`);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            message: 'GTT workflow test completed successfully',
            results: {
                step1_nifty200: {
                    totalStocks: niftyData.data.stocks.length,
                    bohEligible: bohEligibleCount,
                    status: 'SUCCESS'
                },
                step2_gtt_api: {
                    totalBOHStocks: gttData.data.totalBOHStocks,
                    validForGTT: gttData.data.validForGTT,
                    avgTriggerPrice: gttData.data.stats.avgTriggerPrice,
                    totalValue: gttData.data.stats.totalValue,
                    status: 'SUCCESS'
                },
                step3_validation: {
                    sampleOrders: validationResults,
                    allValid,
                    status: allValid ? 'SUCCESS' : 'WARNING'
                },
                overall: {
                    status: 'SUCCESS',
                    readyForProduction: allValid && gttData.data.validForGTT > 0
                }
            }
        });
    } catch (error) {
        console.error('❌ GTT workflow test failed:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: error instanceof Error ? error.message : 'Test failed',
            message: 'GTT workflow test failed'
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__e4f49859._.js.map