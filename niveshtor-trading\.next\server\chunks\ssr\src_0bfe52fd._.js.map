{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/components/layout/dashboard-layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const DashboardLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call DashboardLayout() from the server but DashboardLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/dashboard-layout.tsx <module evaluation>\",\n    \"DashboardLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,4EACA", "debugId": null}}, {"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/components/layout/dashboard-layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const DashboardLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call DashboardLayout() from the server but DashboardLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/dashboard-layout.tsx\",\n    \"DashboardLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,wDACA", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/components/providers/BackgroundDataProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const BackgroundDataProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call BackgroundDataProvider() from the server but BackgroundDataProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/BackgroundDataProvider.tsx <module evaluation>\",\n    \"BackgroundDataProvider\",\n);\nexport const useBackgroundDataContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call useBackgroundDataContext() from the server but useBackgroundDataContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/BackgroundDataProvider.tsx <module evaluation>\",\n    \"useBackgroundDataContext\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,yBAAyB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,qFACA;AAEG,MAAM,2BAA2B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1D;IAAa,MAAM,IAAI,MAAM;AAAgQ,GAC7R,qFACA", "debugId": null}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/components/providers/BackgroundDataProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const BackgroundDataProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call BackgroundDataProvider() from the server but BackgroundDataProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/BackgroundDataProvider.tsx\",\n    \"BackgroundDataProvider\",\n);\nexport const useBackgroundDataContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call useBackgroundDataContext() from the server but useBackgroundDataContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/BackgroundDataProvider.tsx\",\n    \"useBackgroundDataContext\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,yBAAyB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,iEACA;AAEG,MAAM,2BAA2B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1D;IAAa,MAAM,IAAI,MAAM;AAAgQ,GAC7R,iEACA", "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/components/AppInitializer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AppInitializer = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppInitializer() from the server but AppInitializer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/AppInitializer.tsx <module evaluation>\",\n    \"AppInitializer\",\n);\nexport const useAppInitialization = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAppInitialization() from the server but useAppInitialization is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/AppInitializer.tsx <module evaluation>\",\n    \"useAppInitialization\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,mEACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,mEACA", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/components/AppInitializer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AppInitializer = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppInitializer() from the server but AppInitializer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/AppInitializer.tsx\",\n    \"AppInitializer\",\n);\nexport const useAppInitialization = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAppInitialization() from the server but useAppInitialization is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/AppInitializer.tsx\",\n    \"useAppInitialization\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,+CACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,+CACA", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/components/PerformanceMonitor.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const PerformanceMonitor = registerClientReference(\n    function() { throw new Error(\"Attempted to call PerformanceMonitor() from the server but PerformanceMonitor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/PerformanceMonitor.tsx <module evaluation>\",\n    \"PerformanceMonitor\",\n);\nexport const usePerformanceReporting = registerClientReference(\n    function() { throw new Error(\"Attempted to call usePerformanceReporting() from the server but usePerformanceReporting is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/PerformanceMonitor.tsx <module evaluation>\",\n    \"usePerformanceReporting\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,uEACA;AAEG,MAAM,0BAA0B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzD;IAAa,MAAM,IAAI,MAAM;AAA8P,GAC3R,uEACA", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/components/PerformanceMonitor.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const PerformanceMonitor = registerClientReference(\n    function() { throw new Error(\"Attempted to call PerformanceMonitor() from the server but PerformanceMonitor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/PerformanceMonitor.tsx\",\n    \"PerformanceMonitor\",\n);\nexport const usePerformanceReporting = registerClientReference(\n    function() { throw new Error(\"Attempted to call usePerformanceReporting() from the server but usePerformanceReporting is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/PerformanceMonitor.tsx\",\n    \"usePerformanceReporting\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,mDACA;AAEG,MAAM,0BAA0B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzD;IAAa,MAAM,IAAI,MAAM;AAA8P,GAC3R,mDACA", "debugId": null}}, {"offset": {"line": 149, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/app/dashboard/layout.tsx"], "sourcesContent": ["import { DashboardLayout } from '@/components/layout/dashboard-layout';\nimport { BackgroundDataProvider } from '@/components/providers/BackgroundDataProvider';\nimport { AppInitializer } from '@/components/AppInitializer';\nimport { PerformanceMonitor } from '@/components/PerformanceMonitor';\n\nexport default function Layout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return (\n    <AppInitializer>\n      <BackgroundDataProvider>\n        <DashboardLayout>{children}</DashboardLayout>\n        <PerformanceMonitor />\n      </BackgroundDataProvider>\n    </AppInitializer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEe,SAAS,OAAO,EAC7B,QAAQ,EAGT;IACC,qBACE,8OAAC,oIAAA,CAAA,iBAAc;kBACb,cAAA,8OAAC,yJAAA,CAAA,yBAAsB;;8BACrB,8OAAC,mJAAA,CAAA,kBAAe;8BAAE;;;;;;8BAClB,8OAAC,wIAAA,CAAA,qBAAkB;;;;;;;;;;;;;;;;AAI3B", "debugId": null}}]}