(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/holdings-service.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Holdings service to manage current holdings across strategies
__turbopack_context__.s({
    "holdingsService": ()=>holdingsService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
;
class HoldingsService {
    // Get all current holdings
    getAllHoldings() {
        return [
            ...this.holdings
        ];
    }
    // Get holdings for a specific strategy
    getHoldingsByStrategy(strategy) {
        return this.holdings.filter((holding)=>holding.strategy === strategy);
    }
    // Check if a stock is currently held in any strategy
    isStockInHoldings(symbol) {
        return this.holdings.some((holding)=>holding.symbol === symbol);
    }
    // Get all unique symbols in holdings
    getHoldingSymbols() {
        return [
            ...new Set(this.holdings.map((holding)=>holding.symbol))
        ];
    }
    // Add a new holding
    addHolding(holding) {
        const existingIndex = this.holdings.findIndex((h)=>h.symbol === holding.symbol && h.strategy === holding.strategy);
        if (existingIndex >= 0) {
            // Update existing holding (average price calculation)
            const existing = this.holdings[existingIndex];
            const totalQuantity = existing.quantity + holding.quantity;
            const totalValue = existing.quantity * existing.avgPrice + holding.quantity * holding.avgPrice;
            this.holdings[existingIndex] = {
                ...existing,
                quantity: totalQuantity,
                avgPrice: totalValue / totalQuantity,
                currentPrice: holding.currentPrice
            };
        } else {
            // Add new holding
            this.holdings.push({
                ...holding,
                purchaseDate: new Date()
            });
        }
    }
    // Remove a holding
    removeHolding(symbol, strategy) {
        this.holdings = this.holdings.filter((holding)=>!(holding.symbol === symbol && holding.strategy === strategy));
    }
    // Update current price for a holding
    updateCurrentPrice(symbol, currentPrice) {
        this.holdings.forEach((holding)=>{
            if (holding.symbol === symbol) {
                holding.currentPrice = currentPrice;
            }
        });
    }
    // Get stocks that were bought above ₹2000 and are still in holdings
    getStocksAbove2000InHoldings() {
        return this.holdings.filter((holding)=>holding.avgPrice > 2000 || holding.currentPrice > 2000).map((holding)=>holding.symbol);
    }
    // Check if a stock should be eligible for trading
    // (CMP < 2000 OR currently in holdings)
    isStockEligibleForTrading(symbol, currentPrice) {
        return currentPrice < 2000 || this.isStockInHoldings(symbol);
    }
    constructor(){
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "holdings", [
            // Sample holdings for demonstration - in real app, this would come from database
            {
                symbol: 'RELIANCE',
                strategy: 'DARVAS_BOX',
                quantity: 50,
                avgPrice: 2200.00,
                currentPrice: 2456.75,
                purchaseDate: new Date('2024-01-15')
            },
            {
                symbol: 'TCS',
                strategy: 'DARVAS_BOX',
                quantity: 25,
                avgPrice: 3400.00,
                currentPrice: 3234.50,
                purchaseDate: new Date('2024-01-20')
            },
            {
                symbol: 'HDFC',
                strategy: 'WEEKLY_HIGH',
                quantity: 40,
                avgPrice: 1600.00,
                currentPrice: 1678.90,
                purchaseDate: new Date('2024-02-01')
            },
            {
                symbol: 'INFY',
                strategy: 'BOH_FILTER',
                quantity: 60,
                avgPrice: 1500.00,
                currentPrice: 1456.80,
                purchaseDate: new Date('2024-02-10')
            }
        ]);
    }
}
const holdingsService = new HoldingsService();
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/weekly-high-signal-detector.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Weekly High Signal Detection Service
// Monitors for new Weekly High Signals and triggers automatic GTT order creation
__turbopack_context__.s({
    "weeklyHighSignalDetector": ()=>weeklyHighSignalDetector
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$yahoo$2d$finance$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/yahoo-finance.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/nifty-stocks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$holdings$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/holdings-service.ts [app-client] (ecmascript)");
;
;
;
;
class WeeklyHighSignalDetector {
    // Configuration management
    updateConfig(newConfig) {
        this.config = {
            ...this.config,
            ...newConfig
        };
        console.log('⚙️ Signal detector config updated:', this.config);
        if (this.isRunning) {
            this.stop();
            this.start();
        }
    }
    getConfig() {
        return {
            ...this.config
        };
    }
    // Check if market is currently open
    isMarketOpen() {
        const now = new Date();
        const currentHour = now.getHours();
        const currentMinute = now.getMinutes();
        // Market hours: 9:15 AM to 3:30 PM (Monday to Friday)
        const isWeekday = now.getDay() >= 1 && now.getDay() <= 5;
        const isAfterStart = currentHour > this.config.marketStartHour || currentHour === this.config.marketStartHour && currentMinute >= 15;
        const isBeforeEnd = currentHour < this.config.marketEndHour || currentHour === this.config.marketEndHour && currentMinute <= 30;
        return isWeekday && isAfterStart && isBeforeEnd;
    }
    // Generate mock OHLC data for weekly high calculation
    generateOHLCData(currentPrice) {
        const data = [];
        let price = currentPrice * 0.95; // Start 5% below current price
        for(let i = 0; i < 7; i++){
            const open = price;
            const high = price * (1 + Math.random() * 0.08); // Up to 8% higher
            const low = price * (1 - Math.random() * 0.05); // Up to 5% lower
            const close = low + Math.random() * (high - low);
            data.push({
                open,
                high,
                low,
                close
            });
            price = close;
        }
        return data;
    }
    // Calculate signal strength based on proximity to weekly high and volume
    calculateSignalStrength(currentPrice, weeklyHigh, volumeRatio) {
        const percentFromHigh = Math.abs((currentPrice - weeklyHigh) / weeklyHigh * 100);
        if (percentFromHigh <= this.config.strongSignalThreshold && volumeRatio >= this.config.minVolumeRatio) {
            return 'STRONG';
        } else if (percentFromHigh <= this.config.moderateSignalThreshold) {
            return 'MODERATE';
        } else {
            return 'WEAK';
        }
    }
    // Scan for Weekly High Signals
    async scanForSignals() {
        try {
            console.log('🔍 Scanning for Weekly High Signals...');
            // Fetch all Nifty 200 stocks with current prices
            const yahooSymbols = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NIFTY_200_SYMBOLS"].map(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getYahooSymbol"]);
            const quotes = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$yahoo$2d$finance$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["yahooFinanceService"].getMultipleQuotesWithCachedNames(yahooSymbols);
            console.log("📊 Got quotes for ".concat(quotes.length, "/").concat(yahooSymbols.length, " symbols"));
            // Get current holdings
            const holdingSymbols = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$holdings$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["holdingsService"].getAllHoldings().map((h)=>h.symbol);
            // Process each stock for signal detection
            const signals = [];
            for(let i = 0; i < __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NIFTY_200_SYMBOLS"].length; i++){
                const nseSymbol = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NIFTY_200_SYMBOLS"][i];
                const yahooSymbol = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getYahooSymbol"])(nseSymbol);
                const quote = quotes.find((q)=>q.symbol === yahooSymbol);
                if (!quote || quote.price <= 0) continue;
                const price = quote.price;
                const inHoldings = holdingSymbols.includes(nseSymbol);
                const isEligible = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$holdings$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["holdingsService"].isStockEligibleForTrading(nseSymbol, price);
                // Create stock object for BOH eligibility check
                const stock = {
                    symbol: nseSymbol,
                    name: quote.name,
                    price,
                    change: quote.change || 0,
                    changePercent: quote.changePercent || 0,
                    volume: quote.volume || 0,
                    marketCap: quote.marketCap,
                    high52Week: quote.high52Week,
                    low52Week: quote.low52Week,
                    high52WeekDate: quote.high52WeekDate,
                    low52WeekDate: quote.low52WeekDate,
                    isEligible,
                    inHoldings
                };
                const stockWithBOH = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addBOHEligibility"])(stock);
                // Only process BOH eligible stocks
                if (!stockWithBOH.isBOHEligible) continue;
                // Calculate weekly high data
                const ohlcData = this.generateOHLCData(price);
                const lastWeekHighest = Math.max(...ohlcData.map((d)=>d.high));
                const suggestedBuyPrice = lastWeekHighest + 0.05;
                const percentDifference = (price - suggestedBuyPrice) / suggestedBuyPrice * 100;
                const suggestedGTTQuantity = Math.floor(this.config.investmentPerOrder / suggestedBuyPrice);
                // Calculate volume metrics
                const avgVolume = quote.avgVolume || quote.volume || 1;
                const volumeRatio = quote.volume / avgVolume;
                // Calculate signal strength
                const signalStrength = this.calculateSignalStrength(price, lastWeekHighest, volumeRatio);
                // Only include signals that are MODERATE or STRONG
                if (signalStrength === 'WEAK') continue;
                const signal = {
                    symbol: nseSymbol,
                    name: quote.name,
                    currentPrice: price,
                    lastWeekHighest,
                    suggestedBuyPrice,
                    percentDifference,
                    suggestedGTTQuantity,
                    isBOHEligible: true,
                    inHoldings,
                    signalStrength,
                    detectedAt: new Date(),
                    volume: quote.volume || 0,
                    avgVolume,
                    volumeRatio
                };
                signals.push(signal);
            }
            console.log("✅ Found ".concat(signals.length, " Weekly High Signals"));
            return signals;
        } catch (error) {
            console.error('❌ Error scanning for signals:', error);
            return [];
        }
    }
    // Detect new signals by comparing with previous scan
    detectNewSignals(currentSignals) {
        const newSignals = [];
        for (const signal of currentSignals){
            const previousSignal = this.lastSignals.get(signal.symbol);
            // Consider it a new signal if:
            // 1. Stock wasn't in previous signals, OR
            // 2. Signal strength improved (MODERATE -> STRONG), OR
            // 3. Price moved significantly closer to weekly high
            const isNewSignal = !previousSignal || signal.signalStrength === 'STRONG' && previousSignal.signalStrength !== 'STRONG' || Math.abs(signal.percentDifference) < Math.abs(previousSignal.percentDifference) - 1;
            if (isNewSignal) {
                newSignals.push(signal);
                console.log("🆕 New signal detected: ".concat(signal.symbol, " (").concat(signal.signalStrength, ")"));
            }
        }
        return newSignals;
    }
    // Main polling function
    async poll() {
        if (!this.config.enabled) {
            console.log('⏸️ Signal detection is disabled');
            return;
        }
        if (!this.isMarketOpen()) {
            console.log('🕐 Market is closed, skipping signal detection');
            return;
        }
        try {
            console.log('🔄 Polling for Weekly High Signals...');
            const currentSignals = await this.scanForSignals();
            const newSignals = this.detectNewSignals(currentSignals);
            // Update last signals cache
            this.lastSignals.clear();
            currentSignals.forEach((signal)=>{
                this.lastSignals.set(signal.symbol, signal);
            });
            // Notify listeners about all current signals
            this.signalListeners.forEach((listener)=>{
                try {
                    listener(currentSignals);
                } catch (error) {
                    console.error('❌ Error in signal listener:', error);
                }
            });
            // Notify listeners about new signals
            if (newSignals.length > 0) {
                console.log("🚨 ".concat(newSignals.length, " new signals detected!"));
                newSignals.forEach((signal)=>{
                    this.newSignalListeners.forEach((listener)=>{
                        try {
                            listener(signal);
                        } catch (error) {
                            console.error('❌ Error in new signal listener:', error);
                        }
                    });
                });
            }
        } catch (error) {
            console.error('❌ Error in signal polling:', error);
        }
    }
    // Start the detection service
    start() {
        if (this.isRunning) {
            console.log('⚠️ Signal detector is already running');
            return;
        }
        console.log("🚀 Starting Weekly High Signal Detector (polling every ".concat(this.config.pollingIntervalMinutes, " minutes)"));
        this.isRunning = true;
        // Initial scan
        this.poll();
        // Set up polling interval
        this.pollingInterval = setInterval(()=>{
            this.poll();
        }, this.config.pollingIntervalMinutes * 60 * 1000);
    }
    // Stop the detection service
    stop() {
        if (!this.isRunning) {
            console.log('⚠️ Signal detector is not running');
            return;
        }
        console.log('⏹️ Stopping Weekly High Signal Detector');
        this.isRunning = false;
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
            this.pollingInterval = null;
        }
    }
    // Add listener for all signals
    addSignalListener(listener) {
        this.signalListeners.add(listener);
    }
    // Remove listener for all signals
    removeSignalListener(listener) {
        this.signalListeners.delete(listener);
    }
    // Add listener for new signals only
    addNewSignalListener(listener) {
        this.newSignalListeners.add(listener);
    }
    // Remove listener for new signals
    removeNewSignalListener(listener) {
        this.newSignalListeners.delete(listener);
    }
    // Get current status
    getStatus() {
        return {
            isRunning: this.isRunning,
            isMarketOpen: this.isMarketOpen(),
            config: this.config,
            lastSignalCount: this.lastSignals.size,
            listenerCount: this.signalListeners.size + this.newSignalListeners.size
        };
    }
    // Manual trigger for testing
    async triggerManualScan() {
        console.log('🔧 Manual signal scan triggered');
        return await this.scanForSignals();
    }
    constructor(){
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "config", {
            enabled: true,
            pollingIntervalMinutes: 5,
            marketStartHour: 9,
            marketEndHour: 15,
            strongSignalThreshold: 2.0,
            moderateSignalThreshold: 5.0,
            minVolumeRatio: 1.2,
            maxInvestmentPerStock: 10000,
            investmentPerOrder: 2000
        });
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "isRunning", false);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "pollingInterval", null);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "lastSignals", new Map());
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "signalListeners", new Set());
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "newSignalListeners", new Set());
        console.log('📡 Weekly High Signal Detector initialized');
    }
}
const weeklyHighSignalDetector = new WeeklyHighSignalDetector();
// Auto-start the service when imported
if ("TURBOPACK compile-time truthy", 1) {
    // Only run in browser environment
    console.log('📡 Auto-starting Weekly High Signal Detector...');
    weeklyHighSignalDetector.start();
    console.log('✅ Weekly High Signal Detector auto-started successfully');
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/automatic-gtt-service.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Automatic GTT Order Creation Service
// Creates GTT buy orders automatically when new Weekly High Signals are detected
__turbopack_context__.s({
    "automaticGTTService": ()=>automaticGTTService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$weekly$2d$high$2d$signal$2d$detector$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/weekly-high-signal-detector.ts [app-client] (ecmascript)");
;
;
class AutomaticGTTService {
    // Initialize the service and start listening for signals
    async initialize() {
        if (this.isInitialized) {
            console.log('⚠️ Automatic GTT Service already initialized');
            return;
        }
        console.log('🚀 Initializing Automatic GTT Service...');
        // Load existing orders from localStorage or API
        await this.loadExistingOrders();
        // Start listening for new signals
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$weekly$2d$high$2d$signal$2d$detector$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["weeklyHighSignalDetector"].addNewSignalListener(this.handleNewSignal.bind(this));
        // Reset daily counter if it's a new day
        this.resetDailyCounterIfNeeded();
        this.isInitialized = true;
        console.log('✅ Automatic GTT Service initialized successfully');
    }
    // Configuration management
    updateConfig(newConfig) {
        this.config = {
            ...this.config,
            ...newConfig
        };
        console.log('⚙️ Auto GTT config updated:', this.config);
    }
    getConfig() {
        return {
            ...this.config
        };
    }
    // Load existing orders (from localStorage for now, can be replaced with API)
    async loadExistingOrders() {
        try {
            const stored = localStorage.getItem('autoGTTOrders');
            if (stored) {
                const parsedOrders = JSON.parse(stored);
                this.orders = parsedOrders.map((order)=>({
                        ...order,
                        createdAt: new Date(order.createdAt)
                    }));
                console.log("📂 Loaded ".concat(this.orders.length, " existing auto GTT orders"));
            }
            // Load daily counter
            const storedCounter = localStorage.getItem('autoGTTDailyCount');
            const storedDate = localStorage.getItem('autoGTTLastResetDate');
            if (storedCounter && storedDate === new Date().toDateString()) {
                this.dailyOrderCount = parseInt(storedCounter, 10);
            }
        } catch (error) {
            console.error('❌ Error loading existing orders:', error);
        }
    }
    // Save orders to localStorage
    saveOrders() {
        try {
            localStorage.setItem('autoGTTOrders', JSON.stringify(this.orders));
            localStorage.setItem('autoGTTDailyCount', this.dailyOrderCount.toString());
            localStorage.setItem('autoGTTLastResetDate', this.lastResetDate);
        } catch (error) {
            console.error('❌ Error saving orders:', error);
        }
    }
    // Reset daily counter if it's a new day
    resetDailyCounterIfNeeded() {
        const today = new Date().toDateString();
        if (this.lastResetDate !== today) {
            this.dailyOrderCount = 0;
            this.lastResetDate = today;
            console.log('🔄 Daily order counter reset for new day');
        }
    }
    // Check if we can create a new order
    canCreateOrder(signal) {
        // Check if service is enabled
        if (!this.config.enabled) {
            return {
                canCreate: false,
                reason: 'Automatic GTT service is disabled'
            };
        }
        // Check market hours if required
        if (this.config.onlyDuringMarketHours) {
            const now = new Date();
            const currentHour = now.getHours();
            const currentMinute = now.getMinutes();
            const isWeekday = now.getDay() >= 1 && now.getDay() <= 5;
            const isMarketOpen = isWeekday && (currentHour > 9 || currentHour === 9 && currentMinute >= 15) && (currentHour < 15 || currentHour === 15 && currentMinute <= 30);
            if (!isMarketOpen) {
                return {
                    canCreate: false,
                    reason: 'Market is closed'
                };
            }
        }
        // Check daily limit
        this.resetDailyCounterIfNeeded();
        if (this.dailyOrderCount >= this.config.maxOrdersPerDay) {
            return {
                canCreate: false,
                reason: 'Daily order limit reached'
            };
        }
        // Check signal strength
        const strengthOrder = {
            'WEAK': 1,
            'MODERATE': 2,
            'STRONG': 3
        };
        if (strengthOrder[signal.signalStrength] < strengthOrder[this.config.minSignalStrength]) {
            return {
                canCreate: false,
                reason: "Signal strength ".concat(signal.signalStrength, " below minimum ").concat(this.config.minSignalStrength)
            };
        }
        // Check volume confirmation if required
        if (this.config.requireVolumeConfirmation && signal.volumeRatio < 1.2) {
            return {
                canCreate: false,
                reason: 'Insufficient volume confirmation'
            };
        }
        // Check for existing pending orders for this stock
        const existingOrder = this.orders.find((order)=>order.symbol === signal.symbol && order.status === 'PENDING' && order.source === 'SIGNAL');
        if (existingOrder) {
            return {
                canCreate: false,
                reason: 'Pending order already exists for this stock'
            };
        }
        // Check investment limits
        const existingInvestment = this.orders.filter((order)=>order.symbol === signal.symbol && order.status === 'PENDING').reduce((sum, order)=>sum + order.triggerPrice * order.quantity, 0);
        const newInvestment = signal.suggestedBuyPrice * signal.suggestedGTTQuantity;
        if (existingInvestment + newInvestment > this.config.maxInvestmentPerStock) {
            return {
                canCreate: false,
                reason: 'Would exceed maximum investment per stock'
            };
        }
        // Check if quantity is valid
        if (signal.suggestedGTTQuantity <= 0) {
            return {
                canCreate: false,
                reason: 'Invalid quantity calculated'
            };
        }
        // Check if trigger price is reasonable
        if (signal.suggestedBuyPrice <= 0 || signal.suggestedBuyPrice > 5000) {
            return {
                canCreate: false,
                reason: 'Invalid trigger price'
            };
        }
        return {
            canCreate: true
        };
    }
    // Handle new signal detection
    async handleNewSignal(signal) {
        console.log("🔔 New signal received: ".concat(signal.symbol, " (").concat(signal.signalStrength, ")"));
        const validation = this.canCreateOrder(signal);
        if (!validation.canCreate) {
            console.log("⏭️ Skipping order creation for ".concat(signal.symbol, ": ").concat(validation.reason));
            return;
        }
        try {
            await this.createAutoGTTOrder(signal);
        } catch (error) {
            console.error("❌ Failed to create auto GTT order for ".concat(signal.symbol, ":"), error);
        }
    }
    // Create automatic GTT order
    async createAutoGTTOrder(signal) {
        console.log("🤖 Creating automatic GTT order for ".concat(signal.symbol, "..."));
        const order = {
            id: "auto_gtt_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9)),
            symbol: signal.symbol,
            name: signal.name,
            orderType: 'BUY',
            triggerPrice: signal.suggestedBuyPrice,
            quantity: signal.suggestedGTTQuantity,
            status: 'PENDING',
            createdAt: new Date(),
            source: 'SIGNAL',
            signalStrength: signal.signalStrength,
            autoCreated: true,
            originalSignal: signal
        };
        // Add to orders list
        this.orders.push(order);
        this.dailyOrderCount++;
        // Save to storage
        this.saveOrders();
        // Log the creation
        console.log("✅ Auto GTT order created: ".concat(order.symbol, " - Trigger: ₹").concat(order.triggerPrice.toFixed(2), ", Qty: ").concat(order.quantity));
        console.log("📊 Daily orders: ".concat(this.dailyOrderCount, "/").concat(this.config.maxOrdersPerDay));
        // Notify listeners
        this.orderListeners.forEach((listener)=>{
            try {
                listener(order);
            } catch (error) {
                console.error('❌ Error in order listener:', error);
            }
        });
        return order;
    }
    // Get all orders
    getAllOrders() {
        return [
            ...this.orders
        ];
    }
    // Get orders by status
    getOrdersByStatus(status) {
        return this.orders.filter((order)=>order.status === status);
    }
    // Get orders by source
    getOrdersBySource(source) {
        return this.orders.filter((order)=>order.source === source);
    }
    // Cancel an order
    cancelOrder(orderId) {
        const order = this.orders.find((o)=>o.id === orderId);
        if (order && order.status === 'PENDING') {
            order.status = 'CANCELLED';
            this.saveOrders();
            console.log("❌ Order cancelled: ".concat(order.symbol, " (").concat(orderId, ")"));
            return true;
        }
        return false;
    }
    // Update order status (for external triggers)
    updateOrderStatus(orderId, status) {
        const order = this.orders.find((o)=>o.id === orderId);
        if (order) {
            order.status = status;
            this.saveOrders();
            console.log("🔄 Order status updated: ".concat(order.symbol, " -> ").concat(status));
            return true;
        }
        return false;
    }
    // Add order listener
    addOrderListener(listener) {
        this.orderListeners.add(listener);
    }
    // Remove order listener
    removeOrderListener(listener) {
        this.orderListeners.delete(listener);
    }
    // Get service statistics
    getStatistics() {
        const today = new Date().toDateString();
        const todayOrders = this.orders.filter((order)=>order.createdAt.toDateString() === today && order.autoCreated);
        return {
            totalOrders: this.orders.length,
            autoCreatedOrders: this.orders.filter((o)=>o.autoCreated).length,
            todayOrders: todayOrders.length,
            dailyLimit: this.config.maxOrdersPerDay,
            pendingOrders: this.orders.filter((o)=>o.status === 'PENDING').length,
            triggeredOrders: this.orders.filter((o)=>o.status === 'TRIGGERED').length,
            cancelledOrders: this.orders.filter((o)=>o.status === 'CANCELLED').length,
            isEnabled: this.config.enabled,
            isInitialized: this.isInitialized
        };
    }
    // Manual trigger for testing
    async testCreateOrder(symbol) {
        console.log("🧪 Test order creation for ".concat(symbol));
        // Get current signals
        const signals = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$weekly$2d$high$2d$signal$2d$detector$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["weeklyHighSignalDetector"].triggerManualScan();
        const signal = signals.find((s)=>s.symbol === symbol);
        if (!signal) {
            console.log("❌ No signal found for ".concat(symbol));
            return null;
        }
        return await this.createAutoGTTOrder(signal);
    }
    // Start the service
    async start() {
        if (!this.isInitialized) {
            await this.initialize();
        }
        // Start the signal detector if not already running
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$weekly$2d$high$2d$signal$2d$detector$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["weeklyHighSignalDetector"].start();
        console.log('🚀 Automatic GTT Service started');
    }
    // Stop the service
    stop() {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$weekly$2d$high$2d$signal$2d$detector$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["weeklyHighSignalDetector"].stop();
        console.log('⏹️ Automatic GTT Service stopped');
    }
    constructor(){
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "config", {
            enabled: true,
            maxOrdersPerDay: 20,
            maxInvestmentPerStock: 10000,
            investmentPerOrder: 2000,
            minSignalStrength: 'MODERATE',
            requireVolumeConfirmation: true,
            onlyDuringMarketHours: true
        });
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "orders", []);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "dailyOrderCount", 0);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "lastResetDate", new Date().toDateString());
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "orderListeners", new Set());
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "isInitialized", false);
        console.log('🤖 Automatic GTT Service initialized');
    }
}
const automaticGTTService = new AutomaticGTTService();
// Auto-start the service when imported
if ("TURBOPACK compile-time truthy", 1) {
    // Only run in browser environment
    console.log('🤖 Auto-starting Automatic GTT Service...');
    automaticGTTService.start().then(()=>{
        console.log('✅ Automatic GTT Service auto-started successfully');
    }).catch((error)=>{
        console.error('❌ Automatic GTT Service auto-start failed:', error);
    });
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/central-data-manager.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Central Data Management Service
// Manages all stock data, caching, and real-time synchronization across pages
__turbopack_context__.s({
    "centralDataManager": ()=>centralDataManager
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$yahoo$2d$finance$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/yahoo-finance.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/nifty-stocks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$holdings$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/holdings-service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$weekly$2d$high$2d$signal$2d$detector$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/weekly-high-signal-detector.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$automatic$2d$gtt$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/automatic-gtt-service.ts [app-client] (ecmascript)");
;
;
;
;
;
;
class CentralDataManager {
    initializeListeners() {
        this.listeners.set('nifty200', new Set());
        this.listeners.set('bohEligible', new Set());
        this.listeners.set('weeklyHighSignals', new Set());
        this.listeners.set('gttOrders', new Set());
    }
    // Configuration management
    updateConfig(newConfig) {
        this.config = {
            ...this.config,
            ...newConfig
        };
        console.log('⚙️ Central Data Manager config updated:', this.config);
        if (this.isRunning) {
            this.stop();
            this.start();
        }
    }
    getConfig() {
        return {
            ...this.config
        };
    }
    // Market hours detection
    isMarketOpen() {
        const now = new Date();
        const currentHour = now.getHours();
        const currentMinute = now.getMinutes();
        const isWeekday = now.getDay() >= 1 && now.getDay() <= 5;
        const isAfterStart = currentHour > this.config.marketStartHour || currentHour === this.config.marketStartHour && currentMinute >= 15;
        const isBeforeEnd = currentHour < this.config.marketEndHour || currentHour === this.config.marketEndHour && currentMinute <= 30;
        return isWeekday && isAfterStart && isBeforeEnd;
    }
    // Data loading methods
    async loadNifty200Stocks() {
        if (this.cache.isLoading.nifty200) return;
        this.cache.isLoading.nifty200 = true;
        console.log('📈 Loading Nifty 200 stocks...');
        try {
            const yahooSymbols = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NIFTY_200_SYMBOLS"].map(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getYahooSymbol"]);
            const quotes = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$yahoo$2d$finance$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["yahooFinanceService"].getMultipleQuotesWithCachedNames(yahooSymbols);
            const holdingSymbols = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$holdings$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["holdingsService"].getAllHoldings().map((h)=>h.symbol);
            const processedStocks = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NIFTY_200_SYMBOLS"].map((nseSymbol)=>{
                const yahooSymbol = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getYahooSymbol"])(nseSymbol);
                const quote = quotes.find((q)=>q.symbol === yahooSymbol);
                const price = (quote === null || quote === void 0 ? void 0 : quote.price) || 0;
                const inHoldings = holdingSymbols.includes(nseSymbol);
                const isEligible = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$holdings$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["holdingsService"].isStockEligibleForTrading(nseSymbol, price);
                const stock = {
                    symbol: nseSymbol,
                    name: (quote === null || quote === void 0 ? void 0 : quote.name) || nseSymbol,
                    price,
                    change: (quote === null || quote === void 0 ? void 0 : quote.change) || 0,
                    changePercent: (quote === null || quote === void 0 ? void 0 : quote.changePercent) || 0,
                    volume: (quote === null || quote === void 0 ? void 0 : quote.volume) || 0,
                    marketCap: quote === null || quote === void 0 ? void 0 : quote.marketCap,
                    high52Week: quote === null || quote === void 0 ? void 0 : quote.high52Week,
                    low52Week: quote === null || quote === void 0 ? void 0 : quote.low52Week,
                    high52WeekDate: quote === null || quote === void 0 ? void 0 : quote.high52WeekDate,
                    low52WeekDate: quote === null || quote === void 0 ? void 0 : quote.low52WeekDate,
                    isEligible,
                    inHoldings
                };
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addBOHEligibility"])(stock);
            });
            this.cache.nifty200Stocks = processedStocks;
            this.cache.lastUpdated.nifty200 = new Date();
            console.log("✅ Loaded ".concat(processedStocks.length, " Nifty 200 stocks"));
            this.notifyListeners('nifty200', processedStocks);
        } catch (error) {
            console.error('❌ Error loading Nifty 200 stocks:', error);
        } finally{
            this.cache.isLoading.nifty200 = false;
        }
    }
    async loadBOHEligibleStocks() {
        if (this.cache.isLoading.bohEligible) return;
        this.cache.isLoading.bohEligible = true;
        console.log('🔍 Loading BOH eligible stocks...');
        try {
            // Use cached Nifty 200 data if available and recent
            let stocks = this.cache.nifty200Stocks;
            if (stocks.length === 0 || !this.cache.lastUpdated.nifty200 || Date.now() - this.cache.lastUpdated.nifty200.getTime() > 60000) {
                await this.loadNifty200Stocks();
                stocks = this.cache.nifty200Stocks;
            }
            const bohEligibleStocks = stocks.filter((stock)=>stock.isBOHEligible);
            this.cache.bohEligibleStocks = bohEligibleStocks;
            this.cache.lastUpdated.bohEligible = new Date();
            console.log("✅ Loaded ".concat(bohEligibleStocks.length, " BOH eligible stocks"));
            this.notifyListeners('bohEligible', bohEligibleStocks);
        } catch (error) {
            console.error('❌ Error loading BOH eligible stocks:', error);
        } finally{
            this.cache.isLoading.bohEligible = false;
        }
    }
    async loadWeeklyHighSignals() {
        if (this.cache.isLoading.weeklyHighSignals) return;
        this.cache.isLoading.weeklyHighSignals = true;
        console.log('📊 Loading Weekly High Signals...');
        try {
            const signals = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$weekly$2d$high$2d$signal$2d$detector$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["weeklyHighSignalDetector"].triggerManualScan();
            this.cache.weeklyHighSignals = signals;
            this.cache.lastUpdated.weeklyHighSignals = new Date();
            console.log("✅ Loaded ".concat(signals.length, " Weekly High Signals"));
            this.notifyListeners('weeklyHighSignals', signals);
        } catch (error) {
            console.error('❌ Error loading Weekly High Signals:', error);
        } finally{
            this.cache.isLoading.weeklyHighSignals = false;
        }
    }
    async loadGTTOrders() {
        if (this.cache.isLoading.gttOrders) return;
        this.cache.isLoading.gttOrders = true;
        console.log('📋 Loading GTT orders...');
        try {
            const orders = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$automatic$2d$gtt$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["automaticGTTService"].getAllOrders();
            this.cache.gttOrders = orders;
            this.cache.lastUpdated.gttOrders = new Date();
            console.log("✅ Loaded ".concat(orders.length, " GTT orders"));
            this.notifyListeners('gttOrders', orders);
        } catch (error) {
            console.error('❌ Error loading GTT orders:', error);
        } finally{
            this.cache.isLoading.gttOrders = false;
        }
    }
    // Listener management
    addListener(dataType, listener) {
        const listeners = this.listeners.get(dataType);
        if (listeners) {
            listeners.add(listener);
            console.log("👂 Added listener for ".concat(dataType));
        }
    }
    removeListener(dataType, listener) {
        const listeners = this.listeners.get(dataType);
        if (listeners) {
            listeners.delete(listener);
            console.log("🔇 Removed listener for ".concat(dataType));
        }
    }
    notifyListeners(dataType, data) {
        const listeners = this.listeners.get(dataType);
        if (listeners) {
            const timestamp = new Date();
            listeners.forEach((listener)=>{
                try {
                    listener(data, timestamp);
                } catch (error) {
                    console.error("❌ Error in ".concat(dataType, " listener:"), error);
                }
            });
        }
    }
    // Public data access methods
    getNifty200Stocks() {
        return [
            ...this.cache.nifty200Stocks
        ];
    }
    getBOHEligibleStocks() {
        return [
            ...this.cache.bohEligibleStocks
        ];
    }
    getWeeklyHighSignals() {
        return [
            ...this.cache.weeklyHighSignals
        ];
    }
    getGTTOrders() {
        return [
            ...this.cache.gttOrders
        ];
    }
    getLastUpdated(dataType) {
        return this.cache.lastUpdated[dataType];
    }
    isDataLoading(dataType) {
        return this.cache.isLoading[dataType];
    }
    // Cache management
    getCacheStatus() {
        var _this_listeners_get, _this_listeners_get1, _this_listeners_get2, _this_listeners_get3;
        return {
            nifty200Count: this.cache.nifty200Stocks.length,
            bohEligibleCount: this.cache.bohEligibleStocks.length,
            weeklyHighSignalsCount: this.cache.weeklyHighSignals.length,
            gttOrdersCount: this.cache.gttOrders.length,
            lastUpdated: {
                ...this.cache.lastUpdated
            },
            isLoading: {
                ...this.cache.isLoading
            },
            listenerCounts: {
                nifty200: ((_this_listeners_get = this.listeners.get('nifty200')) === null || _this_listeners_get === void 0 ? void 0 : _this_listeners_get.size) || 0,
                bohEligible: ((_this_listeners_get1 = this.listeners.get('bohEligible')) === null || _this_listeners_get1 === void 0 ? void 0 : _this_listeners_get1.size) || 0,
                weeklyHighSignals: ((_this_listeners_get2 = this.listeners.get('weeklyHighSignals')) === null || _this_listeners_get2 === void 0 ? void 0 : _this_listeners_get2.size) || 0,
                gttOrders: ((_this_listeners_get3 = this.listeners.get('gttOrders')) === null || _this_listeners_get3 === void 0 ? void 0 : _this_listeners_get3.size) || 0
            }
        };
    }
    // Force refresh methods
    async refreshNifty200() {
        await this.loadNifty200Stocks();
    }
    async refreshBOHEligible() {
        await this.loadBOHEligibleStocks();
    }
    async refreshWeeklyHighSignals() {
        await this.loadWeeklyHighSignals();
    }
    async refreshGTTOrders() {
        await this.loadGTTOrders();
    }
    async refreshAll() {
        console.log('🔄 Refreshing all data...');
        await Promise.all([
            this.loadNifty200Stocks(),
            this.loadBOHEligibleStocks(),
            this.loadWeeklyHighSignals(),
            this.loadGTTOrders()
        ]);
        console.log('✅ All data refreshed');
    }
    // Service lifecycle
    async initialize() {
        if (this.isInitialized) {
            console.log('⚠️ Central Data Manager already initialized');
            return;
        }
        console.log('🚀 Initializing Central Data Manager...');
        // Load initial data
        await this.refreshAll();
        this.isInitialized = true;
        console.log('✅ Central Data Manager initialized successfully');
    }
    start() {
        if (this.isRunning) {
            console.log('⚠️ Central Data Manager already running');
            return;
        }
        console.log('🚀 Starting Central Data Manager background updates...');
        // Set up periodic updates
        this.intervals.set('nifty200', setInterval(()=>{
            if (this.isMarketOpen() || !this.config.enableRealTimeUpdates) {
                this.loadNifty200Stocks();
            }
        }, this.config.nifty200UpdateInterval * 1000));
        this.intervals.set('bohEligible', setInterval(()=>{
            this.loadBOHEligibleStocks();
        }, this.config.bohEligibleUpdateInterval * 1000));
        this.intervals.set('weeklyHighSignals', setInterval(()=>{
            if (this.isMarketOpen() || !this.config.enableRealTimeUpdates) {
                this.loadWeeklyHighSignals();
            }
        }, this.config.weeklyHighSignalsUpdateInterval * 1000));
        this.intervals.set('gttOrders', setInterval(()=>{
            this.loadGTTOrders();
        }, this.config.gttOrdersUpdateInterval * 1000));
        this.isRunning = true;
        console.log('✅ Central Data Manager background updates started');
    }
    stop() {
        if (!this.isRunning) {
            console.log('⚠️ Central Data Manager not running');
            return;
        }
        console.log('⏹️ Stopping Central Data Manager background updates...');
        this.intervals.forEach((interval, dataType)=>{
            clearInterval(interval);
            console.log("⏹️ Stopped ".concat(dataType, " updates"));
        });
        this.intervals.clear();
        this.isRunning = false;
        console.log('✅ Central Data Manager background updates stopped');
    }
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            isRunning: this.isRunning,
            isMarketOpen: this.isMarketOpen(),
            config: this.config,
            cache: this.getCacheStatus()
        };
    }
    constructor(){
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "cache", {
            nifty200Stocks: [],
            bohEligibleStocks: [],
            weeklyHighSignals: [],
            gttOrders: [],
            lastUpdated: {
                nifty200: null,
                bohEligible: null,
                weeklyHighSignals: null,
                gttOrders: null
            },
            isLoading: {
                nifty200: false,
                bohEligible: false,
                weeklyHighSignals: false,
                gttOrders: false
            }
        });
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "config", {
            nifty200UpdateInterval: 30,
            bohEligibleUpdateInterval: 60,
            weeklyHighSignalsUpdateInterval: 300,
            gttOrdersUpdateInterval: 30,
            marketStartHour: 9,
            marketEndHour: 15,
            enableRealTimeUpdates: true
        });
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "intervals", new Map());
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "listeners", new Map());
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "isInitialized", false);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "isRunning", false);
        console.log('📊 Central Data Manager initialized');
        this.initializeListeners();
    }
}
const centralDataManager = new CentralDataManager();
// Auto-initialize and start the service when imported
if ("TURBOPACK compile-time truthy", 1) {
    // Only run in browser environment
    console.log('🚀 Auto-initializing Central Data Manager...');
    centralDataManager.initialize().then(()=>{
        centralDataManager.start();
        console.log('✅ Central Data Manager auto-started successfully');
    }).catch((error)=>{
        console.error('❌ Central Data Manager auto-start failed:', error);
    });
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/service-initializer.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Service Initializer
// Ensures all background services are imported and auto-started when the app loads
__turbopack_context__.s({
    "initializeAllServices": ()=>initializeAllServices
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/central-data-manager.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$automatic$2d$gtt$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/automatic-gtt-service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$weekly$2d$high$2d$signal$2d$detector$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/weekly-high-signal-detector.ts [app-client] (ecmascript)");
;
;
;
console.log('🚀 Service Initializer: Importing all background services...');
const initializeAllServices = async ()=>{
    console.log('🔧 Initializing all background services...');
    try {
        // Wait a moment for auto-start to complete
        await new Promise((resolve)=>setTimeout(resolve, 1000));
        // Check service status
        const centralStatus = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].getStatus();
        const gttStats = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$automatic$2d$gtt$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["automaticGTTService"].getStatistics();
        const signalStatus = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$weekly$2d$high$2d$signal$2d$detector$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["weeklyHighSignalDetector"].getStatus();
        console.log('📊 Service Status Check:');
        console.log('- Central Data Manager:', centralStatus.isInitialized ? '✅ Running' : '❌ Not Running');
        console.log('- Automatic GTT Service:', gttStats.isInitialized ? '✅ Running' : '❌ Not Running');
        console.log('- Weekly High Signal Detector:', signalStatus.isRunning ? '✅ Running' : '❌ Not Running');
        return {
            centralDataManager: centralStatus.isInitialized,
            automaticGTTService: gttStats.isInitialized,
            weeklyHighSignalDetector: signalStatus.isRunning
        };
    } catch (error) {
        console.error('❌ Service initialization check failed:', error);
        return {
            centralDataManager: false,
            automaticGTTService: false,
            weeklyHighSignalDetector: false
        };
    }
};
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/service-initializer.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/central-data-manager.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$automatic$2d$gtt$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/automatic-gtt-service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$weekly$2d$high$2d$signal$2d$detector$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/weekly-high-signal-detector.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$service$2d$initializer$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/service-initializer.ts [app-client] (ecmascript) <locals>");
}),
"[project]/src/hooks/useCentralData.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// React Hook for Central Data Manager Integration
// Provides real-time data access and automatic updates for all stock-related pages
__turbopack_context__.s({
    "useBOHEligibleStocks": ()=>useBOHEligibleStocks,
    "useCentralData": ()=>useCentralData,
    "useGTTOrders": ()=>useGTTOrders,
    "useNifty200Stocks": ()=>useNifty200Stocks,
    "useWeeklyHighSignals": ()=>useWeeklyHighSignals
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$service$2d$initializer$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/service-initializer.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/central-data-manager.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature();
;
;
function useCentralData() {
    _s();
    const [nifty200, setNifty200] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        data: [],
        lastUpdated: null,
        isLoading: false,
        error: null
    });
    const [bohEligible, setBohEligible] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        data: [],
        lastUpdated: null,
        isLoading: false,
        error: null
    });
    const [weeklyHighSignals, setWeeklyHighSignals] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        data: [],
        lastUpdated: null,
        isLoading: false,
        error: null
    });
    const [gttOrders, setGttOrders] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        data: [],
        lastUpdated: null,
        isLoading: false,
        error: null
    });
    const [isInitialized, setIsInitialized] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isServiceRunning, setIsServiceRunning] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const pollingIntervals = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(new Map());
    // Fetch data directly from services
    const fetchData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCentralData.useCallback[fetchData]": async (dataType)=>{
            try {
                let data = [];
                let lastUpdated = null;
                switch(dataType){
                    case 'nifty200':
                        data = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].getNifty200Stocks();
                        lastUpdated = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].getLastUpdated('nifty200');
                        setNifty200({
                            data,
                            lastUpdated,
                            isLoading: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].isDataLoading('nifty200'),
                            error: null
                        });
                        break;
                    case 'bohEligible':
                        data = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].getBOHEligibleStocks();
                        lastUpdated = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].getLastUpdated('bohEligible');
                        setBohEligible({
                            data,
                            lastUpdated,
                            isLoading: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].isDataLoading('bohEligible'),
                            error: null
                        });
                        break;
                    case 'weeklyHighSignals':
                        data = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].getWeeklyHighSignals();
                        lastUpdated = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].getLastUpdated('weeklyHighSignals');
                        setWeeklyHighSignals({
                            data,
                            lastUpdated,
                            isLoading: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].isDataLoading('weeklyHighSignals'),
                            error: null
                        });
                        break;
                    case 'gttOrders':
                        data = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].getGTTOrders();
                        lastUpdated = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].getLastUpdated('gttOrders');
                        setGttOrders({
                            data,
                            lastUpdated,
                            isLoading: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].isDataLoading('gttOrders'),
                            error: null
                        });
                        break;
                }
                console.log("📊 Updated ".concat(dataType, ": ").concat(data.length, " items"));
            } catch (error) {
                console.error("❌ Error fetching ".concat(dataType, ":"), error);
                const errorState = {
                    data: [],
                    lastUpdated: null,
                    isLoading: false,
                    error: error instanceof Error ? error.message : 'Unknown error'
                };
                switch(dataType){
                    case 'nifty200':
                        setNifty200({
                            "useCentralData.useCallback[fetchData]": (prev)=>({
                                    ...prev,
                                    ...errorState
                                })
                        }["useCentralData.useCallback[fetchData]"]);
                        break;
                    case 'bohEligible':
                        setBohEligible({
                            "useCentralData.useCallback[fetchData]": (prev)=>({
                                    ...prev,
                                    ...errorState
                                })
                        }["useCentralData.useCallback[fetchData]"]);
                        break;
                    case 'weeklyHighSignals':
                        setWeeklyHighSignals({
                            "useCentralData.useCallback[fetchData]": (prev)=>({
                                    ...prev,
                                    ...errorState
                                })
                        }["useCentralData.useCallback[fetchData]"]);
                        break;
                    case 'gttOrders':
                        setGttOrders({
                            "useCentralData.useCallback[fetchData]": (prev)=>({
                                    ...prev,
                                    ...errorState
                                })
                        }["useCentralData.useCallback[fetchData]"]);
                        break;
                }
            }
        }
    }["useCentralData.useCallback[fetchData]"], []);
    // Check service status directly
    const checkServiceStatus = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCentralData.useCallback[checkServiceStatus]": ()=>{
            try {
                const status = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].getStatus();
                setIsInitialized(status.isInitialized);
                setIsServiceRunning(status.isRunning);
                console.log('📊 Service status:', {
                    initialized: status.isInitialized,
                    running: status.isRunning,
                    marketOpen: status.isMarketOpen
                });
            } catch (error) {
                console.error('❌ Error checking service status:', error);
                setIsInitialized(false);
                setIsServiceRunning(false);
            }
        }
    }["useCentralData.useCallback[checkServiceStatus]"], []);
    // Initialize service directly if not already initialized
    const initializeService = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCentralData.useCallback[initializeService]": async ()=>{
            try {
                console.log('🚀 Initializing Central Data Manager directly...');
                if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].getStatus().isInitialized) {
                    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].initialize();
                    console.log('✅ Central Data Manager initialized');
                }
                if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].getStatus().isRunning) {
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].start();
                    console.log('✅ Central Data Manager started');
                }
                setIsInitialized(true);
                setIsServiceRunning(true);
            } catch (error) {
                console.error('❌ Error initializing service:', error);
                setIsInitialized(false);
                setIsServiceRunning(false);
            }
        }
    }["useCentralData.useCallback[initializeService]"], []);
    // Refresh specific data type
    const refreshData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCentralData.useCallback[refreshData]": async (dataType)=>{
            if (dataType) {
                await fetchData(dataType);
            } else {
                // Refresh all data
                await Promise.all([
                    fetchData('nifty200'),
                    fetchData('bohEligible'),
                    fetchData('weeklyHighSignals'),
                    fetchData('gttOrders')
                ]);
            }
        }
    }["useCentralData.useCallback[refreshData]"], [
        fetchData
    ]);
    // Set up polling for real-time updates
    const setupPolling = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCentralData.useCallback[setupPolling]": ()=>{
            // Clear existing intervals
            pollingIntervals.current.forEach({
                "useCentralData.useCallback[setupPolling]": (interval)=>clearInterval(interval)
            }["useCentralData.useCallback[setupPolling]"]);
            pollingIntervals.current.clear();
            // Set up new intervals
            const intervals = {
                nifty200: 30000,
                bohEligible: 60000,
                weeklyHighSignals: 300000,
                gttOrders: 30000 // 30 seconds
            };
            Object.entries(intervals).forEach({
                "useCentralData.useCallback[setupPolling]": (param)=>{
                    let [dataType, interval] = param;
                    const intervalId = setInterval({
                        "useCentralData.useCallback[setupPolling].intervalId": ()=>{
                            fetchData(dataType);
                        }
                    }["useCentralData.useCallback[setupPolling].intervalId"], interval);
                    pollingIntervals.current.set(dataType, intervalId);
                }
            }["useCentralData.useCallback[setupPolling]"]);
            console.log('⏰ Polling intervals set up for real-time updates');
        }
    }["useCentralData.useCallback[setupPolling]"], [
        fetchData
    ]);
    // Set up data listeners for real-time updates
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useCentralData.useEffect": ()=>{
            console.log('🔗 Setting up Central Data Manager listeners...');
            // Add listeners for each data type
            const nifty200Listener = {
                "useCentralData.useEffect.nifty200Listener": (data, timestamp)=>{
                    setNifty200({
                        data,
                        lastUpdated: timestamp,
                        isLoading: false,
                        error: null
                    });
                    console.log("📊 Nifty200 data updated: ".concat(data.length, " stocks"));
                }
            }["useCentralData.useEffect.nifty200Listener"];
            const bohEligibleListener = {
                "useCentralData.useEffect.bohEligibleListener": (data, timestamp)=>{
                    setBohEligible({
                        data,
                        lastUpdated: timestamp,
                        isLoading: false,
                        error: null
                    });
                    console.log("📊 BOH Eligible data updated: ".concat(data.length, " stocks"));
                }
            }["useCentralData.useEffect.bohEligibleListener"];
            const weeklyHighSignalsListener = {
                "useCentralData.useEffect.weeklyHighSignalsListener": (data, timestamp)=>{
                    setWeeklyHighSignals({
                        data,
                        lastUpdated: timestamp,
                        isLoading: false,
                        error: null
                    });
                    console.log("📊 Weekly High Signals updated: ".concat(data.length, " signals"));
                }
            }["useCentralData.useEffect.weeklyHighSignalsListener"];
            const gttOrdersListener = {
                "useCentralData.useEffect.gttOrdersListener": (data, timestamp)=>{
                    setGttOrders({
                        data,
                        lastUpdated: timestamp,
                        isLoading: false,
                        error: null
                    });
                    console.log("📊 GTT Orders updated: ".concat(data.length, " orders"));
                }
            }["useCentralData.useEffect.gttOrdersListener"];
            // Add listeners to Central Data Manager
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].addListener('nifty200', nifty200Listener);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].addListener('bohEligible', bohEligibleListener);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].addListener('weeklyHighSignals', weeklyHighSignalsListener);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].addListener('gttOrders', gttOrdersListener);
            // Initialize and load initial data
            const initialize = {
                "useCentralData.useEffect.initialize": async ()=>{
                    checkServiceStatus();
                    if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].getStatus().isInitialized) {
                        await initializeService();
                    }
                    // Load initial data
                    await refreshData();
                }
            }["useCentralData.useEffect.initialize"];
            initialize();
            // Cleanup listeners on unmount
            return ({
                "useCentralData.useEffect": ()=>{
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].removeListener('nifty200', nifty200Listener);
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].removeListener('bohEligible', bohEligibleListener);
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].removeListener('weeklyHighSignals', weeklyHighSignalsListener);
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].removeListener('gttOrders', gttOrdersListener);
                    pollingIntervals.current.forEach({
                        "useCentralData.useEffect": (interval)=>clearInterval(interval)
                    }["useCentralData.useEffect"]);
                    pollingIntervals.current.clear();
                }
            })["useCentralData.useEffect"];
        }
    }["useCentralData.useEffect"], []);
    // Re-setup polling when service status changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useCentralData.useEffect": ()=>{
            if (isServiceRunning) {
                setupPolling();
            }
        }
    }["useCentralData.useEffect"], [
        isServiceRunning,
        setupPolling
    ]);
    return {
        nifty200,
        bohEligible,
        weeklyHighSignals,
        gttOrders,
        refreshData,
        isInitialized,
        isServiceRunning
    };
}
_s(useCentralData, "ocqP2Cajj1A1LQiUHchJlHIA+9I=");
function useNifty200Stocks() {
    _s1();
    const { nifty200, refreshData } = useCentralData();
    return {
        stocks: nifty200.data,
        lastUpdated: nifty200.lastUpdated,
        isLoading: nifty200.isLoading,
        error: nifty200.error,
        refresh: ()=>refreshData('nifty200')
    };
}
_s1(useNifty200Stocks, "hh1p3yrpgwOccNG5BxY+v1fol3k=", false, function() {
    return [
        useCentralData
    ];
});
function useBOHEligibleStocks() {
    _s2();
    const { bohEligible, refreshData } = useCentralData();
    return {
        stocks: bohEligible.data,
        lastUpdated: bohEligible.lastUpdated,
        isLoading: bohEligible.isLoading,
        error: bohEligible.error,
        refresh: ()=>refreshData('bohEligible')
    };
}
_s2(useBOHEligibleStocks, "ytLLh888rLsxvRKIg1QsrNHb7W0=", false, function() {
    return [
        useCentralData
    ];
});
function useWeeklyHighSignals() {
    _s3();
    const { weeklyHighSignals, refreshData } = useCentralData();
    return {
        signals: weeklyHighSignals.data,
        lastUpdated: weeklyHighSignals.lastUpdated,
        isLoading: weeklyHighSignals.isLoading,
        error: weeklyHighSignals.error,
        refresh: ()=>refreshData('weeklyHighSignals')
    };
}
_s3(useWeeklyHighSignals, "ENRZAmJYVh9S2v8soViOX18A5Y4=", false, function() {
    return [
        useCentralData
    ];
});
function useGTTOrders() {
    _s4();
    const { gttOrders, refreshData } = useCentralData();
    return {
        orders: gttOrders.data,
        lastUpdated: gttOrders.lastUpdated,
        isLoading: gttOrders.isLoading,
        error: gttOrders.error,
        refresh: ()=>refreshData('gttOrders')
    };
}
_s4(useGTTOrders, "S0WK/PRCgyd3/me34GBgMWdmcsc=", false, function() {
    return [
        useCentralData
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/dashboard/weekly-high/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>WeeklyHighSignalPage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-down.js [app-client] (ecmascript) <export default as ChevronDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$up$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronUp$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-up.js [app-client] (ecmascript) <export default as ChevronUp>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/refresh-cw.js [app-client] (ecmascript) <export default as RefreshCw>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trending-up.js [app-client] (ecmascript) <export default as TrendingUp>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/calendar.js [app-client] (ecmascript) <export default as Calendar>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$target$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Target$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/target.js [app-client] (ecmascript) <export default as Target>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader.js [app-client] (ecmascript) <export default as Loader>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-alert.js [app-client] (ecmascript) <export default as AlertCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$holdings$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/holdings-service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useCentralData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useCentralData.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
function WeeklyHighSignalPage() {
    _s();
    // Central Data Manager for real-time Weekly High Signals
    const { signals: weeklyHighSignals, lastUpdated: signalsLastUpdated, isLoading: signalsIsLoading, error: signalsError, refresh: refreshSignals } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useCentralData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useWeeklyHighSignals"])();
    const [stocks, setStocks] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [expandedStocks, setExpandedStocks] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(new Set());
    const [lastUpdate, setLastUpdate] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Toggle expanded state for a stock
    const toggleExpanded = (symbol)=>{
        const newExpanded = new Set(expandedStocks);
        if (newExpanded.has(symbol)) {
            newExpanded.delete(symbol);
        } else {
            newExpanded.add(symbol);
        }
        setExpandedStocks(newExpanded);
    };
    // Generate mock OHLC data for last week
    const generateOHLCData = (currentPrice)=>{
        const days = [
            'Monday',
            'Tuesday',
            'Wednesday',
            'Thursday',
            'Friday'
        ];
        const ohlcData = [];
        // Get last week's dates
        const today = new Date();
        const lastFriday = new Date(today);
        lastFriday.setDate(today.getDate() - (today.getDay() + 2) % 7); // Get last Friday
        let basePrice = currentPrice * (0.95 + Math.random() * 0.1); // Start with price variation
        for(let i = 0; i < 5; i++){
            const date = new Date(lastFriday);
            date.setDate(lastFriday.getDate() - (4 - i)); // Monday to Friday of last week
            const variation = 0.02 + Math.random() * 0.03; // 2-5% daily variation
            const open = basePrice;
            const high = open * (1 + variation);
            const low = open * (1 - variation * 0.7);
            const close = low + (high - low) * Math.random();
            ohlcData.push({
                day: days[i],
                date: date.toLocaleDateString('en-GB', {
                    day: '2-digit',
                    month: 'short',
                    year: 'numeric'
                }),
                open: parseFloat(open.toFixed(2)),
                high: parseFloat(high.toFixed(2)),
                low: parseFloat(low.toFixed(2)),
                close: parseFloat(close.toFixed(2))
            });
            basePrice = close; // Next day starts with previous close
        }
        return ohlcData;
    };
    // Calculate weekly high stock data
    const calculateWeeklyHighData = (stock)=>{
        const ohlcData = generateOHLCData(stock.price);
        const lastWeekHighest = Math.max(...ohlcData.map((d)=>d.high));
        const suggestedBuyPrice = lastWeekHighest + 0.05;
        const percentDifference = (stock.price - suggestedBuyPrice) / suggestedBuyPrice * 100;
        const suggestedGTTQuantity = Math.floor(2000 / suggestedBuyPrice);
        return {
            symbol: stock.symbol,
            name: stock.name,
            currentPrice: stock.price,
            lastWeekHighest,
            suggestedBuyPrice,
            percentDifference,
            suggestedGTTQuantity,
            ohlcData,
            isBOHEligible: stock.isBOHEligible || false,
            inHoldings: stock.inHoldings
        };
    };
    // Load BOH eligible stocks
    const loadBOHEligibleStocks = async ()=>{
        setLoading(true);
        setError(null);
        try {
            console.log('🔍 Loading BOH eligible stocks for Weekly High signals...');
            // Fetch BOH eligible stocks from the API
            const response = await fetch('/api/stocks/nifty200?batchIndex=0&batchSize=200');
            const data = await response.json();
            if (!data.success) {
                throw new Error(data.error || 'Failed to fetch stock data');
            }
            // Get current holdings
            const holdingSymbols = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$holdings$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["holdingsService"].getAllHoldings().map((h)=>h.symbol);
            // Filter for BOH eligible stocks not in holdings
            const bohEligibleStocks = data.data.stocks.filter((stock)=>stock.isBOHEligible && !holdingSymbols.includes(stock.symbol));
            console.log("✅ Found ".concat(bohEligibleStocks.length, " BOH eligible stocks not in holdings"));
            // Calculate weekly high data for each stock
            const weeklyHighStocks = bohEligibleStocks.map(calculateWeeklyHighData);
            // Sort by percentage difference (closest to suggested buy price first)
            weeklyHighStocks.sort((a, b)=>Math.abs(a.percentDifference) - Math.abs(b.percentDifference));
            setStocks(weeklyHighStocks);
            setLastUpdate(new Date());
        } catch (err) {
            console.error('❌ Error loading BOH eligible stocks:', err);
            setError(err instanceof Error ? err.message : 'Failed to load data');
        } finally{
            setLoading(false);
        }
    };
    // Use Central Data Manager signals when available
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "WeeklyHighSignalPage.useEffect": ()=>{
            if (weeklyHighSignals.length > 0) {
                console.log("📊 Using ".concat(weeklyHighSignals.length, " signals from Central Data Manager"));
                // Convert signals to WeeklyHighStock format
                const convertedStocks = weeklyHighSignals.map({
                    "WeeklyHighSignalPage.useEffect.convertedStocks": (signal)=>({
                            symbol: signal.symbol,
                            name: signal.name,
                            currentPrice: signal.currentPrice,
                            lastWeekHigh: signal.lastWeekHigh,
                            suggestedBuyPrice: signal.suggestedBuyPrice,
                            percentDifference: signal.percentDifference,
                            signalStrength: signal.signalStrength,
                            suggestedGTTQuantity: signal.suggestedGTTQuantity,
                            isEligible: true,
                            inHoldings: false
                        })
                }["WeeklyHighSignalPage.useEffect.convertedStocks"]);
                setStocks(convertedStocks);
                setLastUpdate(signalsLastUpdated || new Date());
                setLoading(false);
                setError(null);
            } else if (signalsError) {
                setError(signalsError);
                setLoading(false);
            } else if (!signalsIsLoading && weeklyHighSignals.length === 0) {
                // Fallback to original loading method if no signals from Central Data Manager
                loadBOHEligibleStocks();
            }
        }
    }["WeeklyHighSignalPage.useEffect"], [
        weeklyHighSignals,
        signalsLastUpdated,
        signalsError,
        signalsIsLoading
    ]);
    // Load data on component mount (fallback)
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "WeeklyHighSignalPage.useEffect": ()=>{
            if (weeklyHighSignals.length === 0 && !signalsIsLoading) {
                loadBOHEligibleStocks();
            }
        }
    }["WeeklyHighSignalPage.useEffect"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "p-6 max-w-7xl mx-auto space-y-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-between",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                className: "text-2xl font-bold text-gray-900",
                                children: "Weekly High Signal"
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                lineNumber: 215,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-gray-600 mt-1",
                                children: [
                                    "BOH Eligible Stocks (excluding currently held stocks) • ",
                                    stocks.length,
                                    " stocks found"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                lineNumber: 216,
                                columnNumber: 11
                            }, this),
                            lastUpdate && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-sm text-gray-500 mt-1",
                                children: [
                                    "Last updated: ",
                                    lastUpdate.toLocaleTimeString()
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                lineNumber: 220,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                        lineNumber: 214,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center space-x-3",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: ()=>{
                                console.log('🔄 Refreshing Weekly High Signals via Central Data Manager...');
                                refreshSignals();
                            },
                            disabled: loading || signalsIsLoading,
                            className: "px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center space-x-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__["RefreshCw"], {
                                    className: "h-4 w-4 ".concat(loading || signalsIsLoading ? 'animate-spin' : '')
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                    lineNumber: 235,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    children: "Refresh"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                    lineNumber: 236,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                            lineNumber: 227,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                        lineNumber: 226,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                lineNumber: 213,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid grid-cols-1 md:grid-cols-4 gap-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-white rounded-lg shadow-sm border border-gray-200 p-6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-between",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm font-medium text-gray-600",
                                            children: "BOH Eligible Stocks"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                            lineNumber: 246,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-2xl font-bold text-blue-600 mt-1",
                                            children: stocks.length
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                            lineNumber: 247,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                    lineNumber: 245,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$target$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Target$3e$__["Target"], {
                                    className: "h-8 w-8 text-blue-600"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                    lineNumber: 249,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                            lineNumber: 244,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                        lineNumber: 243,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-white rounded-lg shadow-sm border border-gray-200 p-6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-between",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm font-medium text-gray-600",
                                            children: "Near Buy Price"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                            lineNumber: 256,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-2xl font-bold text-green-600 mt-1",
                                            children: stocks.filter((s)=>Math.abs(s.percentDifference) <= 5).length
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                            lineNumber: 257,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                    lineNumber: 255,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__["TrendingUp"], {
                                    className: "h-8 w-8 text-green-600"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                    lineNumber: 261,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                            lineNumber: 254,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                        lineNumber: 253,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-white rounded-lg shadow-sm border border-gray-200 p-6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-between",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm font-medium text-gray-600",
                                            children: "Avg GTT Quantity"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                            lineNumber: 268,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-2xl font-bold text-gray-900 mt-1",
                                            children: stocks.length > 0 ? Math.round(stocks.reduce((sum, s)=>sum + s.suggestedGTTQuantity, 0) / stocks.length) : 0
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                            lineNumber: 269,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                    lineNumber: 267,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"], {
                                    className: "h-8 w-8 text-gray-600"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                    lineNumber: 273,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                            lineNumber: 266,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                        lineNumber: 265,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-white rounded-lg shadow-sm border border-gray-200 p-6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-between",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm font-medium text-gray-600",
                                            children: "Total Investment"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                            lineNumber: 280,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-2xl font-bold text-purple-600 mt-1",
                                            children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatCurrency"])(stocks.length * 2000)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                            lineNumber: 281,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                    lineNumber: 279,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$target$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Target$3e$__["Target"], {
                                    className: "h-8 w-8 text-purple-600"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                    lineNumber: 285,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                            lineNumber: 278,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                        lineNumber: 277,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                lineNumber: 242,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white rounded-lg shadow-sm border border-gray-200",
                children: [
                    error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-6 border-b border-gray-200",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center text-red-600",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__["AlertCircle"], {
                                    className: "h-5 w-5 mr-2"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                    lineNumber: 295,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    children: [
                                        "Error: ",
                                        error
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                    lineNumber: 296,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                            lineNumber: 294,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                        lineNumber: 293,
                        columnNumber: 11
                    }, this),
                    loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-12 text-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader$3e$__["Loader"], {
                                className: "h-8 w-8 mx-auto mb-4 animate-spin text-blue-600"
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                lineNumber: 303,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-gray-500",
                                children: "Loading BOH eligible stocks..."
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                lineNumber: 304,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-sm text-gray-400 mt-1",
                                children: "Analyzing weekly high patterns"
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                lineNumber: 305,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                        lineNumber: 302,
                        columnNumber: 11
                    }, this) : stocks.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-12 text-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__["TrendingUp"], {
                                className: "h-12 w-12 mx-auto mb-4 text-gray-300"
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                lineNumber: 309,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-gray-500",
                                children: "No BOH eligible stocks found"
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                lineNumber: 310,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-sm text-gray-400 mt-1",
                                children: "All BOH eligible stocks are currently in holdings"
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                lineNumber: 311,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                        lineNumber: 308,
                        columnNumber: 11
                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "divide-y divide-gray-200",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "p-4 bg-gray-50 grid grid-cols-7 gap-4 text-sm font-medium text-gray-700",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: "Stock"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                        lineNumber: 317,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-right",
                                        children: "Current Price"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                        lineNumber: 318,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-right",
                                        children: "Last Week's High"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                        lineNumber: 319,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-right",
                                        children: "Suggested Buy Price"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                        lineNumber: 320,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-right",
                                        children: "% Difference"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                        lineNumber: 321,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-right",
                                        children: "GTT Quantity"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                        lineNumber: 322,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-center",
                                        children: "Details"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                        lineNumber: 323,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                lineNumber: 316,
                                columnNumber: 13
                            }, this),
                            stocks.map((stock)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "p-4 hover:bg-gray-50 grid grid-cols-7 gap-4 items-center",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex items-center space-x-2",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                                    className: "font-medium text-gray-900",
                                                                    children: stock.symbol
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                                    lineNumber: 334,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full",
                                                                    children: "BOH"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                                    lineNumber: 335,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                            lineNumber: 333,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-sm text-gray-600 truncate",
                                                            children: stock.name
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                            lineNumber: 339,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                    lineNumber: 332,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-right",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "font-medium text-gray-900",
                                                        children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatCurrency"])(stock.currentPrice)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                        lineNumber: 344,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                    lineNumber: 343,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-right",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "font-medium text-gray-900",
                                                        children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatCurrency"])(stock.lastWeekHighest)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                        lineNumber: 349,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                    lineNumber: 348,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-right",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "font-medium text-green-600",
                                                            children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatCurrency"])(stock.suggestedBuyPrice)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                            lineNumber: 354,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-xs text-gray-500",
                                                            children: "High + ₹0.05"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                            lineNumber: 355,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                    lineNumber: 353,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-right",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "font-medium ".concat(stock.percentDifference >= 0 ? 'text-green-600' : 'text-red-600'),
                                                        children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatPercentage"])(stock.percentDifference)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                        lineNumber: 360,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                    lineNumber: 359,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-right",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "font-medium text-gray-900",
                                                            children: stock.suggestedGTTQuantity
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                            lineNumber: 371,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-xs text-gray-500",
                                                            children: "₹2,000 ÷ Buy Price"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                            lineNumber: 372,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                    lineNumber: 370,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-center",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        onClick: ()=>toggleExpanded(stock.symbol),
                                                        className: "p-2 hover:bg-gray-100 rounded-lg transition-colors",
                                                        children: expandedStocks.has(stock.symbol) ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$up$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronUp$3e$__["ChevronUp"], {
                                                            className: "h-4 w-4 text-gray-600"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                            lineNumber: 382,
                                                            columnNumber: 25
                                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__["ChevronDown"], {
                                                            className: "h-4 w-4 text-gray-600"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                            lineNumber: 384,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                        lineNumber: 377,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                    lineNumber: 376,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                            lineNumber: 330,
                                            columnNumber: 17
                                        }, this),
                                        expandedStocks.has(stock.symbol) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "px-4 pb-4 bg-gray-50",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "bg-white rounded-lg border border-gray-200 overflow-hidden",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "px-4 py-3 bg-gray-100 border-b border-gray-200",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                                                            className: "font-medium text-gray-900",
                                                            children: [
                                                                "Last Week's OHLC Data - ",
                                                                stock.symbol
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                            lineNumber: 395,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                        lineNumber: 394,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "overflow-x-auto",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
                                                            className: "w-full",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
                                                                    className: "bg-gray-50",
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                                                className: "px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",
                                                                                children: "Day"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                                                lineNumber: 404,
                                                                                columnNumber: 31
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                                                className: "px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",
                                                                                children: "Date"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                                                lineNumber: 407,
                                                                                columnNumber: 31
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                                                className: "px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",
                                                                                children: "Open"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                                                lineNumber: 410,
                                                                                columnNumber: 31
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                                                className: "px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",
                                                                                children: "High"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                                                lineNumber: 413,
                                                                                columnNumber: 31
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                                                className: "px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",
                                                                                children: "Low"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                                                lineNumber: 416,
                                                                                columnNumber: 31
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                                                className: "px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",
                                                                                children: "Close"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                                                lineNumber: 419,
                                                                                columnNumber: 31
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                                                className: "px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",
                                                                                children: "Last Week's Highest"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                                                lineNumber: 422,
                                                                                columnNumber: 31
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                                        lineNumber: 403,
                                                                        columnNumber: 29
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                                    lineNumber: 402,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                                                                    className: "bg-white divide-y divide-gray-200",
                                                                    children: stock.ohlcData.map((dayData, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                                            className: "hover:bg-gray-50",
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                                    className: "px-4 py-3 text-sm font-medium text-gray-900",
                                                                                    children: dayData.day
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                                                    lineNumber: 430,
                                                                                    columnNumber: 33
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                                    className: "px-4 py-3 text-sm text-gray-600",
                                                                                    children: dayData.date
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                                                    lineNumber: 433,
                                                                                    columnNumber: 33
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                                    className: "px-4 py-3 text-sm text-gray-900 text-right",
                                                                                    children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatCurrency"])(dayData.open)
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                                                    lineNumber: 436,
                                                                                    columnNumber: 33
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                                    className: "px-4 py-3 text-sm text-gray-900 text-right",
                                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                        className: dayData.high === stock.lastWeekHighest ? 'font-bold text-green-600' : '',
                                                                                        children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatCurrency"])(dayData.high)
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                                                        lineNumber: 440,
                                                                                        columnNumber: 35
                                                                                    }, this)
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                                                    lineNumber: 439,
                                                                                    columnNumber: 33
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                                    className: "px-4 py-3 text-sm text-gray-900 text-right",
                                                                                    children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatCurrency"])(dayData.low)
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                                                    lineNumber: 444,
                                                                                    columnNumber: 33
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                                    className: "px-4 py-3 text-sm text-gray-900 text-right",
                                                                                    children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatCurrency"])(dayData.close)
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                                                    lineNumber: 447,
                                                                                    columnNumber: 33
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                                    className: "px-4 py-3 text-sm text-right",
                                                                                    children: index === stock.ohlcData.length - 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                        className: "font-bold text-green-600",
                                                                                        children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatCurrency"])(stock.lastWeekHighest)
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                                                        lineNumber: 452,
                                                                                        columnNumber: 37
                                                                                    }, this)
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                                                    lineNumber: 450,
                                                                                    columnNumber: 33
                                                                                }, this)
                                                                            ]
                                                                        }, "".concat(stock.symbol, "-").concat(index), true, {
                                                                            fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                                            lineNumber: 429,
                                                                            columnNumber: 31
                                                                        }, this))
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                                    lineNumber: 427,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                            lineNumber: 401,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                        lineNumber: 400,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                                lineNumber: 393,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                            lineNumber: 392,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, stock.symbol, true, {
                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                    lineNumber: 328,
                                    columnNumber: 15
                                }, this))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                        lineNumber: 314,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                lineNumber: 291,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-blue-50 border border-blue-200 rounded-lg p-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-start",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"], {
                            className: "h-5 w-5 text-blue-600 mt-0.5 mr-3"
                        }, void 0, false, {
                            fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                            lineNumber: 474,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "font-medium text-blue-900",
                                    children: "New Darvas Box Strategy - Signal Timing"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                    lineNumber: 476,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-blue-700 mt-1",
                                    children: "The signal generation for the New Darvas Box Strategy runs every Friday at 8:00 PM. This ensures fresh weekly high analysis for the upcoming trading week."
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                                    lineNumber: 477,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                            lineNumber: 475,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                    lineNumber: 473,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
                lineNumber: 472,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/dashboard/weekly-high/page.tsx",
        lineNumber: 211,
        columnNumber: 5
    }, this);
}
_s(WeeklyHighSignalPage, "A4XmTG1p8QIZCH4snoLSrmO4Rdg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useCentralData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useWeeklyHighSignals"]
    ];
});
_c = WeeklyHighSignalPage;
var _c;
__turbopack_context__.k.register(_c, "WeeklyHighSignalPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_d42bbf2e._.js.map