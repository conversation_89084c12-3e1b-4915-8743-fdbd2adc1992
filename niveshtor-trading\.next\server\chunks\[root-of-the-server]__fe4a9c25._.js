module.exports = {

"[project]/.next-internal/server/app/api/test-signal-pipeline/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[project]/src/lib/cache-service.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Cache service for optimizing data fetching and reducing API calls
__turbopack_context__.s({
    "CacheKeys": ()=>CacheKeys,
    "cacheService": ()=>cacheService
});
class CacheService {
    cache = new Map();
    DEFAULT_TTL = 5 * 60 * 1000;
    STOCK_DATA_TTL = 30 * 1000;
    STOCK_NAMES_TTL = 24 * 60 * 60 * 1000;
    BROKER_DATA_TTL = 10 * 1000;
    PORTFOLIO_DATA_TTL = 60 * 1000;
    // Get TTL based on data type
    getTTL(key) {
        if (key.includes('stock-name') || key.includes('names-map')) {
            return this.STOCK_NAMES_TTL;
        }
        if (key.includes('stock') || key.includes('nifty')) {
            return this.STOCK_DATA_TTL;
        }
        if (key.includes('broker') || key.includes('balance')) {
            return this.BROKER_DATA_TTL;
        }
        if (key.includes('portfolio') || key.includes('holdings')) {
            return this.PORTFOLIO_DATA_TTL;
        }
        return this.DEFAULT_TTL;
    }
    // Set cache entry
    set(key, data, customTTL) {
        const ttl = customTTL || this.getTTL(key);
        const now = Date.now();
        this.cache.set(key, {
            data,
            timestamp: now,
            expiresAt: now + ttl
        });
    }
    // Get cache entry
    get(key) {
        const entry = this.cache.get(key);
        if (!entry) {
            return null;
        }
        // Check if expired
        if (Date.now() > entry.expiresAt) {
            this.cache.delete(key);
            return null;
        }
        return entry.data;
    }
    // Check if key exists and is valid
    has(key) {
        const entry = this.cache.get(key);
        if (!entry) {
            return false;
        }
        // Check if expired
        if (Date.now() > entry.expiresAt) {
            this.cache.delete(key);
            return false;
        }
        return true;
    }
    // Clear specific key
    delete(key) {
        this.cache.delete(key);
    }
    // Clear all cache
    clear() {
        this.cache.clear();
    }
    // Clear expired entries
    cleanup() {
        const now = Date.now();
        for (const [key, entry] of this.cache.entries()){
            if (now > entry.expiresAt) {
                this.cache.delete(key);
            }
        }
    }
    // Get cache stats
    getStats() {
        return {
            size: this.cache.size,
            keys: Array.from(this.cache.keys()),
            hitRate: 0 // TODO: Implement hit rate tracking
        };
    }
    // Cached fetch wrapper
    async cachedFetch(key, fetchFn, customTTL) {
        // Try to get from cache first
        const cached = this.get(key);
        if (cached !== null) {
            return cached;
        }
        // Fetch fresh data
        try {
            const data = await fetchFn();
            this.set(key, data, customTTL);
            return data;
        } catch (error) {
            // If fetch fails, try to return stale data if available
            const staleEntry = this.cache.get(key);
            if (staleEntry) {
                console.warn(`Using stale data for ${key} due to fetch error:`, error);
                return staleEntry.data;
            }
            throw error;
        }
    }
    // Prefetch data in background
    async prefetch(key, fetchFn, customTTL) {
        // Only prefetch if not already cached
        if (!this.has(key)) {
            try {
                const data = await fetchFn();
                this.set(key, data, customTTL);
            } catch (error) {
                console.warn(`Prefetch failed for ${key}:`, error);
            }
        }
    }
    // Batch fetch with caching
    async batchFetch(requests) {
        const results = [];
        const fetchPromises = [];
        for (const request of requests){
            const cached = this.get(request.key);
            if (cached !== null) {
                results.push(cached);
            } else {
                fetchPromises.push(request.fetchFn().then((data)=>{
                    this.set(request.key, data, request.ttl);
                    return data;
                }));
            }
        }
        // Wait for all fetches to complete
        const fetchedResults = await Promise.all(fetchPromises);
        results.push(...fetchedResults);
        return results;
    }
    // Invalidate cache by pattern
    invalidatePattern(pattern) {
        const regex = new RegExp(pattern);
        for (const key of this.cache.keys()){
            if (regex.test(key)) {
                this.cache.delete(key);
            }
        }
    }
    // Auto cleanup interval
    startAutoCleanup(intervalMs = 5 * 60 * 1000) {
        setInterval(()=>{
            this.cleanup();
        }, intervalMs);
    }
}
const cacheService = new CacheService();
// Start auto cleanup
if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
;
const CacheKeys = {
    brokerBalance: (userId)=>`broker-balance-${userId}`,
    fundAllocation: (userId, strategy)=>`fund-allocation-${userId}-${strategy}`,
    portfolioSummary: (userId)=>`portfolio-summary-${userId}`,
    niftyStocks: (batchIndex)=>`nifty-stocks-batch-${batchIndex}`,
    stockQuote: (symbol)=>`stock-quote-${symbol}`,
    stockName: (symbol)=>`stock-name-${symbol}`,
    stockNamesMap: ()=>'stock-names-map',
    stockPriceData: (symbol)=>`stock-price-data-${symbol}`,
    stockSearch: (query)=>`stock-search-${query}`,
    yahooQuotes: (symbols)=>`yahoo-quotes-${symbols.sort().join(',')}`
};
}),
"[project]/src/lib/stock-names-service.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Stock names service for caching and managing stock company names
__turbopack_context__.s({
    "stockNamesService": ()=>stockNamesService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/cache-service.ts [app-route] (ecmascript)");
;
class StockNamesService {
    BATCH_SIZE = 50;
    MAX_RETRIES = 3;
    // Get stock name from cache or fetch if not available
    async getStockName(symbol) {
        // Try to get from cache first
        const cached = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cacheService"].get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CacheKeys"].stockName(symbol));
        if (cached) {
            return cached;
        }
        // If not in cache, fetch from Yahoo Finance
        try {
            const { yahooFinanceService } = await __turbopack_context__.r("[project]/src/lib/yahoo-finance.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
            const quote = await yahooFinanceService.getQuote(symbol);
            const name = quote?.name || symbol.replace('.NS', '');
            // Cache the name for 24 hours
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cacheService"].set(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CacheKeys"].stockName(symbol), name);
            return name;
        } catch (error) {
            console.warn(`Failed to fetch name for ${symbol}:`, error);
            // Return symbol without .NS as fallback
            return symbol.replace('.NS', '');
        }
    }
    // Get multiple stock names efficiently
    async getStockNames(symbols) {
        const namesMap = new Map();
        const uncachedSymbols = [];
        // Check cache for each symbol
        for (const symbol of symbols){
            const cached = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cacheService"].get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CacheKeys"].stockName(symbol));
            if (cached) {
                namesMap.set(symbol, cached);
            } else {
                uncachedSymbols.push(symbol);
            }
        }
        // If all names are cached, return immediately
        if (uncachedSymbols.length === 0) {
            return namesMap;
        }
        console.log(`📝 Fetching names for ${uncachedSymbols.length} uncached stocks`);
        // Fetch uncached names in batches
        const batches = this.chunkArray(uncachedSymbols, this.BATCH_SIZE);
        for (const batch of batches){
            try {
                const { yahooFinanceService } = await __turbopack_context__.r("[project]/src/lib/yahoo-finance.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
                const quotes = await yahooFinanceService.getMultipleQuotes(batch);
                for (const quote of quotes){
                    if (quote && quote.symbol) {
                        // Use the name from the quote, or fallback to symbol without .NS
                        const name = quote.name || quote.symbol.replace('.NS', '');
                        namesMap.set(quote.symbol, name);
                        // Cache the name for 24 hours
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cacheService"].set(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CacheKeys"].stockName(quote.symbol), name);
                    }
                }
                // Ensure all symbols in the batch have names (even if fallback)
                for (const symbol of batch){
                    if (!namesMap.has(symbol)) {
                        const fallbackName = symbol.replace('.NS', '');
                        namesMap.set(symbol, fallbackName);
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cacheService"].set(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CacheKeys"].stockName(symbol), fallbackName);
                        console.log(`📝 Using fallback name for ${symbol}: ${fallbackName}`);
                    }
                }
                // Add delay between batches to avoid rate limiting
                if (batches.indexOf(batch) < batches.length - 1) {
                    await new Promise((resolve)=>setTimeout(resolve, 100));
                }
            } catch (error) {
                console.warn(`Failed to fetch names for batch:`, error);
                // Add fallback names for failed batch
                for (const symbol of batch){
                    if (!namesMap.has(symbol)) {
                        const fallbackName = symbol.replace('.NS', '');
                        namesMap.set(symbol, fallbackName);
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cacheService"].set(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CacheKeys"].stockName(symbol), fallbackName);
                    }
                }
            }
        }
        return namesMap;
    }
    // Get all cached stock names
    getCachedStockNames() {
        const cachedMap = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cacheService"].get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CacheKeys"].stockNamesMap());
        return cachedMap || new Map();
    }
    // Cache stock names map for quick access
    cacheStockNamesMap(namesMap) {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cacheService"].set(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CacheKeys"].stockNamesMap(), namesMap);
    }
    // Preload stock names for given symbols
    async preloadStockNames(symbols) {
        console.log(`🚀 Preloading names for ${symbols.length} stocks`);
        try {
            const namesMap = await this.getStockNames(symbols);
            this.cacheStockNamesMap(namesMap);
            console.log(`✅ Preloaded ${namesMap.size} stock names`);
        } catch (error) {
            console.error('Failed to preload stock names:', error);
        }
    }
    // Check if stock name is cached
    isNameCached(symbol) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cacheService"].has(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CacheKeys"].stockName(symbol));
    }
    // Get cache statistics for stock names
    getNamesCacheStats() {
        const stats = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cacheService"].getStats();
        const nameKeys = stats.keys.filter((key)=>key.includes('stock-name'));
        return {
            cachedCount: nameKeys.length,
            totalRequested: nameKeys.length,
            hitRate: nameKeys.length > 0 ? 0.8 : 0 // Estimated hit rate
        };
    }
    // Clear all cached stock names
    clearNamesCache() {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cacheService"].invalidatePattern('stock-name');
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cacheService"].delete(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CacheKeys"].stockNamesMap());
        console.log('🗑️ Cleared all cached stock names');
    }
    // Refresh stock names (force re-fetch)
    async refreshStockNames(symbols) {
        // Clear existing cache for these symbols
        for (const symbol of symbols){
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cacheService"].delete(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CacheKeys"].stockName(symbol));
        }
        // Fetch fresh names
        return await this.getStockNames(symbols);
    }
    // Utility function to chunk array into smaller arrays
    chunkArray(array, chunkSize) {
        const chunks = [];
        for(let i = 0; i < array.length; i += chunkSize){
            chunks.push(array.slice(i, i + chunkSize));
        }
        return chunks;
    }
    // Get stock name with fallback
    getStockNameSync(symbol) {
        const cached = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cacheService"].get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CacheKeys"].stockName(symbol));
        return cached || symbol.replace('.NS', '');
    }
    // Batch update stock names from quotes
    updateStockNamesFromQuotes(quotes) {
        for (const quote of quotes){
            if (quote && quote.symbol && quote.name) {
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cacheService"].set(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CacheKeys"].stockName(quote.symbol), quote.name);
            }
        }
    }
    // Force refresh all stock names (clears cache and re-fetches)
    async forceRefreshAllNames(symbols) {
        console.log('🔄 Force refreshing all stock names...');
        this.clearNamesCache();
        await this.preloadStockNames(symbols);
    }
}
const stockNamesService = new StockNamesService();
}),
"[project]/src/lib/yahoo-finance.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "yahooFinanceService": ()=>yahooFinanceService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/cache-service.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stock$2d$names$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stock-names-service.ts [app-route] (ecmascript)");
;
;
;
// Yahoo Finance API endpoints - using chart endpoint which is more reliable
const YAHOO_FINANCE_BASE_URL = 'https://query1.finance.yahoo.com/v8/finance/chart';
const YAHOO_SEARCH_URL = 'https://query1.finance.yahoo.com/v1/finance/search';
const YAHOO_QUOTE_URL = 'https://query1.finance.yahoo.com/v7/finance/quote';
const YAHOO_QUOTE_SUMMARY_URL = 'https://query2.finance.yahoo.com/v10/finance/quoteSummary';
// Alternative endpoints for better reliability
const YAHOO_CHART_URL = 'https://query1.finance.yahoo.com/v8/finance/chart';
// Known problematic stocks that often fail - handle with extra care
const PROBLEMATIC_STOCKS = new Set([
    'BOSCHLTD.NS',
    'BSOFT.NS',
    'MINDTREE.NS',
    'PVR.NS',
    'HDFC.NS' // Merged stock
]);
class YahooFinanceService {
    // Real-time update system
    updateListeners = new Set();
    isRealTimeActive = false;
    realTimeInterval = null;
    lastUpdateTime = 0;
    currentSymbols = [];
    cache = new Map();
    CACHE_DURATION = 30 * 1000;
    // Start real-time updates for given symbols
    startRealTimeUpdates(symbols, callback) {
        console.log(`🔄 Starting real-time updates for ${symbols.length} symbols`);
        this.currentSymbols = symbols;
        this.updateListeners.add(callback);
        if (!this.isRealTimeActive) {
            this.isRealTimeActive = true;
            this.scheduleNextUpdate();
        }
    }
    // Stop real-time updates for a specific callback
    stopRealTimeUpdates(callback) {
        this.updateListeners.delete(callback);
        if (this.updateListeners.size === 0) {
            this.isRealTimeActive = false;
            if (this.realTimeInterval) {
                clearTimeout(this.realTimeInterval);
                this.realTimeInterval = null;
            }
            console.log('⏹️ Stopped real-time updates - no active listeners');
        }
    }
    // Schedule the next update
    scheduleNextUpdate() {
        if (!this.isRealTimeActive) return;
        const now = Date.now();
        const timeSinceLastUpdate = now - this.lastUpdateTime;
        const timeUntilNextUpdate = Math.max(0, 30000 - timeSinceLastUpdate); // 30 seconds
        this.realTimeInterval = setTimeout(()=>{
            this.performRealTimeUpdate();
        }, timeUntilNextUpdate);
    }
    // Perform the actual real-time update
    async performRealTimeUpdate() {
        if (!this.isRealTimeActive || this.currentSymbols.length === 0) return;
        try {
            console.log(`🔄 Performing real-time update for ${this.currentSymbols.length} symbols`);
            const quotes = await this.getMultipleQuotesWithCachedNames(this.currentSymbols);
            this.lastUpdateTime = Date.now();
            // Notify all listeners
            this.updateListeners.forEach((callback)=>{
                try {
                    callback(quotes);
                } catch (error) {
                    console.error('❌ Error in update listener:', error);
                }
            });
            console.log(`✅ Real-time update completed: ${quotes.length} quotes updated`);
        } catch (error) {
            console.error('❌ Real-time update failed:', error);
        }
        // Schedule next update
        this.scheduleNextUpdate();
    }
    async makeRequest(url, params = {}) {
        try {
            console.log(`🌐 Making request to: ${url}`, params);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(url, {
                params,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept': 'application/json',
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache',
                    'Referer': 'https://finance.yahoo.com/'
                },
                timeout: 15000
            });
            console.log(`✅ Request successful, status: ${response.status}`);
            return response.data;
        } catch (error) {
            const errorDetails = {
                url,
                params,
                message: error?.message || 'Unknown error',
                status: error?.response?.status || 'No status',
                data: error?.response?.data || 'No data'
            };
            console.error('❌ Yahoo Finance API error:', errorDetails);
            // For historical data requests, return null instead of throwing
            if (url.includes('/v8/finance/chart/')) {
                console.warn(`⚠️ Historical data request failed, returning null`);
                return null;
            }
            throw new Error(`Failed to fetch data from Yahoo Finance: ${error?.message || 'Unknown error'}`);
        }
    }
    // Get price data only (without fetching names from API) - optimized for frequent updates
    async getPriceDataOnly(symbol) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${YAHOO_CHART_URL}/${symbol}`, {
                params: {
                    interval: '1d',
                    range: '1d',
                    includePrePost: false
                },
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Accept': 'application/json',
                    'Referer': 'https://finance.yahoo.com/'
                },
                timeout: 8000
            });
            const data = response.data;
            if (data.chart?.result?.[0]) {
                const result = data.chart.result[0];
                const meta = result.meta;
                const quote = result.indicators?.quote?.[0];
                const currentPrice = meta.regularMarketPrice || quote?.close && quote.close[quote.close.length - 1] || meta.previousClose || 0;
                const previousClose = meta.previousClose || meta.chartPreviousClose || currentPrice;
                const change = meta.regularMarketChange || currentPrice - previousClose;
                const changePercent = meta.regularMarketChangePercent || (previousClose > 0 ? change / previousClose * 100 : 0);
                if (currentPrice > 0) {
                    // Get 52-week high/low dates by analyzing historical data
                    let high52WeekDate;
                    let low52WeekDate;
                    try {
                        // Get 1-year historical data to find exact dates
                        const historicalResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${YAHOO_CHART_URL}/${symbol}`, {
                            params: {
                                interval: '1d',
                                range: '1y',
                                includePrePost: false
                            },
                            headers: {
                                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                                'Accept': 'application/json',
                                'Referer': 'https://finance.yahoo.com/'
                            },
                            timeout: 10000
                        });
                        const historicalData = historicalResponse.data;
                        if (historicalData.chart?.result?.[0]) {
                            const historicalResult = historicalData.chart.result[0];
                            const timestamps = historicalResult.timestamp;
                            const historicalQuote = historicalResult.indicators?.quote?.[0];
                            if (timestamps && historicalQuote?.high && historicalQuote?.low) {
                                const highs = historicalQuote.high;
                                const lows = historicalQuote.low;
                                // Find 52-week high and low with their dates
                                let maxHigh = -Infinity;
                                let minLow = Infinity;
                                let maxHighIndex = -1;
                                let minLowIndex = -1;
                                for(let i = 0; i < highs.length; i++){
                                    if (highs[i] && highs[i] > maxHigh) {
                                        maxHigh = highs[i];
                                        maxHighIndex = i;
                                    }
                                    if (lows[i] && lows[i] < minLow) {
                                        minLow = lows[i];
                                        minLowIndex = i;
                                    }
                                }
                                if (maxHighIndex >= 0 && minLowIndex >= 0) {
                                    high52WeekDate = new Date(timestamps[maxHighIndex] * 1000).toISOString().split('T')[0];
                                    low52WeekDate = new Date(timestamps[minLowIndex] * 1000).toISOString().split('T')[0];
                                }
                            }
                        }
                    } catch (historicalError) {
                        console.warn(`⚠️ Could not fetch historical data for ${symbol}:`, {
                            error: historicalError.message,
                            status: historicalError.response?.status,
                            timeout: historicalError.code === 'ECONNABORTED'
                        });
                    }
                    return {
                        symbol: symbol,
                        price: parseFloat(currentPrice.toString()),
                        change: parseFloat(change.toString()),
                        changePercent: parseFloat(changePercent.toString()),
                        volume: parseInt((meta.regularMarketVolume || quote?.volume?.[quote.volume.length - 1] || 0).toString()),
                        marketCap: meta.marketCap,
                        high52Week: parseFloat((meta.fiftyTwoWeekHigh || 0).toString()),
                        low52Week: parseFloat((meta.fiftyTwoWeekLow || 0).toString()),
                        high52WeekDate,
                        low52WeekDate,
                        avgVolume: parseInt((meta.averageDailyVolume3Month || 0).toString())
                    };
                }
            }
            return null;
        } catch (error) {
            console.warn(`Failed to get price data for ${symbol}:`, error);
            return null;
        }
    }
    // Get multiple price data only (batch operation)
    async getMultiplePriceDataOnly(symbols) {
        const results = [];
        const batchSize = 25;
        for(let i = 0; i < symbols.length; i += batchSize){
            const batch = symbols.slice(i, i + batchSize);
            const batchPromises = batch.map((symbol)=>this.getPriceDataOnly(symbol));
            try {
                const batchResults = await Promise.all(batchPromises);
                results.push(...batchResults.filter((result)=>result !== null));
                // Add delay between batches
                if (i + batchSize < symbols.length) {
                    await new Promise((resolve)=>setTimeout(resolve, 200));
                }
            } catch (error) {
                console.error(`Batch error for symbols ${batch.join(', ')}:`, error);
            }
        }
        return results;
    }
    // Get quote with cached name (optimized for frequent updates)
    async getQuoteWithCachedName(symbol) {
        try {
            // Get price data only
            const priceData = await this.getPriceDataOnly(symbol);
            if (!priceData) return null;
            // Get name from cache or use fallback
            const name = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stock$2d$names$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stockNamesService"].getStockNameSync(symbol);
            return {
                ...priceData,
                name
            };
        } catch (error) {
            console.warn(`Failed to get quote with cached name for ${symbol}:`, error);
            return null;
        }
    }
    // Get multiple quotes with cached names (batch operation)
    async getMultipleQuotesWithCachedNames(symbols) {
        try {
            console.log(`📊 Fetching quotes with cached names for ${symbols.length} symbols`);
            // Get price data for all symbols
            const priceDataList = await this.getMultiplePriceDataOnly(symbols);
            console.log(`💰 Got price data for ${priceDataList.length}/${symbols.length} symbols`);
            // Get cached names for all symbols
            const namesMap = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stock$2d$names$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stockNamesService"].getStockNames(symbols);
            console.log(`📝 Got names for ${namesMap.size}/${symbols.length} symbols`);
            // Combine price data with cached names
            const quotes = [];
            for (const priceData of priceDataList){
                const name = namesMap.get(priceData.symbol) || priceData.symbol.replace('.NS', '');
                const quote = {
                    ...priceData,
                    name
                };
                quotes.push(quote);
                // Log BOH eligibility data for debugging
                if (priceData.high52WeekDate && priceData.low52WeekDate) {
                    const highDate = new Date(priceData.high52WeekDate);
                    const lowDate = new Date(priceData.low52WeekDate);
                    const isBOHEligible = lowDate > highDate;
                    console.log(`🔍 ${priceData.symbol}: High=${priceData.high52WeekDate}, Low=${priceData.low52WeekDate}, BOH=${isBOHEligible}`);
                }
            }
            console.log(`✅ Combined ${quotes.length} quotes with cached names`);
            return quotes;
        } catch (error) {
            console.error('❌ Failed to get multiple quotes with cached names:', error);
            return [];
        }
    }
    async getQuote(symbol) {
        try {
            // Add .NS suffix for NSE stocks if not present
            const formattedSymbol = symbol.includes('.') ? symbol : `${symbol}.NS`;
            const data = await this.makeRequest(YAHOO_QUOTE_URL, {
                symbols: formattedSymbol
            });
            const result = data.quoteResponse?.result?.[0];
            if (!result) return null;
            return {
                symbol: result.symbol,
                name: result.longName || result.shortName || symbol,
                price: result.regularMarketPrice || 0,
                change: result.regularMarketChange || 0,
                changePercent: result.regularMarketChangePercent || 0,
                volume: result.regularMarketVolume || 0,
                marketCap: result.marketCap,
                high52Week: result.fiftyTwoWeekHigh,
                low52Week: result.fiftyTwoWeekLow,
                avgVolume: result.averageDailyVolume3Month
            };
        } catch (error) {
            console.error(`Error fetching quote for ${symbol}:`, error);
            return null;
        }
    }
    async getMultipleQuotes(symbols) {
        console.log(`🔍 Yahoo Finance: Fetching quotes for ${symbols.length} symbols:`, symbols.slice(0, 5));
        try {
            // Format symbols for NSE - ensure .NS suffix
            const formattedSymbols = symbols.map((symbol)=>{
                const formatted = symbol.includes('.') ? symbol : `${symbol}.NS`;
                return formatted;
            });
            console.log(`📝 Formatted symbols:`, formattedSymbols.slice(0, 5));
            const allResults = [];
            // Process each symbol individually using chart endpoint (more reliable)
            for(let i = 0; i < formattedSymbols.length; i++){
                const symbol = formattedSymbols[i];
                try {
                    console.log(`📊 Fetching data for ${symbol} (${i + 1}/${formattedSymbols.length})`);
                    let stockQuote = null;
                    // Try multiple approaches for better success rate
                    // Approach 1: Chart endpoint (most reliable)
                    try {
                        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${YAHOO_CHART_URL}/${symbol}`, {
                            params: {
                                interval: '1d',
                                range: '1d',
                                includePrePost: false
                            },
                            headers: {
                                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                                'Accept': 'application/json',
                                'Accept-Language': 'en-US,en;q=0.9',
                                'Referer': 'https://finance.yahoo.com/'
                            },
                            timeout: 8000
                        });
                        const data = response.data;
                        if (data.chart?.result?.[0]) {
                            const result = data.chart.result[0];
                            const meta = result.meta;
                            const quote = result.indicators?.quote?.[0];
                            // Extract current price from meta or latest quote data
                            const currentPrice = meta.regularMarketPrice || quote?.close && quote.close[quote.close.length - 1] || meta.previousClose || 0;
                            const previousClose = meta.previousClose || meta.chartPreviousClose || currentPrice;
                            // Calculate change and change percent
                            const change = meta.regularMarketChange || currentPrice - previousClose;
                            const changePercent = meta.regularMarketChangePercent || (previousClose > 0 ? change / previousClose * 100 : 0);
                            if (currentPrice > 0) {
                                // Get 52-week high/low dates by analyzing historical data
                                let high52WeekDate;
                                let low52WeekDate;
                                try {
                                    // Get 1-year historical data to find exact dates
                                    const historicalResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${YAHOO_CHART_URL}/${symbol}`, {
                                        params: {
                                            interval: '1d',
                                            range: '1y',
                                            includePrePost: false
                                        },
                                        headers: {
                                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                                            'Accept': 'application/json',
                                            'Referer': 'https://finance.yahoo.com/'
                                        },
                                        timeout: 5000
                                    });
                                    const historicalData = historicalResponse.data;
                                    if (historicalData.chart?.result?.[0]) {
                                        const historicalResult = historicalData.chart.result[0];
                                        const timestamps = historicalResult.timestamp;
                                        const historicalQuote = historicalResult.indicators?.quote?.[0];
                                        if (timestamps && historicalQuote?.high && historicalQuote?.low) {
                                            const highs = historicalQuote.high;
                                            const lows = historicalQuote.low;
                                            // Find 52-week high and low with their dates
                                            let maxHigh = -Infinity;
                                            let minLow = Infinity;
                                            let maxHighIndex = -1;
                                            let minLowIndex = -1;
                                            for(let i = 0; i < highs.length; i++){
                                                if (highs[i] && highs[i] > maxHigh) {
                                                    maxHigh = highs[i];
                                                    maxHighIndex = i;
                                                }
                                                if (lows[i] && lows[i] < minLow) {
                                                    minLow = lows[i];
                                                    minLowIndex = i;
                                                }
                                            }
                                            if (maxHighIndex >= 0 && minLowIndex >= 0) {
                                                high52WeekDate = new Date(timestamps[maxHighIndex] * 1000).toISOString().split('T')[0];
                                                low52WeekDate = new Date(timestamps[minLowIndex] * 1000).toISOString().split('T')[0];
                                            }
                                        }
                                    }
                                } catch (historicalError) {
                                    console.log(`⚠️ Could not fetch historical data for ${symbol} dates`);
                                }
                                stockQuote = {
                                    symbol: symbol,
                                    name: meta.longName || meta.shortName || symbol.replace('.NS', ''),
                                    price: parseFloat(currentPrice.toString()),
                                    change: parseFloat(change.toString()),
                                    changePercent: parseFloat(changePercent.toString()),
                                    volume: parseInt((meta.regularMarketVolume || quote?.volume?.[quote.volume.length - 1] || 0).toString()),
                                    marketCap: meta.marketCap,
                                    high52Week: parseFloat((meta.fiftyTwoWeekHigh || 0).toString()),
                                    low52Week: parseFloat((meta.fiftyTwoWeekLow || 0).toString()),
                                    high52WeekDate,
                                    low52WeekDate,
                                    avgVolume: parseInt((meta.averageDailyVolume3Month || 0).toString())
                                };
                                console.log(`✅ Chart API success for ${symbol}: ₹${stockQuote.price}`);
                            }
                        }
                    } catch (chartError) {
                        const errorMsg = chartError.response?.data?.chart?.error?.description || chartError.message;
                        if (errorMsg?.includes('delisted')) {
                            console.log(`🚫 ${symbol} is delisted: ${errorMsg}`);
                        } else {
                            console.log(`⚠️ Chart API failed for ${symbol}: ${errorMsg}, trying quote API...`);
                        }
                    }
                    // Approach 2: Quote endpoint (fallback)
                    if (!stockQuote) {
                        try {
                            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(YAHOO_QUOTE_URL, {
                                params: {
                                    symbols: symbol,
                                    formatted: true
                                },
                                headers: {
                                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                                    'Accept': 'application/json',
                                    'Referer': 'https://finance.yahoo.com/'
                                },
                                timeout: 8000
                            });
                            const data = response.data;
                            const result = data.quoteResponse?.result?.[0];
                            if (result && result.regularMarketPrice > 0) {
                                stockQuote = {
                                    symbol: symbol,
                                    name: result.longName || result.shortName || symbol.replace('.NS', ''),
                                    price: parseFloat(result.regularMarketPrice) || 0,
                                    change: parseFloat(result.regularMarketChange) || 0,
                                    changePercent: parseFloat(result.regularMarketChangePercent) || 0,
                                    volume: parseInt(result.regularMarketVolume) || 0,
                                    marketCap: result.marketCap,
                                    high52Week: parseFloat(result.fiftyTwoWeekHigh) || 0,
                                    low52Week: parseFloat(result.fiftyTwoWeekLow) || 0,
                                    avgVolume: parseInt(result.averageDailyVolume3Month) || 0
                                };
                                console.log(`✅ Quote API success for ${symbol}: ₹${stockQuote.price}`);
                            }
                        } catch (quoteError) {
                            console.log(`⚠️ Quote API also failed for ${symbol}`);
                        }
                    }
                    // If we got valid data, add it to results
                    if (stockQuote && stockQuote.price > 0) {
                        allResults.push(stockQuote);
                    } else {
                        console.warn(`⚠️ All methods failed for ${symbol} - creating fallback entry`);
                        // Create a fallback entry instead of skipping
                        allResults.push({
                            symbol: symbol,
                            name: symbol.replace('.NS', ''),
                            price: 0,
                            change: 0,
                            changePercent: 0,
                            volume: 0,
                            high52Week: 0,
                            low52Week: 0,
                            avgVolume: 0
                        });
                    }
                    // Small delay to avoid rate limiting
                    if (i < formattedSymbols.length - 1) {
                        await new Promise((resolve)=>setTimeout(resolve, 150));
                    }
                } catch (symbolError) {
                    console.warn(`⚠️ Critical error fetching ${symbol}:`, symbolError.message);
                    // Create a fallback entry instead of skipping
                    allResults.push({
                        symbol: symbol,
                        name: symbol.replace('.NS', ''),
                        price: 0,
                        change: 0,
                        changePercent: 0,
                        volume: 0,
                        high52Week: 0,
                        low52Week: 0,
                        avgVolume: 0
                    });
                }
            }
            // Check if we have a reasonable success rate
            const successRate = allResults.length / formattedSymbols.length;
            console.log(`📊 Success rate: ${(successRate * 100).toFixed(1)}% (${allResults.length}/${formattedSymbols.length})`);
            // If success rate is too low, try batch processing for remaining symbols
            if (successRate < 0.8 && allResults.length < formattedSymbols.length) {
                console.log(`⚠️ Low success rate, trying batch processing for remaining symbols...`);
                const fetchedSymbols = new Set(allResults.map((r)=>r.symbol));
                const remainingSymbols = formattedSymbols.filter((s)=>!fetchedSymbols.has(s));
                if (remainingSymbols.length > 0) {
                    try {
                        // Try batch processing with smaller batches
                        const batchSize = 5;
                        for(let i = 0; i < remainingSymbols.length; i += batchSize){
                            const batch = remainingSymbols.slice(i, i + batchSize);
                            try {
                                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(YAHOO_QUOTE_URL, {
                                    params: {
                                        symbols: batch.join(','),
                                        formatted: true
                                    },
                                    headers: {
                                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                                        'Accept': 'application/json',
                                        'Referer': 'https://finance.yahoo.com/'
                                    },
                                    timeout: 10000
                                });
                                const data = response.data;
                                const results = data.quoteResponse?.result || [];
                                for (const result of results){
                                    if (result && result.regularMarketPrice > 0) {
                                        const batchQuote = {
                                            symbol: result.symbol,
                                            name: result.longName || result.shortName || result.symbol.replace('.NS', ''),
                                            price: parseFloat(result.regularMarketPrice) || 0,
                                            change: parseFloat(result.regularMarketChange) || 0,
                                            changePercent: parseFloat(result.regularMarketChangePercent) || 0,
                                            volume: parseInt(result.regularMarketVolume) || 0,
                                            marketCap: result.marketCap,
                                            high52Week: parseFloat(result.fiftyTwoWeekHigh) || 0,
                                            low52Week: parseFloat(result.fiftyTwoWeekLow) || 0,
                                            avgVolume: parseInt(result.averageDailyVolume3Month) || 0
                                        };
                                        allResults.push(batchQuote);
                                        console.log(`✅ Batch recovery success for ${result.symbol}: ₹${batchQuote.price}`);
                                    }
                                }
                                // Delay between batches
                                await new Promise((resolve)=>setTimeout(resolve, 200));
                            } catch (batchError) {
                                console.error(`❌ Batch processing failed for batch:`, batch);
                            }
                        }
                    } catch (error) {
                        console.error(`❌ Batch recovery failed:`, error);
                    }
                }
            }
            console.log(`🎉 Final results: ${allResults.length} quotes fetched out of ${formattedSymbols.length} requested`);
            console.log(`📊 Sample results:`, allResults.slice(0, 3).map((r)=>({
                    symbol: r.symbol,
                    price: r.price,
                    name: r.name
                })));
            // Cache the results
            const cacheKey = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CacheKeys"].yahooQuotes(symbols);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cacheService"].set(cacheKey, allResults);
            return allResults;
        } catch (error) {
            console.error('❌ Critical error in getMultipleQuotes:', error);
            // Return fallback quotes for all symbols
            return symbols.map((symbol)=>({
                    symbol: symbol.includes('.') ? symbol : `${symbol}.NS`,
                    name: symbol,
                    price: 0,
                    change: 0,
                    changePercent: 0,
                    volume: 0,
                    marketCap: undefined,
                    high52Week: 0,
                    low52Week: 0,
                    avgVolume: 0
                }));
        }
    }
    async searchStocks(query) {
        try {
            const data = await this.makeRequest(YAHOO_SEARCH_URL, {
                q: query,
                quotesCount: 10,
                newsCount: 0
            });
            const quotes = data.quotes || [];
            return quotes.filter((quote)=>quote.isYahooFinance && quote.symbol).map((quote)=>({
                    symbol: quote.symbol,
                    name: quote.longname || quote.shortname || quote.symbol,
                    exchange: quote.exchange || 'NSE',
                    type: quote.quoteType || 'EQUITY'
                }));
        } catch (error) {
            console.error('Error searching stocks:', error);
            return [];
        }
    }
    // Helper method to get Indian stock symbols
    getIndianStockSymbol(symbol) {
        return symbol.includes('.') ? symbol : `${symbol}.NS`;
    }
    // Helper method to format Indian stock symbols for display
    formatSymbolForDisplay(symbol) {
        return symbol.replace('.NS', '').replace('.BO', '');
    }
    // Get historical data for a symbol using existing quote data
    async getHistoricalData(symbol, days = 7) {
        try {
            console.log(`📊 Getting historical data for ${symbol} (${days} days)`);
            // For now, use current quote data and simulate historical data
            // This is a fallback approach since Yahoo Finance historical API is complex
            const currentQuote = await this.getQuoteWithCachedName(symbol);
            if (!currentQuote) {
                console.warn(`No current quote found for ${symbol}`);
                return null;
            }
            // Generate simulated historical data based on current price
            // This is a simplified approach for weekly high calculation
            const historicalData = [];
            const basePrice = currentQuote.price;
            const baseVolume = currentQuote.volume || 1000000;
            for(let i = days - 1; i >= 0; i--){
                const date = new Date();
                date.setDate(date.getDate() - i);
                // Simulate price variation (±5% from current price)
                const variation = (Math.random() - 0.5) * 0.1; // ±5%
                const dayPrice = basePrice * (1 + variation);
                // Simulate intraday high/low (±2% from day price)
                const intraVariation = Math.random() * 0.04; // 0-4%
                const high = dayPrice * (1 + intraVariation);
                const low = dayPrice * (1 - intraVariation);
                // Simulate volume variation
                const volumeVariation = (Math.random() - 0.5) * 0.4; // ±20%
                const volume = Math.floor(baseVolume * (1 + volumeVariation));
                historicalData.push({
                    date,
                    open: dayPrice,
                    high: Math.max(high, dayPrice),
                    low: Math.min(low, dayPrice),
                    close: dayPrice,
                    volume: Math.max(volume, 100000) // Minimum volume
                });
            }
            // Ensure the most recent day has the current price as high
            if (historicalData.length > 0) {
                const lastDay = historicalData[historicalData.length - 1];
                lastDay.high = Math.max(lastDay.high, currentQuote.price);
                lastDay.close = currentQuote.price;
                lastDay.volume = currentQuote.volume || lastDay.volume;
            }
            console.log(`✅ Generated ${historicalData.length} days of historical data for ${symbol}`);
            return historicalData;
        } catch (error) {
            console.error(`❌ Error generating historical data for ${symbol}:`, error);
            return null;
        }
    }
}
const yahooFinanceService = new YahooFinanceService();
}),
"[project]/src/lib/nifty-stocks.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Current Nifty 200 stock symbols - updated list as of 2025
__turbopack_context__.s({
    "NIFTY_200_SYMBOLS": ()=>NIFTY_200_SYMBOLS,
    "addBOHEligibility": ()=>addBOHEligibility,
    "calculateBOHEligibility": ()=>calculateBOHEligibility,
    "getDisplaySymbol": ()=>getDisplaySymbol,
    "getYahooSymbol": ()=>getYahooSymbol
});
const RAW_NIFTY_200_SYMBOLS = [
    'NYKAA',
    'MRF',
    'MANKIND',
    'CHOLAFIN',
    'CONCOR',
    'ICICIPRULI',
    'PREMIERENE',
    'BSE',
    'BANDHANBNK',
    'WAAREEENER',
    'SHRIRAMFIN',
    'SBICARD',
    'DABUR',
    'DIXON',
    'GMRAIRPORT',
    'VBL',
    'BAJFINANCE',
    'BHEL',
    'BIOCON',
    'INDHOTEL',
    'COALINDIA',
    'HYUNDAI',
    'GODREJCP',
    'HINDUNILVR',
    'ADANIENSOL',
    'PATANJALI',
    'SHREECEM',
    'VMM',
    'CUMMINSIND',
    'LODHA',
    'ABB',
    'COCHINSHIP',
    'BRITANNIA',
    'ULTRACEMCO',
    'AUBANK',
    'KALYANKJIL',
    'BDL',
    'DIVISLAB',
    'INDIGO',
    'POWERGRID',
    'OIL',
    'HEROMOTOCO',
    'ASTRAL',
    'ACC',
    'BANKBARODA',
    'BOSCHLTD',
    'MOTILALOFS',
    'TORNTPHARM',
    'TATATECH',
    'MAHABANK',
    'M&M',
    'ASIANPAINT',
    'UNITDSPR',
    'PIIND',
    'ITC',
    'ASHOKLEY',
    'NESTLEIND',
    'HDFCAMC',
    'ADANIGREEN',
    'MARICO',
    'APOLLOTYRE',
    'LTF',
    'HDFCBANK',
    'TVSMOTOR',
    'ADANIPOWER',
    'MARUTI',
    'MOTHERSON',
    'BAJAJHFL',
    'NTPCGREEN',
    'JIOFIN',
    'BAJAJFINSV',
    'JSWENERGY',
    'TORNTPOWER',
    'NTPC',
    'FEDERALBNK',
    'ALKEM',
    'NHPC',
    'BAJAJ-AUTO',
    'EICHERMOT',
    'M&MFIN',
    'ETERNAL',
    'MPHASIS',
    'HUDCO',
    'PETRONET',
    'SUPREMEIND',
    'HAL',
    'CIPLA',
    'IRCTC',
    'KOTAKBANK',
    'POLICYBZR',
    'INDIANB',
    'CANBK',
    'AXISBANK',
    'ONGC',
    'LICI',
    'SWIGGY',
    'TATAMOTORS',
    'IDEA',
    'SOLARINDS',
    'LICHSGFIN',
    'MAZDOCK',
    'TATAPOWER',
    'IREDA',
    'SRF',
    'BAJAJHLDNG',
    'SBIN',
    'BHARTIHEXA',
    'ZYDUSLIFE',
    'VOLTAS',
    'AMBUJACEM',
    'MUTHOOTFIN',
    'TITAN',
    'ADANIPORTS',
    'SBILIFE',
    'ATGL',
    'ADANIENT',
    'YESBANK',
    'INFY',
    'TATACONSUM',
    'EXIDEIND',
    'AUROPHARMA',
    'PAYTM',
    'PFC',
    'TATAELXSI',
    'TATACOMM',
    'SUNPHARMA',
    'INDUSTOWER',
    'JSWSTEEL',
    'ESCORTS',
    'IRFC',
    'BHARTIARTL',
    'LUPIN',
    'RVNL',
    'POLYCAB',
    'CGPOWER',
    'GLENMARK',
    'HAVELLS',
    'PIDILITIND',
    'TCS',
    'NMDC',
    'LTIM',
    'TRENT',
    'SUZLON',
    'DMART',
    'JUBLFOOD',
    'SAIL',
    'COLPAL',
    'LT',
    'MFSL',
    'SONACOMS',
    'PRESTIGE',
    'IDFCFIRSTB',
    'ICICIBANK',
    'SJVN',
    'BEL',
    'OFSS',
    'WIPRO',
    'ICICIGI',
    'ABCAPITAL',
    'COFORGE',
    'JINDALSTEL',
    'GRASIM',
    'BANKINDIA',
    'PAGEIND',
    'ABFRL',
    'TIINDIA',
    'INDUSINDBK',
    'PNB',
    'RECLTD',
    'KPITTECH',
    'HDFCLIFE',
    'RELIANCE',
    'PERSISTENT',
    'DRREDDY',
    'UPL',
    'OLAELEC',
    'TECHM',
    'OBEROIRLTY',
    'APOLLOHOSP',
    'BHARATFORG',
    'NAUKRI',
    'HINDPETRO',
    'DLF',
    'TATASTEEL',
    'BPCL',
    'HINDALCO',
    'IRB',
    'APLAPOLLO',
    'NATIONALUM',
    'HCLTECH',
    'SIEMENS',
    'IOC',
    'GODREJPROP',
    'IGL',
    'HINDZINC',
    'PHOENIXLTD',
    'VEDL',
    'UNIONBANK',
    'MAXHEALTH',
    'GAIL'
];
const NIFTY_200_SYMBOLS = [
    ...new Set(RAW_NIFTY_200_SYMBOLS)
];
// Validation: Log any duplicates found and final count
const duplicates = RAW_NIFTY_200_SYMBOLS.filter((symbol, index)=>RAW_NIFTY_200_SYMBOLS.indexOf(symbol) !== index);
if (duplicates.length > 0) {
    console.warn('Duplicate symbols found and removed:', duplicates);
}
console.log(`Nifty 200 symbols loaded: ${NIFTY_200_SYMBOLS.length} unique symbols`);
function getYahooSymbol(nseSymbol) {
    // Handle special cases for Yahoo Finance symbol mapping
    const symbolMappings = {
        'M&M': 'MM.NS',
        'M&MFIN': 'MMFIN.NS',
        'BAJAJ-AUTO': 'BAJAJ_AUTO.NS',
        'L&T': 'LT.NS',
        'LTF': 'LTFH.NS',
        'BOSCHLTD': 'BOSCHLTD.NS',
        'BSOFT': 'BSOFT.NS' // Birlasoft
    };
    // Handle merged/renamed stocks - map to their current equivalent
    const renamedMappings = {
        'CADILAHC': 'ZYDUSLIFE.NS'
    };
    // Handle merged stocks (for backward compatibility)
    const delistedMappings = {
        'HDFC': 'HDFCBANK.NS',
        'MINDTREE': 'LTIM.NS',
        'PVR': 'PVRINOX.NS',
        ...renamedMappings
    };
    // Stocks that are completely delisted/suspended - these will be skipped
    const delistedStocks = new Set([]);
    // Stocks that might have issues - keep for now but monitor
    const problematicStocks = new Set([
        'WAAREEENER',
        'PREMIERENE',
        'GMRAIRPORT',
        'ADANIENSOL',
        'PATANJALI',
        'VMM',
        'KALYANKJIL',
        'NTPCGREEN',
        'JIOFIN',
        'BHARTIHEXA',
        'ATGL',
        'IREDA',
        'SWIGGY',
        'SOLARINDS',
        'OLAELEC',
        'PHOENIXLTD',
        'MAXHEALTH' // Check if available
    ]);
    // Check if stock is delisted/suspended - return null to skip
    if (delistedStocks.has(nseSymbol)) {
        console.log(`🚫 Skipping delisted/suspended stock: ${nseSymbol}`);
        return null; // This will be handled in the API to skip the stock
    }
    // Check for renamed/merged stocks first
    if (renamedMappings[nseSymbol]) {
        console.log(`📝 Mapping renamed stock ${nseSymbol} to ${renamedMappings[nseSymbol]}`);
        return renamedMappings[nseSymbol];
    }
    if (delistedMappings[nseSymbol]) {
        console.log(`📝 Mapping merged stock ${nseSymbol} to ${delistedMappings[nseSymbol]}`);
        return delistedMappings[nseSymbol];
    }
    if (symbolMappings[nseSymbol]) {
        return symbolMappings[nseSymbol];
    }
    // Log if this is a potentially problematic stock
    if (problematicStocks.has(nseSymbol)) {
        console.log(`⚠️ Fetching potentially new/problematic stock: ${nseSymbol}`);
    }
    return `${nseSymbol}.NS`;
}
function getDisplaySymbol(nseSymbol) {
    return nseSymbol;
}
function calculateBOHEligibility(stock) {
    // BOH Eligible if 52-week low occurred AFTER 52-week high
    // This indicates: High (boom) → Low (bust) → Recovery pattern
    if (stock.high52WeekDate && stock.low52WeekDate) {
        const highDate = new Date(stock.high52WeekDate);
        const lowDate = new Date(stock.low52WeekDate);
        // Return true if low date is after high date (boom → bust → recovery pattern)
        return lowDate > highDate;
    }
    // Fallback: If we don't have dates, use a heuristic based on price and 52-week range
    // This is for testing purposes when Yahoo Finance doesn't provide dates
    if (stock.high52Week && stock.low52Week && stock.price > 0) {
        const priceRange = stock.high52Week - stock.low52Week;
        const currentFromLow = stock.price - stock.low52Week;
        const currentFromHigh = stock.high52Week - stock.price;
        // Consider BOH eligible if:
        // 1. Stock has a significant price range (> 20% of current price)
        // 2. Current price is closer to 52-week low than high (recovery phase)
        // 3. Stock is not at 52-week high (not in boom phase)
        const hasSignificantRange = priceRange > stock.price * 0.2;
        const isInRecoveryPhase = currentFromLow < currentFromHigh;
        const notAtHigh = stock.price < stock.high52Week * 0.95;
        // Make about 60% of eligible stocks BOH eligible for testing
        const randomFactor = (stock.symbol.charCodeAt(0) + stock.symbol.charCodeAt(stock.symbol.length - 1)) % 10;
        const shouldBeBOH = randomFactor < 6; // 60% chance
        return hasSignificantRange && isInRecoveryPhase && notAtHigh && shouldBeBOH;
    }
    return false;
}
function addBOHEligibility(stock) {
    return {
        ...stock,
        isBOHEligible: calculateBOHEligibility(stock)
    };
}
}),
"[project]/src/lib/holdings-service.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Holdings service to manage current holdings across strategies
__turbopack_context__.s({
    "holdingsService": ()=>holdingsService
});
class HoldingsService {
    holdings = [
        // Sample holdings for demonstration - in real app, this would come from database
        {
            symbol: 'RELIANCE',
            strategy: 'DARVAS_BOX',
            quantity: 50,
            avgPrice: 2200.00,
            currentPrice: 2456.75,
            purchaseDate: new Date('2024-01-15')
        },
        {
            symbol: 'TCS',
            strategy: 'DARVAS_BOX',
            quantity: 25,
            avgPrice: 3400.00,
            currentPrice: 3234.50,
            purchaseDate: new Date('2024-01-20')
        },
        {
            symbol: 'HDFC',
            strategy: 'WEEKLY_HIGH',
            quantity: 40,
            avgPrice: 1600.00,
            currentPrice: 1678.90,
            purchaseDate: new Date('2024-02-01')
        },
        {
            symbol: 'INFY',
            strategy: 'BOH_FILTER',
            quantity: 60,
            avgPrice: 1500.00,
            currentPrice: 1456.80,
            purchaseDate: new Date('2024-02-10')
        }
    ];
    // Get all current holdings
    getAllHoldings() {
        return [
            ...this.holdings
        ];
    }
    // Get holdings for a specific strategy
    getHoldingsByStrategy(strategy) {
        return this.holdings.filter((holding)=>holding.strategy === strategy);
    }
    // Check if a stock is currently held in any strategy
    isStockInHoldings(symbol) {
        return this.holdings.some((holding)=>holding.symbol === symbol);
    }
    // Get all unique symbols in holdings
    getHoldingSymbols() {
        return [
            ...new Set(this.holdings.map((holding)=>holding.symbol))
        ];
    }
    // Add a new holding
    addHolding(holding) {
        const existingIndex = this.holdings.findIndex((h)=>h.symbol === holding.symbol && h.strategy === holding.strategy);
        if (existingIndex >= 0) {
            // Update existing holding (average price calculation)
            const existing = this.holdings[existingIndex];
            const totalQuantity = existing.quantity + holding.quantity;
            const totalValue = existing.quantity * existing.avgPrice + holding.quantity * holding.avgPrice;
            this.holdings[existingIndex] = {
                ...existing,
                quantity: totalQuantity,
                avgPrice: totalValue / totalQuantity,
                currentPrice: holding.currentPrice
            };
        } else {
            // Add new holding
            this.holdings.push({
                ...holding,
                purchaseDate: new Date()
            });
        }
    }
    // Remove a holding
    removeHolding(symbol, strategy) {
        this.holdings = this.holdings.filter((holding)=>!(holding.symbol === symbol && holding.strategy === strategy));
    }
    // Update current price for a holding
    updateCurrentPrice(symbol, currentPrice) {
        this.holdings.forEach((holding)=>{
            if (holding.symbol === symbol) {
                holding.currentPrice = currentPrice;
            }
        });
    }
    // Get stocks that were bought above ₹2000 and are still in holdings
    getStocksAbove2000InHoldings() {
        return this.holdings.filter((holding)=>holding.avgPrice > 2000 || holding.currentPrice > 2000).map((holding)=>holding.symbol);
    }
    // Check if a stock should be eligible for trading
    // (CMP < 2000 OR currently in holdings)
    isStockEligibleForTrading(symbol, currentPrice) {
        return currentPrice < 2000 || this.isStockInHoldings(symbol);
    }
}
const holdingsService = new HoldingsService();
}),
"[project]/src/lib/weekly-high-signal-detector.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Weekly High Signal Detection Service
// Monitors for new Weekly High Signals and triggers automatic GTT order creation
__turbopack_context__.s({
    "weeklyHighSignalDetector": ()=>weeklyHighSignalDetector
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$yahoo$2d$finance$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/yahoo-finance.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/nifty-stocks.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$holdings$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/holdings-service.ts [app-route] (ecmascript)");
;
;
;
class WeeklyHighSignalDetector {
    config = {
        enabled: true,
        pollingIntervalMinutes: 5,
        marketStartHour: 9,
        marketEndHour: 15,
        strongSignalThreshold: 2.0,
        moderateSignalThreshold: 5.0,
        minVolumeRatio: 1.2,
        maxInvestmentPerStock: 10000,
        investmentPerOrder: 2000
    };
    isRunning = false;
    pollingInterval = null;
    lastSignals = new Map();
    signalListeners = new Set();
    newSignalListeners = new Set();
    constructor(){
        console.log('📡 Weekly High Signal Detector initialized');
    }
    // Configuration management
    updateConfig(newConfig) {
        this.config = {
            ...this.config,
            ...newConfig
        };
        console.log('⚙️ Signal detector config updated:', this.config);
        if (this.isRunning) {
            this.stop();
            this.start();
        }
    }
    getConfig() {
        return {
            ...this.config
        };
    }
    // Check if market is currently open
    isMarketOpen() {
        const now = new Date();
        const currentHour = now.getHours();
        const currentMinute = now.getMinutes();
        // Market hours: 9:15 AM to 3:30 PM (Monday to Friday)
        const isWeekday = now.getDay() >= 1 && now.getDay() <= 5;
        const isAfterStart = currentHour > this.config.marketStartHour || currentHour === this.config.marketStartHour && currentMinute >= 15;
        const isBeforeEnd = currentHour < this.config.marketEndHour || currentHour === this.config.marketEndHour && currentMinute <= 30;
        return isWeekday && isAfterStart && isBeforeEnd;
    }
    // Generate mock OHLC data for weekly high calculation
    generateOHLCData(currentPrice) {
        const data = [];
        let price = currentPrice * 0.95; // Start 5% below current price
        for(let i = 0; i < 7; i++){
            const open = price;
            const high = price * (1 + Math.random() * 0.08); // Up to 8% higher
            const low = price * (1 - Math.random() * 0.05); // Up to 5% lower
            const close = low + Math.random() * (high - low);
            data.push({
                open,
                high,
                low,
                close
            });
            price = close;
        }
        return data;
    }
    // Calculate signal strength based on proximity to weekly high and volume
    calculateSignalStrength(currentPrice, weeklyHigh, volumeRatio) {
        const percentFromHigh = Math.abs((currentPrice - weeklyHigh) / weeklyHigh * 100);
        if (percentFromHigh <= this.config.strongSignalThreshold && volumeRatio >= this.config.minVolumeRatio) {
            return 'STRONG';
        } else if (percentFromHigh <= this.config.moderateSignalThreshold) {
            return 'MODERATE';
        } else {
            return 'WEAK';
        }
    }
    // Scan for Weekly High Signals
    async scanForSignals() {
        try {
            console.log('🔍 Scanning for Weekly High Signals...');
            // Fetch all Nifty 200 stocks with current prices
            const yahooSymbols = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NIFTY_200_SYMBOLS"].map(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getYahooSymbol"]);
            const quotes = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$yahoo$2d$finance$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["yahooFinanceService"].getMultipleQuotesWithCachedNames(yahooSymbols);
            console.log(`📊 Got quotes for ${quotes.length}/${yahooSymbols.length} symbols`);
            // Get current holdings
            const holdingSymbols = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$holdings$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["holdingsService"].getAllHoldings().map((h)=>h.symbol);
            // Process each stock for signal detection
            const signals = [];
            for(let i = 0; i < __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NIFTY_200_SYMBOLS"].length; i++){
                const nseSymbol = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NIFTY_200_SYMBOLS"][i];
                const yahooSymbol = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getYahooSymbol"])(nseSymbol);
                const quote = quotes.find((q)=>q.symbol === yahooSymbol);
                if (!quote || quote.price <= 0) continue;
                const price = quote.price;
                const inHoldings = holdingSymbols.includes(nseSymbol);
                const isEligible = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$holdings$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["holdingsService"].isStockEligibleForTrading(nseSymbol, price);
                // Create stock object for BOH eligibility check
                const stock = {
                    symbol: nseSymbol,
                    name: quote.name,
                    price,
                    change: quote.change || 0,
                    changePercent: quote.changePercent || 0,
                    volume: quote.volume || 0,
                    marketCap: quote.marketCap,
                    high52Week: quote.high52Week,
                    low52Week: quote.low52Week,
                    high52WeekDate: quote.high52WeekDate,
                    low52WeekDate: quote.low52WeekDate,
                    isEligible,
                    inHoldings
                };
                const stockWithBOH = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$nifty$2d$stocks$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["addBOHEligibility"])(stock);
                // Only process BOH eligible stocks
                if (!stockWithBOH.isBOHEligible) continue;
                // Calculate weekly high data
                const ohlcData = this.generateOHLCData(price);
                const lastWeekHighest = Math.max(...ohlcData.map((d)=>d.high));
                const suggestedBuyPrice = lastWeekHighest + 0.05;
                const percentDifference = (price - suggestedBuyPrice) / suggestedBuyPrice * 100;
                const suggestedGTTQuantity = Math.floor(this.config.investmentPerOrder / suggestedBuyPrice);
                // Calculate volume metrics
                const avgVolume = quote.avgVolume || quote.volume || 1;
                const volumeRatio = quote.volume / avgVolume;
                // Calculate signal strength
                const signalStrength = this.calculateSignalStrength(price, lastWeekHighest, volumeRatio);
                // Only include signals that are MODERATE or STRONG
                if (signalStrength === 'WEAK') continue;
                const signal = {
                    symbol: nseSymbol,
                    name: quote.name,
                    currentPrice: price,
                    lastWeekHighest,
                    suggestedBuyPrice,
                    percentDifference,
                    suggestedGTTQuantity,
                    isBOHEligible: true,
                    inHoldings,
                    signalStrength,
                    detectedAt: new Date(),
                    volume: quote.volume || 0,
                    avgVolume,
                    volumeRatio
                };
                signals.push(signal);
            }
            console.log(`✅ Found ${signals.length} Weekly High Signals`);
            return signals;
        } catch (error) {
            console.error('❌ Error scanning for signals:', error);
            return [];
        }
    }
    // Detect new signals by comparing with previous scan
    detectNewSignals(currentSignals) {
        const newSignals = [];
        for (const signal of currentSignals){
            const previousSignal = this.lastSignals.get(signal.symbol);
            // Consider it a new signal if:
            // 1. Stock wasn't in previous signals, OR
            // 2. Signal strength improved (MODERATE -> STRONG), OR
            // 3. Price moved significantly closer to weekly high
            const isNewSignal = !previousSignal || signal.signalStrength === 'STRONG' && previousSignal.signalStrength !== 'STRONG' || Math.abs(signal.percentDifference) < Math.abs(previousSignal.percentDifference) - 1;
            if (isNewSignal) {
                newSignals.push(signal);
                console.log(`🆕 New signal detected: ${signal.symbol} (${signal.signalStrength})`);
            }
        }
        return newSignals;
    }
    // Main polling function
    async poll() {
        if (!this.config.enabled) {
            console.log('⏸️ Signal detection is disabled');
            return;
        }
        if (!this.isMarketOpen()) {
            console.log('🕐 Market is closed, skipping signal detection');
            return;
        }
        try {
            console.log('🔄 Polling for Weekly High Signals...');
            const currentSignals = await this.scanForSignals();
            const newSignals = this.detectNewSignals(currentSignals);
            // Update last signals cache
            this.lastSignals.clear();
            currentSignals.forEach((signal)=>{
                this.lastSignals.set(signal.symbol, signal);
            });
            // Notify listeners about all current signals
            this.signalListeners.forEach((listener)=>{
                try {
                    listener(currentSignals);
                } catch (error) {
                    console.error('❌ Error in signal listener:', error);
                }
            });
            // Notify listeners about new signals
            if (newSignals.length > 0) {
                console.log(`🚨 ${newSignals.length} new signals detected!`);
                newSignals.forEach((signal)=>{
                    this.newSignalListeners.forEach((listener)=>{
                        try {
                            listener(signal);
                        } catch (error) {
                            console.error('❌ Error in new signal listener:', error);
                        }
                    });
                });
            }
        } catch (error) {
            console.error('❌ Error in signal polling:', error);
        }
    }
    // Start the detection service
    start() {
        if (this.isRunning) {
            console.log('⚠️ Signal detector is already running');
            return;
        }
        console.log(`🚀 Starting Weekly High Signal Detector (polling every ${this.config.pollingIntervalMinutes} minutes)`);
        this.isRunning = true;
        // Initial scan
        this.poll();
        // Set up polling interval
        this.pollingInterval = setInterval(()=>{
            this.poll();
        }, this.config.pollingIntervalMinutes * 60 * 1000);
    }
    // Stop the detection service
    stop() {
        if (!this.isRunning) {
            console.log('⚠️ Signal detector is not running');
            return;
        }
        console.log('⏹️ Stopping Weekly High Signal Detector');
        this.isRunning = false;
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
            this.pollingInterval = null;
        }
    }
    // Add listener for all signals
    addSignalListener(listener) {
        this.signalListeners.add(listener);
    }
    // Remove listener for all signals
    removeSignalListener(listener) {
        this.signalListeners.delete(listener);
    }
    // Add listener for new signals only
    addNewSignalListener(listener) {
        this.newSignalListeners.add(listener);
    }
    // Remove listener for new signals
    removeNewSignalListener(listener) {
        this.newSignalListeners.delete(listener);
    }
    // Get current status
    getStatus() {
        return {
            isRunning: this.isRunning,
            isMarketOpen: this.isMarketOpen(),
            config: this.config,
            lastSignalCount: this.lastSignals.size,
            listenerCount: this.signalListeners.size + this.newSignalListeners.size
        };
    }
    // Manual trigger for testing
    async triggerManualScan() {
        console.log('🔧 Manual signal scan triggered');
        return await this.scanForSignals();
    }
}
const weeklyHighSignalDetector = new WeeklyHighSignalDetector();
// Auto-start the service when imported
if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
;
}),
"[project]/src/lib/automatic-gtt-service.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Automatic GTT Order Creation Service
// Creates GTT buy orders automatically when new Weekly High Signals are detected
__turbopack_context__.s({
    "automaticGTTService": ()=>automaticGTTService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$weekly$2d$high$2d$signal$2d$detector$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/weekly-high-signal-detector.ts [app-route] (ecmascript)");
;
class AutomaticGTTService {
    config = {
        enabled: true,
        maxOrdersPerDay: 20,
        maxInvestmentPerStock: 10000,
        investmentPerOrder: 2000,
        minSignalStrength: 'MODERATE',
        requireVolumeConfirmation: true,
        onlyDuringMarketHours: true
    };
    orders = [];
    dailyOrderCount = 0;
    lastResetDate = new Date().toDateString();
    orderListeners = new Set();
    isInitialized = false;
    constructor(){
        console.log('🤖 Automatic GTT Service initialized');
    }
    // Initialize the service and start listening for signals
    async initialize() {
        if (this.isInitialized) {
            console.log('⚠️ Automatic GTT Service already initialized');
            return;
        }
        console.log('🚀 Initializing Automatic GTT Service...');
        // Load existing orders from localStorage or API
        await this.loadExistingOrders();
        // Start listening for new signals
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$weekly$2d$high$2d$signal$2d$detector$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["weeklyHighSignalDetector"].addNewSignalListener(this.handleNewSignal.bind(this));
        // Reset daily counter if it's a new day
        this.resetDailyCounterIfNeeded();
        this.isInitialized = true;
        console.log('✅ Automatic GTT Service initialized successfully');
    }
    // Configuration management
    updateConfig(newConfig) {
        this.config = {
            ...this.config,
            ...newConfig
        };
        console.log('⚙️ Auto GTT config updated:', this.config);
    }
    getConfig() {
        return {
            ...this.config
        };
    }
    // Load existing orders (from localStorage for now, can be replaced with API)
    async loadExistingOrders() {
        try {
            const stored = localStorage.getItem('autoGTTOrders');
            if (stored) {
                const parsedOrders = JSON.parse(stored);
                this.orders = parsedOrders.map((order)=>({
                        ...order,
                        createdAt: new Date(order.createdAt)
                    }));
                console.log(`📂 Loaded ${this.orders.length} existing auto GTT orders`);
            }
            // Load daily counter
            const storedCounter = localStorage.getItem('autoGTTDailyCount');
            const storedDate = localStorage.getItem('autoGTTLastResetDate');
            if (storedCounter && storedDate === new Date().toDateString()) {
                this.dailyOrderCount = parseInt(storedCounter, 10);
            }
        } catch (error) {
            console.error('❌ Error loading existing orders:', error);
        }
    }
    // Save orders to localStorage
    saveOrders() {
        try {
            localStorage.setItem('autoGTTOrders', JSON.stringify(this.orders));
            localStorage.setItem('autoGTTDailyCount', this.dailyOrderCount.toString());
            localStorage.setItem('autoGTTLastResetDate', this.lastResetDate);
        } catch (error) {
            console.error('❌ Error saving orders:', error);
        }
    }
    // Reset daily counter if it's a new day
    resetDailyCounterIfNeeded() {
        const today = new Date().toDateString();
        if (this.lastResetDate !== today) {
            this.dailyOrderCount = 0;
            this.lastResetDate = today;
            console.log('🔄 Daily order counter reset for new day');
        }
    }
    // Check if we can create a new order
    canCreateOrder(signal) {
        // Check if service is enabled
        if (!this.config.enabled) {
            return {
                canCreate: false,
                reason: 'Automatic GTT service is disabled'
            };
        }
        // Check market hours if required
        if (this.config.onlyDuringMarketHours) {
            const now = new Date();
            const currentHour = now.getHours();
            const currentMinute = now.getMinutes();
            const isWeekday = now.getDay() >= 1 && now.getDay() <= 5;
            const isMarketOpen = isWeekday && (currentHour > 9 || currentHour === 9 && currentMinute >= 15) && (currentHour < 15 || currentHour === 15 && currentMinute <= 30);
            if (!isMarketOpen) {
                return {
                    canCreate: false,
                    reason: 'Market is closed'
                };
            }
        }
        // Check daily limit
        this.resetDailyCounterIfNeeded();
        if (this.dailyOrderCount >= this.config.maxOrdersPerDay) {
            return {
                canCreate: false,
                reason: 'Daily order limit reached'
            };
        }
        // Check signal strength
        const strengthOrder = {
            'WEAK': 1,
            'MODERATE': 2,
            'STRONG': 3
        };
        if (strengthOrder[signal.signalStrength] < strengthOrder[this.config.minSignalStrength]) {
            return {
                canCreate: false,
                reason: `Signal strength ${signal.signalStrength} below minimum ${this.config.minSignalStrength}`
            };
        }
        // Check volume confirmation if required
        if (this.config.requireVolumeConfirmation && signal.volumeRatio < 1.2) {
            return {
                canCreate: false,
                reason: 'Insufficient volume confirmation'
            };
        }
        // Check for existing pending orders for this stock
        const existingOrder = this.orders.find((order)=>order.symbol === signal.symbol && order.status === 'PENDING' && order.source === 'SIGNAL');
        if (existingOrder) {
            return {
                canCreate: false,
                reason: 'Pending order already exists for this stock'
            };
        }
        // Check investment limits
        const existingInvestment = this.orders.filter((order)=>order.symbol === signal.symbol && order.status === 'PENDING').reduce((sum, order)=>sum + order.triggerPrice * order.quantity, 0);
        const newInvestment = signal.suggestedBuyPrice * signal.suggestedGTTQuantity;
        if (existingInvestment + newInvestment > this.config.maxInvestmentPerStock) {
            return {
                canCreate: false,
                reason: 'Would exceed maximum investment per stock'
            };
        }
        // Check if quantity is valid
        if (signal.suggestedGTTQuantity <= 0) {
            return {
                canCreate: false,
                reason: 'Invalid quantity calculated'
            };
        }
        // Check if trigger price is reasonable
        if (signal.suggestedBuyPrice <= 0 || signal.suggestedBuyPrice > 5000) {
            return {
                canCreate: false,
                reason: 'Invalid trigger price'
            };
        }
        return {
            canCreate: true
        };
    }
    // Handle new signal detection
    async handleNewSignal(signal) {
        console.log(`🔔 New signal received: ${signal.symbol} (${signal.signalStrength})`);
        const validation = this.canCreateOrder(signal);
        if (!validation.canCreate) {
            console.log(`⏭️ Skipping order creation for ${signal.symbol}: ${validation.reason}`);
            return;
        }
        try {
            await this.createAutoGTTOrder(signal);
        } catch (error) {
            console.error(`❌ Failed to create auto GTT order for ${signal.symbol}:`, error);
        }
    }
    // Create automatic GTT order
    async createAutoGTTOrder(signal) {
        console.log(`🤖 Creating automatic GTT order for ${signal.symbol}...`);
        const order = {
            id: `auto_gtt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            symbol: signal.symbol,
            name: signal.name,
            orderType: 'BUY',
            triggerPrice: signal.suggestedBuyPrice,
            quantity: signal.suggestedGTTQuantity,
            status: 'PENDING',
            createdAt: new Date(),
            source: 'SIGNAL',
            signalStrength: signal.signalStrength,
            autoCreated: true,
            originalSignal: signal
        };
        // Add to orders list
        this.orders.push(order);
        this.dailyOrderCount++;
        // Save to storage
        this.saveOrders();
        // Log the creation
        console.log(`✅ Auto GTT order created: ${order.symbol} - Trigger: ₹${order.triggerPrice.toFixed(2)}, Qty: ${order.quantity}`);
        console.log(`📊 Daily orders: ${this.dailyOrderCount}/${this.config.maxOrdersPerDay}`);
        // Notify listeners
        this.orderListeners.forEach((listener)=>{
            try {
                listener(order);
            } catch (error) {
                console.error('❌ Error in order listener:', error);
            }
        });
        return order;
    }
    // Get all orders
    getAllOrders() {
        return [
            ...this.orders
        ];
    }
    // Get orders by status
    getOrdersByStatus(status) {
        return this.orders.filter((order)=>order.status === status);
    }
    // Get orders by source
    getOrdersBySource(source) {
        return this.orders.filter((order)=>order.source === source);
    }
    // Cancel an order
    cancelOrder(orderId) {
        const order = this.orders.find((o)=>o.id === orderId);
        if (order && order.status === 'PENDING') {
            order.status = 'CANCELLED';
            this.saveOrders();
            console.log(`❌ Order cancelled: ${order.symbol} (${orderId})`);
            return true;
        }
        return false;
    }
    // Update order status (for external triggers)
    updateOrderStatus(orderId, status) {
        const order = this.orders.find((o)=>o.id === orderId);
        if (order) {
            order.status = status;
            this.saveOrders();
            console.log(`🔄 Order status updated: ${order.symbol} -> ${status}`);
            return true;
        }
        return false;
    }
    // Add order listener
    addOrderListener(listener) {
        this.orderListeners.add(listener);
    }
    // Remove order listener
    removeOrderListener(listener) {
        this.orderListeners.delete(listener);
    }
    // Get service statistics
    getStatistics() {
        const today = new Date().toDateString();
        const todayOrders = this.orders.filter((order)=>order.createdAt.toDateString() === today && order.autoCreated);
        return {
            totalOrders: this.orders.length,
            autoCreatedOrders: this.orders.filter((o)=>o.autoCreated).length,
            todayOrders: todayOrders.length,
            dailyLimit: this.config.maxOrdersPerDay,
            pendingOrders: this.orders.filter((o)=>o.status === 'PENDING').length,
            triggeredOrders: this.orders.filter((o)=>o.status === 'TRIGGERED').length,
            cancelledOrders: this.orders.filter((o)=>o.status === 'CANCELLED').length,
            isEnabled: this.config.enabled,
            isInitialized: this.isInitialized
        };
    }
    // Manual trigger for testing
    async testCreateOrder(symbol) {
        console.log(`🧪 Test order creation for ${symbol}`);
        // Get current signals
        const signals = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$weekly$2d$high$2d$signal$2d$detector$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["weeklyHighSignalDetector"].triggerManualScan();
        const signal = signals.find((s)=>s.symbol === symbol);
        if (!signal) {
            console.log(`❌ No signal found for ${symbol}`);
            return null;
        }
        return await this.createAutoGTTOrder(signal);
    }
    // Start the service
    async start() {
        if (!this.isInitialized) {
            await this.initialize();
        }
        // Start the signal detector if not already running
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$weekly$2d$high$2d$signal$2d$detector$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["weeklyHighSignalDetector"].start();
        console.log('🚀 Automatic GTT Service started');
    }
    // Stop the service
    stop() {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$weekly$2d$high$2d$signal$2d$detector$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["weeklyHighSignalDetector"].stop();
        console.log('⏹️ Automatic GTT Service stopped');
    }
}
const automaticGTTService = new AutomaticGTTService();
// Auto-start the service when imported
if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
;
}),
"[project]/src/app/api/test-signal-pipeline/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$weekly$2d$high$2d$signal$2d$detector$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/weekly-high-signal-detector.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$automatic$2d$gtt$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/automatic-gtt-service.ts [app-route] (ecmascript)");
;
;
;
async function POST(request) {
    try {
        console.log('🧪 Testing complete signal-to-order pipeline...');
        const testResults = {
            timestamp: new Date().toISOString(),
            steps: []
        };
        // Step 1: Check service status
        console.log('📊 Step 1: Checking service status...');
        const signalDetectorStatus = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$weekly$2d$high$2d$signal$2d$detector$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["weeklyHighSignalDetector"].getStatus();
        const gttServiceStats = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$automatic$2d$gtt$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["automaticGTTService"].getStatistics();
        testResults.steps.push({
            step: 1,
            name: 'Service Status Check',
            status: signalDetectorStatus.isRunning && gttServiceStats.isInitialized ? 'PASS' : 'FAIL',
            data: {
                signalDetector: signalDetectorStatus,
                gttService: gttServiceStats
            }
        });
        // Step 2: Get current orders count
        console.log('📋 Step 2: Getting current orders count...');
        const initialOrders = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$automatic$2d$gtt$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["automaticGTTService"].getAllOrders();
        const initialOrderCount = initialOrders.length;
        const initialPendingSignalOrders = initialOrders.filter((o)=>o.source === 'SIGNAL' && o.status === 'PENDING').length;
        testResults.steps.push({
            step: 2,
            name: 'Initial Order Count',
            status: 'PASS',
            data: {
                totalOrders: initialOrderCount,
                pendingSignalOrders: initialPendingSignalOrders,
                existingSymbols: initialOrders.filter((o)=>o.source === 'SIGNAL' && o.status === 'PENDING').map((o)=>o.symbol)
            }
        });
        // Step 3: Trigger signal detection
        console.log('📡 Step 3: Triggering signal detection...');
        const detectedSignals = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$weekly$2d$high$2d$signal$2d$detector$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["weeklyHighSignalDetector"].triggerManualScan();
        testResults.steps.push({
            step: 3,
            name: 'Signal Detection',
            status: detectedSignals.length > 0 ? 'PASS' : 'FAIL',
            data: {
                totalSignals: detectedSignals.length,
                strongSignals: detectedSignals.filter((s)=>s.signalStrength === 'STRONG').length,
                moderateSignals: detectedSignals.filter((s)=>s.signalStrength === 'MODERATE').length,
                sampleSignals: detectedSignals.slice(0, 3).map((s)=>({
                        symbol: s.symbol,
                        strength: s.signalStrength,
                        currentPrice: s.currentPrice,
                        suggestedBuyPrice: s.suggestedBuyPrice
                    }))
            }
        });
        // Step 4: Check for eligible signals (no existing orders)
        console.log('🔍 Step 4: Finding eligible signals for new orders...');
        const existingSymbols = new Set(initialOrders.filter((order)=>order.source === 'SIGNAL' && order.status === 'PENDING').map((order)=>order.symbol));
        const eligibleSignals = detectedSignals.filter((signal)=>!existingSymbols.has(signal.symbol));
        testResults.steps.push({
            step: 4,
            name: 'Eligible Signal Analysis',
            status: 'PASS',
            data: {
                totalSignals: detectedSignals.length,
                existingOrderSymbols: existingSymbols.size,
                eligibleSignals: eligibleSignals.length,
                eligibleSymbols: eligibleSignals.slice(0, 5).map((s)=>s.symbol)
            }
        });
        // Step 5: Test automatic order creation for eligible signals
        console.log('🤖 Step 5: Testing automatic order creation...');
        let newOrdersCreated = 0;
        const orderCreationResults = [];
        if (eligibleSignals.length > 0) {
            // Test creating orders for first 3 eligible signals
            const signalsToTest = eligibleSignals.slice(0, 3);
            for (const signal of signalsToTest){
                try {
                    console.log(`🧪 Testing order creation for ${signal.symbol}...`);
                    const order = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$automatic$2d$gtt$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["automaticGTTService"].testCreateOrder(signal.symbol);
                    if (order) {
                        newOrdersCreated++;
                        orderCreationResults.push({
                            symbol: signal.symbol,
                            status: 'SUCCESS',
                            orderId: order.id,
                            triggerPrice: order.triggerPrice,
                            quantity: order.quantity
                        });
                    } else {
                        orderCreationResults.push({
                            symbol: signal.symbol,
                            status: 'FAILED',
                            error: 'No order returned'
                        });
                    }
                } catch (error) {
                    orderCreationResults.push({
                        symbol: signal.symbol,
                        status: 'ERROR',
                        error: error instanceof Error ? error.message : 'Unknown error'
                    });
                }
            }
        }
        testResults.steps.push({
            step: 5,
            name: 'Automatic Order Creation Test',
            status: eligibleSignals.length === 0 ? 'SKIP' : newOrdersCreated > 0 ? 'PASS' : 'FAIL',
            data: {
                eligibleSignalsCount: eligibleSignals.length,
                ordersAttempted: orderCreationResults.length,
                ordersCreated: newOrdersCreated,
                results: orderCreationResults
            }
        });
        // Step 6: Verify final order count
        console.log('📊 Step 6: Verifying final order count...');
        const finalOrders = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$automatic$2d$gtt$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["automaticGTTService"].getAllOrders();
        const finalOrderCount = finalOrders.length;
        const orderCountIncrease = finalOrderCount - initialOrderCount;
        testResults.steps.push({
            step: 6,
            name: 'Final Order Count Verification',
            status: orderCountIncrease >= 0 ? 'PASS' : 'FAIL',
            data: {
                initialOrderCount,
                finalOrderCount,
                orderCountIncrease,
                newOrdersExpected: newOrdersCreated,
                newOrdersActual: orderCountIncrease
            }
        });
        // Step 7: Test real-time UI updates
        console.log('🖥️ Step 7: Testing real-time UI updates...');
        // This would be tested by checking if the Central Data Manager has the updated orders
        const centralDataOrders = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$automatic$2d$gtt$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["automaticGTTService"].getAllOrders(); // This should trigger UI updates
        testResults.steps.push({
            step: 7,
            name: 'Real-time UI Updates',
            status: 'PASS',
            data: {
                ordersAvailableForUI: centralDataOrders.length,
                lastOrderCreated: centralDataOrders[centralDataOrders.length - 1]?.createdAt || null
            }
        });
        // Calculate overall results
        const passedSteps = testResults.steps.filter((s)=>s.status === 'PASS').length;
        const skippedSteps = testResults.steps.filter((s)=>s.status === 'SKIP').length;
        const totalSteps = testResults.steps.length;
        const effectiveSteps = totalSteps - skippedSteps;
        const overallStatus = passedSteps >= effectiveSteps ? 'SUCCESS' : 'PARTIAL_SUCCESS';
        const summary = {
            overallStatus,
            passedSteps,
            skippedSteps,
            totalSteps,
            effectiveSteps,
            successRate: effectiveSteps > 0 ? `${Math.round(passedSteps / effectiveSteps * 100)}%` : '0%',
            pipelineWorking: overallStatus === 'SUCCESS',
            signalDetectionWorking: testResults.steps.find((s)=>s.step === 3)?.status === 'PASS',
            orderCreationWorking: testResults.steps.find((s)=>s.step === 5)?.status === 'PASS' || testResults.steps.find((s)=>s.step === 5)?.status === 'SKIP',
            realTimeUpdatesWorking: testResults.steps.find((s)=>s.step === 7)?.status === 'PASS',
            newOrdersCreated,
            finalOrderCount
        };
        console.log(`🎯 Signal-to-order pipeline test: ${summary.successRate} success rate`);
        console.log(`📊 Final result: ${newOrdersCreated} new orders created, ${finalOrderCount} total orders`);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            message: 'Signal-to-order pipeline test completed',
            summary,
            testResults
        });
    } catch (error) {
        console.error('❌ Signal-to-order pipeline test failed:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: error instanceof Error ? error.message : 'Pipeline test failed',
            message: 'Signal-to-order pipeline test failed'
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__fe4a9c25._.js.map