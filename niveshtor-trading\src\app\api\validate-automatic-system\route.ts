import { NextRequest, NextResponse } from 'next/server';
import { automaticGTTService } from '@/lib/automatic-gtt-service';
import { weeklyHighSignalDetector } from '@/lib/weekly-high-signal-detector';
import { centralDataManager } from '@/lib/central-data-manager';

export async function GET(request: NextRequest) {
  try {
    console.log('🎯 Validating complete automatic GTT order creation system...');

    const validationResults = {
      timestamp: new Date().toISOString(),
      validations: [] as any[]
    };

    // Validation 1: Service Status
    console.log('🔧 Validation 1: Service Status...');
    const signalDetectorStatus = weeklyHighSignalDetector.getStatus();
    const gttServiceStats = automaticGTTService.getStatistics();
    const centralStatus = centralDataManager.getStatus();

    const servicesRunning = signalDetectorStatus.isRunning && 
                           gttServiceStats.isInitialized && 
                           centralStatus.isInitialized && centralStatus.isRunning;

    validationResults.validations.push({
      name: 'Background Services Running',
      status: servicesRunning ? 'PASS' : 'FAIL',
      data: {
        weeklyHighSignalDetector: {
          running: signalDetectorStatus.isRunning,
          listenerCount: signalDetectorStatus.listenerCount,
          lastSignalCount: signalDetectorStatus.lastSignalCount
        },
        automaticGTTService: {
          initialized: gttServiceStats.isInitialized,
          totalOrders: gttServiceStats.totalOrders,
          todayOrders: gttServiceStats.todayOrders
        },
        centralDataManager: {
          initialized: centralStatus.isInitialized,
          running: centralStatus.isRunning,
          marketOpen: centralStatus.isMarketOpen
        }
      }
    });

    // Validation 2: Signal Detection
    console.log('📡 Validation 2: Signal Detection...');
    const currentSignals = await weeklyHighSignalDetector.triggerManualScan();
    
    validationResults.validations.push({
      name: 'Weekly High Signal Detection',
      status: currentSignals.length > 0 ? 'PASS' : 'FAIL',
      data: {
        totalSignals: currentSignals.length,
        strongSignals: currentSignals.filter(s => s.signalStrength === 'STRONG').length,
        moderateSignals: currentSignals.filter(s => s.signalStrength === 'MODERATE').length,
        detectionWorking: currentSignals.length > 0
      }
    });

    // Validation 3: Order Coverage
    console.log('📊 Validation 3: Order Coverage...');
    const allOrders = automaticGTTService.getAllOrders();
    const pendingSignalOrders = allOrders.filter(o => o.source === 'SIGNAL' && o.status === 'PENDING');
    
    const existingSymbols = new Set(pendingSignalOrders.map(o => o.symbol));
    const signalsWithoutOrders = currentSignals.filter(signal => !existingSymbols.has(signal.symbol));
    
    const coveragePercentage = currentSignals.length > 0 
      ? Math.round(((currentSignals.length - signalsWithoutOrders.length) / currentSignals.length) * 100)
      : 100;

    validationResults.validations.push({
      name: 'Signal-to-Order Coverage',
      status: coveragePercentage >= 80 ? 'PASS' : 'FAIL',
      data: {
        totalSignals: currentSignals.length,
        ordersCreated: pendingSignalOrders.length,
        signalsWithoutOrders: signalsWithoutOrders.length,
        coveragePercentage,
        coverageGood: coveragePercentage >= 80
      }
    });

    // Validation 4: Listener System
    console.log('🔗 Validation 4: Listener System...');
    const expectedListeners = 2; // Should have 2 listeners: handleNewSignal and handleAllSignals
    const actualListeners = signalDetectorStatus.listenerCount;
    
    validationResults.validations.push({
      name: 'Signal Listener System',
      status: actualListeners >= 1 ? 'PASS' : 'FAIL',
      data: {
        expectedListeners,
        actualListeners,
        listenerSystemActive: actualListeners >= 1,
        newSignalListeners: 'Connected to handleNewSignal',
        allSignalListeners: 'Connected to handleAllSignals'
      }
    });

    // Validation 5: Automatic Creation Logic
    console.log('🤖 Validation 5: Automatic Creation Logic...');
    let automaticCreationWorking = false;
    let testOrderCreated = null;

    if (signalsWithoutOrders.length > 0) {
      // Test automatic creation with one signal without an order
      try {
        const testSignal = signalsWithoutOrders[0];
        testOrderCreated = await automaticGTTService.testCreateOrder(testSignal.symbol);
        automaticCreationWorking = testOrderCreated !== null;
      } catch (error) {
        console.error('Test order creation failed:', error);
      }
    } else {
      // All signals have orders - system is working correctly
      automaticCreationWorking = true;
    }

    validationResults.validations.push({
      name: 'Automatic Order Creation Logic',
      status: automaticCreationWorking ? 'PASS' : 'FAIL',
      data: {
        signalsWithoutOrders: signalsWithoutOrders.length,
        testOrderCreated: testOrderCreated !== null,
        automaticCreationWorking,
        allSignalsHaveOrders: signalsWithoutOrders.length === 0,
        systemStatus: signalsWithoutOrders.length === 0 
          ? 'All signals covered - working perfectly'
          : testOrderCreated 
            ? 'New order created - working correctly'
            : 'Order creation failed - needs investigation'
      }
    });

    // Validation 6: Real-time UI Updates
    console.log('🔄 Validation 6: Real-time UI Updates...');
    const centralOrders = centralDataManager.getGTTOrders();
    const serviceOrders = automaticGTTService.getAllOrders();
    const uiSyncWorking = centralOrders.length === serviceOrders.length;

    validationResults.validations.push({
      name: 'Real-time UI Synchronization',
      status: uiSyncWorking ? 'PASS' : 'FAIL',
      data: {
        centralDataManagerOrders: centralOrders.length,
        automaticGTTServiceOrders: serviceOrders.length,
        ordersSynchronized: uiSyncWorking,
        lastSyncTime: centralDataManager.getLastUpdated('gttOrders'),
        realTimeUpdatesWorking: uiSyncWorking
      }
    });

    // Validation 7: Market Hours Automation
    console.log('⏰ Validation 7: Market Hours Automation...');
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    const isWeekday = now.getDay() >= 1 && now.getDay() <= 5;
    const isMarketHours = isWeekday && 
                         (currentHour > 9 || (currentHour === 9 && currentMinute >= 15)) &&
                         (currentHour < 15 || (currentHour === 15 && currentMinute <= 30));

    validationResults.validations.push({
      name: 'Market Hours Automation',
      status: 'PASS', // Always pass, just informational
      data: {
        currentTime: now.toLocaleString(),
        isWeekday,
        isMarketHours,
        automationActive: isMarketHours,
        nextMarketOpen: isMarketHours ? 'Currently open' : 'Next weekday 9:15 AM',
        scanInterval: '5 minutes during market hours',
        orderCheckInterval: '2 minutes during market hours'
      }
    });

    // Calculate overall results
    const passedValidations = validationResults.validations.filter(v => v.status === 'PASS').length;
    const totalValidations = validationResults.validations.length;
    const overallStatus = passedValidations === totalValidations ? 'FULLY_AUTOMATIC' : 
                         passedValidations >= totalValidations * 0.85 ? 'MOSTLY_AUTOMATIC' : 'NEEDS_ATTENTION';

    const summary = {
      overallStatus,
      passedValidations,
      totalValidations,
      successRate: `${Math.round((passedValidations / totalValidations) * 100)}%`,
      
      // Key system status
      backgroundServicesRunning: validationResults.validations.find(v => v.name === 'Background Services Running')?.status === 'PASS',
      signalDetectionWorking: validationResults.validations.find(v => v.name === 'Weekly High Signal Detection')?.status === 'PASS',
      orderCoverageGood: validationResults.validations.find(v => v.name === 'Signal-to-Order Coverage')?.status === 'PASS',
      listenerSystemActive: validationResults.validations.find(v => v.name === 'Signal Listener System')?.status === 'PASS',
      automaticCreationWorking: validationResults.validations.find(v => v.name === 'Automatic Order Creation Logic')?.status === 'PASS',
      realTimeUpdatesWorking: validationResults.validations.find(v => v.name === 'Real-time UI Synchronization')?.status === 'PASS',
      
      // Current metrics
      totalSignals: currentSignals.length,
      totalOrders: allOrders.length,
      signalOrders: pendingSignalOrders.length,
      coveragePercentage,
      isMarketHours,
      
      // System readiness
      fullyAutomated: overallStatus === 'FULLY_AUTOMATIC',
      manualInterventionRequired: overallStatus === 'NEEDS_ATTENTION',
      systemMessage: overallStatus === 'FULLY_AUTOMATIC' 
        ? 'System is fully automated - no manual intervention required'
        : overallStatus === 'MOSTLY_AUTOMATIC'
          ? 'System is mostly automated - minor issues detected'
          : 'System needs attention - manual intervention may be required'
    };

    console.log(`🎉 Automatic system validation: ${summary.systemMessage}`);

    return NextResponse.json({
      success: true,
      message: 'Automatic GTT order creation system validation completed',
      summary,
      validationResults
    });

  } catch (error) {
    console.error('❌ Automatic system validation failed:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Validation failed',
        message: 'Automatic system validation failed'
      },
      { status: 500 }
    );
  }
}
