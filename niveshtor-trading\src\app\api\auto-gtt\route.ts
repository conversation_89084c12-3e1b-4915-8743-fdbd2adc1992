import { NextRequest, NextResponse } from 'next/server';
import { automaticGTTService } from '@/lib/automatic-gtt-service';
import { weeklyHighSignalDetector } from '@/lib/weekly-high-signal-detector';

// GET - Get service status and statistics
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const action = url.searchParams.get('action');

    switch (action) {
      case 'status':
        const detectorStatus = weeklyHighSignalDetector.getStatus();
        const serviceStats = automaticGTTService.getStatistics();
        
        return NextResponse.json({
          success: true,
          data: {
            detector: detectorStatus,
            service: serviceStats,
            timestamp: new Date().toISOString()
          }
        });

      case 'orders':
        const orders = automaticGTTService.getAllOrders();
        return NextResponse.json({
          success: true,
          data: orders
        });

      case 'signals':
        const signals = await weeklyHighSignalDetector.triggerManualScan();
        return NextResponse.json({
          success: true,
          data: signals
        });

      default:
        return NextResponse.json({
          success: true,
          message: 'Automatic GTT Service API',
          endpoints: {
            'GET ?action=status': 'Get service status and statistics',
            'GET ?action=orders': 'Get all automatic GTT orders',
            'GET ?action=signals': 'Get current Weekly High Signals',
            'POST': 'Control service (start/stop/configure)'
          }
        });
    }

  } catch (error) {
    console.error('❌ Auto GTT API GET error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'API error' 
      },
      { status: 500 }
    );
  }
}

// POST - Control the service (start/stop/configure)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, config } = body;

    switch (action) {
      case 'start':
        await automaticGTTService.start();
        return NextResponse.json({
          success: true,
          message: 'Automatic GTT Service started',
          data: automaticGTTService.getStatistics()
        });

      case 'stop':
        automaticGTTService.stop();
        return NextResponse.json({
          success: true,
          message: 'Automatic GTT Service stopped',
          data: automaticGTTService.getStatistics()
        });

      case 'configure':
        if (config) {
          automaticGTTService.updateConfig(config);
          weeklyHighSignalDetector.updateConfig(config);
        }
        return NextResponse.json({
          success: true,
          message: 'Configuration updated',
          data: {
            serviceConfig: automaticGTTService.getConfig(),
            detectorConfig: weeklyHighSignalDetector.getConfig()
          }
        });

      case 'test':
        const { symbol } = body;
        if (!symbol) {
          throw new Error('Symbol required for test');
        }
        
        const testOrder = await automaticGTTService.testCreateOrder(symbol);
        return NextResponse.json({
          success: true,
          message: testOrder ? 'Test order created' : 'No signal found for symbol',
          data: testOrder
        });

      case 'cancel':
        const { orderId } = body;
        if (!orderId) {
          throw new Error('Order ID required for cancellation');
        }
        
        const cancelled = automaticGTTService.cancelOrder(orderId);
        return NextResponse.json({
          success: cancelled,
          message: cancelled ? 'Order cancelled' : 'Order not found or cannot be cancelled'
        });

      default:
        throw new Error('Invalid action. Use: start, stop, configure, test, cancel');
    }

  } catch (error) {
    console.error('❌ Auto GTT API POST error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'API error' 
      },
      { status: 500 }
    );
  }
}
