import { NextResponse } from 'next/server';

export async function GET() {
  try {
    console.log('🌐 Testing network connectivity...');
    
    // Test external connectivity
    const testUrls = [
      'https://httpbin.org/status/200',
      'https://jsonplaceholder.typicode.com/posts/1',
      'https://query1.finance.yahoo.com/v7/finance/quote?symbols=RELIANCE.NS'
    ];
    
    const results = [];
    
    for (const url of testUrls) {
      try {
        const startTime = Date.now();
        const response = await fetch(url, {
          method: 'GET',
          signal: AbortSignal.timeout(10000) // 10 second timeout
        });
        const endTime = Date.now();
        
        results.push({
          url,
          status: response.status,
          statusText: response.statusText,
          responseTime: endTime - startTime,
          success: response.ok
        });
      } catch (error) {
        results.push({
          url,
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false
        });
      }
    }
    
    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      results
    });
    
  } catch (error) {
    console.error('❌ Network test error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
