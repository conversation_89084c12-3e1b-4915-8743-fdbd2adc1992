// Dynamic Data Updater Service
// Handles continuous updates of dynamic data (prices, P&L, signals) without affecting static elements

import { yahooFinanceService } from './yahoo-finance';
import { staticDataCache } from './static-data-cache';
import { weeklyHighSignalDetector } from './weekly-high-signal-detector';
import { automaticGTTService } from './automatic-gtt-service';
import { holdingsService } from './holdings-service';

export interface DynamicUpdateConfig {
  stockPricesInterval: number; // 30 seconds
  weeklyHighSignalsInterval: number; // 5 minutes
  gttOrdersInterval: number; // 30 seconds
  holdingsInterval: number; // 30 seconds
  enableDuringMarketHours: boolean;
  marketStartHour: number; // 9
  marketEndHour: number; // 15
}

export interface DynamicDataListeners {
  onStockPricesUpdate: (prices: Map<string, any>) => void;
  onHoldingsUpdate: (holdings: Map<string, any>) => void;
  onWeeklyHighSignalsUpdate: (signals: Map<string, any>) => void;
  onGTTOrdersUpdate: (orders: Map<string, any>) => void;
}

class DynamicDataUpdaterService {
  private config: DynamicUpdateConfig = {
    stockPricesInterval: 30 * 1000, // 30 seconds
    weeklyHighSignalsInterval: 5 * 60 * 1000, // 5 minutes
    gttOrdersInterval: 30 * 1000, // 30 seconds
    holdingsInterval: 30 * 1000, // 30 seconds
    enableDuringMarketHours: true,
    marketStartHour: 9,
    marketEndHour: 15
  };
  
  private intervals: Map<string, NodeJS.Timeout> = new Map();
  private listeners: Partial<DynamicDataListeners> = {};
  private isRunning = false;
  private lastUpdateTimes: Map<string, Date> = new Map();
  
  constructor() {
    console.log('⚡ Dynamic Data Updater Service initialized');
  }
  
  // Start continuous dynamic data updates
  start(): void {
    if (this.isRunning) {
      console.log('⚡ Dynamic updater already running');
      return;
    }
    
    if (!staticDataCache.isStaticCacheReady()) {
      console.warn('⚠️ Static cache not ready, delaying dynamic updates...');
      setTimeout(() => this.start(), 2000);
      return;
    }
    
    console.log('🚀 Starting dynamic data updates...');
    this.isRunning = true;
    
    // Start all update intervals
    this.startStockPricesUpdates();
    this.startWeeklyHighSignalsUpdates();
    this.startGTTOrdersUpdates();
    this.startHoldingsUpdates();
    
    console.log('✅ All dynamic data update intervals started');
  }
  
  // Stop all dynamic updates
  stop(): void {
    console.log('⏹️ Stopping dynamic data updates...');
    this.intervals.forEach((interval, key) => {
      clearInterval(interval);
      console.log(`⏹️ Stopped ${key} updates`);
    });
    this.intervals.clear();
    this.isRunning = false;
  }
  
  // Check if market is open
  private isMarketOpen(): boolean {
    if (!this.config.enableDuringMarketHours) return true;
    
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    const isWeekday = now.getDay() >= 1 && now.getDay() <= 5;
    
    const isAfterStart = currentHour > this.config.marketStartHour || 
                       (currentHour === this.config.marketStartHour && currentMinute >= 15);
    const isBeforeEnd = currentHour < this.config.marketEndHour || 
                       (currentHour === this.config.marketEndHour && currentMinute <= 30);
    
    return isWeekday && isAfterStart && isBeforeEnd;
  }
  
  // Start stock prices updates (every 30 seconds)
  private startStockPricesUpdates(): void {
    const updateStockPrices = async () => {
      if (!this.isMarketOpen()) {
        console.log('🕐 Market closed, skipping stock price updates');
        return;
      }
      
      try {
        console.log('💰 Updating stock prices...');
        const stockUniverse = staticDataCache.getStockUniverse();
        
        // Update prices in batches to avoid overwhelming the API
        const batchSize = 25;
        const batches = [];
        for (let i = 0; i < stockUniverse.length; i += batchSize) {
          batches.push(stockUniverse.slice(i, i + batchSize));
        }
        
        let updatedCount = 0;
        for (const batch of batches) {
          const symbols = batch.map(stock => stock.symbol);
          
          try {
            const priceData = await yahooFinanceService.getBatchStockPrices(symbols);
            
            for (const stock of batch) {
              const price = priceData[stock.symbol];
              if (price) {
                staticDataCache.updateStockPrice(stock.symbol, {
                  currentPrice: price.regularMarketPrice || 0,
                  dayChange: price.regularMarketChange || 0,
                  dayChangePercent: price.regularMarketChangePercent || 0,
                  lastUpdated: new Date()
                });
                updatedCount++;
              }
            }
          } catch (batchError) {
            console.warn(`⚠️ Failed to update batch of ${symbols.length} stock prices:`, batchError);
          }
          
          // Small delay between batches
          await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        console.log(`✅ Updated ${updatedCount} stock prices`);
        this.lastUpdateTimes.set('stockPrices', new Date());
        
        // Notify listeners
        if (this.listeners.onStockPricesUpdate) {
          this.listeners.onStockPricesUpdate(staticDataCache.getAllStockPrices());
        }
        
      } catch (error) {
        console.error('❌ Error updating stock prices:', error);
      }
    };
    
    // Initial update
    updateStockPrices();
    
    // Set up interval
    const interval = setInterval(updateStockPrices, this.config.stockPricesInterval);
    this.intervals.set('stockPrices', interval);
    console.log(`⏰ Stock prices updates scheduled every ${this.config.stockPricesInterval/1000}s`);
  }
  
  // Start weekly high signals updates (every 5 minutes)
  private startWeeklyHighSignalsUpdates(): void {
    const updateWeeklyHighSignals = async () => {
      if (!this.isMarketOpen()) {
        console.log('🕐 Market closed, skipping weekly high signals updates');
        return;
      }
      
      try {
        console.log('📊 Updating weekly high signals...');
        const signals = await weeklyHighSignalDetector.triggerManualScan();
        
        const signalsMap = new Map();
        signals.forEach(signal => {
          signalsMap.set(signal.symbol, {
            signalStrength: signal.signalStrength,
            percentDifference: signal.percentDifference,
            lastWeekHigh: signal.lastWeekHigh,
            suggestedBuyPrice: signal.suggestedBuyPrice,
            lastUpdated: new Date()
          });
        });
        
        console.log(`✅ Updated ${signals.length} weekly high signals`);
        this.lastUpdateTimes.set('weeklyHighSignals', new Date());
        
        // Notify listeners
        if (this.listeners.onWeeklyHighSignalsUpdate) {
          this.listeners.onWeeklyHighSignalsUpdate(signalsMap);
        }
        
      } catch (error) {
        console.error('❌ Error updating weekly high signals:', error);
      }
    };
    
    // Initial update
    updateWeeklyHighSignals();
    
    // Set up interval
    const interval = setInterval(updateWeeklyHighSignals, this.config.weeklyHighSignalsInterval);
    this.intervals.set('weeklyHighSignals', interval);
    console.log(`⏰ Weekly high signals updates scheduled every ${this.config.weeklyHighSignalsInterval/1000/60}min`);
  }
  
  // Start GTT orders updates (every 30 seconds)
  private startGTTOrdersUpdates(): void {
    const updateGTTOrders = async () => {
      try {
        console.log('📋 Updating GTT orders status...');
        const orders = automaticGTTService.getAllOrders();
        
        const ordersMap = new Map();
        orders.forEach(order => {
          ordersMap.set(order.id, {
            status: order.status,
            triggerPrice: order.triggerPrice,
            quantity: order.quantity,
            currentPrice: staticDataCache.getStockPrice(order.symbol)?.currentPrice || 0,
            lastUpdated: new Date()
          });
        });
        
        console.log(`✅ Updated ${orders.length} GTT orders`);
        this.lastUpdateTimes.set('gttOrders', new Date());
        
        // Notify listeners
        if (this.listeners.onGTTOrdersUpdate) {
          this.listeners.onGTTOrdersUpdate(ordersMap);
        }
        
      } catch (error) {
        console.error('❌ Error updating GTT orders:', error);
      }
    };
    
    // Initial update
    updateGTTOrders();
    
    // Set up interval
    const interval = setInterval(updateGTTOrders, this.config.gttOrdersInterval);
    this.intervals.set('gttOrders', interval);
    console.log(`⏰ GTT orders updates scheduled every ${this.config.gttOrdersInterval/1000}s`);
  }
  
  // Start holdings updates (every 30 seconds)
  private startHoldingsUpdates(): void {
    const updateHoldings = async () => {
      if (!this.isMarketOpen()) {
        console.log('🕐 Market closed, skipping holdings updates');
        return;
      }
      
      try {
        console.log('💼 Updating holdings P&L...');
        const holdings = holdingsService.getAllHoldings();
        
        const holdingsMap = new Map();
        for (const holding of holdings) {
          const currentPrice = staticDataCache.getStockPrice(holding.symbol)?.currentPrice || holding.currentPrice;
          const marketValue = currentPrice * holding.quantity;
          const pnl = marketValue - (holding.avgPrice * holding.quantity);
          const pnlPercent = ((currentPrice - holding.avgPrice) / holding.avgPrice) * 100;
          
          holdingsMap.set(holding.symbol, {
            currentPrice,
            marketValue,
            pnl,
            pnlPercent,
            lastUpdated: new Date()
          });
        }
        
        console.log(`✅ Updated ${holdings.length} holdings P&L`);
        this.lastUpdateTimes.set('holdings', new Date());
        
        // Notify listeners
        if (this.listeners.onHoldingsUpdate) {
          this.listeners.onHoldingsUpdate(holdingsMap);
        }
        
      } catch (error) {
        console.error('❌ Error updating holdings:', error);
      }
    };
    
    // Initial update
    updateHoldings();
    
    // Set up interval
    const interval = setInterval(updateHoldings, this.config.holdingsInterval);
    this.intervals.set('holdings', interval);
    console.log(`⏰ Holdings updates scheduled every ${this.config.holdingsInterval/1000}s`);
  }
  
  // Add listeners for dynamic data updates
  addListener(type: keyof DynamicDataListeners, listener: any): void {
    this.listeners[type] = listener;
    console.log(`👂 Added listener for ${type} updates`);
  }
  
  removeListener(type: keyof DynamicDataListeners): void {
    delete this.listeners[type];
    console.log(`🔇 Removed listener for ${type} updates`);
  }
  
  // Get status
  getStatus() {
    return {
      isRunning: this.isRunning,
      isMarketOpen: this.isMarketOpen(),
      activeIntervals: Array.from(this.intervals.keys()),
      lastUpdateTimes: Object.fromEntries(this.lastUpdateTimes),
      config: this.config
    };
  }
  
  // Force update specific data type
  async forceUpdate(type: 'stockPrices' | 'weeklyHighSignals' | 'gttOrders' | 'holdings'): Promise<void> {
    console.log(`🔄 Force updating ${type}...`);
    
    switch (type) {
      case 'stockPrices':
        // Trigger stock prices update immediately
        break;
      case 'weeklyHighSignals':
        // Trigger weekly high signals update immediately
        break;
      case 'gttOrders':
        // Trigger GTT orders update immediately
        break;
      case 'holdings':
        // Trigger holdings update immediately
        break;
    }
  }
}

export const dynamicDataUpdater = new DynamicDataUpdaterService();
