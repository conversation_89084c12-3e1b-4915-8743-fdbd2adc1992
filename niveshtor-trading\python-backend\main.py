from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import os
from dotenv import load_dotenv
import pyotp
from logzero import logger
from SmartApi import SmartConnect
from typing import Optional, Dict, Any
import json

# Load environment variables
load_dotenv()

app = FastAPI(title="Niveshtor Trading Backend", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # Next.js dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global SmartAPI instance
smart_api: Optional[SmartConnect] = None
auth_token: Optional[str] = None
feed_token: Optional[str] = None

# Pydantic models
class LoginRequest(BaseModel):
    api_key: str
    client_id: str
    password: str
    totp_secret: str

class OrderRequest(BaseModel):
    variety: str = "NORMAL"
    tradingsymbol: str
    symboltoken: str
    transactiontype: str  # BUY/SELL
    exchange: str
    ordertype: str = "LIMIT"
    producttype: str = "INTRADAY"
    duration: str = "DAY"
    price: str
    quantity: str
    squareoff: str = "0"
    stoploss: str = "0"

class GTTRequest(BaseModel):
    tradingsymbol: str
    symboltoken: str
    exchange: str
    producttype: str = "MARGIN"
    transactiontype: str  # BUY/SELL
    price: float
    qty: int
    disclosedqty: int
    triggerprice: float
    timeperiod: int = 365

class HistoricalDataRequest(BaseModel):
    exchange: str
    symboltoken: str
    interval: str = "ONE_MINUTE"
    fromdate: str  # Format: "2021-02-08 09:00"
    todate: str    # Format: "2021-02-08 09:16"

@app.get("/")
async def root():
    return {"message": "Niveshtor Trading Backend API"}

@app.post("/api/smartapi/login")
async def login_smartapi(request: LoginRequest):
    global smart_api, auth_token, feed_token
    
    try:
        smart_api = SmartConnect(request.api_key)
        
        # Generate TOTP
        totp = pyotp.TOTP(request.totp_secret).now()
        
        # Generate session
        data = smart_api.generateSession(request.client_id, request.password, totp)
        
        if data['status'] == False:
            logger.error(data)
            raise HTTPException(status_code=400, detail=data.get('message', 'Login failed'))
        
        # Store tokens
        auth_token = data['data']['jwtToken']
        refresh_token = data['data']['refreshToken']
        feed_token = smart_api.getfeedToken()
        
        # Get user profile
        profile = smart_api.getProfile(refresh_token)
        smart_api.generateToken(refresh_token)
        
        return {
            "status": "success",
            "message": "Login successful",
            "data": {
                "auth_token": auth_token,
                "feed_token": feed_token,
                "profile": profile['data'] if profile['status'] else None
            }
        }
        
    except Exception as e:
        logger.exception(f"Login failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/smartapi/profile")
async def get_profile():
    global smart_api
    
    if not smart_api:
        raise HTTPException(status_code=401, detail="Not logged in")
    
    try:
        # This would need refresh token - simplified for now
        return {"status": "success", "message": "Profile endpoint"}
    except Exception as e:
        logger.exception(f"Get profile failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/smartapi/place-order")
async def place_order(request: OrderRequest):
    global smart_api
    
    if not smart_api:
        raise HTTPException(status_code=401, detail="Not logged in")
    
    try:
        order_params = request.dict()
        order_id = smart_api.placeOrder(order_params)
        
        return {
            "status": "success",
            "message": "Order placed successfully",
            "data": {"order_id": order_id}
        }
        
    except Exception as e:
        logger.exception(f"Order placement failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/smartapi/gtt-create")
async def create_gtt_rule(request: GTTRequest):
    global smart_api
    
    if not smart_api:
        raise HTTPException(status_code=401, detail="Not logged in")
    
    try:
        gtt_params = request.dict()
        rule_id = smart_api.gttCreateRule(gtt_params)
        
        return {
            "status": "success",
            "message": "GTT rule created successfully",
            "data": {"rule_id": rule_id}
        }
        
    except Exception as e:
        logger.exception(f"GTT rule creation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/smartapi/gtt-list")
async def get_gtt_list(status: str = "FORALL", page: int = 1, count: int = 10):
    global smart_api
    
    if not smart_api:
        raise HTTPException(status_code=401, detail="Not logged in")
    
    try:
        status_list = [status]
        gtt_list = smart_api.gttLists(status_list, page, count)
        
        return {
            "status": "success",
            "message": "GTT list retrieved successfully",
            "data": gtt_list
        }
        
    except Exception as e:
        logger.exception(f"GTT list retrieval failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/smartapi/historical-data")
async def get_historical_data(request: HistoricalDataRequest):
    global smart_api
    
    if not smart_api:
        raise HTTPException(status_code=401, detail="Not logged in")
    
    try:
        historic_params = request.dict()
        candle_data = smart_api.getCandleData(historic_params)
        
        return {
            "status": "success",
            "message": "Historical data retrieved successfully",
            "data": candle_data
        }
        
    except Exception as e:
        logger.exception(f"Historical data retrieval failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/smartapi/logout")
async def logout_smartapi():
    global smart_api, auth_token, feed_token
    
    if not smart_api:
        raise HTTPException(status_code=401, detail="Not logged in")
    
    try:
        # This would need client_id - simplified for now
        smart_api = None
        auth_token = None
        feed_token = None
        
        return {
            "status": "success",
            "message": "Logout successful"
        }
        
    except Exception as e:
        logger.exception(f"Logout failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
