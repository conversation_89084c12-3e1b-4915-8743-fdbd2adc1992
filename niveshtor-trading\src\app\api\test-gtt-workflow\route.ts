import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing complete GTT workflow...');

    const requestUrl = new URL(request.url);
    const baseUrl = `${requestUrl.protocol}//${requestUrl.host}`;

    // Step 1: Test Nifty 200 API
    console.log('📊 Step 1: Testing Nifty 200 API...');
    const niftyResponse = await fetch(`${baseUrl}/api/stocks/nifty200?batchIndex=0&batchSize=50`);
    const niftyData = await niftyResponse.json();
    
    if (!niftyData.success) {
      throw new Error('Nifty 200 API failed');
    }

    const bohEligibleCount = niftyData.data.stocks.filter((stock: any) => stock.isBOHEligible).length;
    console.log(`✅ Nifty 200 API: ${niftyData.data.stocks.length} stocks, ${bohEligibleCount} BOH eligible`);

    // Step 2: Test GTT Create Signal Orders API
    console.log('🎯 Step 2: Testing GTT Create Signal Orders API...');
    const gttResponse = await fetch(`${baseUrl}/api/gtt/create-signal-orders`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    });
    const gttData = await gttResponse.json();

    if (!gttData.success) {
      throw new Error('GTT Create Signal Orders API failed');
    }

    console.log(`✅ GTT API: ${gttData.data.totalBOHStocks} BOH stocks, ${gttData.data.validForGTT} valid for GTT`);

    // Step 3: Validate data consistency
    console.log('🔍 Step 3: Validating data consistency...');
    const sampleOrders = gttData.data.orders.slice(0, 5);
    
    const validationResults = sampleOrders.map((order: any) => ({
      symbol: order.symbol,
      triggerPrice: order.triggerPrice,
      quantity: order.quantity,
      isValid: order.triggerPrice > 0 && order.quantity > 0,
      estimatedValue: order.triggerPrice * order.quantity
    }));

    const allValid = validationResults.every(r => r.isValid);
    console.log(`✅ Validation: ${allValid ? 'All orders valid' : 'Some orders invalid'}`);

    return NextResponse.json({
      success: true,
      message: 'GTT workflow test completed successfully',
      results: {
        step1_nifty200: {
          totalStocks: niftyData.data.stocks.length,
          bohEligible: bohEligibleCount,
          status: 'SUCCESS'
        },
        step2_gtt_api: {
          totalBOHStocks: gttData.data.totalBOHStocks,
          validForGTT: gttData.data.validForGTT,
          avgTriggerPrice: gttData.data.stats.avgTriggerPrice,
          totalValue: gttData.data.stats.totalValue,
          status: 'SUCCESS'
        },
        step3_validation: {
          sampleOrders: validationResults,
          allValid,
          status: allValid ? 'SUCCESS' : 'WARNING'
        },
        overall: {
          status: 'SUCCESS',
          readyForProduction: allValid && gttData.data.validForGTT > 0
        }
      }
    });

  } catch (error) {
    console.error('❌ GTT workflow test failed:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Test failed',
        message: 'GTT workflow test failed'
      },
      { status: 500 }
    );
  }
}
