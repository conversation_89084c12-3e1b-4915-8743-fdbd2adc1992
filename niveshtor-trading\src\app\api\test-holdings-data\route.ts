import { NextRequest, NextResponse } from 'next/server';
import { holdingsService } from '@/lib/holdings-service';

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing holdings data calculations...');

    const testResults = {
      timestamp: new Date().toISOString(),
      tests: [] as any[]
    };

    // Test 1: Get all detailed holdings data
    console.log('📊 Test 1: Getting all detailed holdings data...');
    const detailedHoldings = await holdingsService.getAllDetailedHoldingData();

    testResults.tests.push({
      name: 'Detailed Holdings Data Retrieval',
      status: detailedHoldings.length > 0 ? 'PASS' : 'FAIL',
      data: {
        totalHoldings: detailedHoldings.length,
        symbols: detailedHoldings.map(h => h.symbol),
        sampleData: detailedHoldings[0] ? {
          symbol: detailedHoldings[0].symbol,
          name: detailedHoldings[0].name,
          totalQuantity: detailedHoldings[0].totalQuantity,
          totalInvested: detailedHoldings[0].totalInvested,
          averagePrice: detailedHoldings[0].averagePrice,
          currentPrice: detailedHoldings[0].currentPrice,
          targetPrice: detailedHoldings[0].targetPrice,
          overallNotionalPL: detailedHoldings[0].overallNotionalPL,
          notionalPLPercent: detailedHoldings[0].notionalPLPercent,
          transactionCount: detailedHoldings[0].transactions.length
        } : null
      }
    });

    // Test 2: Verify transaction data structure
    console.log('💰 Test 2: Verifying transaction data structure...');
    const sampleHolding = detailedHoldings[0];
    const hasValidTransactions = sampleHolding && sampleHolding.transactions.length > 0 &&
                                sampleHolding.transactions[0].id &&
                                sampleHolding.transactions[0].symbol &&
                                sampleHolding.transactions[0].type &&
                                sampleHolding.transactions[0].quantity &&
                                sampleHolding.transactions[0].price &&
                                sampleHolding.transactions[0].amount &&
                                sampleHolding.transactions[0].date;

    testResults.tests.push({
      name: 'Transaction Data Structure',
      status: hasValidTransactions ? 'PASS' : 'FAIL',
      data: {
        sampleTransaction: sampleHolding?.transactions[0] || null,
        requiredFields: ['id', 'symbol', 'type', 'quantity', 'price', 'amount', 'date', 'strategy'],
        hasAllFields: hasValidTransactions
      }
    });

    // Test 3: Verify calculations
    console.log('🧮 Test 3: Verifying calculations...');
    let calculationsCorrect = true;
    const calculationTests = [];

    for (const holding of detailedHoldings) {
      // Test average price calculation
      const buyTransactions = holding.transactions.filter(t => t.type === 'BUY');
      const totalBuyAmount = buyTransactions.reduce((sum, t) => sum + t.amount, 0);
      const totalBuyQuantity = buyTransactions.reduce((sum, t) => sum + t.quantity, 0);
      const expectedAvgPrice = totalBuyQuantity > 0 ? totalBuyAmount / totalBuyQuantity : 0;
      
      // Test target price (6% above average)
      const expectedTargetPrice = holding.averagePrice * 1.06;
      
      const calculationTest = {
        symbol: holding.symbol,
        averagePriceCorrect: Math.abs(holding.averagePrice - expectedAvgPrice) < 0.01,
        targetPriceCorrect: Math.abs(holding.targetPrice - expectedTargetPrice) < 0.01,
        totalInvestedCorrect: holding.totalInvested === totalBuyAmount,
        totalQuantityCorrect: holding.totalQuantity === totalBuyQuantity,
        calculations: {
          expected: {
            averagePrice: expectedAvgPrice,
            targetPrice: expectedTargetPrice,
            totalInvested: totalBuyAmount,
            totalQuantity: totalBuyQuantity
          },
          actual: {
            averagePrice: holding.averagePrice,
            targetPrice: holding.targetPrice,
            totalInvested: holding.totalInvested,
            totalQuantity: holding.totalQuantity
          }
        }
      };
      
      calculationTests.push(calculationTest);
      
      if (!calculationTest.averagePriceCorrect || !calculationTest.targetPriceCorrect || 
          !calculationTest.totalInvestedCorrect || !calculationTest.totalQuantityCorrect) {
        calculationsCorrect = false;
      }
    }

    testResults.tests.push({
      name: 'Financial Calculations',
      status: calculationsCorrect ? 'PASS' : 'FAIL',
      data: {
        calculationsCorrect,
        detailedTests: calculationTests
      }
    });

    // Test 4: Verify all required columns are present
    console.log('📋 Test 4: Verifying all required columns...');
    const requiredColumns = [
      'symbol', 'name', 'transactions', 'totalQuantity', 'totalInvested', 
      'averagePrice', 'currentPrice', 'targetPrice', 'totalInvestedAmount',
      'profitAmount', 'profitPercent', 'overallNotionalPL', 'notionalPLPercent',
      'lastWeekHighPrice', 'ignorableLowerPrice', 'isLastWeekHighOutsideIgnorableRange'
    ];

    const allColumnsPresent = sampleHolding && requiredColumns.every(col => 
      sampleHolding.hasOwnProperty(col) && sampleHolding[col as keyof typeof sampleHolding] !== undefined
    );

    testResults.tests.push({
      name: 'Required Columns Present',
      status: allColumnsPresent ? 'PASS' : 'FAIL',
      data: {
        requiredColumns,
        presentColumns: sampleHolding ? Object.keys(sampleHolding) : [],
        allColumnsPresent,
        missingColumns: sampleHolding ? requiredColumns.filter(col => 
          !sampleHolding.hasOwnProperty(col) || sampleHolding[col as keyof typeof sampleHolding] === undefined
        ) : requiredColumns
      }
    });

    // Calculate overall results
    const passedTests = testResults.tests.filter(t => t.status === 'PASS').length;
    const totalTests = testResults.tests.length;
    const overallStatus = passedTests === totalTests ? 'ALL_PASS' : 
                         passedTests >= totalTests * 0.75 ? 'MOSTLY_PASS' : 'NEEDS_ATTENTION';

    const summary = {
      overallStatus,
      passedTests,
      totalTests,
      successRate: `${Math.round((passedTests / totalTests) * 100)}%`,
      
      // Key metrics
      totalHoldings: detailedHoldings.length,
      totalTransactions: detailedHoldings.reduce((sum, h) => sum + h.transactions.length, 0),
      dataIntegrityGood: overallStatus !== 'NEEDS_ATTENTION',
      
      // System readiness
      readyForProduction: overallStatus === 'ALL_PASS',
      systemMessage: overallStatus === 'ALL_PASS' 
        ? 'Holdings data system is fully functional and ready'
        : overallStatus === 'MOSTLY_PASS'
          ? 'Holdings data system is mostly functional with minor issues'
          : 'Holdings data system needs attention before production use'
    };

    console.log(`🎯 Holdings data test: ${summary.systemMessage}`);

    return NextResponse.json({
      success: true,
      message: 'Holdings data calculation test completed',
      summary,
      testResults
    });

  } catch (error) {
    console.error('❌ Holdings data test failed:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Test failed',
        message: 'Holdings data test failed'
      },
      { status: 500 }
    );
  }
}
