// Holdings service to manage current holdings across strategies

export interface Holding {
  symbol: string;
  strategy: string;
  quantity: number;
  avgPrice: number;
  currentPrice: number;
  purchaseDate: Date;
}

class HoldingsService {
  private holdings: Holding[] = [
    // Sample holdings for demonstration - in real app, this would come from database
    {
      symbol: 'RELIANCE',
      strategy: 'DARVAS_BOX',
      quantity: 50,
      avgPrice: 2200.00,
      currentPrice: 2456.75,
      purchaseDate: new Date('2024-01-15')
    },
    {
      symbol: 'TCS',
      strategy: 'DARVAS_BOX',
      quantity: 25,
      avgPrice: 3400.00,
      currentPrice: 3234.50,
      purchaseDate: new Date('2024-01-20')
    },
    {
      symbol: 'HDFC',
      strategy: 'WEEKLY_HIGH',
      quantity: 40,
      avgPrice: 1600.00,
      currentPrice: 1678.90,
      purchaseDate: new Date('2024-02-01')
    },
    {
      symbol: 'INFY',
      strategy: 'BOH_FILTER',
      quantity: 60,
      avgPrice: 1500.00,
      currentPrice: 1456.80,
      purchaseDate: new Date('2024-02-10')
    }
  ];

  // Get all current holdings
  getAllHoldings(): Holding[] {
    return [...this.holdings];
  }

  // Get holdings for a specific strategy
  getHoldingsByStrategy(strategy: string): Holding[] {
    return this.holdings.filter(holding => holding.strategy === strategy);
  }

  // Check if a stock is currently held in any strategy
  isStockInHoldings(symbol: string): boolean {
    return this.holdings.some(holding => holding.symbol === symbol);
  }

  // Get all unique symbols in holdings
  getHoldingSymbols(): string[] {
    return [...new Set(this.holdings.map(holding => holding.symbol))];
  }

  // Add a new holding
  addHolding(holding: Omit<Holding, 'purchaseDate'>): void {
    const existingIndex = this.holdings.findIndex(
      h => h.symbol === holding.symbol && h.strategy === holding.strategy
    );

    if (existingIndex >= 0) {
      // Update existing holding (average price calculation)
      const existing = this.holdings[existingIndex];
      const totalQuantity = existing.quantity + holding.quantity;
      const totalValue = (existing.quantity * existing.avgPrice) + (holding.quantity * holding.avgPrice);
      
      this.holdings[existingIndex] = {
        ...existing,
        quantity: totalQuantity,
        avgPrice: totalValue / totalQuantity,
        currentPrice: holding.currentPrice
      };
    } else {
      // Add new holding
      this.holdings.push({
        ...holding,
        purchaseDate: new Date()
      });
    }
  }

  // Remove a holding
  removeHolding(symbol: string, strategy: string): void {
    this.holdings = this.holdings.filter(
      holding => !(holding.symbol === symbol && holding.strategy === strategy)
    );
  }

  // Update current price for a holding
  updateCurrentPrice(symbol: string, currentPrice: number): void {
    this.holdings.forEach(holding => {
      if (holding.symbol === symbol) {
        holding.currentPrice = currentPrice;
      }
    });
  }

  // Get stocks that were bought above ₹2000 and are still in holdings
  getStocksAbove2000InHoldings(): string[] {
    return this.holdings
      .filter(holding => holding.avgPrice > 2000 || holding.currentPrice > 2000)
      .map(holding => holding.symbol);
  }

  // Check if a stock should be eligible for trading
  // (CMP < 2000 OR currently in holdings)
  isStockEligibleForTrading(symbol: string, currentPrice: number): boolean {
    return currentPrice < 2000 || this.isStockInHoldings(symbol);
  }
}

export const holdingsService = new HoldingsService();
