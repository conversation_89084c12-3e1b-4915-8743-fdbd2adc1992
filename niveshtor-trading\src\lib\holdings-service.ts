// Holdings service to manage current holdings across strategies

import { yahooFinanceService } from './yahoo-finance';
import { stockNamesService } from './stock-names-service';
import { weeklyHighSignalDetector } from './weekly-high-signal-detector';

export interface Transaction {
  id: string;
  symbol: string;
  type: 'BUY' | 'SELL';
  quantity: number;
  price: number;
  amount: number;
  date: Date;
  strategy: string;
}

export interface Holding {
  symbol: string;
  strategy: string;
  quantity: number;
  avgPrice: number;
  currentPrice: number;
  purchaseDate: Date;
}

export interface DetailedHoldingData {
  symbol: string;
  name: string;
  transactions: Transaction[];
  totalQuantity: number;
  totalInvested: number;
  averagePrice: number;
  currentPrice: number;
  targetPrice: number;
  totalInvestedAmount: number;
  profitAmount: number;
  profitPercent: number;
  overallNotionalPL: number;
  notionalPLPercent: number;
  lastWeekHighPrice: number;
  ignorableLowerPrice: number;
  isLastWeekHighOutsideIgnorableRange: boolean;
}

class HoldingsService {
  private holdings: Holding[] = [
    // Sample holdings for demonstration - in real app, this would come from database
    {
      symbol: 'RELIANCE',
      strategy: 'DARVAS_BOX',
      quantity: 50,
      avgPrice: 2200.00,
      currentPrice: 2456.75,
      purchaseDate: new Date('2024-01-15')
    },
    {
      symbol: 'TCS',
      strategy: 'DARVAS_BOX',
      quantity: 25,
      avgPrice: 3400.00,
      currentPrice: 3234.50,
      purchaseDate: new Date('2024-01-20')
    },
    {
      symbol: 'HDFC',
      strategy: 'WEEKLY_HIGH',
      quantity: 40,
      avgPrice: 1600.00,
      currentPrice: 1678.90,
      purchaseDate: new Date('2024-02-01')
    },
    {
      symbol: 'INFY',
      strategy: 'BOH_FILTER',
      quantity: 60,
      avgPrice: 1500.00,
      currentPrice: 1456.80,
      purchaseDate: new Date('2024-02-10')
    }
  ];

  // Sample transaction history for detailed view
  private transactions: Transaction[] = [
    // RELIANCE transactions
    {
      id: 'txn_001',
      symbol: 'RELIANCE',
      type: 'BUY',
      quantity: 30,
      price: 2150.00,
      amount: 64500.00,
      date: new Date('2024-01-15'),
      strategy: 'DARVAS_BOX'
    },
    {
      id: 'txn_002',
      symbol: 'RELIANCE',
      type: 'BUY',
      quantity: 20,
      price: 2280.00,
      amount: 45600.00,
      date: new Date('2024-01-25'),
      strategy: 'DARVAS_BOX'
    },
    // TCS transactions
    {
      id: 'txn_003',
      symbol: 'TCS',
      type: 'BUY',
      quantity: 15,
      price: 3350.00,
      amount: 50250.00,
      date: new Date('2024-01-20'),
      strategy: 'DARVAS_BOX'
    },
    {
      id: 'txn_004',
      symbol: 'TCS',
      type: 'BUY',
      quantity: 10,
      price: 3480.00,
      amount: 34800.00,
      date: new Date('2024-02-05'),
      strategy: 'DARVAS_BOX'
    },
    // HDFC transactions
    {
      id: 'txn_005',
      symbol: 'HDFC',
      type: 'BUY',
      quantity: 25,
      price: 1580.00,
      amount: 39500.00,
      date: new Date('2024-02-01'),
      strategy: 'WEEKLY_HIGH'
    },
    {
      id: 'txn_006',
      symbol: 'HDFC',
      type: 'BUY',
      quantity: 15,
      price: 1630.00,
      amount: 24450.00,
      date: new Date('2024-02-15'),
      strategy: 'WEEKLY_HIGH'
    },
    // INFY transactions (including a partial sell)
    {
      id: 'txn_007',
      symbol: 'INFY',
      type: 'BUY',
      quantity: 40,
      price: 1480.00,
      amount: 59200.00,
      date: new Date('2024-02-10'),
      strategy: 'BOH_FILTER'
    },
    {
      id: 'txn_008',
      symbol: 'INFY',
      type: 'BUY',
      quantity: 30,
      price: 1520.00,
      amount: 45600.00,
      date: new Date('2024-02-20'),
      strategy: 'BOH_FILTER'
    },
    {
      id: 'txn_009',
      symbol: 'INFY',
      type: 'SELL',
      quantity: 10,
      price: 1580.00,
      amount: 15800.00,
      date: new Date('2024-03-01'),
      strategy: 'BOH_FILTER'
    }
  ];

  // Get all current holdings
  getAllHoldings(): Holding[] {
    return [...this.holdings];
  }

  // Get holdings for a specific strategy
  getHoldingsByStrategy(strategy: string): Holding[] {
    return this.holdings.filter(holding => holding.strategy === strategy);
  }

  // Check if a stock is currently held in any strategy
  isStockInHoldings(symbol: string): boolean {
    return this.holdings.some(holding => holding.symbol === symbol);
  }

  // Get all unique symbols in holdings
  getHoldingSymbols(): string[] {
    return [...new Set(this.holdings.map(holding => holding.symbol))];
  }

  // Add a new holding
  addHolding(holding: Omit<Holding, 'purchaseDate'>): void {
    const existingIndex = this.holdings.findIndex(
      h => h.symbol === holding.symbol && h.strategy === holding.strategy
    );

    if (existingIndex >= 0) {
      // Update existing holding (average price calculation)
      const existing = this.holdings[existingIndex];
      const totalQuantity = existing.quantity + holding.quantity;
      const totalValue = (existing.quantity * existing.avgPrice) + (holding.quantity * holding.avgPrice);
      
      this.holdings[existingIndex] = {
        ...existing,
        quantity: totalQuantity,
        avgPrice: totalValue / totalQuantity,
        currentPrice: holding.currentPrice
      };
    } else {
      // Add new holding
      this.holdings.push({
        ...holding,
        purchaseDate: new Date()
      });
    }
  }

  // Remove a holding
  removeHolding(symbol: string, strategy: string): void {
    this.holdings = this.holdings.filter(
      holding => !(holding.symbol === symbol && holding.strategy === strategy)
    );
  }

  // Update current price for a holding
  updateCurrentPrice(symbol: string, currentPrice: number): void {
    this.holdings.forEach(holding => {
      if (holding.symbol === symbol) {
        holding.currentPrice = currentPrice;
      }
    });
  }

  // Get stocks that were bought above ₹2000 and are still in holdings
  getStocksAbove2000InHoldings(): string[] {
    return this.holdings
      .filter(holding => holding.avgPrice > 2000 || holding.currentPrice > 2000)
      .map(holding => holding.symbol);
  }

  // Check if a stock should be eligible for trading
  // (CMP < 2000 OR currently in holdings)
  isStockEligibleForTrading(symbol: string, currentPrice: number): boolean {
    return currentPrice < 2000 || this.isStockInHoldings(symbol);
  }

  // Get transactions for a specific symbol
  getTransactionsBySymbol(symbol: string): Transaction[] {
    return this.transactions.filter(txn => txn.symbol === symbol);
  }

  // Get all unique symbols from transactions
  getAllTransactionSymbols(): string[] {
    return [...new Set(this.transactions.map(txn => txn.symbol))];
  }

  // Calculate detailed holding data for a symbol
  async getDetailedHoldingData(symbol: string): Promise<DetailedHoldingData | null> {
    const transactions = this.getTransactionsBySymbol(symbol);
    if (transactions.length === 0) return null;

    try {
      // Get stock name from stock names service
      const stockName = await stockNamesService.getStockName(symbol) || symbol;

      // Get current price from Yahoo Finance
      const priceData = await yahooFinanceService.getStockPrice(symbol);
      const currentPrice = priceData?.regularMarketPrice || this.holdings.find(h => h.symbol === symbol)?.currentPrice || 0;

      // Calculate basic metrics
      let totalQuantity = 0;
      let totalInvested = 0;
      let totalSold = 0;
      let totalSaleAmount = 0;

      transactions.forEach(txn => {
        if (txn.type === 'BUY') {
          totalQuantity += txn.quantity;
          totalInvested += txn.amount;
        } else if (txn.type === 'SELL') {
          totalQuantity -= txn.quantity;
          totalSold += txn.quantity;
          totalSaleAmount += txn.amount;
        }
      });

      const averagePrice = totalQuantity > 0 ? totalInvested / (totalQuantity + totalSold) : 0;
      const targetPrice = averagePrice * 1.06; // 6% profit target

      // Calculate P&L
      const profitAmount = totalSaleAmount - (totalSold * averagePrice);
      const profitPercent = totalSold > 0 ? (profitAmount / (totalSold * averagePrice)) * 100 : 0;

      // Calculate notional P&L for current holdings
      const overallNotionalPL = (currentPrice - averagePrice) * totalQuantity;
      const notionalPLPercent = totalQuantity > 0 ? (overallNotionalPL / (averagePrice * totalQuantity)) * 100 : 0;

      // Get weekly high data from signal detector
      let lastWeekHighPrice = currentPrice * 1.05; // Default fallback
      let ignorableLowerPrice = currentPrice * 0.95; // Default fallback
      let isLastWeekHighOutsideIgnorableRange = true;

      try {
        // Try to get real weekly high data
        const signals = await weeklyHighSignalDetector.triggerManualScan();
        const signalForStock = signals.find(s => s.symbol === symbol);

        if (signalForStock) {
          lastWeekHighPrice = signalForStock.lastWeekHigh;
          // Calculate ignorable lower price based on BOH logic (simplified)
          ignorableLowerPrice = currentPrice * 0.95; // 5% below current as threshold
          isLastWeekHighOutsideIgnorableRange = lastWeekHighPrice > ignorableLowerPrice;
        }
      } catch (error) {
        console.warn(`Could not fetch weekly high data for ${symbol}:`, error);
      }

      return {
        symbol,
        name: stockName,
        transactions,
        totalQuantity,
        totalInvested,
        averagePrice,
        currentPrice,
        targetPrice,
        totalInvestedAmount: totalInvested,
        profitAmount,
        profitPercent,
        overallNotionalPL,
        notionalPLPercent,
        lastWeekHighPrice,
        ignorableLowerPrice,
        isLastWeekHighOutsideIgnorableRange
      };
    } catch (error) {
      console.error(`Error calculating detailed holding data for ${symbol}:`, error);
      return null;
    }
  }

  // Get detailed data for all holdings
  async getAllDetailedHoldingData(): Promise<DetailedHoldingData[]> {
    const symbols = this.getAllTransactionSymbols();
    const detailedData: DetailedHoldingData[] = [];

    for (const symbol of symbols) {
      const data = await this.getDetailedHoldingData(symbol);
      if (data) {
        detailedData.push(data);
      }
    }

    return detailedData;
  }
}

export const holdingsService = new HoldingsService();
