// React Hook for Central Data Manager Integration
// Provides real-time data access and automatic updates for all stock-related pages

import { useState, useEffect, useCallback, useRef } from 'react';
import { NiftyStock } from '@/lib/nifty-stocks';
import { WeeklyHighSignal } from '@/lib/weekly-high-signal-detector';
import { AutoGTTOrder } from '@/lib/automatic-gtt-service';

type DataType = 'nifty200' | 'bohEligible' | 'weeklyHighSignals' | 'gttOrders';

interface DataState<T> {
  data: T[];
  lastUpdated: Date | null;
  isLoading: boolean;
  error: string | null;
}

interface CentralDataHook {
  nifty200: DataState<NiftyStock>;
  bohEligible: DataState<NiftyStock>;
  weeklyHighSignals: DataState<WeeklyHighSignal>;
  gttOrders: DataState<AutoGTTOrder>;
  refreshData: (dataType?: DataType) => Promise<void>;
  isInitialized: boolean;
  isServiceRunning: boolean;
}

export function useCentralData(): CentralDataHook {
  const [nifty200, setNifty200] = useState<DataState<NiftyStock>>({
    data: [],
    lastUpdated: null,
    isLoading: false,
    error: null
  });

  const [bohEligible, setBohEligible] = useState<DataState<NiftyStock>>({
    data: [],
    lastUpdated: null,
    isLoading: false,
    error: null
  });

  const [weeklyHighSignals, setWeeklyHighSignals] = useState<DataState<WeeklyHighSignal>>({
    data: [],
    lastUpdated: null,
    isLoading: false,
    error: null
  });

  const [gttOrders, setGttOrders] = useState<DataState<AutoGTTOrder>>({
    data: [],
    lastUpdated: null,
    isLoading: false,
    error: null
  });

  const [isInitialized, setIsInitialized] = useState(false);
  const [isServiceRunning, setIsServiceRunning] = useState(false);

  const pollingIntervals = useRef<Map<DataType, NodeJS.Timeout>>(new Map());

  // Fetch data from API
  const fetchData = useCallback(async (dataType: DataType) => {
    try {
      const response = await fetch(`/api/data-manager?action=${dataType === 'bohEligible' ? 'boh-eligible' : dataType === 'weeklyHighSignals' ? 'weekly-high-signals' : dataType === 'gttOrders' ? 'gtt-orders' : dataType}`);
      const result = await response.json();

      if (result.success) {
        const newState = {
          data: result.data || [],
          lastUpdated: result.lastUpdated ? new Date(result.lastUpdated) : new Date(),
          isLoading: result.isLoading || false,
          error: null
        };

        switch (dataType) {
          case 'nifty200':
            setNifty200(newState);
            break;
          case 'bohEligible':
            setBohEligible(newState);
            break;
          case 'weeklyHighSignals':
            setWeeklyHighSignals(newState);
            break;
          case 'gttOrders':
            setGttOrders(newState);
            break;
        }

        console.log(`📊 Updated ${dataType}: ${result.data?.length || 0} items`);
      } else {
        throw new Error(result.error || 'Failed to fetch data');
      }
    } catch (error) {
      console.error(`❌ Error fetching ${dataType}:`, error);
      
      const errorState = {
        data: [],
        lastUpdated: null,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };

      switch (dataType) {
        case 'nifty200':
          setNifty200(prev => ({ ...prev, ...errorState }));
          break;
        case 'bohEligible':
          setBohEligible(prev => ({ ...prev, ...errorState }));
          break;
        case 'weeklyHighSignals':
          setWeeklyHighSignals(prev => ({ ...prev, ...errorState }));
          break;
        case 'gttOrders':
          setGttOrders(prev => ({ ...prev, ...errorState }));
          break;
      }
    }
  }, []);

  // Check service status
  const checkServiceStatus = useCallback(async () => {
    try {
      const response = await fetch('/api/data-manager?action=status');
      const result = await response.json();

      if (result.success) {
        setIsInitialized(result.data.isInitialized);
        setIsServiceRunning(result.data.isRunning);
      }
    } catch (error) {
      console.error('❌ Error checking service status:', error);
    }
  }, []);

  // Initialize service if not already initialized
  const initializeService = useCallback(async () => {
    try {
      console.log('🚀 Initializing Central Data Manager...');
      
      const response = await fetch('/api/data-manager', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'initialize' })
      });

      const result = await response.json();
      
      if (result.success) {
        console.log('✅ Central Data Manager initialized');
        setIsInitialized(true);
        
        // Start the service
        const startResponse = await fetch('/api/data-manager', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ action: 'start' })
        });

        const startResult = await startResponse.json();
        if (startResult.success) {
          setIsServiceRunning(true);
          console.log('✅ Central Data Manager started');
        }
      }
    } catch (error) {
      console.error('❌ Error initializing service:', error);
    }
  }, []);

  // Refresh specific data type
  const refreshData = useCallback(async (dataType?: DataType) => {
    if (dataType) {
      await fetchData(dataType);
    } else {
      // Refresh all data
      await Promise.all([
        fetchData('nifty200'),
        fetchData('bohEligible'),
        fetchData('weeklyHighSignals'),
        fetchData('gttOrders')
      ]);
    }
  }, [fetchData]);

  // Set up polling for real-time updates
  const setupPolling = useCallback(() => {
    // Clear existing intervals
    pollingIntervals.current.forEach(interval => clearInterval(interval));
    pollingIntervals.current.clear();

    // Set up new intervals
    const intervals = {
      nifty200: 30000, // 30 seconds
      bohEligible: 60000, // 1 minute
      weeklyHighSignals: 300000, // 5 minutes
      gttOrders: 30000 // 30 seconds
    };

    Object.entries(intervals).forEach(([dataType, interval]) => {
      const intervalId = setInterval(() => {
        fetchData(dataType as DataType);
      }, interval);
      
      pollingIntervals.current.set(dataType as DataType, intervalId);
    });

    console.log('⏰ Polling intervals set up for real-time updates');
  }, [fetchData]);

  // Initialize on mount
  useEffect(() => {
    const initialize = async () => {
      // Check if service is already running
      await checkServiceStatus();
      
      // Initialize service if needed
      if (!isInitialized) {
        await initializeService();
      }

      // Load initial data
      await refreshData();

      // Set up polling for real-time updates
      setupPolling();
    };

    initialize();

    // Cleanup on unmount
    return () => {
      pollingIntervals.current.forEach(interval => clearInterval(interval));
      pollingIntervals.current.clear();
    };
  }, []);

  // Re-setup polling when service status changes
  useEffect(() => {
    if (isServiceRunning) {
      setupPolling();
    }
  }, [isServiceRunning, setupPolling]);

  return {
    nifty200,
    bohEligible,
    weeklyHighSignals,
    gttOrders,
    refreshData,
    isInitialized,
    isServiceRunning
  };
}

// Specialized hooks for individual data types
export function useNifty200Stocks() {
  const { nifty200, refreshData } = useCentralData();
  
  return {
    stocks: nifty200.data,
    lastUpdated: nifty200.lastUpdated,
    isLoading: nifty200.isLoading,
    error: nifty200.error,
    refresh: () => refreshData('nifty200')
  };
}

export function useBOHEligibleStocks() {
  const { bohEligible, refreshData } = useCentralData();
  
  return {
    stocks: bohEligible.data,
    lastUpdated: bohEligible.lastUpdated,
    isLoading: bohEligible.isLoading,
    error: bohEligible.error,
    refresh: () => refreshData('bohEligible')
  };
}

export function useWeeklyHighSignals() {
  const { weeklyHighSignals, refreshData } = useCentralData();
  
  return {
    signals: weeklyHighSignals.data,
    lastUpdated: weeklyHighSignals.lastUpdated,
    isLoading: weeklyHighSignals.isLoading,
    error: weeklyHighSignals.error,
    refresh: () => refreshData('weeklyHighSignals')
  };
}

export function useGTTOrders() {
  const { gttOrders, refreshData } = useCentralData();
  
  return {
    orders: gttOrders.data,
    lastUpdated: gttOrders.lastUpdated,
    isLoading: gttOrders.isLoading,
    error: gttOrders.error,
    refresh: () => refreshData('gttOrders')
  };
}
