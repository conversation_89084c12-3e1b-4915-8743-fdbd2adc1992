// React Hook for Central Data Manager Integration
// Provides real-time data access and automatic updates for all stock-related pages

import { useState, useEffect, useCallback, useRef } from 'react';
import { NiftyStock } from '@/lib/nifty-stocks';
import { WeeklyHighSignal } from '@/lib/weekly-high-signal-detector';
import { AutoGTTOrder } from '@/lib/automatic-gtt-service';
import { centralDataManager, automaticGTTService, weeklyHighSignalDetector } from '@/lib/service-initializer';

type DataType = 'nifty200' | 'bohEligible' | 'weeklyHighSignals' | 'gttOrders';

interface DataState<T> {
  data: T[];
  lastUpdated: Date | null;
  isLoading: boolean;
  error: string | null;
}

interface CentralDataHook {
  nifty200: DataState<NiftyStock>;
  bohEligible: DataState<NiftyStock>;
  weeklyHighSignals: DataState<WeeklyHighSignal>;
  gttOrders: DataState<AutoGTTOrder>;
  refreshData: (dataType?: DataType) => Promise<void>;
  isInitialized: boolean;
  isServiceRunning: boolean;
}

export function useCentralData(): CentralDataHook {
  const [nifty200, setNifty200] = useState<DataState<NiftyStock>>({
    data: [],
    lastUpdated: null,
    isLoading: false,
    error: null
  });

  const [bohEligible, setBohEligible] = useState<DataState<NiftyStock>>({
    data: [],
    lastUpdated: null,
    isLoading: false,
    error: null
  });

  const [weeklyHighSignals, setWeeklyHighSignals] = useState<DataState<WeeklyHighSignal>>({
    data: [],
    lastUpdated: null,
    isLoading: false,
    error: null
  });

  const [gttOrders, setGttOrders] = useState<DataState<AutoGTTOrder>>({
    data: [],
    lastUpdated: null,
    isLoading: false,
    error: null
  });

  const [isInitialized, setIsInitialized] = useState(false);
  const [isServiceRunning, setIsServiceRunning] = useState(false);

  const pollingIntervals = useRef<Map<DataType, NodeJS.Timeout>>(new Map());

  // Fetch data directly from services
  const fetchData = useCallback(async (dataType: DataType) => {
    try {
      let data: any[] = [];
      let lastUpdated: Date | null = null;

      switch (dataType) {
        case 'nifty200':
          data = centralDataManager.getNifty200Stocks();
          lastUpdated = centralDataManager.getLastUpdated('nifty200');
          setNifty200({
            data,
            lastUpdated,
            isLoading: centralDataManager.isDataLoading('nifty200'),
            error: null
          });
          break;
        case 'bohEligible':
          data = centralDataManager.getBOHEligibleStocks();
          lastUpdated = centralDataManager.getLastUpdated('bohEligible');
          setBohEligible({
            data,
            lastUpdated,
            isLoading: centralDataManager.isDataLoading('bohEligible'),
            error: null
          });
          break;
        case 'weeklyHighSignals':
          data = centralDataManager.getWeeklyHighSignals();
          lastUpdated = centralDataManager.getLastUpdated('weeklyHighSignals');
          setWeeklyHighSignals({
            data,
            lastUpdated,
            isLoading: centralDataManager.isDataLoading('weeklyHighSignals'),
            error: null
          });
          break;
        case 'gttOrders':
          data = centralDataManager.getGTTOrders();
          lastUpdated = centralDataManager.getLastUpdated('gttOrders');
          setGttOrders({
            data,
            lastUpdated,
            isLoading: centralDataManager.isDataLoading('gttOrders'),
            error: null
          });
          break;
      }

      console.log(`📊 Updated ${dataType}: ${data.length} items`);
    } catch (error) {
      console.error(`❌ Error fetching ${dataType}:`, error);

      const errorState = {
        data: [],
        lastUpdated: null,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };

      switch (dataType) {
        case 'nifty200':
          setNifty200(prev => ({ ...prev, ...errorState }));
          break;
        case 'bohEligible':
          setBohEligible(prev => ({ ...prev, ...errorState }));
          break;
        case 'weeklyHighSignals':
          setWeeklyHighSignals(prev => ({ ...prev, ...errorState }));
          break;
        case 'gttOrders':
          setGttOrders(prev => ({ ...prev, ...errorState }));
          break;
      }
    }
  }, []);

  // Check service status directly
  const checkServiceStatus = useCallback(() => {
    try {
      const status = centralDataManager.getStatus();
      setIsInitialized(status.isInitialized);
      setIsServiceRunning(status.isRunning);

      console.log('📊 Service status:', {
        initialized: status.isInitialized,
        running: status.isRunning,
        marketOpen: status.isMarketOpen
      });
    } catch (error) {
      console.error('❌ Error checking service status:', error);
      setIsInitialized(false);
      setIsServiceRunning(false);
    }
  }, []);

  // Initialize service directly if not already initialized
  const initializeService = useCallback(async () => {
    try {
      console.log('🚀 Initializing Central Data Manager directly...');

      if (!centralDataManager.getStatus().isInitialized) {
        await centralDataManager.initialize();
        console.log('✅ Central Data Manager initialized');
      }

      if (!centralDataManager.getStatus().isRunning) {
        centralDataManager.start();
        console.log('✅ Central Data Manager started');
      }

      setIsInitialized(true);
      setIsServiceRunning(true);
    } catch (error) {
      console.error('❌ Error initializing service:', error);
      setIsInitialized(false);
      setIsServiceRunning(false);
    }
  }, []);

  // Refresh specific data type
  const refreshData = useCallback(async (dataType?: DataType) => {
    if (dataType) {
      await fetchData(dataType);
    } else {
      // Refresh all data
      await Promise.all([
        fetchData('nifty200'),
        fetchData('bohEligible'),
        fetchData('weeklyHighSignals'),
        fetchData('gttOrders')
      ]);
    }
  }, [fetchData]);

  // Set up polling for real-time updates
  const setupPolling = useCallback(() => {
    // Clear existing intervals
    pollingIntervals.current.forEach(interval => clearInterval(interval));
    pollingIntervals.current.clear();

    // Set up new intervals
    const intervals = {
      nifty200: 30000, // 30 seconds
      bohEligible: 60000, // 1 minute
      weeklyHighSignals: 300000, // 5 minutes
      gttOrders: 30000 // 30 seconds
    };

    Object.entries(intervals).forEach(([dataType, interval]) => {
      const intervalId = setInterval(() => {
        fetchData(dataType as DataType);
      }, interval);
      
      pollingIntervals.current.set(dataType as DataType, intervalId);
    });

    console.log('⏰ Polling intervals set up for real-time updates');
  }, [fetchData]);

  // Set up data listeners for real-time updates
  useEffect(() => {
    console.log('🔗 Setting up Central Data Manager listeners...');

    // Add listeners for each data type
    const nifty200Listener = (data: any[], timestamp: Date) => {
      setNifty200({
        data,
        lastUpdated: timestamp,
        isLoading: false,
        error: null
      });
      console.log(`📊 Nifty200 data updated: ${data.length} stocks`);
    };

    const bohEligibleListener = (data: any[], timestamp: Date) => {
      setBohEligible({
        data,
        lastUpdated: timestamp,
        isLoading: false,
        error: null
      });
      console.log(`📊 BOH Eligible data updated: ${data.length} stocks`);
    };

    const weeklyHighSignalsListener = (data: any[], timestamp: Date) => {
      setWeeklyHighSignals({
        data,
        lastUpdated: timestamp,
        isLoading: false,
        error: null
      });
      console.log(`📊 Weekly High Signals updated: ${data.length} signals`);
    };

    const gttOrdersListener = (data: any[], timestamp: Date) => {
      setGttOrders({
        data,
        lastUpdated: timestamp,
        isLoading: false,
        error: null
      });
      console.log(`📊 GTT Orders updated: ${data.length} orders`);
    };

    // Add listeners to Central Data Manager
    centralDataManager.addListener('nifty200', nifty200Listener);
    centralDataManager.addListener('bohEligible', bohEligibleListener);
    centralDataManager.addListener('weeklyHighSignals', weeklyHighSignalsListener);
    centralDataManager.addListener('gttOrders', gttOrdersListener);

    // Initialize and load initial data
    const initialize = async () => {
      checkServiceStatus();

      if (!centralDataManager.getStatus().isInitialized) {
        await initializeService();
      }

      // Load initial data
      await refreshData();
    };

    initialize();

    // Cleanup listeners on unmount
    return () => {
      centralDataManager.removeListener('nifty200', nifty200Listener);
      centralDataManager.removeListener('bohEligible', bohEligibleListener);
      centralDataManager.removeListener('weeklyHighSignals', weeklyHighSignalsListener);
      centralDataManager.removeListener('gttOrders', gttOrdersListener);

      pollingIntervals.current.forEach(interval => clearInterval(interval));
      pollingIntervals.current.clear();
    };
  }, []);

  // Re-setup polling when service status changes
  useEffect(() => {
    if (isServiceRunning) {
      setupPolling();
    }
  }, [isServiceRunning, setupPolling]);

  return {
    nifty200,
    bohEligible,
    weeklyHighSignals,
    gttOrders,
    refreshData,
    isInitialized,
    isServiceRunning
  };
}

// Specialized hooks for individual data types
export function useNifty200Stocks() {
  const { nifty200, refreshData } = useCentralData();
  
  return {
    stocks: nifty200.data,
    lastUpdated: nifty200.lastUpdated,
    isLoading: nifty200.isLoading,
    error: nifty200.error,
    refresh: () => refreshData('nifty200')
  };
}

export function useBOHEligibleStocks() {
  const { bohEligible, refreshData } = useCentralData();
  
  return {
    stocks: bohEligible.data,
    lastUpdated: bohEligible.lastUpdated,
    isLoading: bohEligible.isLoading,
    error: bohEligible.error,
    refresh: () => refreshData('bohEligible')
  };
}

export function useWeeklyHighSignals() {
  const { weeklyHighSignals, refreshData } = useCentralData();
  
  return {
    signals: weeklyHighSignals.data,
    lastUpdated: weeklyHighSignals.lastUpdated,
    isLoading: weeklyHighSignals.isLoading,
    error: weeklyHighSignals.error,
    refresh: () => refreshData('weeklyHighSignals')
  };
}

export function useGTTOrders() {
  const { gttOrders, refreshData } = useCentralData();
  
  return {
    orders: gttOrders.data,
    lastUpdated: gttOrders.lastUpdated,
    isLoading: gttOrders.isLoading,
    error: gttOrders.error,
    refresh: () => refreshData('gttOrders')
  };
}
