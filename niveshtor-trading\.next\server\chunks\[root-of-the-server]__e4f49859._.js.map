{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/app/api/test-gtt-workflow/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport async function GET(request: NextRequest) {\n  try {\n    console.log('🧪 Testing complete GTT workflow...');\n\n    const requestUrl = new URL(request.url);\n    const baseUrl = `${requestUrl.protocol}//${requestUrl.host}`;\n\n    // Step 1: Test Nifty 200 API\n    console.log('📊 Step 1: Testing Nifty 200 API...');\n    const niftyResponse = await fetch(`${baseUrl}/api/stocks/nifty200?batchIndex=0&batchSize=50`);\n    const niftyData = await niftyResponse.json();\n    \n    if (!niftyData.success) {\n      throw new Error('Nifty 200 API failed');\n    }\n\n    const bohEligibleCount = niftyData.data.stocks.filter((stock: any) => stock.isBOHEligible).length;\n    console.log(`✅ Nifty 200 API: ${niftyData.data.stocks.length} stocks, ${bohEligibleCount} BOH eligible`);\n\n    // Step 2: Test GTT Create Signal Orders API\n    console.log('🎯 Step 2: Testing GTT Create Signal Orders API...');\n    const gttResponse = await fetch(`${baseUrl}/api/gtt/create-signal-orders`, {\n      method: 'POST',\n      headers: { 'Content-Type': 'application/json' }\n    });\n    const gttData = await gttResponse.json();\n\n    if (!gttData.success) {\n      throw new Error('GTT Create Signal Orders API failed');\n    }\n\n    console.log(`✅ GTT API: ${gttData.data.totalBOHStocks} BOH stocks, ${gttData.data.validForGTT} valid for GTT`);\n\n    // Step 3: Validate data consistency\n    console.log('🔍 Step 3: Validating data consistency...');\n    const sampleOrders = gttData.data.orders.slice(0, 5);\n    \n    const validationResults = sampleOrders.map((order: any) => ({\n      symbol: order.symbol,\n      triggerPrice: order.triggerPrice,\n      quantity: order.quantity,\n      isValid: order.triggerPrice > 0 && order.quantity > 0,\n      estimatedValue: order.triggerPrice * order.quantity\n    }));\n\n    const allValid = validationResults.every(r => r.isValid);\n    console.log(`✅ Validation: ${allValid ? 'All orders valid' : 'Some orders invalid'}`);\n\n    return NextResponse.json({\n      success: true,\n      message: 'GTT workflow test completed successfully',\n      results: {\n        step1_nifty200: {\n          totalStocks: niftyData.data.stocks.length,\n          bohEligible: bohEligibleCount,\n          status: 'SUCCESS'\n        },\n        step2_gtt_api: {\n          totalBOHStocks: gttData.data.totalBOHStocks,\n          validForGTT: gttData.data.validForGTT,\n          avgTriggerPrice: gttData.data.stats.avgTriggerPrice,\n          totalValue: gttData.data.stats.totalValue,\n          status: 'SUCCESS'\n        },\n        step3_validation: {\n          sampleOrders: validationResults,\n          allValid,\n          status: allValid ? 'SUCCESS' : 'WARNING'\n        },\n        overall: {\n          status: 'SUCCESS',\n          readyForProduction: allValid && gttData.data.validForGTT > 0\n        }\n      }\n    });\n\n  } catch (error) {\n    console.error('❌ GTT workflow test failed:', error);\n    return NextResponse.json(\n      { \n        success: false, \n        error: error instanceof Error ? error.message : 'Test failed',\n        message: 'GTT workflow test failed'\n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,MAAM,aAAa,IAAI,IAAI,QAAQ,GAAG;QACtC,MAAM,UAAU,GAAG,WAAW,QAAQ,CAAC,EAAE,EAAE,WAAW,IAAI,EAAE;QAE5D,6BAA6B;QAC7B,QAAQ,GAAG,CAAC;QACZ,MAAM,gBAAgB,MAAM,MAAM,GAAG,QAAQ,8CAA8C,CAAC;QAC5F,MAAM,YAAY,MAAM,cAAc,IAAI;QAE1C,IAAI,CAAC,UAAU,OAAO,EAAE;YACtB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,mBAAmB,UAAU,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,QAAe,MAAM,aAAa,EAAE,MAAM;QACjG,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,UAAU,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,iBAAiB,aAAa,CAAC;QAEvG,4CAA4C;QAC5C,QAAQ,GAAG,CAAC;QACZ,MAAM,cAAc,MAAM,MAAM,GAAG,QAAQ,6BAA6B,CAAC,EAAE;YACzE,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAmB;QAChD;QACA,MAAM,UAAU,MAAM,YAAY,IAAI;QAEtC,IAAI,CAAC,QAAQ,OAAO,EAAE;YACpB,MAAM,IAAI,MAAM;QAClB;QAEA,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,QAAQ,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC;QAE7G,oCAAoC;QACpC,QAAQ,GAAG,CAAC;QACZ,MAAM,eAAe,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG;QAElD,MAAM,oBAAoB,aAAa,GAAG,CAAC,CAAC,QAAe,CAAC;gBAC1D,QAAQ,MAAM,MAAM;gBACpB,cAAc,MAAM,YAAY;gBAChC,UAAU,MAAM,QAAQ;gBACxB,SAAS,MAAM,YAAY,GAAG,KAAK,MAAM,QAAQ,GAAG;gBACpD,gBAAgB,MAAM,YAAY,GAAG,MAAM,QAAQ;YACrD,CAAC;QAED,MAAM,WAAW,kBAAkB,KAAK,CAAC,CAAA,IAAK,EAAE,OAAO;QACvD,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,WAAW,qBAAqB,uBAAuB;QAEpF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,SAAS;gBACP,gBAAgB;oBACd,aAAa,UAAU,IAAI,CAAC,MAAM,CAAC,MAAM;oBACzC,aAAa;oBACb,QAAQ;gBACV;gBACA,eAAe;oBACb,gBAAgB,QAAQ,IAAI,CAAC,cAAc;oBAC3C,aAAa,QAAQ,IAAI,CAAC,WAAW;oBACrC,iBAAiB,QAAQ,IAAI,CAAC,KAAK,CAAC,eAAe;oBACnD,YAAY,QAAQ,IAAI,CAAC,KAAK,CAAC,UAAU;oBACzC,QAAQ;gBACV;gBACA,kBAAkB;oBAChB,cAAc;oBACd;oBACA,QAAQ,WAAW,YAAY;gBACjC;gBACA,SAAS;oBACP,QAAQ;oBACR,oBAAoB,YAAY,QAAQ,IAAI,CAAC,WAAW,GAAG;gBAC7D;YACF;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,SAAS;QACX,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}