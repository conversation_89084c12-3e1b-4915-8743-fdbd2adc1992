import { NextRequest, NextResponse } from 'next/server';
import { yahooFinanceService } from '@/lib/yahoo-finance';
import { NIFTY_200_SYMBOLS, getYahooSymbol, getDisplaySymbol, NiftyStock, addBOHEligibility } from '@/lib/nifty-stocks';
import { holdingsService } from '@/lib/holdings-service';
import { cacheService, CacheKeys } from '@/lib/cache-service';
import { stockNamesService } from '@/lib/stock-names-service';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const batchSize = parseInt(searchParams.get('batchSize') || '25'); // Reduced batch size
    const batchIndex = parseInt(searchParams.get('batchIndex') || '0');

    // Check cache first
    const cacheKey = CacheKeys.niftyStocks(batchIndex);
    const cached = cacheService.get<any>(cacheKey);

    if (cached) {
      return NextResponse.json({
        success: true,
        data: cached,
        cached: true
      });
    }

    // Calculate batch range
    const startIndex = batchIndex * batchSize;
    const endIndex = Math.min(startIndex + batchSize, NIFTY_200_SYMBOLS.length);
    const batchSymbols = NIFTY_200_SYMBOLS.slice(startIndex, endIndex);

    console.log(`Fetching batch ${batchIndex}: symbols ${startIndex} to ${endIndex-1} (${batchSymbols.length} symbols)`);

    // Convert NSE symbols to Yahoo Finance format, filtering out delisted stocks
    const yahooSymbols = batchSymbols
      .map(getYahooSymbol)
      .filter(symbol => symbol !== null); // Remove delisted stocks

    const validBatchSymbols = batchSymbols.filter(symbol => getYahooSymbol(symbol) !== null);

    // Check if this is a force refresh request
    const forceRefresh = searchParams.get('forceRefresh') === 'true';

    // Preload stock names if not cached or force refresh
    if (forceRefresh || !stockNamesService.isNameCached(yahooSymbols[0])) {
      console.log('🔄 Preloading stock names...');
      await stockNamesService.preloadStockNames(yahooSymbols);
    }

    // Fetch quotes using optimized method with cached names
    const quotes = await Promise.race([
      yahooFinanceService.getMultipleQuotesWithCachedNames(yahooSymbols),
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Yahoo Finance API timeout')), 30000) // Increased to 30 seconds
      )
    ]);

    console.log(`Yahoo Finance returned ${quotes.length} quotes for ${yahooSymbols.length} symbols`);

    // Get current holdings
    const holdingSymbols = holdingsService.getHoldingSymbols();

    // Process and filter stocks
    const processedStocks: NiftyStock[] = batchSymbols.map(nseSymbol => {
      const yahooSymbol = getYahooSymbol(nseSymbol);
      const quote = quotes.find(q => q.symbol === yahooSymbol);

      const price = quote?.price || 0;
      const change = quote?.change || 0;
      const changePercent = quote?.changePercent || 0;
      const volume = quote?.volume || 0;
      const marketCap = quote?.marketCap;
      const high52Week = quote?.high52Week;
      const low52Week = quote?.low52Week;
      const high52WeekDate = quote?.high52WeekDate;
      const low52WeekDate = quote?.low52WeekDate;

      const inHoldings = holdingSymbols.includes(nseSymbol);
      const isEligible = holdingsService.isStockEligibleForTrading(nseSymbol, price);

      const stock: NiftyStock = {
        symbol: nseSymbol,
        name: quote?.name || nseSymbol,
        price,
        change,
        changePercent,
        volume,
        marketCap,
        high52Week,
        low52Week,
        high52WeekDate,
        low52WeekDate,
        isEligible,
        inHoldings
      };

      // Add BOH eligibility calculation
      return addBOHEligibility(stock);
    });

    // Filter to show only eligible stocks (CMP < 2000 OR in holdings)
    const eligibleStocks = processedStocks.filter(stock => stock.isEligible);

    // Sort by market cap (descending) for better user experience
    eligibleStocks.sort((a, b) => (b.marketCap || 0) - (a.marketCap || 0));

    console.log(`Processed ${processedStocks.length} stocks, ${eligibleStocks.length} eligible (CMP < 2000 or in holdings)`);

    const totalBatches = Math.ceil(NIFTY_200_SYMBOLS.length / batchSize);
    const hasMore = batchIndex < totalBatches - 1;

    const responseData = {
      stocks: eligibleStocks,
      pagination: {
        batchIndex,
        batchSize,
        totalSymbols: NIFTY_200_SYMBOLS.length,
        totalBatches,
        hasMore,
        processedCount: endIndex,
        eligibleCount: eligibleStocks.length
      }
    };

    // Cache the response
    cacheService.set(cacheKey, responseData);

    return NextResponse.json({
      success: true,
      data: responseData
    });

  } catch (error) {
    console.error('Error fetching Nifty 200 stocks:', error);

    // Try to return cached data even if stale
    const cacheKey = CacheKeys.niftyStocks(parseInt(new URL(request.url).searchParams.get('batchIndex') || '0'));
    const staleData = cacheService.get<any>(cacheKey);

    if (staleData) {
      return NextResponse.json({
        success: true,
        data: staleData,
        stale: true,
        warning: 'Using cached data due to API error'
      });
    }

    return NextResponse.json({
      success: false,
      error: 'Failed to fetch Nifty 200 stocks',
      data: null
    }, { status: 500 });
  }
}

// Get all eligible stocks (for search functionality)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { searchQuery = '' } = body;
    
    console.log('Fetching all Nifty 200 stocks for search...');
    
    // Fetch all symbols in smaller batches to avoid timeout
    const batchSize = 25;
    const totalBatches = Math.ceil(NIFTY_200_SYMBOLS.length / batchSize);
    const allStocks: NiftyStock[] = [];
    
    for (let i = 0; i < totalBatches; i++) {
      const startIndex = i * batchSize;
      const endIndex = Math.min(startIndex + batchSize, NIFTY_200_SYMBOLS.length);
      const batchSymbols = NIFTY_200_SYMBOLS.slice(startIndex, endIndex);
      
      try {
        const yahooSymbols = batchSymbols.map(getYahooSymbol);

        // Use optimized method with cached names for batch processing
        const quotes = await yahooFinanceService.getMultipleQuotesWithCachedNames(yahooSymbols);
        
        const holdingSymbols = holdingsService.getHoldingSymbols();
        
        const batchStocks: NiftyStock[] = batchSymbols.map(nseSymbol => {
          const yahooSymbol = getYahooSymbol(nseSymbol);
          const quote = quotes.find(q => q.symbol === yahooSymbol);
          
          const price = quote?.price || 0;
          const inHoldings = holdingSymbols.includes(nseSymbol);
          const isEligible = holdingsService.isStockEligibleForTrading(nseSymbol, price);

          const stock: NiftyStock = {
            symbol: nseSymbol,
            name: quote?.name || nseSymbol,
            price,
            change: quote?.change || 0,
            changePercent: quote?.changePercent || 0,
            volume: quote?.volume || 0,
            marketCap: quote?.marketCap,
            high52Week: quote?.high52Week,
            low52Week: quote?.low52Week,
            high52WeekDate: quote?.high52WeekDate,
            low52WeekDate: quote?.low52WeekDate,
            isEligible,
            inHoldings
          };

          return addBOHEligibility(stock);
        });
        
        allStocks.push(...batchStocks);
        
        // Small delay to avoid overwhelming the API
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (batchError) {
        console.error(`Error in batch ${i}:`, batchError);
        // Continue with next batch even if one fails
      }
    }
    
    // Filter eligible stocks
    let eligibleStocks = allStocks.filter(stock => stock.isEligible);
    
    // Apply search filter if provided
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      eligibleStocks = eligibleStocks.filter(stock => 
        stock.symbol.toLowerCase().includes(query) ||
        stock.name.toLowerCase().includes(query)
      );
    }
    
    // Sort by market cap (descending)
    eligibleStocks.sort((a, b) => (b.marketCap || 0) - (a.marketCap || 0));
    
    return NextResponse.json({
      success: true,
      data: {
        stocks: eligibleStocks,
        totalCount: eligibleStocks.length,
        searchQuery
      }
    });

  } catch (error) {
    console.error('Error searching Nifty 200 stocks:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to search Nifty 200 stocks'
    }, { status: 500 });
  }
}
