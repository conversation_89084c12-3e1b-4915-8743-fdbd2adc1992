import { NextRequest, NextResponse } from 'next/server';

// In-memory storage for fund allocations (replace with database in production)
let fundAllocations: { [key: string]: any } = {};

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || 'default-user';
    const strategy = searchParams.get('strategy') || 'DARVAS_BOX';

    const key = `${userId}-${strategy}`;

    // Check if allocation exists in memory
    let fundAllocation = fundAllocations[key];

    if (!fundAllocation) {
      // Create default allocation if none exists
      fundAllocation = {
        id: `allocation-${key}-${Date.now()}`,
        userId,
        strategyName: strategy,
        totalAllocatedAmount: 50000,
        maxPerStock: 10000,
        maxPerTrade: 2000,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        stockAllocations: [
          {
            id: 'stock-reliance-1',
            symbol: 'RELIANCE',
            allocatedAmount: 8000,
            usedAmount: 4000,
            tradesCount: 2,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          },
          {
            id: 'stock-tcs-1',
            symbol: 'TCS',
            allocatedAmount: 6000,
            usedAmount: 2000,
            tradesCount: 1,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        ]
      };

      // Store in memory
      fundAllocations[key] = fundAllocation;
    }

    return NextResponse.json({
      success: true,
      data: fundAllocation
    });

  } catch (error) {
    console.error('Error fetching fund allocation:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch fund allocation'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      userId = 'default-user',
      strategyName = 'DARVAS_BOX',
      totalAllocatedAmount,
      maxPerStock,
      maxPerTrade
    } = body;

    const key = `${userId}-${strategyName}`;

    // Get existing allocation or create new one
    let fundAllocation = fundAllocations[key];

    if (!fundAllocation) {
      fundAllocation = {
        id: `allocation-${key}-${Date.now()}`,
        userId,
        strategyName,
        totalAllocatedAmount: 50000,
        maxPerStock: 10000,
        maxPerTrade: 2000,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        stockAllocations: []
      };
    }

    // Update allocation with new values
    fundAllocation.totalAllocatedAmount = parseFloat(totalAllocatedAmount) || fundAllocation.totalAllocatedAmount;
    fundAllocation.maxPerStock = parseFloat(maxPerStock) || fundAllocation.maxPerStock;
    fundAllocation.maxPerTrade = parseFloat(maxPerTrade) || fundAllocation.maxPerTrade;
    fundAllocation.updatedAt = new Date().toISOString();

    // Store updated allocation
    fundAllocations[key] = fundAllocation;

    return NextResponse.json({
      success: true,
      message: 'Fund allocation updated successfully',
      data: fundAllocation
    });

  } catch (error) {
    console.error('Error updating fund allocation:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to update fund allocation'
    }, { status: 500 });
  }
}

// Update stock-wise allocation
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      userId = 'default-user',
      strategyName = 'DARVAS_BOX',
      symbol,
      allocatedAmount
    } = body;

    const key = `${userId}-${strategyName}`;

    // Get fund allocation
    let fundAllocation = fundAllocations[key];

    if (!fundAllocation) {
      return NextResponse.json({
        success: false,
        error: 'Fund allocation not found'
      }, { status: 404 });
    }

    // Find existing stock allocation or create new one
    let stockAllocation = fundAllocation.stockAllocations.find((stock: any) => stock.symbol === symbol);

    if (stockAllocation) {
      // Update existing allocation
      stockAllocation.allocatedAmount = parseFloat(allocatedAmount);
      stockAllocation.updatedAt = new Date().toISOString();
    } else {
      // Create new stock allocation
      stockAllocation = {
        id: `stock-${symbol}-${Date.now()}`,
        symbol,
        allocatedAmount: parseFloat(allocatedAmount),
        usedAmount: 0,
        tradesCount: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      fundAllocation.stockAllocations.push(stockAllocation);
    }

    // Update fund allocation timestamp
    fundAllocation.updatedAt = new Date().toISOString();

    // Store updated allocation
    fundAllocations[key] = fundAllocation;

    return NextResponse.json({
      success: true,
      message: 'Stock allocation updated successfully',
      data: stockAllocation
    });

  } catch (error) {
    console.error('Error updating stock allocation:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to update stock allocation'
    }, { status: 500 });
  }
}
