{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/app/api/test-integration/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport async function GET(request: NextRequest) {\n  try {\n    console.log('🔗 Testing complete integration workflow...');\n\n    const requestUrl = new URL(request.url);\n    const baseUrl = `${requestUrl.protocol}//${requestUrl.host}`;\n\n    const results = {\n      timestamp: new Date().toISOString(),\n      tests: [] as any[]\n    };\n\n    // Test 1: Weekly High Signal Detection\n    console.log('📊 Test 1: Weekly High Signal Detection');\n    try {\n      const signalResponse = await fetch(`${baseUrl}/api/auto-gtt?action=signals`);\n      const signalData = await signalResponse.json();\n      \n      results.tests.push({\n        name: 'Weekly High Signal Detection',\n        status: signalData.success ? 'PASS' : 'FAIL',\n        data: {\n          signalsFound: signalData.data?.length || 0,\n          strongSignals: signalData.data?.filter((s: any) => s.signalStrength === 'STRONG').length || 0,\n          moderateSignals: signalData.data?.filter((s: any) => s.signalStrength === 'MODERATE').length || 0\n        },\n        error: signalData.success ? null : signalData.error\n      });\n    } catch (error) {\n      results.tests.push({\n        name: 'Weekly High Signal Detection',\n        status: 'ERROR',\n        error: error instanceof Error ? error.message : 'Unknown error'\n      });\n    }\n\n    // Test 2: Auto GTT Service Status\n    console.log('🤖 Test 2: Auto GTT Service Status');\n    try {\n      const statusResponse = await fetch(`${baseUrl}/api/auto-gtt?action=status`);\n      const statusData = await statusResponse.json();\n      \n      results.tests.push({\n        name: 'Auto GTT Service Status',\n        status: statusData.success ? 'PASS' : 'FAIL',\n        data: {\n          serviceInitialized: statusData.data?.service?.isInitialized || false,\n          detectorRunning: statusData.data?.detector?.isRunning || false,\n          marketOpen: statusData.data?.detector?.isMarketOpen || false,\n          autoOrdersCount: statusData.data?.service?.autoCreatedOrders || 0\n        },\n        error: statusData.success ? null : statusData.error\n      });\n    } catch (error) {\n      results.tests.push({\n        name: 'Auto GTT Service Status',\n        status: 'ERROR',\n        error: error instanceof Error ? error.message : 'Unknown error'\n      });\n    }\n\n    // Test 3: Manual GTT Order Creation\n    console.log('📝 Test 3: Manual GTT Order Creation');\n    try {\n      const gttResponse = await fetch(`${baseUrl}/api/gtt/create-signal-orders`, {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' }\n      });\n      const gttData = await gttResponse.json();\n      \n      results.tests.push({\n        name: 'Manual GTT Order Creation',\n        status: gttData.success ? 'PASS' : 'FAIL',\n        data: {\n          totalBOHStocks: gttData.data?.totalBOHStocks || 0,\n          validForGTT: gttData.data?.validForGTT || 0,\n          ordersCreated: gttData.data?.orders?.length || 0,\n          totalValue: gttData.data?.stats?.totalValue || 0\n        },\n        error: gttData.success ? null : gttData.error\n      });\n    } catch (error) {\n      results.tests.push({\n        name: 'Manual GTT Order Creation',\n        status: 'ERROR',\n        error: error instanceof Error ? error.message : 'Unknown error'\n      });\n    }\n\n    // Test 4: Auto GTT Orders Retrieval\n    console.log('📋 Test 4: Auto GTT Orders Retrieval');\n    try {\n      const ordersResponse = await fetch(`${baseUrl}/api/auto-gtt?action=orders`);\n      const ordersData = await ordersResponse.json();\n      \n      results.tests.push({\n        name: 'Auto GTT Orders Retrieval',\n        status: ordersData.success ? 'PASS' : 'FAIL',\n        data: {\n          totalOrders: ordersData.data?.length || 0,\n          pendingOrders: ordersData.data?.filter((o: any) => o.status === 'PENDING').length || 0,\n          autoCreatedOrders: ordersData.data?.filter((o: any) => o.autoCreated).length || 0,\n          signalOrders: ordersData.data?.filter((o: any) => o.source === 'SIGNAL').length || 0\n        },\n        error: ordersData.success ? null : ordersData.error\n      });\n    } catch (error) {\n      results.tests.push({\n        name: 'Auto GTT Orders Retrieval',\n        status: 'ERROR',\n        error: error instanceof Error ? error.message : 'Unknown error'\n      });\n    }\n\n    // Test 5: System Health Check\n    console.log('🏥 Test 5: System Health Check');\n    try {\n      const healthResponse = await fetch(`${baseUrl}/api/test-auto-gtt-system`);\n      const healthData = await healthResponse.json();\n      \n      results.tests.push({\n        name: 'System Health Check',\n        status: healthData.success && healthData.results?.overall?.status === 'SUCCESS' ? 'PASS' : 'FAIL',\n        data: {\n          overallStatus: healthData.results?.overall?.status || 'UNKNOWN',\n          readyForProduction: healthData.results?.overall?.readyForProduction || false,\n          healthScore: healthData.results?.step5_health_check?.score || '0/0',\n          signalsDetected: healthData.results?.step2_signal_detection?.signalsFound || 0\n        },\n        error: healthData.success ? null : healthData.error\n      });\n    } catch (error) {\n      results.tests.push({\n        name: 'System Health Check',\n        status: 'ERROR',\n        error: error instanceof Error ? error.message : 'Unknown error'\n      });\n    }\n\n    // Calculate overall results\n    const passedTests = results.tests.filter(t => t.status === 'PASS').length;\n    const totalTests = results.tests.length;\n    const overallStatus = passedTests === totalTests ? 'ALL_PASS' : \n                         passedTests >= totalTests * 0.8 ? 'MOSTLY_PASS' : 'FAIL';\n\n    const summary = {\n      overallStatus,\n      passedTests,\n      totalTests,\n      successRate: `${Math.round((passedTests / totalTests) * 100)}%`,\n      readyForProduction: overallStatus === 'ALL_PASS' || overallStatus === 'MOSTLY_PASS',\n      recommendations: [] as string[]\n    };\n\n    // Add recommendations based on failed tests\n    results.tests.forEach(test => {\n      if (test.status !== 'PASS') {\n        switch (test.name) {\n          case 'Weekly High Signal Detection':\n            summary.recommendations.push('Check Yahoo Finance API connectivity and BOH eligibility logic');\n            break;\n          case 'Auto GTT Service Status':\n            summary.recommendations.push('Restart the automatic GTT service and check configuration');\n            break;\n          case 'Manual GTT Order Creation':\n            summary.recommendations.push('Verify GTT order creation API and stock data availability');\n            break;\n          case 'Auto GTT Orders Retrieval':\n            summary.recommendations.push('Check order storage and retrieval mechanisms');\n            break;\n          case 'System Health Check':\n            summary.recommendations.push('Run detailed system diagnostics and check all components');\n            break;\n        }\n      }\n    });\n\n    console.log(`🎯 Integration Test Complete: ${summary.successRate} success rate`);\n\n    return NextResponse.json({\n      success: true,\n      message: 'Integration test completed',\n      summary,\n      results\n    });\n\n  } catch (error) {\n    console.error('❌ Integration test failed:', error);\n    return NextResponse.json(\n      { \n        success: false, \n        error: error instanceof Error ? error.message : 'Integration test failed',\n        message: 'Integration test failed'\n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,MAAM,aAAa,IAAI,IAAI,QAAQ,GAAG;QACtC,MAAM,UAAU,GAAG,WAAW,QAAQ,CAAC,EAAE,EAAE,WAAW,IAAI,EAAE;QAE5D,MAAM,UAAU;YACd,WAAW,IAAI,OAAO,WAAW;YACjC,OAAO,EAAE;QACX;QAEA,uCAAuC;QACvC,QAAQ,GAAG,CAAC;QACZ,IAAI;YACF,MAAM,iBAAiB,MAAM,MAAM,GAAG,QAAQ,4BAA4B,CAAC;YAC3E,MAAM,aAAa,MAAM,eAAe,IAAI;YAE5C,QAAQ,KAAK,CAAC,IAAI,CAAC;gBACjB,MAAM;gBACN,QAAQ,WAAW,OAAO,GAAG,SAAS;gBACtC,MAAM;oBACJ,cAAc,WAAW,IAAI,EAAE,UAAU;oBACzC,eAAe,WAAW,IAAI,EAAE,OAAO,CAAC,IAAW,EAAE,cAAc,KAAK,UAAU,UAAU;oBAC5F,iBAAiB,WAAW,IAAI,EAAE,OAAO,CAAC,IAAW,EAAE,cAAc,KAAK,YAAY,UAAU;gBAClG;gBACA,OAAO,WAAW,OAAO,GAAG,OAAO,WAAW,KAAK;YACrD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,IAAI,CAAC;gBACjB,MAAM;gBACN,QAAQ;gBACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;QAEA,kCAAkC;QAClC,QAAQ,GAAG,CAAC;QACZ,IAAI;YACF,MAAM,iBAAiB,MAAM,MAAM,GAAG,QAAQ,2BAA2B,CAAC;YAC1E,MAAM,aAAa,MAAM,eAAe,IAAI;YAE5C,QAAQ,KAAK,CAAC,IAAI,CAAC;gBACjB,MAAM;gBACN,QAAQ,WAAW,OAAO,GAAG,SAAS;gBACtC,MAAM;oBACJ,oBAAoB,WAAW,IAAI,EAAE,SAAS,iBAAiB;oBAC/D,iBAAiB,WAAW,IAAI,EAAE,UAAU,aAAa;oBACzD,YAAY,WAAW,IAAI,EAAE,UAAU,gBAAgB;oBACvD,iBAAiB,WAAW,IAAI,EAAE,SAAS,qBAAqB;gBAClE;gBACA,OAAO,WAAW,OAAO,GAAG,OAAO,WAAW,KAAK;YACrD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,IAAI,CAAC;gBACjB,MAAM;gBACN,QAAQ;gBACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;QAEA,oCAAoC;QACpC,QAAQ,GAAG,CAAC;QACZ,IAAI;YACF,MAAM,cAAc,MAAM,MAAM,GAAG,QAAQ,6BAA6B,CAAC,EAAE;gBACzE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;YACA,MAAM,UAAU,MAAM,YAAY,IAAI;YAEtC,QAAQ,KAAK,CAAC,IAAI,CAAC;gBACjB,MAAM;gBACN,QAAQ,QAAQ,OAAO,GAAG,SAAS;gBACnC,MAAM;oBACJ,gBAAgB,QAAQ,IAAI,EAAE,kBAAkB;oBAChD,aAAa,QAAQ,IAAI,EAAE,eAAe;oBAC1C,eAAe,QAAQ,IAAI,EAAE,QAAQ,UAAU;oBAC/C,YAAY,QAAQ,IAAI,EAAE,OAAO,cAAc;gBACjD;gBACA,OAAO,QAAQ,OAAO,GAAG,OAAO,QAAQ,KAAK;YAC/C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,IAAI,CAAC;gBACjB,MAAM;gBACN,QAAQ;gBACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;QAEA,oCAAoC;QACpC,QAAQ,GAAG,CAAC;QACZ,IAAI;YACF,MAAM,iBAAiB,MAAM,MAAM,GAAG,QAAQ,2BAA2B,CAAC;YAC1E,MAAM,aAAa,MAAM,eAAe,IAAI;YAE5C,QAAQ,KAAK,CAAC,IAAI,CAAC;gBACjB,MAAM;gBACN,QAAQ,WAAW,OAAO,GAAG,SAAS;gBACtC,MAAM;oBACJ,aAAa,WAAW,IAAI,EAAE,UAAU;oBACxC,eAAe,WAAW,IAAI,EAAE,OAAO,CAAC,IAAW,EAAE,MAAM,KAAK,WAAW,UAAU;oBACrF,mBAAmB,WAAW,IAAI,EAAE,OAAO,CAAC,IAAW,EAAE,WAAW,EAAE,UAAU;oBAChF,cAAc,WAAW,IAAI,EAAE,OAAO,CAAC,IAAW,EAAE,MAAM,KAAK,UAAU,UAAU;gBACrF;gBACA,OAAO,WAAW,OAAO,GAAG,OAAO,WAAW,KAAK;YACrD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,IAAI,CAAC;gBACjB,MAAM;gBACN,QAAQ;gBACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;QAEA,8BAA8B;QAC9B,QAAQ,GAAG,CAAC;QACZ,IAAI;YACF,MAAM,iBAAiB,MAAM,MAAM,GAAG,QAAQ,yBAAyB,CAAC;YACxE,MAAM,aAAa,MAAM,eAAe,IAAI;YAE5C,QAAQ,KAAK,CAAC,IAAI,CAAC;gBACjB,MAAM;gBACN,QAAQ,WAAW,OAAO,IAAI,WAAW,OAAO,EAAE,SAAS,WAAW,YAAY,SAAS;gBAC3F,MAAM;oBACJ,eAAe,WAAW,OAAO,EAAE,SAAS,UAAU;oBACtD,oBAAoB,WAAW,OAAO,EAAE,SAAS,sBAAsB;oBACvE,aAAa,WAAW,OAAO,EAAE,oBAAoB,SAAS;oBAC9D,iBAAiB,WAAW,OAAO,EAAE,wBAAwB,gBAAgB;gBAC/E;gBACA,OAAO,WAAW,OAAO,GAAG,OAAO,WAAW,KAAK;YACrD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,IAAI,CAAC;gBACjB,MAAM;gBACN,QAAQ;gBACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;QAEA,4BAA4B;QAC5B,MAAM,cAAc,QAAQ,KAAK,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;QACzE,MAAM,aAAa,QAAQ,KAAK,CAAC,MAAM;QACvC,MAAM,gBAAgB,gBAAgB,aAAa,aAC9B,eAAe,aAAa,MAAM,gBAAgB;QAEvE,MAAM,UAAU;YACd;YACA;YACA;YACA,aAAa,GAAG,KAAK,KAAK,CAAC,AAAC,cAAc,aAAc,KAAK,CAAC,CAAC;YAC/D,oBAAoB,kBAAkB,cAAc,kBAAkB;YACtE,iBAAiB,EAAE;QACrB;QAEA,4CAA4C;QAC5C,QAAQ,KAAK,CAAC,OAAO,CAAC,CAAA;YACpB,IAAI,KAAK,MAAM,KAAK,QAAQ;gBAC1B,OAAQ,KAAK,IAAI;oBACf,KAAK;wBACH,QAAQ,eAAe,CAAC,IAAI,CAAC;wBAC7B;oBACF,KAAK;wBACH,QAAQ,eAAe,CAAC,IAAI,CAAC;wBAC7B;oBACF,KAAK;wBACH,QAAQ,eAAe,CAAC,IAAI,CAAC;wBAC7B;oBACF,KAAK;wBACH,QAAQ,eAAe,CAAC,IAAI,CAAC;wBAC7B;oBACF,KAAK;wBACH,QAAQ,eAAe,CAAC,IAAI,CAAC;wBAC7B;gBACJ;YACF;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,QAAQ,WAAW,CAAC,aAAa,CAAC;QAE/E,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT;YACA;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,SAAS;QACX,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}