{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/app/api/broker/balance/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const userId = searchParams.get('userId') || 'default-user';\n\n    // Try to fetch from SmartAPI backend\n    try {\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), 5000);\n\n      const response = await fetch('http://localhost:8000/api/smartapi/profile', {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        signal: controller.signal\n      });\n\n      clearTimeout(timeoutId);\n\n      if (response.ok) {\n        const profileData = await response.json();\n\n        if (profileData.status === 'success' && profileData.data) {\n          // Extract balance data from SmartAPI profile\n          const balanceData = {\n            id: `balance-${userId}-${Date.now()}`,\n            availableCash: profileData.data.availablecash || 0,\n            marginUsed: profileData.data.marginused || 0,\n            marginAvailable: profileData.data.marginAvailable || 0,\n            totalBalance: (profileData.data.availablecash || 0) + (profileData.data.marginused || 0),\n            lastSyncAt: new Date().toISOString()\n          };\n\n          return NextResponse.json({\n            success: true,\n            data: balanceData,\n            source: 'smartapi'\n          });\n        }\n      }\n    } catch (backendError) {\n      console.log('SmartAPI backend not available:', backendError);\n    }\n\n    // If SmartAPI is not available, try to get data from local storage or return empty state\n    const fallbackBalance = {\n      id: `balance-${userId}-fallback`,\n      availableCash: 0,\n      marginUsed: 0,\n      marginAvailable: 0,\n      totalBalance: 0,\n      lastSyncAt: new Date().toISOString()\n    };\n\n    return NextResponse.json({\n      success: false,\n      error: 'Broker not connected. Please connect your broker account.',\n      data: fallbackBalance,\n      source: 'fallback'\n    });\n\n  } catch (error) {\n    console.error('Error fetching broker balance:', error);\n    return NextResponse.json({\n      success: false,\n      error: 'Failed to fetch broker balance',\n      data: null\n    }, { status: 500 });\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const userId = searchParams.get('userId') || 'default-user';\n\n    // Force refresh balance from broker by calling GET endpoint\n    const baseUrl = request.nextUrl.origin;\n    const response = await fetch(`${baseUrl}/api/broker/balance?userId=${userId}`, {\n      method: 'GET'\n    });\n\n    const data = await response.json();\n\n    return NextResponse.json({\n      success: data.success,\n      message: data.success ? 'Balance refreshed successfully' : 'Failed to refresh balance',\n      data: data.data,\n      source: data.source\n    });\n\n  } catch (error) {\n    console.error('Error refreshing broker balance:', error);\n    return NextResponse.json({\n      success: false,\n      error: 'Failed to refresh broker balance'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAE7C,qCAAqC;QACrC,IAAI;YACF,MAAM,aAAa,IAAI;YACvB,MAAM,YAAY,WAAW,IAAM,WAAW,KAAK,IAAI;YAEvD,MAAM,WAAW,MAAM,MAAM,8CAA8C;gBACzE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,QAAQ,WAAW,MAAM;YAC3B;YAEA,aAAa;YAEb,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,cAAc,MAAM,SAAS,IAAI;gBAEvC,IAAI,YAAY,MAAM,KAAK,aAAa,YAAY,IAAI,EAAE;oBACxD,6CAA6C;oBAC7C,MAAM,cAAc;wBAClB,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE,KAAK,GAAG,IAAI;wBACrC,eAAe,YAAY,IAAI,CAAC,aAAa,IAAI;wBACjD,YAAY,YAAY,IAAI,CAAC,UAAU,IAAI;wBAC3C,iBAAiB,YAAY,IAAI,CAAC,eAAe,IAAI;wBACrD,cAAc,CAAC,YAAY,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,UAAU,IAAI,CAAC;wBACvF,YAAY,IAAI,OAAO,WAAW;oBACpC;oBAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;wBACvB,SAAS;wBACT,MAAM;wBACN,QAAQ;oBACV;gBACF;YACF;QACF,EAAE,OAAO,cAAc;YACrB,QAAQ,GAAG,CAAC,mCAAmC;QACjD;QAEA,yFAAyF;QACzF,MAAM,kBAAkB;YACtB,IAAI,CAAC,QAAQ,EAAE,OAAO,SAAS,CAAC;YAChC,eAAe;YACf,YAAY;YACZ,iBAAiB;YACjB,cAAc;YACd,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;YACP,MAAM;YACN,QAAQ;QACV;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;YACP,MAAM;QACR,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAE7C,4DAA4D;QAC5D,MAAM,UAAU,QAAQ,OAAO,CAAC,MAAM;QACtC,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,2BAA2B,EAAE,QAAQ,EAAE;YAC7E,QAAQ;QACV;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS,KAAK,OAAO;YACrB,SAAS,KAAK,OAAO,GAAG,mCAAmC;YAC3D,MAAM,KAAK,IAAI;YACf,QAAQ,KAAK,MAAM;QACrB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}