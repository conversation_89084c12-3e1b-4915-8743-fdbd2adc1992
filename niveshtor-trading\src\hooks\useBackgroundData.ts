// React hook for seamless background data integration
import { useState, useEffect, useCallback, useRef } from 'react';
import { backgroundDataService, BackgroundUpdateListener, RealTimeUpdateListener } from '@/lib/background-data-service';
import { stockNamesService } from '@/lib/stock-names-service';
import { NiftyStock } from '@/lib/nifty-stocks';

interface UseBackgroundDataOptions {
  enablePriceUpdates?: boolean;
  enableNameUpdates?: boolean;
  enableRealTimeUpdates?: boolean;
  onError?: (error: Error) => void;
  onRealTimeUpdate?: (quotes: any[]) => void;
}

interface UseBackgroundDataReturn {
  // Status
  isNamesReady: boolean;
  isUpdating: boolean;
  lastUpdate: Date | null;
  error: Error | null;
  
  // Data
  stockNames: Map<string, string>;
  
  // Methods
  getStockName: (symbol: string) => string;
  forceUpdate: () => Promise<void>;
  clearError: () => void;
}

export function useBackgroundData(options: UseBackgroundDataOptions = {}): UseBackgroundDataReturn {
  const {
    enablePriceUpdates = true,
    enableNameUpdates = true,
    enableRealTimeUpdates = false,
    onError,
    onRealTimeUpdate
  } = options;

  // State
  const [isNamesReady, setIsNamesReady] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [stockNames, setStockNames] = useState<Map<string, string>>(new Map());
  
  // Refs
  const listenerRef = useRef<BackgroundUpdateListener | null>(null);
  const realTimeListenerRef = useRef<RealTimeUpdateListener | null>(null);
  const mountedRef = useRef(true);

  // Create listener
  const createListener = useCallback((): BackgroundUpdateListener => ({
    onPriceUpdate: (data: any[]) => {
      if (!mountedRef.current || !enablePriceUpdates) return;
      
      console.log(`🔄 Received background price update: ${data.length} stocks`);
      setLastUpdate(new Date());
      setIsUpdating(false);
      
      // Trigger custom event for other components to listen
      window.dispatchEvent(new CustomEvent('backgroundPriceUpdate', {
        detail: { data, timestamp: new Date() }
      }));
    },
    
    onNamesUpdate: (namesMap: Map<string, string>) => {
      if (!mountedRef.current || !enableNameUpdates) return;
      
      console.log(`📝 Received background names update: ${namesMap.size} names`);
      setStockNames(new Map(namesMap));
      setIsNamesReady(true);
      setError(null);
      
      // Trigger custom event for other components to listen
      window.dispatchEvent(new CustomEvent('backgroundNamesUpdate', {
        detail: { namesMap, timestamp: new Date() }
      }));
    },
    
    onError: (err: Error) => {
      if (!mountedRef.current) return;
      
      console.error('❌ Background data error:', err);
      setError(err);
      setIsUpdating(false);
      
      if (onError) {
        onError(err);
      }
    }
  }), [enablePriceUpdates, enableNameUpdates, onError]);

  // Create real-time listener
  const createRealTimeListener = useCallback((): RealTimeUpdateListener => ({
    onRealTimeUpdate: (quotes: any[]) => {
      if (!mountedRef.current || !enableRealTimeUpdates) return;

      console.log(`⚡ Received real-time update: ${quotes.length} quotes`);
      setLastUpdate(new Date());

      // Call custom callback if provided
      if (onRealTimeUpdate) {
        onRealTimeUpdate(quotes);
      }

      // Trigger custom event for components to listen
      window.dispatchEvent(new CustomEvent('realTimePriceUpdate', {
        detail: { quotes, timestamp: new Date() }
      }));
    },

    onError: (err: Error) => {
      if (!mountedRef.current) return;

      console.error('❌ Real-time update error:', err);
      setError(err);

      if (onError) {
        onError(err);
      }
    }
  }), [enableRealTimeUpdates, onRealTimeUpdate, onError]);

  // Initialize and setup listener
  useEffect(() => {
    mountedRef.current = true;
    
    const initializeData = async () => {
      try {
        // Check if names are already ready
        const namesReady = backgroundDataService.areNamesReady();
        setIsNamesReady(namesReady);

        if (namesReady) {
          // Load existing cached names
          const cachedNames = stockNamesService.getCachedStockNames();
          setStockNames(cachedNames);
        }

        // Create and register listener
        const listener = createListener();
        listenerRef.current = listener;
        backgroundDataService.addListener(listener);

        // Create and register real-time listener if enabled
        if (enableRealTimeUpdates) {
          const realTimeListener = createRealTimeListener();
          realTimeListenerRef.current = realTimeListener;
          backgroundDataService.addRealTimeListener(realTimeListener);
        }

        // Initialize background service if not already done
        await backgroundDataService.initialize();

      } catch (err) {
        console.error('❌ Failed to initialize background data hook:', err);
        setError(err as Error);
        // Set names ready to true to allow pages to work even if background service fails
        setIsNamesReady(true);
      }
    };
    
    initializeData();
    
    // Cleanup
    return () => {
      mountedRef.current = false;
      if (listenerRef.current) {
        backgroundDataService.removeListener(listenerRef.current);
      }
      if (realTimeListenerRef.current) {
        backgroundDataService.removeRealTimeListener(realTimeListenerRef.current);
      }
    };
  }, [createListener, createRealTimeListener, enableRealTimeUpdates]);

  // Get stock name with fallback
  const getStockName = useCallback((symbol: string): string => {
    // First try from local state
    const name = stockNames.get(symbol);
    if (name) return name;

    // Fallback to sync method
    try {
      return stockNamesService.getStockNameSync(symbol);
    } catch (error) {
      // Ultimate fallback - just return symbol without .NS
      return symbol.replace('.NS', '');
    }
  }, [stockNames]);

  // Force update
  const forceUpdate = useCallback(async (): Promise<void> => {
    setIsUpdating(true);
    setError(null);
    
    try {
      await backgroundDataService.forceUpdate();
    } catch (err) {
      setError(err as Error);
      throw err;
    }
  }, []);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    // Status
    isNamesReady,
    isUpdating,
    lastUpdate,
    error,
    
    // Data
    stockNames,
    
    // Methods
    getStockName,
    forceUpdate,
    clearError
  };
}

// Hook for listening to background price updates
export function useBackgroundPriceUpdates(callback: (data: any[]) => void) {
  useEffect(() => {
    const handlePriceUpdate = (event: CustomEvent) => {
      callback(event.detail.data);
    };

    window.addEventListener('backgroundPriceUpdate', handlePriceUpdate as EventListener);
    
    return () => {
      window.removeEventListener('backgroundPriceUpdate', handlePriceUpdate as EventListener);
    };
  }, [callback]);
}

// Hook for listening to background name updates
export function useBackgroundNameUpdates(callback: (namesMap: Map<string, string>) => void) {
  useEffect(() => {
    const handleNameUpdate = (event: CustomEvent) => {
      callback(event.detail.namesMap);
    };

    window.addEventListener('backgroundNamesUpdate', handleNameUpdate as EventListener);
    
    return () => {
      window.removeEventListener('backgroundNamesUpdate', handleNameUpdate as EventListener);
    };
  }, [callback]);
}
