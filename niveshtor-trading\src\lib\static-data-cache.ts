// Static Data Cache Service
// Terminal-style data separation: Static data loads once and persists, dynamic data updates continuously

import { stockNamesService } from './stock-names-service';
import { NIFTY_200_SYMBOLS } from './nifty-stocks';
import { holdingsService } from './holdings-service';
import { yahooFinanceService } from './yahoo-finance';

export interface StaticStockData {
  symbol: string;
  name: string;
  exchange: string;
  sector?: string;
  industry?: string;
  isBOHEligible: boolean;
  isInHoldings: boolean;
}

export interface StaticGTTOrderData {
  id: string;
  symbol: string;
  name: string;
  orderType: 'BUY' | 'SELL';
  source: 'MANUAL' | 'SIGNAL' | 'HOLDING';
  strategy?: string;
  createdAt: Date;
  autoCreated: boolean;
}

export interface StaticDataCache {
  // Stock Universe Static Data
  stockUniverse: StaticStockData[];
  
  // BOH Eligible Static Data (subset of stock universe)
  bohEligibleStocks: StaticStockData[];
  
  // Holdings Static Data
  holdingsStatic: {
    symbol: string;
    name: string;
    strategy: string;
    transactions: Array<{
      id: string;
      type: 'BUY' | 'SELL';
      date: Date;
      strategy: string;
    }>;
  }[];
  
  // GTT Orders Static Data
  gttOrdersStatic: StaticGTTOrderData[];
  
  // Cache metadata
  loadedAt: Date;
  version: string;
  sessionId: string;
}

export interface DynamicDataCache {
  // Stock Prices (updates every 30s)
  stockPrices: Map<string, {
    currentPrice: number;
    dayChange: number;
    dayChangePercent: number;
    lastUpdated: Date;
  }>;
  
  // Holdings Dynamic Data (updates every 30s)
  holdingsDynamic: Map<string, {
    currentPrice: number;
    marketValue: number;
    pnl: number;
    pnlPercent: number;
    lastUpdated: Date;
  }>;
  
  // Weekly High Signals Dynamic Data (updates every 5min)
  weeklyHighSignalsDynamic: Map<string, {
    signalStrength: 'STRONG' | 'MODERATE';
    percentDifference: number;
    lastWeekHigh: number;
    suggestedBuyPrice: number;
    lastUpdated: Date;
  }>;
  
  // GTT Orders Dynamic Data (updates every 30s)
  gttOrdersDynamic: Map<string, {
    status: 'PENDING' | 'TRIGGERED' | 'CANCELLED' | 'EXPIRED';
    triggerPrice: number;
    quantity: number;
    currentPrice: number;
    lastUpdated: Date;
  }>;
}

class StaticDataCacheService {
  private staticCache: StaticDataCache | null = null;
  private dynamicCache: DynamicDataCache = {
    stockPrices: new Map(),
    holdingsDynamic: new Map(),
    weeklyHighSignalsDynamic: new Map(),
    gttOrdersDynamic: new Map()
  };
  
  private sessionId: string;
  private isInitialized = false;
  private isLoading = false;
  
  // Cache TTL settings
  private readonly STATIC_CACHE_TTL = 24 * 60 * 60 * 1000; // 24 hours
  private readonly SESSION_STORAGE_KEY = 'niveshtor_static_cache';
  
  constructor() {
    this.sessionId = this.generateSessionId();
    console.log(`🏗️ Static Data Cache Service initialized with session: ${this.sessionId}`);
  }
  
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  // Initialize static data cache (called once on app start)
  async initializeStaticCache(): Promise<void> {
    if (this.isInitialized || this.isLoading) {
      console.log('📋 Static cache already initialized or loading...');
      return;
    }
    
    this.isLoading = true;
    console.log('🚀 Initializing static data cache...');
    
    try {
      // Try to load from session storage first
      const cachedData = this.loadFromSessionStorage();
      if (cachedData && this.isCacheValid(cachedData)) {
        console.log('✅ Loaded valid static cache from session storage');
        this.staticCache = cachedData;
        this.isInitialized = true;
        this.isLoading = false;
        return;
      }
      
      // Build fresh static cache
      console.log('🔄 Building fresh static data cache...');
      await this.buildStaticCache();
      
      // Save to session storage
      this.saveToSessionStorage();
      
      this.isInitialized = true;
      console.log('✅ Static data cache initialized successfully');
      
    } catch (error) {
      console.error('❌ Failed to initialize static data cache:', error);
      throw error;
    } finally {
      this.isLoading = false;
    }
  }
  
  private async buildStaticCache(): Promise<void> {
    console.log('🏗️ Building static data cache...');
    
    // Load stock names for all Nifty 200 symbols
    console.log('📝 Loading stock names...');
    await stockNamesService.preloadStockNames(NIFTY_200_SYMBOLS);
    
    // Build stock universe static data
    const stockUniverse: StaticStockData[] = NIFTY_200_SYMBOLS.map(symbol => {
      const name = stockNamesService.getStockName(symbol) || symbol;
      const isInHoldings = holdingsService.isStockInHoldings(symbol);
      
      return {
        symbol,
        name,
        exchange: 'NSE',
        isBOHEligible: true, // Will be refined later
        isInHoldings
      };
    });
    
    // Filter BOH eligible stocks (static criteria)
    const bohEligibleStocks = stockUniverse.filter(stock => 
      stock.isBOHEligible && !stock.isInHoldings
    );
    
    // Build holdings static data
    const holdings = holdingsService.getAllHoldings();
    const holdingsStatic = holdings.map(holding => ({
      symbol: holding.symbol,
      name: stockNamesService.getStockName(holding.symbol) || holding.symbol,
      strategy: holding.strategy,
      transactions: [] // Will be populated from transaction history
    }));
    
    // Build GTT orders static data (structure only)
    const gttOrdersStatic: StaticGTTOrderData[] = []; // Will be populated from service
    
    this.staticCache = {
      stockUniverse,
      bohEligibleStocks,
      holdingsStatic,
      gttOrdersStatic,
      loadedAt: new Date(),
      version: '1.0.0',
      sessionId: this.sessionId
    };
    
    console.log(`✅ Built static cache: ${stockUniverse.length} stocks, ${bohEligibleStocks.length} BOH eligible`);
  }
  
  private loadFromSessionStorage(): StaticDataCache | null {
    try {
      const cached = sessionStorage.getItem(this.SESSION_STORAGE_KEY);
      if (cached) {
        const data = JSON.parse(cached);
        // Convert date strings back to Date objects
        data.loadedAt = new Date(data.loadedAt);
        data.holdingsStatic.forEach((holding: any) => {
          holding.transactions.forEach((txn: any) => {
            txn.date = new Date(txn.date);
          });
        });
        data.gttOrdersStatic.forEach((order: any) => {
          order.createdAt = new Date(order.createdAt);
        });
        return data;
      }
    } catch (error) {
      console.warn('⚠️ Failed to load static cache from session storage:', error);
    }
    return null;
  }
  
  private saveToSessionStorage(): void {
    try {
      if (this.staticCache) {
        sessionStorage.setItem(this.SESSION_STORAGE_KEY, JSON.stringify(this.staticCache));
        console.log('💾 Saved static cache to session storage');
      }
    } catch (error) {
      console.warn('⚠️ Failed to save static cache to session storage:', error);
    }
  }
  
  private isCacheValid(cache: StaticDataCache): boolean {
    const now = new Date();
    const cacheAge = now.getTime() - cache.loadedAt.getTime();
    const isValid = cacheAge < this.STATIC_CACHE_TTL && cache.sessionId === this.sessionId;
    
    console.log(`🔍 Cache validation: Age=${Math.round(cacheAge/1000/60)}min, Valid=${isValid}`);
    return isValid;
  }
  
  // Getters for static data (instant access)
  getStockUniverse(): StaticStockData[] {
    return this.staticCache?.stockUniverse || [];
  }
  
  getBOHEligibleStocks(): StaticStockData[] {
    return this.staticCache?.bohEligibleStocks || [];
  }
  
  getHoldingsStatic(): StaticDataCache['holdingsStatic'] {
    return this.staticCache?.holdingsStatic || [];
  }
  
  getGTTOrdersStatic(): StaticGTTOrderData[] {
    return this.staticCache?.gttOrdersStatic || [];
  }
  
  getStockName(symbol: string): string {
    const stock = this.staticCache?.stockUniverse.find(s => s.symbol === symbol);
    return stock?.name || symbol;
  }
  
  // Dynamic data methods
  updateStockPrice(symbol: string, priceData: DynamicDataCache['stockPrices'] extends Map<string, infer T> ? T : never): void {
    this.dynamicCache.stockPrices.set(symbol, priceData);
  }
  
  getStockPrice(symbol: string) {
    return this.dynamicCache.stockPrices.get(symbol);
  }
  
  getAllStockPrices(): Map<string, any> {
    return this.dynamicCache.stockPrices;
  }
  
  // Status methods
  isStaticCacheReady(): boolean {
    return this.isInitialized && this.staticCache !== null;
  }
  
  getStaticCacheStatus() {
    return {
      isInitialized: this.isInitialized,
      isLoading: this.isLoading,
      stockCount: this.staticCache?.stockUniverse.length || 0,
      bohEligibleCount: this.staticCache?.bohEligibleStocks.length || 0,
      loadedAt: this.staticCache?.loadedAt || null,
      sessionId: this.sessionId
    };
  }
  
  // Clear cache (for testing or forced refresh)
  clearCache(): void {
    this.staticCache = null;
    this.dynamicCache = {
      stockPrices: new Map(),
      holdingsDynamic: new Map(),
      weeklyHighSignalsDynamic: new Map(),
      gttOrdersDynamic: new Map()
    };
    this.isInitialized = false;
    sessionStorage.removeItem(this.SESSION_STORAGE_KEY);
    console.log('🗑️ Static data cache cleared');
  }
}

export const staticDataCache = new StaticDataCacheService();
