'use client';

import { useState } from 'react';
import { 
  Briefcase, 
  TrendingUp, 
  TrendingDown, 
  DollarSign,
  <PERSON><PERSON>hart,
  MoreVertical,
  Eye,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';
import { formatCurrency, formatPercentage, getChangeColor } from '@/lib/utils';

interface Holding {
  symbol: string;
  name: string;
  quantity: number;
  avgPrice: number;
  currentPrice: number;
  pnl: number;
  pnlPercent: number;
  marketValue: number;
  dayChange: number;
  dayChangePercent: number;
  sector: string;
}

export default function CurrentHoldingPage() {
  const [holdings, setHoldings] = useState<Holding[]>([
    {
      symbol: 'RELIANCE',
      name: 'Reliance Industries Ltd',
      quantity: 50,
      avgPrice: 2200.00,
      currentPrice: 2456.75,
      pnl: 12837.50,
      pnlPercent: 11.67,
      marketValue: 122837.50,
      dayChange: 45.20,
      dayChangePercent: 1.87,
      sector: 'Energy'
    },
    {
      symbol: 'TCS',
      name: 'Tata Consultancy Services',
      quantity: 25,
      avgPrice: 3400.00,
      currentPrice: 3234.50,
      pnl: -4137.50,
      pnlPercent: -4.87,
      marketValue: 80862.50,
      dayChange: -23.45,
      dayChangePercent: -0.72,
      sector: 'IT'
    },
    {
      symbol: 'HDFC',
      name: 'HDFC Bank Limited',
      quantity: 40,
      avgPrice: 1600.00,
      currentPrice: 1678.90,
      pnl: 3156.00,
      pnlPercent: 4.93,
      marketValue: 67156.00,
      dayChange: 28.50,
      dayChangePercent: 1.73,
      sector: 'Banking'
    },
    {
      symbol: 'INFY',
      name: 'Infosys Limited',
      quantity: 60,
      avgPrice: 1500.00,
      currentPrice: 1456.80,
      pnl: -2592.00,
      pnlPercent: -2.88,
      marketValue: 87408.00,
      dayChange: 12.30,
      dayChangePercent: 0.85,
      sector: 'IT'
    }
  ]);

  const totalInvestment = holdings.reduce((sum, holding) => sum + (holding.avgPrice * holding.quantity), 0);
  const totalMarketValue = holdings.reduce((sum, holding) => sum + holding.marketValue, 0);
  const totalPnL = totalMarketValue - totalInvestment;
  const totalPnLPercent = (totalPnL / totalInvestment) * 100;
  const totalDayChange = holdings.reduce((sum, holding) => sum + (holding.dayChange * holding.quantity), 0);

  const HoldingRow = ({ holding }: { holding: Holding }) => (
    <div className="flex items-center justify-between p-4 hover:bg-gray-50 border-b border-gray-100">
      <div className="flex-1">
        <div className="flex items-center space-x-3">
          <div>
            <h4 className="font-medium text-gray-900">{holding.symbol}</h4>
            <p className="text-sm text-gray-600 truncate max-w-xs">{holding.name}</p>
            <p className="text-xs text-gray-500">{holding.sector}</p>
          </div>
        </div>
      </div>
      
      <div className="flex items-center space-x-6">
        <div className="text-right">
          <p className="text-sm text-gray-600">Qty: {holding.quantity}</p>
          <p className="text-sm text-gray-600">Avg: {formatCurrency(holding.avgPrice)}</p>
        </div>
        
        <div className="text-right">
          <p className="font-medium text-gray-900">{formatCurrency(holding.currentPrice)}</p>
          <p className={`text-sm ${getChangeColor(holding.dayChange)}`}>
            {holding.dayChange >= 0 ? '+' : ''}{holding.dayChange.toFixed(2)} ({formatPercentage(holding.dayChangePercent)})
          </p>
        </div>
        
        <div className="text-right">
          <p className="font-medium text-gray-900">{formatCurrency(holding.marketValue)}</p>
          <p className={`text-sm font-medium ${getChangeColor(holding.pnl)}`}>
            {holding.pnl >= 0 ? '+' : ''}{formatCurrency(Math.abs(holding.pnl))} ({formatPercentage(holding.pnlPercent)})
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
            <Eye className="h-4 w-4" />
          </button>
          <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
            <MoreVertical className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );

  const sectorAllocation = holdings.reduce((acc, holding) => {
    const sector = holding.sector;
    if (!acc[sector]) {
      acc[sector] = { value: 0, percentage: 0 };
    }
    acc[sector].value += holding.marketValue;
    return acc;
  }, {} as Record<string, { value: number; percentage: number }>);

  // Calculate percentages
  Object.keys(sectorAllocation).forEach(sector => {
    sectorAllocation[sector].percentage = (sectorAllocation[sector].value / totalMarketValue) * 100;
  });

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Current Holdings</h1>
          <p className="text-gray-600 mt-1">Active positions and P&L tracking</p>
        </div>
        <div className="flex space-x-3">
          <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            Export Report
          </button>
          <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
            Refresh Prices
          </button>
        </div>
      </div>

      {/* Portfolio Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Investment</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{formatCurrency(totalInvestment)}</p>
            </div>
            <DollarSign className="h-8 w-8 text-blue-600" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Current Value</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{formatCurrency(totalMarketValue)}</p>
            </div>
            <Briefcase className="h-8 w-8 text-gray-600" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total P&L</p>
              <p className={`text-2xl font-bold mt-1 ${getChangeColor(totalPnL)}`}>
                {totalPnL >= 0 ? '+' : ''}{formatCurrency(Math.abs(totalPnL))}
              </p>
              <p className={`text-sm ${getChangeColor(totalPnL)}`}>
                {formatPercentage(totalPnLPercent)}
              </p>
            </div>
            {totalPnL >= 0 ? (
              <TrendingUp className="h-8 w-8 text-green-600" />
            ) : (
              <TrendingDown className="h-8 w-8 text-red-600" />
            )}
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Day&apos;s P&L</p>
              <p className={`text-2xl font-bold mt-1 ${getChangeColor(totalDayChange)}`}>
                {totalDayChange >= 0 ? '+' : ''}{formatCurrency(Math.abs(totalDayChange))}
              </p>
            </div>
            {totalDayChange >= 0 ? (
              <ArrowUpRight className="h-8 w-8 text-green-600" />
            ) : (
              <ArrowDownRight className="h-8 w-8 text-red-600" />
            )}
          </div>
        </div>
      </div>

      {/* Holdings and Sector Allocation */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Holdings List */}
        <div className="lg:col-span-2 bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Holdings ({holdings.length})</h3>
          </div>
          
          <div>
            {holdings.map((holding) => (
              <HoldingRow key={holding.symbol} holding={holding} />
            ))}
          </div>
        </div>

        {/* Sector Allocation */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Sector Allocation</h3>
            <PieChart className="h-5 w-5 text-gray-400" />
          </div>
          
          <div className="space-y-4">
            {Object.entries(sectorAllocation).map(([sector, data]) => (
              <div key={sector} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${
                    sector === 'IT' ? 'bg-blue-500' :
                    sector === 'Banking' ? 'bg-green-500' :
                    sector === 'Energy' ? 'bg-orange-500' :
                    'bg-gray-500'
                  }`}></div>
                  <span className="text-sm font-medium text-gray-900">{sector}</span>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">{data.percentage.toFixed(1)}%</p>
                  <p className="text-xs text-gray-600">{formatCurrency(data.value)}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">Performance Metrics</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600 mb-2">
              {holdings.filter(h => h.pnl > 0).length}
            </div>
            <p className="text-sm text-gray-600">Profitable Positions</p>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600 mb-2">
              {holdings.filter(h => h.pnl < 0).length}
            </div>
            <p className="text-sm text-gray-600">Loss Making Positions</p>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900 mb-2">
              {((holdings.filter(h => h.pnl > 0).length / holdings.length) * 100).toFixed(1)}%
            </div>
            <p className="text-sm text-gray-600">Win Rate</p>
          </div>
        </div>
      </div>
    </div>
  );
}
