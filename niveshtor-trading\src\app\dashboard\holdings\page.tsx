'use client';

import { useState, useEffect } from 'react';
import {
  Briefcase,
  TrendingUp,
  TrendingDown,
  DollarSign,
  PieChart,
  ChevronDown,
  ChevronRight,
  Calendar,
  Target,
  Activity,
  Loader
} from 'lucide-react';
import { formatCurrency, formatPercentage, getChangeColor } from '@/lib/utils';
import { holdingsService, DetailedHoldingData, Transaction } from '@/lib/holdings-service';

interface ExpandedState {
  [symbol: string]: boolean;
}

export default function CurrentHoldingPage() {
  const [detailedHoldings, setDetailedHoldings] = useState<DetailedHoldingData[]>([]);
  const [expandedStocks, setExpandedStocks] = useState<ExpandedState>({});
  const [loading, setLoading] = useState(true);
  const [loadingDetails, setLoadingDetails] = useState<{[symbol: string]: boolean}>({});
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadHoldingsData();
  }, []);

  const loadHoldingsData = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await holdingsService.getAllDetailedHoldingData();
      setDetailedHoldings(data);
    } catch (error) {
      console.error('Error loading holdings data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load holdings data');
    } finally {
      setLoading(false);
    }
  };

  const toggleExpanded = async (symbol: string) => {
    if (!expandedStocks[symbol]) {
      setLoadingDetails(prev => ({ ...prev, [symbol]: true }));
      // Simulate loading delay for detailed data
      await new Promise(resolve => setTimeout(resolve, 500));
      setLoadingDetails(prev => ({ ...prev, [symbol]: false }));
    }

    setExpandedStocks(prev => ({
      ...prev,
      [symbol]: !prev[symbol]
    }));
  };

  // Calculate portfolio summary
  const totalInvestment = detailedHoldings.reduce((sum, holding) => sum + holding.totalInvested, 0);
  const totalCurrentValue = detailedHoldings.reduce((sum, holding) => sum + (holding.currentPrice * holding.totalQuantity), 0);
  const totalPnL = totalCurrentValue - totalInvestment;
  const totalPnLPercent = totalInvestment > 0 ? (totalPnL / totalInvestment) * 100 : 0;
  const CollapsibleStockRow = ({ holding }: { holding: DetailedHoldingData }) => {
    const isExpanded = expandedStocks[holding.symbol];
    const isLoadingDetails = loadingDetails[holding.symbol];
    const marketValue = holding.currentPrice * holding.totalQuantity;

    return (
      <div className="border border-gray-200 rounded-lg mb-4 overflow-hidden">
        {/* Stock Header Row */}
        <div
          className="flex items-center justify-between p-4 bg-white hover:bg-gray-50 cursor-pointer transition-colors"
          onClick={() => toggleExpanded(holding.symbol)}
        >
          <div className="flex items-center space-x-3">
            {isExpanded ? (
              <ChevronDown className="h-5 w-5 text-gray-400" />
            ) : (
              <ChevronRight className="h-5 w-5 text-gray-400" />
            )}
            <div>
              <h4 className="font-semibold text-gray-900">{holding.symbol}</h4>
              <p className="text-sm text-gray-600 truncate max-w-xs">{holding.name}</p>
            </div>
          </div>

          <div className="flex items-center space-x-6">
            <div className="text-right">
              <p className="text-sm text-gray-600">Qty: {holding.totalQuantity}</p>
              <p className="text-sm text-gray-600">Avg: {formatCurrency(holding.averagePrice)}</p>
            </div>

            <div className="text-right">
              <p className="font-medium text-gray-900">{formatCurrency(holding.currentPrice)}</p>
              <p className="text-sm text-gray-500">Current Price</p>
            </div>

            <div className="text-right">
              <p className="font-medium text-gray-900">{formatCurrency(marketValue)}</p>
              <p className={`text-sm font-medium ${getChangeColor(holding.overallNotionalPL)}`}>
                {holding.overallNotionalPL >= 0 ? '+' : ''}{formatCurrency(Math.abs(holding.overallNotionalPL))} ({formatPercentage(holding.notionalPLPercent)})
              </p>
            </div>

            <div className="text-right">
              <p className="font-medium text-green-600">{formatCurrency(holding.targetPrice)}</p>
              <p className="text-sm text-gray-500">Target (6%)</p>
            </div>
          </div>
        </div>

        {/* Expanded Details */}
        {isExpanded && (
          <div className="border-t border-gray-200 bg-gray-50">
            {isLoadingDetails ? (
              <div className="flex items-center justify-center py-8">
                <Loader className="h-6 w-6 animate-spin text-blue-600" />
                <span className="ml-2 text-gray-600">Loading transaction details...</span>
              </div>
            ) : (
              <TransactionTable holding={holding} />
            )}
          </div>
        )}
      </div>
    );
  };
  const TransactionTable = ({ holding }: { holding: DetailedHoldingData }) => {
    const calculateRunningTotal = (transactions: Transaction[], currentIndex: number): number => {
      return transactions
        .slice(0, currentIndex + 1)
        .filter(txn => txn.type === 'BUY')
        .reduce((sum, txn) => sum + txn.amount, 0);
    };

    const calculateHoldingDays = (buyDate: Date, sellDate?: Date): number => {
      const endDate = sellDate || new Date();
      const diffTime = Math.abs(endDate.getTime() - buyDate.getTime());
      return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    };

    return (
      <div className="p-4">
        <div className="overflow-x-auto">
          <div className="mb-4 text-sm text-gray-600 bg-blue-50 border border-blue-200 rounded-lg p-3">
            <p className="font-medium text-blue-900 mb-1">Transaction Details for {holding.name}</p>
            <p>This table shows all buy/sell transactions with comprehensive performance analysis including profit calculations, holding periods, and breakout signals.</p>
            <p className="text-xs text-blue-600 mt-2">💡 Tip: Scroll horizontally to view all columns on smaller screens</p>
          </div>
          <div className="min-w-max">
            <table className="w-full divide-y divide-gray-200 text-sm border border-gray-200 rounded-lg">
            <thead className="bg-gray-50 sticky top-0">
              <tr>
                <th className="px-2 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider border-r border-gray-200">Buy Date</th>
                <th className="px-2 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider border-r border-gray-200">Stock Name</th>
                <th className="px-2 py-3 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider border-r border-gray-200">Buy Price</th>
                <th className="px-2 py-3 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider border-r border-gray-200">Actual Buy Qty</th>
                <th className="px-2 py-3 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider border-r border-gray-200">Amount</th>
                <th className="px-2 py-3 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider border-r border-gray-200">Total Qty</th>
                <th className="px-2 py-3 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider border-r border-gray-200">Total Invested</th>
                <th className="px-2 py-3 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider border-r border-gray-200">Average Price</th>
                <th className="px-2 py-3 text-right text-xs font-semibold text-green-700 uppercase tracking-wider border-r border-gray-200">Target Price</th>
                <th className="px-2 py-3 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider border-r border-gray-200">Sell Price</th>
                <th className="px-2 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider border-r border-gray-200">Sell Date</th>
                <th className="px-2 py-3 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider border-r border-gray-200">Total Invested on Date</th>
                <th className="px-2 py-3 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider border-r border-gray-200">Holding Days</th>
                <th className="px-2 py-3 text-right text-xs font-semibold text-blue-700 uppercase tracking-wider border-r border-gray-200">Profit Amount</th>
                <th className="px-2 py-3 text-right text-xs font-semibold text-blue-700 uppercase tracking-wider border-r border-gray-200">Profit %</th>
                <th className="px-2 py-3 text-right text-xs font-semibold text-purple-700 uppercase tracking-wider border-r border-gray-200">Overall Notional P/L</th>
                <th className="px-2 py-3 text-right text-xs font-semibold text-purple-700 uppercase tracking-wider border-r border-gray-200">Notional P/L %</th>
                <th className="px-2 py-3 text-right text-xs font-semibold text-orange-700 uppercase tracking-wider border-r border-gray-200">Last Week High</th>
                <th className="px-2 py-3 text-right text-xs font-semibold text-orange-700 uppercase tracking-wider border-r border-gray-200">Ignorable Lower</th>
                <th className="px-2 py-3 text-center text-xs font-semibold text-red-700 uppercase tracking-wider">Outside Range?</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {holding.transactions.map((transaction, index) => {
                const runningTotal = calculateRunningTotal(holding.transactions, index);
                const holdingDays = calculateHoldingDays(transaction.date);
                const sellTransaction = holding.transactions.find(t => t.type === 'SELL' && t.symbol === transaction.symbol);

                // Calculate profit for sold transactions
                const profitAmount = sellTransaction && transaction.type === 'BUY'
                  ? (sellTransaction.price - transaction.price) * Math.min(transaction.quantity, sellTransaction.quantity)
                  : 0;
                const profitPercent = profitAmount !== 0 ? (profitAmount / transaction.amount) * 100 : 0;

                return (
                  <tr key={transaction.id} className={`border-b border-gray-100 ${transaction.type === 'SELL' ? 'bg-red-50' : 'hover:bg-gray-50'}`}>
                    <td className="px-2 py-3 whitespace-nowrap text-sm text-gray-900 border-r border-gray-100">
                      {transaction.date.toLocaleDateString()}
                    </td>
                    <td className="px-2 py-3 whitespace-nowrap text-sm text-gray-900 border-r border-gray-100 max-w-xs truncate">
                      {holding.name}
                    </td>
                    <td className="px-2 py-3 whitespace-nowrap text-sm text-gray-900 text-right border-r border-gray-100">
                      {transaction.type === 'BUY' ? formatCurrency(transaction.price) : '-'}
                    </td>
                    <td className="px-2 py-3 whitespace-nowrap text-sm text-gray-900 text-right border-r border-gray-100">
                      {transaction.type === 'BUY' ? transaction.quantity : '-'}
                    </td>
                    <td className="px-2 py-3 whitespace-nowrap text-sm text-gray-900 text-right border-r border-gray-100">
                      {transaction.type === 'BUY' ? formatCurrency(transaction.amount) : '-'}
                    </td>
                    <td className="px-2 py-3 whitespace-nowrap text-sm text-gray-900 text-right border-r border-gray-100 font-medium">
                      {holding.totalQuantity}
                    </td>
                    <td className="px-2 py-3 whitespace-nowrap text-sm text-gray-900 text-right border-r border-gray-100 font-medium">
                      {formatCurrency(holding.totalInvested)}
                    </td>
                    <td className="px-2 py-3 whitespace-nowrap text-sm text-gray-900 text-right border-r border-gray-100 font-medium">
                      {formatCurrency(holding.averagePrice)}
                    </td>
                    <td className="px-2 py-3 whitespace-nowrap text-sm text-green-600 text-right border-r border-gray-100 font-semibold">
                      {formatCurrency(holding.targetPrice)}
                    </td>
                    <td className="px-2 py-3 whitespace-nowrap text-sm text-gray-900 text-right border-r border-gray-100">
                      {sellTransaction ? formatCurrency(sellTransaction.price) : '-'}
                    </td>
                    <td className="px-2 py-3 whitespace-nowrap text-sm text-gray-900 border-r border-gray-100">
                      {sellTransaction ? sellTransaction.date.toLocaleDateString() : '-'}
                    </td>
                    <td className="px-2 py-3 whitespace-nowrap text-sm text-gray-900 text-right border-r border-gray-100">
                      {formatCurrency(runningTotal)}
                    </td>
                    <td className="px-2 py-3 whitespace-nowrap text-sm text-gray-900 text-right border-r border-gray-100">
                      {holdingDays} days
                    </td>
                    <td className={`px-2 py-3 whitespace-nowrap text-sm text-right border-r border-gray-100 font-medium ${getChangeColor(profitAmount)}`}>
                      {profitAmount !== 0 ? `${profitAmount >= 0 ? '+' : ''}${formatCurrency(Math.abs(profitAmount))}` : '-'}
                    </td>
                    <td className={`px-2 py-3 whitespace-nowrap text-sm text-right border-r border-gray-100 font-medium ${getChangeColor(profitAmount)}`}>
                      {profitAmount !== 0 ? `${profitAmount >= 0 ? '+' : ''}${formatPercentage(profitPercent)}` : '-'}
                    </td>
                    <td className={`px-2 py-3 whitespace-nowrap text-sm text-right border-r border-gray-100 font-semibold ${getChangeColor(holding.overallNotionalPL)}`}>
                      {holding.overallNotionalPL >= 0 ? '+' : ''}{formatCurrency(Math.abs(holding.overallNotionalPL))}
                    </td>
                    <td className={`px-2 py-3 whitespace-nowrap text-sm text-right border-r border-gray-100 font-semibold ${getChangeColor(holding.overallNotionalPL)}`}>
                      {holding.overallNotionalPL >= 0 ? '+' : ''}{formatPercentage(holding.notionalPLPercent)}
                    </td>
                    <td className="px-2 py-3 whitespace-nowrap text-sm text-orange-600 text-right border-r border-gray-100 font-medium">
                      {formatCurrency(holding.lastWeekHighPrice)}
                    </td>
                    <td className="px-2 py-3 whitespace-nowrap text-sm text-orange-600 text-right border-r border-gray-100 font-medium">
                      {formatCurrency(holding.ignorableLowerPrice)}
                    </td>
                    <td className="px-2 py-3 whitespace-nowrap text-sm text-center">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-semibold ${
                        holding.isLastWeekHighOutsideIgnorableRange
                          ? 'bg-green-100 text-green-800 border border-green-200'
                          : 'bg-red-100 text-red-800 border border-red-200'
                      }`}>
                        {holding.isLastWeekHighOutsideIgnorableRange ? 'YES' : 'NO'}
                      </span>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading holdings data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center max-w-md">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <div className="text-red-600 mb-4">
              <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-red-900 mb-2">Error Loading Holdings</h3>
            <p className="text-red-700 mb-4">{error}</p>
            <button
              onClick={loadHoldingsData}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Current Holdings</h1>
          <p className="text-gray-600 mt-1">Detailed transaction history and performance analysis</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={loadHoldingsData}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
          >
            <Activity className="h-4 w-4" />
            <span>Refresh Data</span>
          </button>
          <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
            Export Report
          </button>
        </div>
      </div>

      {/* Portfolio Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Investment</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{formatCurrency(totalInvestment)}</p>
            </div>
            <DollarSign className="h-8 w-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Current Value</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{formatCurrency(totalCurrentValue)}</p>
            </div>
            <Briefcase className="h-8 w-8 text-gray-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total P&L</p>
              <p className={`text-2xl font-bold mt-1 ${getChangeColor(totalPnL)}`}>
                {totalPnL >= 0 ? '+' : ''}{formatCurrency(Math.abs(totalPnL))}
              </p>
              <p className={`text-sm ${getChangeColor(totalPnL)}`}>
                {formatPercentage(totalPnLPercent)}
              </p>
            </div>
            {totalPnL >= 0 ? (
              <TrendingUp className="h-8 w-8 text-green-600" />
            ) : (
              <TrendingDown className="h-8 w-8 text-red-600" />
            )}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Holdings Count</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{detailedHoldings.length}</p>
              <p className="text-sm text-gray-500">Active Stocks</p>
            </div>
            <PieChart className="h-8 w-8 text-purple-600" />
          </div>
        </div>
      </div>

      {/* Holdings List with Collapsible Details */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">
              Holdings ({detailedHoldings.length})
            </h3>
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <Calendar className="h-4 w-4" />
              <span>Click to expand transaction details</span>
            </div>
          </div>
        </div>

        <div className="p-6">
          {detailedHoldings.length === 0 ? (
            <div className="text-center py-8">
              <Briefcase className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No holdings found</p>
            </div>
          ) : (
            <div className="space-y-4">
              {detailedHoldings.map((holding) => (
                <CollapsibleStockRow key={holding.symbol} holding={holding} />
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">Performance Metrics</h3>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600 mb-2">
              {detailedHoldings.filter(h => h.overallNotionalPL > 0).length}
            </div>
            <p className="text-sm text-gray-600">Profitable Positions</p>
          </div>

          <div className="text-center">
            <div className="text-2xl font-bold text-red-600 mb-2">
              {detailedHoldings.filter(h => h.overallNotionalPL < 0).length}
            </div>
            <p className="text-sm text-gray-600">Loss Making Positions</p>
          </div>

          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900 mb-2">
              {detailedHoldings.length > 0 ?
                ((detailedHoldings.filter(h => h.overallNotionalPL > 0).length / detailedHoldings.length) * 100).toFixed(1)
                : '0'}%
            </div>
            <p className="text-sm text-gray-600">Win Rate</p>
          </div>

          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600 mb-2">
              {detailedHoldings.filter(h => h.isLastWeekHighOutsideIgnorableRange).length}
            </div>
            <p className="text-sm text-gray-600">Breakout Signals</p>
          </div>
        </div>
      </div>
    </div>
  );
}
