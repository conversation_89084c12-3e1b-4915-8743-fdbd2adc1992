'use client';

import { useState, useEffect } from 'react';
import {
  ChevronDown,
  ChevronUp,
  RefreshCw,
  TrendingUp,
  Calendar,
  Target,
  Loader,
  AlertCircle
} from 'lucide-react';
import { formatCurrency, formatPercentage } from '@/lib/utils';
import { NiftyStock } from '@/lib/nifty-stocks';
import { holdingsService } from '@/lib/holdings-service';
import { yahooFinanceService } from '@/lib/yahoo-finance';
import { useWeeklyHighSignals } from '@/hooks/useCentralData';

interface OHLCData {
  day: string;
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
}

interface WeeklyHighStock {
  symbol: string;
  name: string;
  currentPrice: number;
  lastWeekHighest: number;
  suggestedBuyPrice: number;
  percentDifference: number;
  suggestedGTTQuantity: number;
  ohlcData: OHLCData[];
  isBOHEligible: boolean;
  inHoldings: boolean;
}

export default function WeeklyHighSignalPage() {
  const [stocks, setStocks] = useState<WeeklyHighStock[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedStocks, setExpandedStocks] = useState<Set<string>>(new Set());
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  // Toggle expanded state for a stock
  const toggleExpanded = (symbol: string) => {
    const newExpanded = new Set(expandedStocks);
    if (newExpanded.has(symbol)) {
      newExpanded.delete(symbol);
    } else {
      newExpanded.add(symbol);
    }
    setExpandedStocks(newExpanded);
  };

  // Generate mock OHLC data for last week
  const generateOHLCData = (currentPrice: number): OHLCData[] => {
    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
    const ohlcData: OHLCData[] = [];

    // Get last week's dates
    const today = new Date();
    const lastFriday = new Date(today);
    lastFriday.setDate(today.getDate() - ((today.getDay() + 2) % 7)); // Get last Friday

    let basePrice = currentPrice * (0.95 + Math.random() * 0.1); // Start with price variation

    for (let i = 0; i < 5; i++) {
      const date = new Date(lastFriday);
      date.setDate(lastFriday.getDate() - (4 - i)); // Monday to Friday of last week

      const variation = 0.02 + Math.random() * 0.03; // 2-5% daily variation
      const open = basePrice;
      const high = open * (1 + variation);
      const low = open * (1 - variation * 0.7);
      const close = low + (high - low) * Math.random();

      ohlcData.push({
        day: days[i],
        date: date.toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' }),
        open: parseFloat(open.toFixed(2)),
        high: parseFloat(high.toFixed(2)),
        low: parseFloat(low.toFixed(2)),
        close: parseFloat(close.toFixed(2))
      });

      basePrice = close; // Next day starts with previous close
    }

    return ohlcData;
  };

  // Calculate weekly high stock data
  const calculateWeeklyHighData = (stock: NiftyStock): WeeklyHighStock => {
    const ohlcData = generateOHLCData(stock.price);
    const lastWeekHighest = Math.max(...ohlcData.map(d => d.high));
    const suggestedBuyPrice = lastWeekHighest + 0.05;
    const percentDifference = ((stock.price - suggestedBuyPrice) / suggestedBuyPrice) * 100;
    const suggestedGTTQuantity = Math.floor(2000 / suggestedBuyPrice);

    return {
      symbol: stock.symbol,
      name: stock.name,
      currentPrice: stock.price,
      lastWeekHighest,
      suggestedBuyPrice,
      percentDifference,
      suggestedGTTQuantity,
      ohlcData,
      isBOHEligible: stock.isBOHEligible || false,
      inHoldings: stock.inHoldings
    };
  };

  // Load BOH eligible stocks
  const loadBOHEligibleStocks = async () => {
    setLoading(true);
    setError(null);

    try {
      console.log('🔍 Loading BOH eligible stocks for Weekly High signals...');

      // Fetch BOH eligible stocks from the API
      const response = await fetch('/api/stocks/nifty200?batchIndex=0&batchSize=200');
      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch stock data');
      }

      // Get current holdings
      const holdingSymbols = holdingsService.getAllHoldings().map(h => h.symbol);

      // Filter for BOH eligible stocks not in holdings
      const bohEligibleStocks = data.data.stocks.filter((stock: NiftyStock) =>
        stock.isBOHEligible && !holdingSymbols.includes(stock.symbol)
      );

      console.log(`✅ Found ${bohEligibleStocks.length} BOH eligible stocks not in holdings`);

      // Calculate weekly high data for each stock
      const weeklyHighStocks = bohEligibleStocks.map(calculateWeeklyHighData);

      // Sort by percentage difference (closest to suggested buy price first)
      weeklyHighStocks.sort((a, b) => Math.abs(a.percentDifference) - Math.abs(b.percentDifference));

      setStocks(weeklyHighStocks);
      setLastUpdate(new Date());

    } catch (err) {
      console.error('❌ Error loading BOH eligible stocks:', err);
      setError(err instanceof Error ? err.message : 'Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    loadBOHEligibleStocks();
  }, []);

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Weekly High Signal</h1>
          <p className="text-gray-600 mt-1">
            BOH Eligible Stocks (excluding currently held stocks) • {stocks.length} stocks found
          </p>
          {lastUpdate && (
            <p className="text-sm text-gray-500 mt-1">
              Last updated: {lastUpdate.toLocaleTimeString()}
            </p>
          )}
        </div>

        <div className="flex items-center space-x-3">
          <button
            onClick={loadBOHEligibleStocks}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">BOH Eligible Stocks</p>
              <p className="text-2xl font-bold text-blue-600 mt-1">{stocks.length}</p>
            </div>
            <Target className="h-8 w-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Near Buy Price</p>
              <p className="text-2xl font-bold text-green-600 mt-1">
                {stocks.filter(s => Math.abs(s.percentDifference) <= 5).length}
              </p>
            </div>
            <TrendingUp className="h-8 w-8 text-green-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg GTT Quantity</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">
                {stocks.length > 0 ? Math.round(stocks.reduce((sum, s) => sum + s.suggestedGTTQuantity, 0) / stocks.length) : 0}
              </p>
            </div>
            <Calendar className="h-8 w-8 text-gray-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Investment</p>
              <p className="text-2xl font-bold text-purple-600 mt-1">
                {formatCurrency(stocks.length * 2000)}
              </p>
            </div>
            <Target className="h-8 w-8 text-purple-600" />
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {error && (
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center text-red-600">
              <AlertCircle className="h-5 w-5 mr-2" />
              <span>Error: {error}</span>
            </div>
          </div>
        )}

        {loading ? (
          <div className="p-12 text-center">
            <Loader className="h-8 w-8 mx-auto mb-4 animate-spin text-blue-600" />
            <p className="text-gray-500">Loading BOH eligible stocks...</p>
            <p className="text-sm text-gray-400 mt-1">Analyzing weekly high patterns</p>
          </div>
        ) : stocks.length === 0 ? (
          <div className="p-12 text-center">
            <TrendingUp className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p className="text-gray-500">No BOH eligible stocks found</p>
            <p className="text-sm text-gray-400 mt-1">All BOH eligible stocks are currently in holdings</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {/* Table Header */}
            <div className="p-4 bg-gray-50 grid grid-cols-7 gap-4 text-sm font-medium text-gray-700">
              <div>Stock</div>
              <div className="text-right">Current Price</div>
              <div className="text-right">Last Week's High</div>
              <div className="text-right">Suggested Buy Price</div>
              <div className="text-right">% Difference</div>
              <div className="text-right">GTT Quantity</div>
              <div className="text-center">Details</div>
            </div>

            {/* Stock Rows */}
            {stocks.map((stock) => (
              <div key={stock.symbol}>
                {/* Main Row */}
                <div className="p-4 hover:bg-gray-50 grid grid-cols-7 gap-4 items-center">
                  {/* Stock Info */}
                  <div>
                    <div className="flex items-center space-x-2">
                      <h4 className="font-medium text-gray-900">{stock.symbol}</h4>
                      <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                        BOH
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 truncate">{stock.name}</p>
                  </div>

                  {/* Current Price */}
                  <div className="text-right">
                    <p className="font-medium text-gray-900">{formatCurrency(stock.currentPrice)}</p>
                  </div>

                  {/* Last Week's High */}
                  <div className="text-right">
                    <p className="font-medium text-gray-900">{formatCurrency(stock.lastWeekHighest)}</p>
                  </div>

                  {/* Suggested Buy Price */}
                  <div className="text-right">
                    <p className="font-medium text-green-600">{formatCurrency(stock.suggestedBuyPrice)}</p>
                    <p className="text-xs text-gray-500">High + ₹0.05</p>
                  </div>

                  {/* % Difference */}
                  <div className="text-right">
                    <p className={`font-medium ${
                      stock.percentDifference >= 0
                        ? 'text-green-600'
                        : 'text-red-600'
                    }`}>
                      {formatPercentage(stock.percentDifference)}
                    </p>
                  </div>

                  {/* GTT Quantity */}
                  <div className="text-right">
                    <p className="font-medium text-gray-900">{stock.suggestedGTTQuantity}</p>
                    <p className="text-xs text-gray-500">₹2,000 ÷ Buy Price</p>
                  </div>

                  {/* Expand Button */}
                  <div className="text-center">
                    <button
                      onClick={() => toggleExpanded(stock.symbol)}
                      className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                    >
                      {expandedStocks.has(stock.symbol) ? (
                        <ChevronUp className="h-4 w-4 text-gray-600" />
                      ) : (
                        <ChevronDown className="h-4 w-4 text-gray-600" />
                      )}
                    </button>
                  </div>
                </div>

                {/* Expanded OHLC Table */}
                {expandedStocks.has(stock.symbol) && (
                  <div className="px-4 pb-4 bg-gray-50">
                    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                      <div className="px-4 py-3 bg-gray-100 border-b border-gray-200">
                        <h5 className="font-medium text-gray-900">
                          Last Week's OHLC Data - {stock.symbol}
                        </h5>
                      </div>

                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Day
                              </th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Date
                              </th>
                              <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Open
                              </th>
                              <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                High
                              </th>
                              <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Low
                              </th>
                              <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Close
                              </th>
                              <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Last Week's Highest
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {stock.ohlcData.map((dayData, index) => (
                              <tr key={`${stock.symbol}-${index}`} className="hover:bg-gray-50">
                                <td className="px-4 py-3 text-sm font-medium text-gray-900">
                                  {dayData.day}
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-600">
                                  {dayData.date}
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900 text-right">
                                  {formatCurrency(dayData.open)}
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900 text-right">
                                  <span className={dayData.high === stock.lastWeekHighest ? 'font-bold text-green-600' : ''}>
                                    {formatCurrency(dayData.high)}
                                  </span>
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900 text-right">
                                  {formatCurrency(dayData.low)}
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900 text-right">
                                  {formatCurrency(dayData.close)}
                                </td>
                                <td className="px-4 py-3 text-sm text-right">
                                  {index === stock.ohlcData.length - 1 && (
                                    <span className="font-bold text-green-600">
                                      {formatCurrency(stock.lastWeekHighest)}
                                    </span>
                                  )}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Signal Timing Info */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <Calendar className="h-5 w-5 text-blue-600 mt-0.5 mr-3" />
          <div>
            <h3 className="font-medium text-blue-900">New Darvas Box Strategy - Signal Timing</h3>
            <p className="text-blue-700 mt-1">
              The signal generation for the New Darvas Box Strategy runs every Friday at 8:00 PM.
              This ensures fresh weekly high analysis for the upcoming trading week.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
