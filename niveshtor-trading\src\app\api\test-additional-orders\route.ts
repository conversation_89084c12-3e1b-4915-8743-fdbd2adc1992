import { NextRequest, NextResponse } from 'next/server';
import { automaticGTTService } from '@/lib/automatic-gtt-service';
import { weeklyHighSignalDetector } from '@/lib/weekly-high-signal-detector';

export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Testing creation of additional orders...');

    // Get current signals and existing orders
    const signals = await weeklyHighSignalDetector.triggerManualScan();
    const existingOrders = automaticGTTService.getAllOrders();
    
    // Find signals that don't have existing orders
    const existingSymbols = new Set(
      existingOrders
        .filter(order => order.source === 'SIGNAL' && order.status === 'PENDING')
        .map(order => order.symbol)
    );

    const eligibleSignals = signals.filter(signal => !existingSymbols.has(signal.symbol));
    
    console.log(`📊 Found ${signals.length} signals, ${existingOrders.length} existing orders`);
    console.log(`✅ ${eligibleSignals.length} signals eligible for new orders`);

    if (eligibleSignals.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No new signals available for order creation',
        data: {
          totalSignals: signals.length,
          existingOrders: existingOrders.length,
          eligibleSignals: 0,
          reason: 'All signals already have pending orders'
        }
      });
    }

    // Create orders for next 5 eligible signals
    const signalsToTest = eligibleSignals.slice(0, 5);
    const results = [];

    for (const signal of signalsToTest) {
      try {
        console.log(`🤖 Testing order creation for ${signal.symbol}...`);
        
        const order = await automaticGTTService.testCreateOrder(signal.symbol);
        
        if (order) {
          results.push({
            symbol: signal.symbol,
            status: 'SUCCESS',
            orderId: order.id,
            triggerPrice: order.triggerPrice,
            quantity: order.quantity,
            signalStrength: signal.signalStrength
          });
          console.log(`✅ Order created for ${signal.symbol}`);
        } else {
          results.push({
            symbol: signal.symbol,
            status: 'FAILED',
            error: 'No order returned'
          });
        }
      } catch (error) {
        results.push({
          symbol: signal.symbol,
          status: 'ERROR',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    // Get updated counts
    const updatedOrders = automaticGTTService.getAllOrders();
    const newOrdersCreated = updatedOrders.length - existingOrders.length;

    console.log(`🎉 Test completed: ${results.filter(r => r.status === 'SUCCESS').length} new orders created`);

    return NextResponse.json({
      success: true,
      message: `Created ${results.filter(r => r.status === 'SUCCESS').length} additional orders`,
      data: {
        initialOrderCount: existingOrders.length,
        finalOrderCount: updatedOrders.length,
        newOrdersCreated,
        totalSignals: signals.length,
        eligibleSignals: eligibleSignals.length,
        testedSignals: signalsToTest.length,
        successfulCreations: results.filter(r => r.status === 'SUCCESS').length,
        results
      }
    });

  } catch (error) {
    console.error('❌ Additional order test failed:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Test failed',
        message: 'Additional order test failed'
      },
      { status: 500 }
    );
  }
}
