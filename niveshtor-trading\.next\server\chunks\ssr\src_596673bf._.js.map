{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/components/ui/RealTimeIndicator.tsx"], "sourcesContent": ["// Real-time update status indicator\nimport React from 'react';\nimport { Wifi, WifiOff, Activity, Clock } from 'lucide-react';\n\ninterface RealTimeIndicatorProps {\n  isActive: boolean;\n  lastUpdate: Date | null;\n  updateCount: number;\n  error: Error | null;\n  className?: string;\n}\n\nexport function RealTimeIndicator({ \n  isActive, \n  lastUpdate, \n  updateCount, \n  error, \n  className = '' \n}: RealTimeIndicatorProps) {\n  const formatLastUpdate = (date: Date | null) => {\n    if (!date) return 'Never';\n    \n    const now = new Date();\n    const diff = now.getTime() - date.getTime();\n    const seconds = Math.floor(diff / 1000);\n    \n    if (seconds < 60) return `${seconds}s ago`;\n    if (seconds < 3600) return `${Math.floor(seconds / 60)}m ago`;\n    return date.toLocaleTimeString();\n  };\n\n  const getStatusColor = () => {\n    if (error) return 'text-red-500';\n    if (isActive) return 'text-green-500';\n    return 'text-gray-400';\n  };\n\n  const getStatusIcon = () => {\n    if (error) return <WifiOff className=\"h-4 w-4\" />;\n    if (isActive) return <Wifi className=\"h-4 w-4\" />;\n    return <WifiOff className=\"h-4 w-4\" />;\n  };\n\n  const getStatusText = () => {\n    if (error) return 'Connection Error';\n    if (isActive) return 'Live Updates';\n    return 'Offline';\n  };\n\n  return (\n    <div className={`flex items-center space-x-2 text-sm ${className}`}>\n      {/* Status Icon and Text */}\n      <div className={`flex items-center space-x-1 ${getStatusColor()}`}>\n        {getStatusIcon()}\n        <span className=\"font-medium\">{getStatusText()}</span>\n      </div>\n      \n      {/* Update Count */}\n      {isActive && updateCount > 0 && (\n        <div className=\"flex items-center space-x-1 text-gray-500\">\n          <Activity className=\"h-3 w-3\" />\n          <span>{updateCount} updates</span>\n        </div>\n      )}\n      \n      {/* Last Update Time */}\n      <div className=\"flex items-center space-x-1 text-gray-500\">\n        <Clock className=\"h-3 w-3\" />\n        <span>{formatLastUpdate(lastUpdate)}</span>\n      </div>\n      \n      {/* Error Message */}\n      {error && (\n        <div className=\"text-red-500 text-xs\">\n          {error.message}\n        </div>\n      )}\n    </div>\n  );\n}\n\n// Compact version for smaller spaces\nexport function CompactRealTimeIndicator({ \n  isActive, \n  lastUpdate, \n  error, \n  className = '' \n}: Omit<RealTimeIndicatorProps, 'updateCount'>) {\n  const getStatusColor = () => {\n    if (error) return 'text-red-500';\n    if (isActive) return 'text-green-500';\n    return 'text-gray-400';\n  };\n\n  const getStatusIcon = () => {\n    if (error) return <WifiOff className=\"h-3 w-3\" />;\n    if (isActive) return <Wifi className=\"h-3 w-3\" />;\n    return <WifiOff className=\"h-3 w-3\" />;\n  };\n\n  return (\n    <div className={`flex items-center space-x-1 ${getStatusColor()} ${className}`}>\n      {getStatusIcon()}\n      {isActive && (\n        <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\" />\n      )}\n    </div>\n  );\n}\n\n// Hook for using real-time indicator data\nexport function useRealTimeIndicator() {\n  const [isActive, setIsActive] = React.useState(false);\n  const [lastUpdate, setLastUpdate] = React.useState<Date | null>(null);\n  const [updateCount, setUpdateCount] = React.useState(0);\n  const [error, setError] = React.useState<Error | null>(null);\n\n  // Listen for real-time updates\n  React.useEffect(() => {\n    const handleRealTimeUpdate = (event: CustomEvent) => {\n      setLastUpdate(new Date());\n      setUpdateCount(prev => prev + 1);\n      setError(null);\n      setIsActive(true);\n    };\n\n    const handleError = (event: CustomEvent) => {\n      setError(event.detail.error);\n    };\n\n    window.addEventListener('realTimePriceUpdate', handleRealTimeUpdate as EventListener);\n    window.addEventListener('realTimeError', handleError as EventListener);\n\n    return () => {\n      window.removeEventListener('realTimePriceUpdate', handleRealTimeUpdate as EventListener);\n      window.removeEventListener('realTimeError', handleError as EventListener);\n    };\n  }, []);\n\n  return {\n    isActive,\n    lastUpdate,\n    updateCount,\n    error,\n    clearError: () => setError(null)\n  };\n}\n"], "names": [], "mappings": "AAAA,oCAAoC;;;;;;;AACpC;AACA;AAAA;AAAA;AAAA;;;;AAUO,SAAS,kBAAkB,EAChC,QAAQ,EACR,UAAU,EACV,WAAW,EACX,KAAK,EACL,YAAY,EAAE,EACS;IACvB,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,MAAM,IAAI;QAChB,MAAM,OAAO,IAAI,OAAO,KAAK,KAAK,OAAO;QACzC,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO;QAElC,IAAI,UAAU,IAAI,OAAO,GAAG,QAAQ,KAAK,CAAC;QAC1C,IAAI,UAAU,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC;QAC7D,OAAO,KAAK,kBAAkB;IAChC;IAEA,MAAM,iBAAiB;QACrB,IAAI,OAAO,OAAO;QAClB,IAAI,UAAU,OAAO;QACrB,OAAO;IACT;IAEA,MAAM,gBAAgB;QACpB,IAAI,OAAO,qBAAO,8OAAC,4MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QACrC,IAAI,UAAU,qBAAO,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QACrC,qBAAO,8OAAC,4MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;IAC5B;IAEA,MAAM,gBAAgB;QACpB,IAAI,OAAO,OAAO;QAClB,IAAI,UAAU,OAAO;QACrB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,oCAAoC,EAAE,WAAW;;0BAEhE,8OAAC;gBAAI,WAAW,CAAC,4BAA4B,EAAE,kBAAkB;;oBAC9D;kCACD,8OAAC;wBAAK,WAAU;kCAAe;;;;;;;;;;;;YAIhC,YAAY,cAAc,mBACzB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC;;4BAAM;4BAAY;;;;;;;;;;;;;0BAKvB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,8OAAC;kCAAM,iBAAiB;;;;;;;;;;;;YAIzB,uBACC,8OAAC;gBAAI,WAAU;0BACZ,MAAM,OAAO;;;;;;;;;;;;AAKxB;AAGO,SAAS,yBAAyB,EACvC,QAAQ,EACR,UAAU,EACV,KAAK,EACL,YAAY,EAAE,EAC8B;IAC5C,MAAM,iBAAiB;QACrB,IAAI,OAAO,OAAO;QAClB,IAAI,UAAU,OAAO;QACrB,OAAO;IACT;IAEA,MAAM,gBAAgB;QACpB,IAAI,OAAO,qBAAO,8OAAC,4MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QACrC,IAAI,UAAU,qBAAO,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QACrC,qBAAO,8OAAC,4MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;IAC5B;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,4BAA4B,EAAE,iBAAiB,CAAC,EAAE,WAAW;;YAC3E;YACA,0BACC,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAIvB;AAGO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAc;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACrD,MAAM,CAAC,OAAO,SAAS,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAe;IAEvD,+BAA+B;IAC/B,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,MAAM,uBAAuB,CAAC;YAC5B,cAAc,IAAI;YAClB,eAAe,CAAA,OAAQ,OAAO;YAC9B,SAAS;YACT,YAAY;QACd;QAEA,MAAM,cAAc,CAAC;YACnB,SAAS,MAAM,MAAM,CAAC,KAAK;QAC7B;QAEA,OAAO,gBAAgB,CAAC,uBAAuB;QAC/C,OAAO,gBAAgB,CAAC,iBAAiB;QAEzC,OAAO;YACL,OAAO,mBAAmB,CAAC,uBAAuB;YAClD,OAAO,mBAAmB,CAAC,iBAAiB;QAC9C;IACF,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;QACA,YAAY,IAAM,SAAS;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 230, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/hooks/useBackgroundData.ts"], "sourcesContent": ["// React hook for seamless background data integration\nimport { useState, useEffect, useCallback, useRef } from 'react';\nimport { backgroundDataService, BackgroundUpdateListener, RealTimeUpdateListener } from '@/lib/background-data-service';\nimport { stockNamesService } from '@/lib/stock-names-service';\nimport { NiftyStock } from '@/lib/nifty-stocks';\n\ninterface UseBackgroundDataOptions {\n  enablePriceUpdates?: boolean;\n  enableNameUpdates?: boolean;\n  enableRealTimeUpdates?: boolean;\n  onError?: (error: Error) => void;\n  onRealTimeUpdate?: (quotes: any[]) => void;\n}\n\ninterface UseBackgroundDataReturn {\n  // Status\n  isNamesReady: boolean;\n  isUpdating: boolean;\n  lastUpdate: Date | null;\n  error: Error | null;\n  \n  // Data\n  stockNames: Map<string, string>;\n  \n  // Methods\n  getStockName: (symbol: string) => string;\n  forceUpdate: () => Promise<void>;\n  clearError: () => void;\n}\n\nexport function useBackgroundData(options: UseBackgroundDataOptions = {}): UseBackgroundDataReturn {\n  const {\n    enablePriceUpdates = true,\n    enableNameUpdates = true,\n    enableRealTimeUpdates = false,\n    onError,\n    onRealTimeUpdate\n  } = options;\n\n  // State\n  const [isNamesReady, setIsNamesReady] = useState(false);\n  const [isUpdating, setIsUpdating] = useState(false);\n  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);\n  const [error, setError] = useState<Error | null>(null);\n  const [stockNames, setStockNames] = useState<Map<string, string>>(new Map());\n  \n  // Refs\n  const listenerRef = useRef<BackgroundUpdateListener | null>(null);\n  const realTimeListenerRef = useRef<RealTimeUpdateListener | null>(null);\n  const mountedRef = useRef(true);\n\n  // Create listener\n  const createListener = useCallback((): BackgroundUpdateListener => ({\n    onPriceUpdate: (data: any[]) => {\n      if (!mountedRef.current || !enablePriceUpdates) return;\n      \n      console.log(`🔄 Received background price update: ${data.length} stocks`);\n      setLastUpdate(new Date());\n      setIsUpdating(false);\n      \n      // Trigger custom event for other components to listen\n      window.dispatchEvent(new CustomEvent('backgroundPriceUpdate', {\n        detail: { data, timestamp: new Date() }\n      }));\n    },\n    \n    onNamesUpdate: (namesMap: Map<string, string>) => {\n      if (!mountedRef.current || !enableNameUpdates) return;\n      \n      console.log(`📝 Received background names update: ${namesMap.size} names`);\n      setStockNames(new Map(namesMap));\n      setIsNamesReady(true);\n      setError(null);\n      \n      // Trigger custom event for other components to listen\n      window.dispatchEvent(new CustomEvent('backgroundNamesUpdate', {\n        detail: { namesMap, timestamp: new Date() }\n      }));\n    },\n    \n    onError: (err: Error) => {\n      if (!mountedRef.current) return;\n      \n      console.error('❌ Background data error:', err);\n      setError(err);\n      setIsUpdating(false);\n      \n      if (onError) {\n        onError(err);\n      }\n    }\n  }), [enablePriceUpdates, enableNameUpdates, onError]);\n\n  // Create real-time listener\n  const createRealTimeListener = useCallback((): RealTimeUpdateListener => ({\n    onRealTimeUpdate: (quotes: any[]) => {\n      if (!mountedRef.current || !enableRealTimeUpdates) return;\n\n      console.log(`⚡ Received real-time update: ${quotes.length} quotes`);\n      setLastUpdate(new Date());\n\n      // Call custom callback if provided\n      if (onRealTimeUpdate) {\n        onRealTimeUpdate(quotes);\n      }\n\n      // Trigger custom event for components to listen\n      window.dispatchEvent(new CustomEvent('realTimePriceUpdate', {\n        detail: { quotes, timestamp: new Date() }\n      }));\n    },\n\n    onError: (err: Error) => {\n      if (!mountedRef.current) return;\n\n      console.error('❌ Real-time update error:', err);\n      setError(err);\n\n      if (onError) {\n        onError(err);\n      }\n    }\n  }), [enableRealTimeUpdates, onRealTimeUpdate, onError]);\n\n  // Initialize and setup listener\n  useEffect(() => {\n    mountedRef.current = true;\n    \n    const initializeData = async () => {\n      try {\n        // Check if names are already ready\n        const namesReady = backgroundDataService.areNamesReady();\n        setIsNamesReady(namesReady);\n\n        if (namesReady) {\n          // Load existing cached names\n          const cachedNames = stockNamesService.getCachedStockNames();\n          setStockNames(cachedNames);\n        }\n\n        // Create and register listener\n        const listener = createListener();\n        listenerRef.current = listener;\n        backgroundDataService.addListener(listener);\n\n        // Create and register real-time listener if enabled\n        if (enableRealTimeUpdates) {\n          const realTimeListener = createRealTimeListener();\n          realTimeListenerRef.current = realTimeListener;\n          backgroundDataService.addRealTimeListener(realTimeListener);\n        }\n\n        // Initialize background service if not already done\n        await backgroundDataService.initialize();\n\n      } catch (err) {\n        console.error('❌ Failed to initialize background data hook:', err);\n        setError(err as Error);\n        // Set names ready to true to allow pages to work even if background service fails\n        setIsNamesReady(true);\n      }\n    };\n    \n    initializeData();\n    \n    // Cleanup\n    return () => {\n      mountedRef.current = false;\n      if (listenerRef.current) {\n        backgroundDataService.removeListener(listenerRef.current);\n      }\n      if (realTimeListenerRef.current) {\n        backgroundDataService.removeRealTimeListener(realTimeListenerRef.current);\n      }\n    };\n  }, [createListener, createRealTimeListener, enableRealTimeUpdates]);\n\n  // Get stock name with fallback\n  const getStockName = useCallback((symbol: string): string => {\n    // First try from local state\n    const name = stockNames.get(symbol);\n    if (name) return name;\n\n    // Fallback to sync method\n    try {\n      return stockNamesService.getStockNameSync(symbol);\n    } catch (error) {\n      // Ultimate fallback - just return symbol without .NS\n      return symbol.replace('.NS', '');\n    }\n  }, [stockNames]);\n\n  // Force update\n  const forceUpdate = useCallback(async (): Promise<void> => {\n    setIsUpdating(true);\n    setError(null);\n    \n    try {\n      await backgroundDataService.forceUpdate();\n    } catch (err) {\n      setError(err as Error);\n      throw err;\n    }\n  }, []);\n\n  // Clear error\n  const clearError = useCallback(() => {\n    setError(null);\n  }, []);\n\n  return {\n    // Status\n    isNamesReady,\n    isUpdating,\n    lastUpdate,\n    error,\n    \n    // Data\n    stockNames,\n    \n    // Methods\n    getStockName,\n    forceUpdate,\n    clearError\n  };\n}\n\n// Hook for listening to background price updates\nexport function useBackgroundPriceUpdates(callback: (data: any[]) => void) {\n  useEffect(() => {\n    const handlePriceUpdate = (event: CustomEvent) => {\n      callback(event.detail.data);\n    };\n\n    window.addEventListener('backgroundPriceUpdate', handlePriceUpdate as EventListener);\n    \n    return () => {\n      window.removeEventListener('backgroundPriceUpdate', handlePriceUpdate as EventListener);\n    };\n  }, [callback]);\n}\n\n// Hook for listening to background name updates\nexport function useBackgroundNameUpdates(callback: (namesMap: Map<string, string>) => void) {\n  useEffect(() => {\n    const handleNameUpdate = (event: CustomEvent) => {\n      callback(event.detail.namesMap);\n    };\n\n    window.addEventListener('backgroundNamesUpdate', handleNameUpdate as EventListener);\n    \n    return () => {\n      window.removeEventListener('backgroundNamesUpdate', handleNameUpdate as EventListener);\n    };\n  }, [callback]);\n}\n"], "names": [], "mappings": "AAAA,sDAAsD;;;;;;AACtD;AACA;AACA;;;;AA2BO,SAAS,kBAAkB,UAAoC,CAAC,CAAC;IACtE,MAAM,EACJ,qBAAqB,IAAI,EACzB,oBAAoB,IAAI,EACxB,wBAAwB,KAAK,EAC7B,OAAO,EACP,gBAAgB,EACjB,GAAG;IAEJ,QAAQ;IACR,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC1D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB,IAAI;IAEtE,OAAO;IACP,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAmC;IAC5D,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAiC;IAClE,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE1B,kBAAkB;IAClB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAgC,CAAC;YAClE,eAAe,CAAC;gBACd,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC,oBAAoB;gBAEhD,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,KAAK,MAAM,CAAC,OAAO,CAAC;gBACxE,cAAc,IAAI;gBAClB,cAAc;gBAEd,sDAAsD;gBACtD,OAAO,aAAa,CAAC,IAAI,YAAY,yBAAyB;oBAC5D,QAAQ;wBAAE;wBAAM,WAAW,IAAI;oBAAO;gBACxC;YACF;YAEA,eAAe,CAAC;gBACd,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC,mBAAmB;gBAE/C,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,SAAS,IAAI,CAAC,MAAM,CAAC;gBACzE,cAAc,IAAI,IAAI;gBACtB,gBAAgB;gBAChB,SAAS;gBAET,sDAAsD;gBACtD,OAAO,aAAa,CAAC,IAAI,YAAY,yBAAyB;oBAC5D,QAAQ;wBAAE;wBAAU,WAAW,IAAI;oBAAO;gBAC5C;YACF;YAEA,SAAS,CAAC;gBACR,IAAI,CAAC,WAAW,OAAO,EAAE;gBAEzB,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,SAAS;gBACT,cAAc;gBAEd,IAAI,SAAS;oBACX,QAAQ;gBACV;YACF;QACF,CAAC,GAAG;QAAC;QAAoB;QAAmB;KAAQ;IAEpD,4BAA4B;IAC5B,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAA8B,CAAC;YACxE,kBAAkB,CAAC;gBACjB,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC,uBAAuB;gBAEnD,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,OAAO,MAAM,CAAC,OAAO,CAAC;gBAClE,cAAc,IAAI;gBAElB,mCAAmC;gBACnC,IAAI,kBAAkB;oBACpB,iBAAiB;gBACnB;gBAEA,gDAAgD;gBAChD,OAAO,aAAa,CAAC,IAAI,YAAY,uBAAuB;oBAC1D,QAAQ;wBAAE;wBAAQ,WAAW,IAAI;oBAAO;gBAC1C;YACF;YAEA,SAAS,CAAC;gBACR,IAAI,CAAC,WAAW,OAAO,EAAE;gBAEzB,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,SAAS;gBAET,IAAI,SAAS;oBACX,QAAQ;gBACV;YACF;QACF,CAAC,GAAG;QAAC;QAAuB;QAAkB;KAAQ;IAEtD,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW,OAAO,GAAG;QAErB,MAAM,iBAAiB;YACrB,IAAI;gBACF,mCAAmC;gBACnC,MAAM,aAAa,2IAAA,CAAA,wBAAqB,CAAC,aAAa;gBACtD,gBAAgB;gBAEhB,IAAI,YAAY;oBACd,6BAA6B;oBAC7B,MAAM,cAAc,uIAAA,CAAA,oBAAiB,CAAC,mBAAmB;oBACzD,cAAc;gBAChB;gBAEA,+BAA+B;gBAC/B,MAAM,WAAW;gBACjB,YAAY,OAAO,GAAG;gBACtB,2IAAA,CAAA,wBAAqB,CAAC,WAAW,CAAC;gBAElC,oDAAoD;gBACpD,IAAI,uBAAuB;oBACzB,MAAM,mBAAmB;oBACzB,oBAAoB,OAAO,GAAG;oBAC9B,2IAAA,CAAA,wBAAqB,CAAC,mBAAmB,CAAC;gBAC5C;gBAEA,oDAAoD;gBACpD,MAAM,2IAAA,CAAA,wBAAqB,CAAC,UAAU;YAExC,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,gDAAgD;gBAC9D,SAAS;gBACT,kFAAkF;gBAClF,gBAAgB;YAClB;QACF;QAEA;QAEA,UAAU;QACV,OAAO;YACL,WAAW,OAAO,GAAG;YACrB,IAAI,YAAY,OAAO,EAAE;gBACvB,2IAAA,CAAA,wBAAqB,CAAC,cAAc,CAAC,YAAY,OAAO;YAC1D;YACA,IAAI,oBAAoB,OAAO,EAAE;gBAC/B,2IAAA,CAAA,wBAAqB,CAAC,sBAAsB,CAAC,oBAAoB,OAAO;YAC1E;QACF;IACF,GAAG;QAAC;QAAgB;QAAwB;KAAsB;IAElE,+BAA+B;IAC/B,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,6BAA6B;QAC7B,MAAM,OAAO,WAAW,GAAG,CAAC;QAC5B,IAAI,MAAM,OAAO;QAEjB,0BAA0B;QAC1B,IAAI;YACF,OAAO,uIAAA,CAAA,oBAAiB,CAAC,gBAAgB,CAAC;QAC5C,EAAE,OAAO,OAAO;YACd,qDAAqD;YACrD,OAAO,OAAO,OAAO,CAAC,OAAO;QAC/B;IACF,GAAG;QAAC;KAAW;IAEf,eAAe;IACf,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,cAAc;QACd,SAAS;QAET,IAAI;YACF,MAAM,2IAAA,CAAA,wBAAqB,CAAC,WAAW;QACzC,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,MAAM;QACR;IACF,GAAG,EAAE;IAEL,cAAc;IACd,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,SAAS;IACX,GAAG,EAAE;IAEL,OAAO;QACL,SAAS;QACT;QACA;QACA;QACA;QAEA,OAAO;QACP;QAEA,UAAU;QACV;QACA;QACA;IACF;AACF;AAGO,SAAS,0BAA0B,QAA+B;IACvE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB,CAAC;YACzB,SAAS,MAAM,MAAM,CAAC,IAAI;QAC5B;QAEA,OAAO,gBAAgB,CAAC,yBAAyB;QAEjD,OAAO;YACL,OAAO,mBAAmB,CAAC,yBAAyB;QACtD;IACF,GAAG;QAAC;KAAS;AACf;AAGO,SAAS,yBAAyB,QAAiD;IACxF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB,CAAC;YACxB,SAAS,MAAM,MAAM,CAAC,QAAQ;QAChC;QAEA,OAAO,gBAAgB,CAAC,yBAAyB;QAEjD,OAAO;YACL,OAAO,mBAAmB,CAAC,yBAAyB;QACtD;IACF,GAAG;QAAC;KAAS;AACf", "debugId": null}}, {"offset": {"line": 450, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/hooks/useRealTimeStocks.ts"], "sourcesContent": ["// React hook for real-time stock data updates\nimport { useState, useEffect, useCallback, useRef } from 'react';\nimport { useBackgroundData } from './useBackgroundData';\n\ninterface UseRealTimeStocksOptions {\n  onUpdate?: (quotes: any[]) => void;\n  onError?: (error: Error) => void;\n}\n\ninterface UseRealTimeStocksReturn {\n  // Status\n  isActive: boolean;\n  lastUpdate: Date | null;\n  updateCount: number;\n  error: Error | null;\n  \n  // Methods\n  start: () => void;\n  stop: () => void;\n  forceUpdate: () => Promise<void>;\n  clearError: () => void;\n}\n\nexport function useRealTimeStocks(options: UseRealTimeStocksOptions = {}): UseRealTimeStocksReturn {\n  const { onUpdate, onError } = options;\n  \n  // State\n  const [isActive, setIsActive] = useState(false);\n  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);\n  const [updateCount, setUpdateCount] = useState(0);\n  const [error, setError] = useState<Error | null>(null);\n  \n  // Refs\n  const mountedRef = useRef(true);\n  const onUpdateRef = useRef(onUpdate);\n  const onErrorRef = useRef(onError);\n  \n  // Update refs when callbacks change\n  useEffect(() => {\n    onUpdateRef.current = onUpdate;\n    onErrorRef.current = onError;\n  }, [onUpdate, onError]);\n  \n  // Real-time update handler\n  const handleRealTimeUpdate = useCallback((quotes: any[]) => {\n    if (!mountedRef.current) return;\n    \n    setLastUpdate(new Date());\n    setUpdateCount(prev => prev + 1);\n    setError(null);\n    \n    // Call custom callback if provided\n    if (onUpdateRef.current) {\n      onUpdateRef.current(quotes);\n    }\n  }, []);\n  \n  // Error handler\n  const handleError = useCallback((err: Error) => {\n    if (!mountedRef.current) return;\n    \n    setError(err);\n    \n    if (onErrorRef.current) {\n      onErrorRef.current(err);\n    }\n  }, []);\n  \n  // Background data hook with real-time enabled\n  const { forceUpdate: forceBackgroundUpdate } = useBackgroundData({\n    enableRealTimeUpdates: isActive,\n    onRealTimeUpdate: handleRealTimeUpdate,\n    onError: handleError\n  });\n  \n  // Start real-time updates\n  const start = useCallback(() => {\n    console.log('🚀 Starting real-time stock updates...');\n    setIsActive(true);\n    setError(null);\n  }, []);\n  \n  // Stop real-time updates\n  const stop = useCallback(() => {\n    console.log('⏹️ Stopping real-time stock updates...');\n    setIsActive(false);\n  }, []);\n  \n  // Force immediate update\n  const forceUpdate = useCallback(async () => {\n    try {\n      await forceBackgroundUpdate();\n    } catch (err) {\n      handleError(err as Error);\n    }\n  }, [forceBackgroundUpdate, handleError]);\n  \n  // Clear error\n  const clearError = useCallback(() => {\n    setError(null);\n  }, []);\n  \n  // Cleanup on unmount\n  useEffect(() => {\n    mountedRef.current = true;\n    \n    return () => {\n      mountedRef.current = false;\n    };\n  }, []);\n  \n  return {\n    isActive,\n    lastUpdate,\n    updateCount,\n    error,\n    start,\n    stop,\n    forceUpdate,\n    clearError\n  };\n}\n\n// Hook for listening to real-time updates via custom events\nexport function useRealTimeUpdates(callback: (quotes: any[]) => void) {\n  const callbackRef = useRef(callback);\n  \n  // Update callback ref\n  useEffect(() => {\n    callbackRef.current = callback;\n  }, [callback]);\n  \n  useEffect(() => {\n    const handleRealTimeUpdate = (event: CustomEvent) => {\n      const { quotes } = event.detail;\n      callbackRef.current(quotes);\n    };\n    \n    window.addEventListener('realTimePriceUpdate', handleRealTimeUpdate as EventListener);\n    \n    return () => {\n      window.removeEventListener('realTimePriceUpdate', handleRealTimeUpdate as EventListener);\n    };\n  }, []);\n}\n\n// Hook for seamless stock data with real-time updates\nexport function useSeamlessStockData() {\n  const [stocks, setStocks] = useState<any[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  \n  // Background data for names\n  const { isNamesReady, getStockName } = useBackgroundData();\n  \n  // Real-time updates for prices\n  const realTimeStocks = useRealTimeStocks({\n    onUpdate: (quotes) => {\n      // Update stocks with new price data\n      setStocks(prevStocks => {\n        const updatedStocks = [...prevStocks];\n        \n        quotes.forEach(quote => {\n          const index = updatedStocks.findIndex(stock => stock.symbol === quote.symbol);\n          if (index >= 0) {\n            updatedStocks[index] = { ...updatedStocks[index], ...quote };\n          } else {\n            updatedStocks.push({\n              ...quote,\n              name: getStockName(quote.symbol)\n            });\n          }\n        });\n        \n        return updatedStocks;\n      });\n      \n      setIsLoading(false);\n    }\n  });\n  \n  // Start real-time updates when names are ready\n  useEffect(() => {\n    if (isNamesReady && !realTimeStocks.isActive) {\n      realTimeStocks.start();\n    }\n  }, [isNamesReady, realTimeStocks]);\n  \n  return {\n    stocks,\n    isLoading: isLoading && !isNamesReady,\n    isRealTimeActive: realTimeStocks.isActive,\n    lastUpdate: realTimeStocks.lastUpdate,\n    updateCount: realTimeStocks.updateCount,\n    error: realTimeStocks.error,\n    forceUpdate: realTimeStocks.forceUpdate,\n    getStockName\n  };\n}\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;;;AAC9C;AACA;;;AAqBO,SAAS,kBAAkB,UAAoC,CAAC,CAAC;IACtE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG;IAE9B,QAAQ;IACR,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IAEjD,OAAO;IACP,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE1B,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY,OAAO,GAAG;QACtB,WAAW,OAAO,GAAG;IACvB,GAAG;QAAC;QAAU;KAAQ;IAEtB,2BAA2B;IAC3B,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACxC,IAAI,CAAC,WAAW,OAAO,EAAE;QAEzB,cAAc,IAAI;QAClB,eAAe,CAAA,OAAQ,OAAO;QAC9B,SAAS;QAET,mCAAmC;QACnC,IAAI,YAAY,OAAO,EAAE;YACvB,YAAY,OAAO,CAAC;QACtB;IACF,GAAG,EAAE;IAEL,gBAAgB;IAChB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,IAAI,CAAC,WAAW,OAAO,EAAE;QAEzB,SAAS;QAET,IAAI,WAAW,OAAO,EAAE;YACtB,WAAW,OAAO,CAAC;QACrB;IACF,GAAG,EAAE;IAEL,8CAA8C;IAC9C,MAAM,EAAE,aAAa,qBAAqB,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD,EAAE;QAC/D,uBAAuB;QACvB,kBAAkB;QAClB,SAAS;IACX;IAEA,0BAA0B;IAC1B,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACxB,QAAQ,GAAG,CAAC;QACZ,YAAY;QACZ,SAAS;IACX,GAAG,EAAE;IAEL,yBAAyB;IACzB,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvB,QAAQ,GAAG,CAAC;QACZ,YAAY;IACd,GAAG,EAAE;IAEL,yBAAyB;IACzB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI;YACF,MAAM;QACR,EAAE,OAAO,KAAK;YACZ,YAAY;QACd;IACF,GAAG;QAAC;QAAuB;KAAY;IAEvC,cAAc;IACd,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,SAAS;IACX,GAAG,EAAE;IAEL,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW,OAAO,GAAG;QAErB,OAAO;YACL,WAAW,OAAO,GAAG;QACvB;IACF,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAGO,SAAS,mBAAmB,QAAiC;IAClE,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,sBAAsB;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY,OAAO,GAAG;IACxB,GAAG;QAAC;KAAS;IAEb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,uBAAuB,CAAC;YAC5B,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,MAAM;YAC/B,YAAY,OAAO,CAAC;QACtB;QAEA,OAAO,gBAAgB,CAAC,uBAAuB;QAE/C,OAAO;YACL,OAAO,mBAAmB,CAAC,uBAAuB;QACpD;IACF,GAAG,EAAE;AACP;AAGO,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,4BAA4B;IAC5B,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD;IAEvD,+BAA+B;IAC/B,MAAM,iBAAiB,kBAAkB;QACvC,UAAU,CAAC;YACT,oCAAoC;YACpC,UAAU,CAAA;gBACR,MAAM,gBAAgB;uBAAI;iBAAW;gBAErC,OAAO,OAAO,CAAC,CAAA;oBACb,MAAM,QAAQ,cAAc,SAAS,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK,MAAM,MAAM;oBAC5E,IAAI,SAAS,GAAG;wBACd,aAAa,CAAC,MAAM,GAAG;4BAAE,GAAG,aAAa,CAAC,MAAM;4BAAE,GAAG,KAAK;wBAAC;oBAC7D,OAAO;wBACL,cAAc,IAAI,CAAC;4BACjB,GAAG,KAAK;4BACR,MAAM,aAAa,MAAM,MAAM;wBACjC;oBACF;gBACF;gBAEA,OAAO;YACT;YAEA,aAAa;QACf;IACF;IAEA,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB,CAAC,eAAe,QAAQ,EAAE;YAC5C,eAAe,KAAK;QACtB;IACF,GAAG;QAAC;QAAc;KAAe;IAEjC,OAAO;QACL;QACA,WAAW,aAAa,CAAC;QACzB,kBAAkB,eAAe,QAAQ;QACzC,YAAY,eAAe,UAAU;QACrC,aAAa,eAAe,WAAW;QACvC,OAAO,eAAe,KAAK;QAC3B,aAAa,eAAe,WAAW;QACvC;IACF;AACF", "debugId": null}}, {"offset": {"line": 623, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/hooks/useCentralData.ts"], "sourcesContent": ["// React Hook for Central Data Manager Integration\n// Provides real-time data access and automatic updates for all stock-related pages\n\nimport { useState, useEffect, useCallback, useRef } from 'react';\nimport { NiftyStock } from '@/lib/nifty-stocks';\nimport { WeeklyHighSignal } from '@/lib/weekly-high-signal-detector';\nimport { AutoGTTOrder } from '@/lib/automatic-gtt-service';\n\ntype DataType = 'nifty200' | 'bohEligible' | 'weeklyHighSignals' | 'gttOrders';\n\ninterface DataState<T> {\n  data: T[];\n  lastUpdated: Date | null;\n  isLoading: boolean;\n  error: string | null;\n}\n\ninterface CentralDataHook {\n  nifty200: DataState<NiftyStock>;\n  bohEligible: DataState<NiftyStock>;\n  weeklyHighSignals: DataState<WeeklyHighSignal>;\n  gttOrders: DataState<AutoGTTOrder>;\n  refreshData: (dataType?: DataType) => Promise<void>;\n  isInitialized: boolean;\n  isServiceRunning: boolean;\n}\n\nexport function useCentralData(): CentralDataHook {\n  const [nifty200, setNifty200] = useState<DataState<NiftyStock>>({\n    data: [],\n    lastUpdated: null,\n    isLoading: false,\n    error: null\n  });\n\n  const [bohEligible, setBohEligible] = useState<DataState<NiftyStock>>({\n    data: [],\n    lastUpdated: null,\n    isLoading: false,\n    error: null\n  });\n\n  const [weeklyHighSignals, setWeeklyHighSignals] = useState<DataState<WeeklyHighSignal>>({\n    data: [],\n    lastUpdated: null,\n    isLoading: false,\n    error: null\n  });\n\n  const [gttOrders, setGttOrders] = useState<DataState<AutoGTTOrder>>({\n    data: [],\n    lastUpdated: null,\n    isLoading: false,\n    error: null\n  });\n\n  const [isInitialized, setIsInitialized] = useState(false);\n  const [isServiceRunning, setIsServiceRunning] = useState(false);\n\n  const pollingIntervals = useRef<Map<DataType, NodeJS.Timeout>>(new Map());\n\n  // Fetch data from API\n  const fetchData = useCallback(async (dataType: DataType) => {\n    try {\n      const response = await fetch(`/api/data-manager?action=${dataType === 'bohEligible' ? 'boh-eligible' : dataType === 'weeklyHighSignals' ? 'weekly-high-signals' : dataType === 'gttOrders' ? 'gtt-orders' : dataType}`);\n      const result = await response.json();\n\n      if (result.success) {\n        const newState = {\n          data: result.data || [],\n          lastUpdated: result.lastUpdated ? new Date(result.lastUpdated) : new Date(),\n          isLoading: result.isLoading || false,\n          error: null\n        };\n\n        switch (dataType) {\n          case 'nifty200':\n            setNifty200(newState);\n            break;\n          case 'bohEligible':\n            setBohEligible(newState);\n            break;\n          case 'weeklyHighSignals':\n            setWeeklyHighSignals(newState);\n            break;\n          case 'gttOrders':\n            setGttOrders(newState);\n            break;\n        }\n\n        console.log(`📊 Updated ${dataType}: ${result.data?.length || 0} items`);\n      } else {\n        throw new Error(result.error || 'Failed to fetch data');\n      }\n    } catch (error) {\n      console.error(`❌ Error fetching ${dataType}:`, error);\n      \n      const errorState = {\n        data: [],\n        lastUpdated: null,\n        isLoading: false,\n        error: error instanceof Error ? error.message : 'Unknown error'\n      };\n\n      switch (dataType) {\n        case 'nifty200':\n          setNifty200(prev => ({ ...prev, ...errorState }));\n          break;\n        case 'bohEligible':\n          setBohEligible(prev => ({ ...prev, ...errorState }));\n          break;\n        case 'weeklyHighSignals':\n          setWeeklyHighSignals(prev => ({ ...prev, ...errorState }));\n          break;\n        case 'gttOrders':\n          setGttOrders(prev => ({ ...prev, ...errorState }));\n          break;\n      }\n    }\n  }, []);\n\n  // Check service status\n  const checkServiceStatus = useCallback(async () => {\n    try {\n      const response = await fetch('/api/data-manager?action=status');\n      const result = await response.json();\n\n      if (result.success) {\n        setIsInitialized(result.data.isInitialized);\n        setIsServiceRunning(result.data.isRunning);\n      }\n    } catch (error) {\n      console.error('❌ Error checking service status:', error);\n    }\n  }, []);\n\n  // Initialize service if not already initialized\n  const initializeService = useCallback(async () => {\n    try {\n      console.log('🚀 Initializing Central Data Manager...');\n      \n      const response = await fetch('/api/data-manager', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ action: 'initialize' })\n      });\n\n      const result = await response.json();\n      \n      if (result.success) {\n        console.log('✅ Central Data Manager initialized');\n        setIsInitialized(true);\n        \n        // Start the service\n        const startResponse = await fetch('/api/data-manager', {\n          method: 'POST',\n          headers: { 'Content-Type': 'application/json' },\n          body: JSON.stringify({ action: 'start' })\n        });\n\n        const startResult = await startResponse.json();\n        if (startResult.success) {\n          setIsServiceRunning(true);\n          console.log('✅ Central Data Manager started');\n        }\n      }\n    } catch (error) {\n      console.error('❌ Error initializing service:', error);\n    }\n  }, []);\n\n  // Refresh specific data type\n  const refreshData = useCallback(async (dataType?: DataType) => {\n    if (dataType) {\n      await fetchData(dataType);\n    } else {\n      // Refresh all data\n      await Promise.all([\n        fetchData('nifty200'),\n        fetchData('bohEligible'),\n        fetchData('weeklyHighSignals'),\n        fetchData('gttOrders')\n      ]);\n    }\n  }, [fetchData]);\n\n  // Set up polling for real-time updates\n  const setupPolling = useCallback(() => {\n    // Clear existing intervals\n    pollingIntervals.current.forEach(interval => clearInterval(interval));\n    pollingIntervals.current.clear();\n\n    // Set up new intervals\n    const intervals = {\n      nifty200: 30000, // 30 seconds\n      bohEligible: 60000, // 1 minute\n      weeklyHighSignals: 300000, // 5 minutes\n      gttOrders: 30000 // 30 seconds\n    };\n\n    Object.entries(intervals).forEach(([dataType, interval]) => {\n      const intervalId = setInterval(() => {\n        fetchData(dataType as DataType);\n      }, interval);\n      \n      pollingIntervals.current.set(dataType as DataType, intervalId);\n    });\n\n    console.log('⏰ Polling intervals set up for real-time updates');\n  }, [fetchData]);\n\n  // Initialize on mount\n  useEffect(() => {\n    const initialize = async () => {\n      // Check if service is already running\n      await checkServiceStatus();\n      \n      // Initialize service if needed\n      if (!isInitialized) {\n        await initializeService();\n      }\n\n      // Load initial data\n      await refreshData();\n\n      // Set up polling for real-time updates\n      setupPolling();\n    };\n\n    initialize();\n\n    // Cleanup on unmount\n    return () => {\n      pollingIntervals.current.forEach(interval => clearInterval(interval));\n      pollingIntervals.current.clear();\n    };\n  }, []);\n\n  // Re-setup polling when service status changes\n  useEffect(() => {\n    if (isServiceRunning) {\n      setupPolling();\n    }\n  }, [isServiceRunning, setupPolling]);\n\n  return {\n    nifty200,\n    bohEligible,\n    weeklyHighSignals,\n    gttOrders,\n    refreshData,\n    isInitialized,\n    isServiceRunning\n  };\n}\n\n// Specialized hooks for individual data types\nexport function useNifty200Stocks() {\n  const { nifty200, refreshData } = useCentralData();\n  \n  return {\n    stocks: nifty200.data,\n    lastUpdated: nifty200.lastUpdated,\n    isLoading: nifty200.isLoading,\n    error: nifty200.error,\n    refresh: () => refreshData('nifty200')\n  };\n}\n\nexport function useBOHEligibleStocks() {\n  const { bohEligible, refreshData } = useCentralData();\n  \n  return {\n    stocks: bohEligible.data,\n    lastUpdated: bohEligible.lastUpdated,\n    isLoading: bohEligible.isLoading,\n    error: bohEligible.error,\n    refresh: () => refreshData('bohEligible')\n  };\n}\n\nexport function useWeeklyHighSignals() {\n  const { weeklyHighSignals, refreshData } = useCentralData();\n  \n  return {\n    signals: weeklyHighSignals.data,\n    lastUpdated: weeklyHighSignals.lastUpdated,\n    isLoading: weeklyHighSignals.isLoading,\n    error: weeklyHighSignals.error,\n    refresh: () => refreshData('weeklyHighSignals')\n  };\n}\n\nexport function useGTTOrders() {\n  const { gttOrders, refreshData } = useCentralData();\n  \n  return {\n    orders: gttOrders.data,\n    lastUpdated: gttOrders.lastUpdated,\n    isLoading: gttOrders.isLoading,\n    error: gttOrders.error,\n    refresh: () => refreshData('gttOrders')\n  };\n}\n"], "names": [], "mappings": "AAAA,kDAAkD;AAClD,mFAAmF;;;;;;;;AAEnF;;AAwBO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;QAC9D,MAAM,EAAE;QACR,aAAa;QACb,WAAW;QACX,OAAO;IACT;IAEA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;QACpE,MAAM,EAAE;QACR,aAAa;QACb,WAAW;QACX,OAAO;IACT;IAEA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+B;QACtF,MAAM,EAAE;QACR,aAAa;QACb,WAAW;QACX,OAAO;IACT;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;QAClE,MAAM,EAAE;QACR,aAAa;QACb,WAAW;QACX,OAAO;IACT;IAEA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAiC,IAAI;IAEnE,sBAAsB;IACtB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACnC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,yBAAyB,EAAE,aAAa,gBAAgB,iBAAiB,aAAa,sBAAsB,wBAAwB,aAAa,cAAc,eAAe,UAAU;YACtN,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,WAAW;oBACf,MAAM,OAAO,IAAI,IAAI,EAAE;oBACvB,aAAa,OAAO,WAAW,GAAG,IAAI,KAAK,OAAO,WAAW,IAAI,IAAI;oBACrE,WAAW,OAAO,SAAS,IAAI;oBAC/B,OAAO;gBACT;gBAEA,OAAQ;oBACN,KAAK;wBACH,YAAY;wBACZ;oBACF,KAAK;wBACH,eAAe;wBACf;oBACF,KAAK;wBACH,qBAAqB;wBACrB;oBACF,KAAK;wBACH,aAAa;wBACb;gBACJ;gBAEA,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE,OAAO,IAAI,EAAE,UAAU,EAAE,MAAM,CAAC;YACzE,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,iBAAiB,EAAE,SAAS,CAAC,CAAC,EAAE;YAE/C,MAAM,aAAa;gBACjB,MAAM,EAAE;gBACR,aAAa;gBACb,WAAW;gBACX,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;YAEA,OAAQ;gBACN,KAAK;oBACH,YAAY,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,GAAG,UAAU;wBAAC,CAAC;oBAC/C;gBACF,KAAK;oBACH,eAAe,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,GAAG,UAAU;wBAAC,CAAC;oBAClD;gBACF,KAAK;oBACH,qBAAqB,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,GAAG,UAAU;wBAAC,CAAC;oBACxD;gBACF,KAAK;oBACH,aAAa,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,GAAG,UAAU;wBAAC,CAAC;oBAChD;YACJ;QACF;IACF,GAAG,EAAE;IAEL,uBAAuB;IACvB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,iBAAiB,OAAO,IAAI,CAAC,aAAa;gBAC1C,oBAAoB,OAAO,IAAI,CAAC,SAAS;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF,GAAG,EAAE;IAEL,gDAAgD;IAChD,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,QAAQ;gBAAa;YAC9C;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,QAAQ,GAAG,CAAC;gBACZ,iBAAiB;gBAEjB,oBAAoB;gBACpB,MAAM,gBAAgB,MAAM,MAAM,qBAAqB;oBACrD,QAAQ;oBACR,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,MAAM,KAAK,SAAS,CAAC;wBAAE,QAAQ;oBAAQ;gBACzC;gBAEA,MAAM,cAAc,MAAM,cAAc,IAAI;gBAC5C,IAAI,YAAY,OAAO,EAAE;oBACvB,oBAAoB;oBACpB,QAAQ,GAAG,CAAC;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD;IACF,GAAG,EAAE;IAEL,6BAA6B;IAC7B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrC,IAAI,UAAU;YACZ,MAAM,UAAU;QAClB,OAAO;YACL,mBAAmB;YACnB,MAAM,QAAQ,GAAG,CAAC;gBAChB,UAAU;gBACV,UAAU;gBACV,UAAU;gBACV,UAAU;aACX;QACH;IACF,GAAG;QAAC;KAAU;IAEd,uCAAuC;IACvC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,2BAA2B;QAC3B,iBAAiB,OAAO,CAAC,OAAO,CAAC,CAAA,WAAY,cAAc;QAC3D,iBAAiB,OAAO,CAAC,KAAK;QAE9B,uBAAuB;QACvB,MAAM,YAAY;YAChB,UAAU;YACV,aAAa;YACb,mBAAmB;YACnB,WAAW,MAAM,aAAa;QAChC;QAEA,OAAO,OAAO,CAAC,WAAW,OAAO,CAAC,CAAC,CAAC,UAAU,SAAS;YACrD,MAAM,aAAa,YAAY;gBAC7B,UAAU;YACZ,GAAG;YAEH,iBAAiB,OAAO,CAAC,GAAG,CAAC,UAAsB;QACrD;QAEA,QAAQ,GAAG,CAAC;IACd,GAAG;QAAC;KAAU;IAEd,sBAAsB;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa;YACjB,sCAAsC;YACtC,MAAM;YAEN,+BAA+B;YAC/B,IAAI,CAAC,eAAe;gBAClB,MAAM;YACR;YAEA,oBAAoB;YACpB,MAAM;YAEN,uCAAuC;YACvC;QACF;QAEA;QAEA,qBAAqB;QACrB,OAAO;YACL,iBAAiB,OAAO,CAAC,OAAO,CAAC,CAAA,WAAY,cAAc;YAC3D,iBAAiB,OAAO,CAAC,KAAK;QAChC;IACF,GAAG,EAAE;IAEL,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,kBAAkB;YACpB;QACF;IACF,GAAG;QAAC;QAAkB;KAAa;IAEnC,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAGO,SAAS;IACd,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG;IAElC,OAAO;QACL,QAAQ,SAAS,IAAI;QACrB,aAAa,SAAS,WAAW;QACjC,WAAW,SAAS,SAAS;QAC7B,OAAO,SAAS,KAAK;QACrB,SAAS,IAAM,YAAY;IAC7B;AACF;AAEO,SAAS;IACd,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG;IAErC,OAAO;QACL,QAAQ,YAAY,IAAI;QACxB,aAAa,YAAY,WAAW;QACpC,WAAW,YAAY,SAAS;QAChC,OAAO,YAAY,KAAK;QACxB,SAAS,IAAM,YAAY;IAC7B;AACF;AAEO,SAAS;IACd,MAAM,EAAE,iBAAiB,EAAE,WAAW,EAAE,GAAG;IAE3C,OAAO;QACL,SAAS,kBAAkB,IAAI;QAC/B,aAAa,kBAAkB,WAAW;QAC1C,WAAW,kBAAkB,SAAS;QACtC,OAAO,kBAAkB,KAAK;QAC9B,SAAS,IAAM,YAAY;IAC7B;AACF;AAEO,SAAS;IACd,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG;IAEnC,OAAO;QACL,QAAQ,UAAU,IAAI;QACtB,aAAa,UAAU,WAAW;QAClC,WAAW,UAAU,SAAS;QAC9B,OAAO,UAAU,KAAK;QACtB,SAAS,IAAM,YAAY;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 900, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/app/dashboard/stocks/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { \n  Search, \n  RefreshCw,\n  Filter,\n  AlertCircle,\n  Loader\n} from 'lucide-react';\nimport { formatCurrency, formatPercentage, getChangeColor } from '@/lib/utils';\nimport { NiftyStock } from '@/lib/nifty-stocks';\nimport { cacheService, CacheKeys } from '@/lib/cache-service';\nimport { StockListSkeleton, InlineLoading } from '@/components/ui/LoadingStates';\nimport { RealTimeIndicator } from '@/components/ui/RealTimeIndicator';\nimport { useBackgroundData } from '@/hooks/useBackgroundData';\nimport { useRealTimeStocks, useRealTimeUpdates } from '@/hooks/useRealTimeStocks';\nimport { useNifty200Stocks } from '@/hooks/useCentralData';\n\nexport default function StockUniversalPage() {\n  // Central Data Manager for instant loading and real-time updates\n  const {\n    stocks: centralStocks,\n    lastUpdated: centralLastUpdated,\n    isLoading: centralIsLoading,\n    error: centralError,\n    refresh: refreshCentralData\n  } = useNifty200Stocks();\n\n  // Background data hook for stock names (fallback)\n  const {\n    isNamesReady,\n    getStockName,\n    forceUpdate: forceBackgroundUpdate,\n    error: backgroundError\n  } = useBackgroundData();\n\n  // Search state\n  const [searchQuery, setSearchQuery] = useState('');\n  const [searchResults, setSearchResults] = useState<NiftyStock[]>([]);\n  const [isSearching, setIsSearching] = useState(false);\n\n  // Nifty 200 stocks state - now loads instantly with cached names\n  const [niftyStocks, setNiftyStocks] = useState<NiftyStock[]>([]);\n  const [loadingPriceData, setLoadingPriceData] = useState(false);\n  const [niftyError, setNiftyError] = useState<string | null>(null);\n\n  // Filter state\n  const [showFilters, setShowFilters] = useState(false);\n  const [priceFilter, setPriceFilter] = useState({ min: 0, max: 10000 });\n  const [showOnlyHoldings, setShowOnlyHoldings] = useState(false);\n  const [showAbove2000, setShowAbove2000] = useState(false);\n\n  // Use Central Data Manager for instant data loading\n  useEffect(() => {\n    if (centralStocks.length > 0) {\n      setNiftyStocks(centralStocks);\n      setLoadingPriceData(false);\n      setNiftyError(null);\n      console.log(`📊 Loaded ${centralStocks.length} stocks from Central Data Manager`);\n    } else if (centralError) {\n      setNiftyError(centralError);\n      setLoadingPriceData(false);\n    } else if (centralIsLoading) {\n      setLoadingPriceData(true);\n    }\n  }, [centralStocks, centralError, centralIsLoading]);\n\n  // Legacy load price data function (now uses Central Data Manager)\n  const loadPriceData = async (forceRefresh = false) => {\n    if (forceRefresh) {\n      console.log('🔄 Force refreshing data via Central Data Manager...');\n      await refreshCentralData();\n      return;\n    }\n\n    // If Central Data Manager has data, use it immediately\n    if (centralStocks.length > 0) {\n      setNiftyStocks(centralStocks);\n      setLoadingPriceData(false);\n      return;\n    }\n\n    // Fallback to original loading logic if Central Data Manager is not ready\n    setLoadingPriceData(true);\n    setNiftyError(null);\n\n    try {\n\n      const batchSize = 25;\n      const totalBatches = Math.ceil(200 / batchSize);\n      const allStocks: NiftyStock[] = [];\n\n      console.log(`🚀 Loading price data for ${totalBatches} batches${forceRefresh ? ' (force refresh)' : ''}...`);\n\n      for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {\n        try {\n          let batchData;\n          if (!forceRefresh) {\n            const cacheKey = CacheKeys.niftyStocks(batchIndex);\n            const cached = cacheService.get<any>(cacheKey);\n            if (cached) {\n              batchData = cached;\n            }\n          }\n\n          if (!batchData) {\n            try {\n              // Pass forceRefresh parameter to API to control name caching\n              const url = `/api/stocks/nifty200?batchIndex=${batchIndex}&batchSize=${batchSize}${forceRefresh ? '&forceRefresh=true' : ''}`;\n              console.log(`🔄 Fetching batch ${batchIndex + 1}/${totalBatches}: ${url}`);\n\n              const response = await fetch(url, {\n                method: 'GET',\n                headers: {\n                  'Content-Type': 'application/json',\n                },\n                // Add timeout to prevent hanging\n                signal: AbortSignal.timeout(30000) // 30 second timeout\n              });\n\n              if (!response.ok) {\n                throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n              }\n\n              const data = await response.json();\n\n              if (data.success) {\n                batchData = data.data;\n\n                // Cache the batch data (but names are cached separately in the service)\n                if (!forceRefresh) {\n                  const cacheKey = CacheKeys.niftyStocks(batchIndex);\n                  cacheService.set(cacheKey, batchData);\n                }\n\n                console.log(`✅ Successfully fetched batch ${batchIndex + 1}: ${batchData.stocks?.length || 0} stocks`);\n              } else {\n                console.error(`❌ API returned error for batch ${batchIndex}:`, data.error);\n                continue;\n              }\n            } catch (fetchError) {\n              console.error(`❌ Network error fetching batch ${batchIndex}:`, fetchError);\n              // Continue with next batch instead of failing completely\n              continue;\n            }\n          }\n\n          if (batchData && batchData.stocks) {\n            // Use cached names from background service for instant display\n            const stocksWithCachedNames = batchData.stocks.map((stock: NiftyStock) => ({\n              ...stock,\n              name: getStockName(stock.symbol) || stock.name\n            }));\n\n            allStocks.push(...stocksWithCachedNames);\n            setNiftyStocks([...allStocks]);\n            console.log(`✅ Loaded batch ${batchIndex + 1}/${totalBatches}: ${stocksWithCachedNames.length} stocks (Total: ${allStocks.length})`);\n          }\n\n          if (batchIndex < totalBatches - 1) {\n            await new Promise(resolve => setTimeout(resolve, 200));\n          }\n\n        } catch (batchError) {\n          console.error(`Error loading batch ${batchIndex}:`, batchError);\n        }\n      }\n\n      setNiftyStocks(allStocks);\n      console.log(`🎉 Loaded all price data: ${allStocks.length} total stocks`);\n\n    } catch (error) {\n      console.error('Error loading price data:', error);\n      setNiftyError('Failed to load stock price data');\n    } finally {\n      setLoadingPriceData(false);\n    }\n  };\n\n  // Search within Nifty 200 stocks\n  const handleSearch = async (query: string) => {\n    if (!query.trim()) {\n      setSearchResults([]);\n      return;\n    }\n\n    setIsSearching(true);\n    try {\n      const response = await fetch('/api/stocks/nifty200', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ searchQuery: query }),\n      });\n      \n      const data = await response.json();\n      if (data.success) {\n        setSearchResults(data.data.stocks);\n      } else {\n        setSearchResults([]);\n      }\n    } catch (error) {\n      console.error('Search error:', error);\n      setSearchResults([]);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n\n  // Filter stocks based on current filters\n  const getFilteredStocks = (stocks: NiftyStock[]) => {\n    return stocks.filter(stock => {\n      // General filters\n      if (stock.price < priceFilter.min || stock.price > priceFilter.max) return false;\n      if (showOnlyHoldings && !stock.inHoldings) return false;\n      if (showAbove2000 && stock.price <= 2000) return false;\n      return true;\n    });\n  };\n\n  // Load initial data when names are ready\n  useEffect(() => {\n    if (isNamesReady) {\n      loadPriceData(false);\n    }\n  }, [isNamesReady]);\n\n  // Handle search with debouncing\n  useEffect(() => {\n    const debounceTimer = setTimeout(() => {\n      handleSearch(searchQuery);\n    }, 300);\n    return () => clearTimeout(debounceTimer);\n  }, [searchQuery]);\n\n  // Real-time stock updates\n  const realTimeStocks = useRealTimeStocks({\n    onUpdate: (quotes) => {\n      console.log(`⚡ Received real-time update: ${quotes.length} quotes`);\n\n      // Update existing stocks with new price data\n      setNiftyStocks(currentStocks => {\n        const updatedStocks = currentStocks.map(stock => {\n          const updatedQuote = quotes.find(quote => quote.symbol === stock.symbol);\n          if (updatedQuote) {\n            return {\n              ...stock,\n              price: updatedQuote.price || stock.price,\n              change: updatedQuote.change || stock.change,\n              changePercent: updatedQuote.changePercent || stock.changePercent,\n              volume: updatedQuote.volume || stock.volume,\n              high52Week: updatedQuote.high52Week || stock.high52Week,\n              low52Week: updatedQuote.low52Week || stock.low52Week,\n              high52WeekDate: updatedQuote.high52WeekDate || stock.high52WeekDate,\n              low52WeekDate: updatedQuote.low52WeekDate || stock.low52WeekDate,\n              isBOHEligible: updatedQuote.isBOHEligible || stock.isBOHEligible\n            };\n          }\n          return stock;\n        });\n\n        return updatedStocks;\n      });\n    },\n    onError: (error) => {\n      console.error('❌ Real-time update error:', error);\n      setNiftyError('Real-time updates temporarily unavailable');\n    }\n  });\n\n  // Start real-time updates when names are ready\n  useEffect(() => {\n    if (isNamesReady && !realTimeStocks.isActive) {\n      console.log('🚀 Starting real-time updates for Stock Universal page');\n      realTimeStocks.start();\n    }\n  }, [isNamesReady, realTimeStocks]);\n\n  // Stock row component for All Stocks tab\n  const StockRow = ({ stock }: { stock: NiftyStock }) => (\n    <div className=\"flex items-center justify-between p-4 hover:bg-gray-50 border-b border-gray-100\">\n      <div className=\"flex-1\">\n        <div className=\"flex items-center space-x-3\">\n          <div>\n            <div className=\"flex items-center space-x-2\">\n              <h4 className=\"font-medium text-gray-900\">{stock.symbol}</h4>\n              {stock.inHoldings && (\n                <span className=\"px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full\">\n                  In Holdings\n                </span>\n              )}\n              {stock.price >= 2000 && !stock.inHoldings && (\n                <span className=\"px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full\">\n                  Above ₹2000\n                </span>\n              )}\n            </div>\n            <p className=\"text-sm text-gray-600 truncate max-w-xs\">{stock.name}</p>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"flex items-center space-x-6\">\n        <div className=\"text-right\">\n          <div className=\"font-semibold text-gray-900\">\n            {formatCurrency(stock.price)}\n          </div>\n          <div className={`text-sm ${getChangeColor(stock.change)}`}>\n            {stock.change >= 0 ? '+' : ''}{formatCurrency(stock.change)} ({formatPercentage(stock.changePercent)})\n          </div>\n        </div>\n\n        <div className=\"text-right text-sm text-gray-600\">\n          <div>Vol: {stock.volume?.toLocaleString() || 'N/A'}</div>\n          <div className=\"text-xs\">\n            {stock.marketCap ? `₹${(stock.marketCap / 10000000).toFixed(0)}Cr` : 'N/A'}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Page Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Stock Universal - Nifty 200</h1>\n          <p className=\"text-gray-600 mt-1\">\n            All Nifty 200 stocks with real-time prices • {getFilteredStocks(niftyStocks).length} eligible stocks\n            {loadingPriceData && niftyStocks.length > 0 && (\n              <span className=\"text-blue-600\"> (Loading {niftyStocks.length}/200...)</span>\n            )}\n          </p>\n          <div className=\"mt-2\">\n            <RealTimeIndicator\n              isActive={realTimeStocks.isActive}\n              lastUpdate={realTimeStocks.lastUpdate}\n              updateCount={realTimeStocks.updateCount}\n              error={realTimeStocks.error}\n            />\n          </div>\n        </div>\n        <div className=\"flex space-x-3\">\n          <button \n            onClick={() => setShowFilters(!showFilters)}\n            className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2\"\n          >\n            <Filter className=\"h-4 w-4\" />\n            <span>Filters</span>\n          </button>\n          <button\n            onClick={() => loadPriceData(true)}\n            disabled={loadingPriceData}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center space-x-2\"\n          >\n            <RefreshCw className={`h-4 w-4 ${loadingPriceData ? 'animate-spin' : ''}`} />\n            <span>Refresh All</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Search */}\n      <div className=\"relative\">\n        <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n        <input\n          type=\"text\"\n          placeholder=\"Search stocks by symbol or name...\"\n          value={searchQuery}\n          onChange={(e) => setSearchQuery(e.target.value)}\n          className=\"w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n        />\n        {isSearching && (\n          <div className=\"absolute right-3 top-1/2 transform -translate-y-1/2\">\n            <RefreshCw className=\"h-5 w-5 text-gray-400 animate-spin\" />\n          </div>\n        )}\n      </div>\n\n      {/* Filters Panel */}\n      {showFilters && (\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Filter Options</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">Price Range</label>\n              <div className=\"flex space-x-2\">\n                <input\n                  type=\"number\"\n                  placeholder=\"Min\"\n                  value={priceFilter.min}\n                  onChange={(e) => setPriceFilter(prev => ({ ...prev, min: Number(e.target.value) }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                />\n                <input\n                  type=\"number\"\n                  placeholder=\"Max\"\n                  value={priceFilter.max}\n                  onChange={(e) => setPriceFilter(prev => ({ ...prev, max: Number(e.target.value) }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                />\n              </div>\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">Stock Filters</label>\n              <div className=\"space-y-2\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={showOnlyHoldings}\n                    onChange={(e) => setShowOnlyHoldings(e.target.checked)}\n                    className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-600\">Show only stocks in holdings</span>\n                </label>\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={showAbove2000}\n                    onChange={(e) => setShowAbove2000(e.target.checked)}\n                    className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-600\">Show stocks above ₹2000</span>\n                </label>\n              </div>\n            </div>\n            \n            <div className=\"flex items-end\">\n              <button\n                onClick={() => {\n                  setPriceFilter({ min: 0, max: 10000 });\n                  setShowOnlyHoldings(false);\n                  setShowAbove2000(false);\n                }}\n                className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n              >\n                Reset Filters\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Error Display */}\n      {niftyError && (\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <AlertCircle className=\"h-5 w-5 text-red-600 mr-2\" />\n            <span className=\"text-red-800\">{niftyError}</span>\n          </div>\n        </div>\n      )}\n\n      {/* Search Results */}\n      {searchQuery && searchResults.length > 0 && (\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 mb-6\">\n          <div className=\"p-4 border-b border-gray-200\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">\n              Search Results for &quot;{searchQuery}&quot; ({searchResults.length})\n            </h3>\n          </div>\n          <div>\n            {searchResults.map((stock, index) => (\n              <StockRow key={`search-${stock.symbol}-${index}`} stock={stock} />\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Main Stock List */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n        {/* Header */}\n        <div className=\"p-4 border-b border-gray-200\">\n          <h3 className=\"text-lg font-semibold text-gray-900\">\n            All Nifty 200 Stocks ({getFilteredStocks(niftyStocks).length}/{niftyStocks.length})\n          </h3>\n          <p className=\"text-sm text-gray-600 mt-1\">\n            Real-time prices from Yahoo Finance API\n          </p>\n        </div>\n\n        <div>\n          {!isNamesReady ? (\n            <div className=\"p-8 text-center text-gray-500\">\n              <Loader className=\"h-8 w-8 mx-auto mb-4 animate-spin text-blue-600\" />\n              <p>Initializing stock data...</p>\n              <p className=\"text-sm mt-1\">Loading stock names for the first time</p>\n            </div>\n          ) : loadingPriceData && niftyStocks.length === 0 ? (\n            <StockListSkeleton count={10} />\n          ) : (\n            <>\n              {getFilteredStocks(niftyStocks).map((stock, index) => (\n                <StockRow key={`nifty-${stock.symbol}-${index}`} stock={stock} />\n              ))}\n\n              {loadingPriceData && niftyStocks.length > 0 && (\n                <div className=\"p-4 border-t border-gray-100 bg-blue-50\">\n                  <div className=\"flex items-center justify-center space-x-2\">\n                    <Loader className=\"h-4 w-4 animate-spin text-blue-600\" />\n                    <span className=\"text-blue-700 text-sm\">\n                      Updating price data... ({niftyStocks.length}/200)\n                    </span>\n                  </div>\n                </div>\n              )}\n\n              {niftyStocks.length === 0 && !loadingPriceData && isNamesReady && (\n                <div className=\"p-8 text-center text-gray-500\">\n                  <AlertCircle className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n                  <p>No stocks loaded.</p>\n                  <p className=\"text-sm mt-1\">Try refreshing the data.</p>\n                </div>\n              )}\n            </>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAEA;AACA;AACA;AACA;AACA;AACA;AAjBA;;;;;;;;;;;AAmBe,SAAS;IACtB,iEAAiE;IACjE,MAAM,EACJ,QAAQ,aAAa,EACrB,aAAa,kBAAkB,EAC/B,WAAW,gBAAgB,EAC3B,OAAO,YAAY,EACnB,SAAS,kBAAkB,EAC5B,GAAG,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD;IAEpB,kDAAkD;IAClD,MAAM,EACJ,YAAY,EACZ,YAAY,EACZ,aAAa,qBAAqB,EAClC,OAAO,eAAe,EACvB,GAAG,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD;IAEpB,eAAe;IACf,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,iEAAiE;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE5D,eAAe;IACf,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,KAAK;QAAG,KAAK;IAAM;IACpE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,oDAAoD;IACpD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,eAAe;YACf,oBAAoB;YACpB,cAAc;YACd,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,cAAc,MAAM,CAAC,iCAAiC,CAAC;QAClF,OAAO,IAAI,cAAc;YACvB,cAAc;YACd,oBAAoB;QACtB,OAAO,IAAI,kBAAkB;YAC3B,oBAAoB;QACtB;IACF,GAAG;QAAC;QAAe;QAAc;KAAiB;IAElD,kEAAkE;IAClE,MAAM,gBAAgB,OAAO,eAAe,KAAK;QAC/C,IAAI,cAAc;YAChB,QAAQ,GAAG,CAAC;YACZ,MAAM;YACN;QACF;QAEA,uDAAuD;QACvD,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,eAAe;YACf,oBAAoB;YACpB;QACF;QAEA,0EAA0E;QAC1E,oBAAoB;QACpB,cAAc;QAEd,IAAI;YAEF,MAAM,YAAY;YAClB,MAAM,eAAe,KAAK,IAAI,CAAC,MAAM;YACrC,MAAM,YAA0B,EAAE;YAElC,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,aAAa,QAAQ,EAAE,eAAe,qBAAqB,GAAG,GAAG,CAAC;YAE3G,IAAK,IAAI,aAAa,GAAG,aAAa,cAAc,aAAc;gBAChE,IAAI;oBACF,IAAI;oBACJ,IAAI,CAAC,cAAc;wBACjB,MAAM,WAAW,8HAAA,CAAA,YAAS,CAAC,WAAW,CAAC;wBACvC,MAAM,SAAS,8HAAA,CAAA,eAAY,CAAC,GAAG,CAAM;wBACrC,IAAI,QAAQ;4BACV,YAAY;wBACd;oBACF;oBAEA,IAAI,CAAC,WAAW;wBACd,IAAI;4BACF,6DAA6D;4BAC7D,MAAM,MAAM,CAAC,gCAAgC,EAAE,WAAW,WAAW,EAAE,YAAY,eAAe,uBAAuB,IAAI;4BAC7H,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,aAAa,EAAE,CAAC,EAAE,aAAa,EAAE,EAAE,KAAK;4BAEzE,MAAM,WAAW,MAAM,MAAM,KAAK;gCAChC,QAAQ;gCACR,SAAS;oCACP,gBAAgB;gCAClB;gCACA,iCAAiC;gCACjC,QAAQ,YAAY,OAAO,CAAC,OAAO,oBAAoB;4BACzD;4BAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gCAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;4BACnE;4BAEA,MAAM,OAAO,MAAM,SAAS,IAAI;4BAEhC,IAAI,KAAK,OAAO,EAAE;gCAChB,YAAY,KAAK,IAAI;gCAErB,wEAAwE;gCACxE,IAAI,CAAC,cAAc;oCACjB,MAAM,WAAW,8HAAA,CAAA,YAAS,CAAC,WAAW,CAAC;oCACvC,8HAAA,CAAA,eAAY,CAAC,GAAG,CAAC,UAAU;gCAC7B;gCAEA,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,aAAa,EAAE,EAAE,EAAE,UAAU,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC;4BACvG,OAAO;gCACL,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,WAAW,CAAC,CAAC,EAAE,KAAK,KAAK;gCACzE;4BACF;wBACF,EAAE,OAAO,YAAY;4BACnB,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,WAAW,CAAC,CAAC,EAAE;4BAE/D;wBACF;oBACF;oBAEA,IAAI,aAAa,UAAU,MAAM,EAAE;wBACjC,+DAA+D;wBAC/D,MAAM,wBAAwB,UAAU,MAAM,CAAC,GAAG,CAAC,CAAC,QAAsB,CAAC;gCACzE,GAAG,KAAK;gCACR,MAAM,aAAa,MAAM,MAAM,KAAK,MAAM,IAAI;4BAChD,CAAC;wBAED,UAAU,IAAI,IAAI;wBAClB,eAAe;+BAAI;yBAAU;wBAC7B,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,aAAa,EAAE,CAAC,EAAE,aAAa,EAAE,EAAE,sBAAsB,MAAM,CAAC,gBAAgB,EAAE,UAAU,MAAM,CAAC,CAAC,CAAC;oBACrI;oBAEA,IAAI,aAAa,eAAe,GAAG;wBACjC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACnD;gBAEF,EAAE,OAAO,YAAY;oBACnB,QAAQ,KAAK,CAAC,CAAC,oBAAoB,EAAE,WAAW,CAAC,CAAC,EAAE;gBACtD;YACF;YAEA,eAAe;YACf,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,UAAU,MAAM,CAAC,aAAa,CAAC;QAE1E,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,cAAc;QAChB,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,iCAAiC;IACjC,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,iBAAiB,EAAE;YACnB;QACF;QAEA,eAAe;QACf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,aAAa;gBAAM;YAC5C;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,iBAAiB,KAAK,IAAI,CAAC,MAAM;YACnC,OAAO;gBACL,iBAAiB,EAAE;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,iBAAiB,EAAE;QACrB,SAAU;YACR,eAAe;QACjB;IACF;IAEA,yCAAyC;IACzC,MAAM,oBAAoB,CAAC;QACzB,OAAO,OAAO,MAAM,CAAC,CAAA;YACnB,kBAAkB;YAClB,IAAI,MAAM,KAAK,GAAG,YAAY,GAAG,IAAI,MAAM,KAAK,GAAG,YAAY,GAAG,EAAE,OAAO;YAC3E,IAAI,oBAAoB,CAAC,MAAM,UAAU,EAAE,OAAO;YAClD,IAAI,iBAAiB,MAAM,KAAK,IAAI,MAAM,OAAO;YACjD,OAAO;QACT;IACF;IAEA,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc;YAChB,cAAc;QAChB;IACF,GAAG;QAAC;KAAa;IAEjB,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,WAAW;YAC/B,aAAa;QACf,GAAG;QACH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAY;IAEhB,0BAA0B;IAC1B,MAAM,iBAAiB,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD,EAAE;QACvC,UAAU,CAAC;YACT,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,OAAO,MAAM,CAAC,OAAO,CAAC;YAElE,6CAA6C;YAC7C,eAAe,CAAA;gBACb,MAAM,gBAAgB,cAAc,GAAG,CAAC,CAAA;oBACtC,MAAM,eAAe,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK,MAAM,MAAM;oBACvE,IAAI,cAAc;wBAChB,OAAO;4BACL,GAAG,KAAK;4BACR,OAAO,aAAa,KAAK,IAAI,MAAM,KAAK;4BACxC,QAAQ,aAAa,MAAM,IAAI,MAAM,MAAM;4BAC3C,eAAe,aAAa,aAAa,IAAI,MAAM,aAAa;4BAChE,QAAQ,aAAa,MAAM,IAAI,MAAM,MAAM;4BAC3C,YAAY,aAAa,UAAU,IAAI,MAAM,UAAU;4BACvD,WAAW,aAAa,SAAS,IAAI,MAAM,SAAS;4BACpD,gBAAgB,aAAa,cAAc,IAAI,MAAM,cAAc;4BACnE,eAAe,aAAa,aAAa,IAAI,MAAM,aAAa;4BAChE,eAAe,aAAa,aAAa,IAAI,MAAM,aAAa;wBAClE;oBACF;oBACA,OAAO;gBACT;gBAEA,OAAO;YACT;QACF;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,cAAc;QAChB;IACF;IAEA,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB,CAAC,eAAe,QAAQ,EAAE;YAC5C,QAAQ,GAAG,CAAC;YACZ,eAAe,KAAK;QACtB;IACF,GAAG;QAAC;QAAc;KAAe;IAEjC,yCAAyC;IACzC,MAAM,WAAW,CAAC,EAAE,KAAK,EAAyB,iBAChD,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA6B,MAAM,MAAM;;;;;;wCACtD,MAAM,UAAU,kBACf,8OAAC;4CAAK,WAAU;sDAA6D;;;;;;wCAI9E,MAAM,KAAK,IAAI,QAAQ,CAAC,MAAM,UAAU,kBACvC,8OAAC;4CAAK,WAAU;sDAAyD;;;;;;;;;;;;8CAK7E,8OAAC;oCAAE,WAAU;8CAA2C,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAKxE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,KAAK;;;;;;8CAE7B,8OAAC;oCAAI,WAAW,CAAC,QAAQ,EAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,MAAM,GAAG;;wCACtD,MAAM,MAAM,IAAI,IAAI,MAAM;wCAAI,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,MAAM;wCAAE;wCAAG,CAAA,GAAA,mHAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,aAAa;wCAAE;;;;;;;;;;;;;sCAIzG,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;wCAAI;wCAAM,MAAM,MAAM,EAAE,oBAAoB;;;;;;;8CAC7C,8OAAC;oCAAI,WAAU;8CACZ,MAAM,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,MAAM,SAAS,GAAG,QAAQ,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG;;;;;;;;;;;;;;;;;;;;;;;;IAS/E,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;;oCAAqB;oCACc,kBAAkB,aAAa,MAAM;oCAAC;oCACnF,oBAAoB,YAAY,MAAM,GAAG,mBACxC,8OAAC;wCAAK,WAAU;;4CAAgB;4CAAW,YAAY,MAAM;4CAAC;;;;;;;;;;;;;0CAGlE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6IAAA,CAAA,oBAAiB;oCAChB,UAAU,eAAe,QAAQ;oCACjC,YAAY,eAAe,UAAU;oCACrC,aAAa,eAAe,WAAW;oCACvC,OAAO,eAAe,KAAK;;;;;;;;;;;;;;;;;kCAIjC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,eAAe,CAAC;gCAC/B,WAAU;;kDAEV,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCACC,SAAS,IAAM,cAAc;gCAC7B,UAAU;gCACV,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAW,CAAC,QAAQ,EAAE,mBAAmB,iBAAiB,IAAI;;;;;;kDACzE,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,8OAAC;wBACC,MAAK;wBACL,aAAY;wBACZ,OAAO;wBACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wBAC9C,WAAU;;;;;;oBAEX,6BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;;;;;;;YAM1B,6BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO,YAAY,GAAG;gDACtB,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,KAAK,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAE,CAAC;gDACjF,WAAU;;;;;;0DAEZ,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO,YAAY,GAAG;gDACtB,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,KAAK,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAE,CAAC;gDACjF,WAAU;;;;;;;;;;;;;;;;;;0CAKhB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,SAAS;wDACT,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,OAAO;wDACrD,WAAU;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;0DAE/C,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,SAAS;wDACT,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,OAAO;wDAClD,WAAU;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;;;;;;;;;;;;;0CAKnD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS;wCACP,eAAe;4CAAE,KAAK;4CAAG,KAAK;wCAAM;wCACpC,oBAAoB;wCACpB,iBAAiB;oCACnB;oCACA,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;YASR,4BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,8OAAC;4BAAK,WAAU;sCAAgB;;;;;;;;;;;;;;;;;YAMrC,eAAe,cAAc,MAAM,GAAG,mBACrC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;;gCAAsC;gCACxB;gCAAY;gCAAS,cAAc,MAAM;gCAAC;;;;;;;;;;;;kCAGxE,8OAAC;kCACE,cAAc,GAAG,CAAC,CAAC,OAAO,sBACzB,8OAAC;gCAAiD,OAAO;+BAA1C,CAAC,OAAO,EAAE,MAAM,MAAM,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;;;;;;;0BAOxD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAsC;oCAC3B,kBAAkB,aAAa,MAAM;oCAAC;oCAAE,YAAY,MAAM;oCAAC;;;;;;;0CAEpF,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAK5C,8OAAC;kCACE,CAAC,6BACA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;8CAAE;;;;;;8CACH,8OAAC;oCAAE,WAAU;8CAAe;;;;;;;;;;;mCAE5B,oBAAoB,YAAY,MAAM,KAAK,kBAC7C,8OAAC,yIAAA,CAAA,oBAAiB;4BAAC,OAAO;;;;;iDAE1B;;gCACG,kBAAkB,aAAa,GAAG,CAAC,CAAC,OAAO,sBAC1C,8OAAC;wCAAgD,OAAO;uCAAzC,CAAC,MAAM,EAAE,MAAM,MAAM,CAAC,CAAC,EAAE,OAAO;;;;;gCAGhD,oBAAoB,YAAY,MAAM,GAAG,mBACxC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAK,WAAU;;oDAAwB;oDACb,YAAY,MAAM;oDAAC;;;;;;;;;;;;;;;;;;gCAMnD,YAAY,MAAM,KAAK,KAAK,CAAC,oBAAoB,8BAChD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC;sDAAE;;;;;;sDACH,8OAAC;4CAAE,WAAU;sDAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C", "debugId": null}}]}