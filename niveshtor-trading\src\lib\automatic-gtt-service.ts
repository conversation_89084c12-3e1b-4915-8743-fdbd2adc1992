// Automatic GTT Order Creation Service
// Creates GTT buy orders automatically when new Weekly High Signals are detected

import { weeklyHighSignalDetector, WeeklyHighSignal } from './weekly-high-signal-detector';

export interface AutoGTTOrder {
  id: string;
  symbol: string;
  name: string;
  orderType: 'BUY' | 'SELL';
  triggerPrice: number;
  quantity: number;
  status: 'PENDING' | 'TRIGGERED' | 'CANCELLED' | 'EXPIRED';
  createdAt: Date;
  source: 'SIGNAL' | 'HOLDING' | 'SALE';
  signalStrength?: 'STRONG' | 'MODERATE' | 'WEAK';
  autoCreated: boolean;
  originalSignal?: WeeklyHighSignal;
}

export interface AutoGTTConfig {
  enabled: boolean;
  maxOrdersPerDay: number;
  maxInvestmentPerStock: number;
  investmentPerOrder: number;
  minSignalStrength: 'STRONG' | 'MODERATE' | 'WEAK';
  requireVolumeConfirmation: boolean;
  onlyDuringMarketHours: boolean;
}

class AutomaticGTTService {
  private config: AutoGTTConfig = {
    enabled: true,
    maxOrdersPerDay: 20,
    maxInvestmentPerStock: 10000,
    investmentPerOrder: 2000,
    minSignalStrength: 'MODERATE',
    requireVolumeConfirmation: true,
    onlyDuringMarketHours: true
  };

  private orders: AutoGTTOrder[] = [];
  private dailyOrderCount = 0;
  private lastResetDate = new Date().toDateString();
  private orderListeners = new Set<(order: AutoGTTOrder) => void>();
  private isInitialized = false;
  private periodicCheckInterval: NodeJS.Timeout | null = null;

  constructor() {
    console.log('🤖 Automatic GTT Service initialized');
  }

  // Initialize the service and start listening for signals
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('⚠️ Automatic GTT Service already initialized');
      return;
    }

    console.log('🚀 Initializing Automatic GTT Service...');

    // Load existing orders from localStorage or API
    await this.loadExistingOrders();

    // Start listening for new signals
    weeklyHighSignalDetector.addNewSignalListener(this.handleNewSignal.bind(this));

    // Also listen for all current signals to catch any that don't have orders yet
    weeklyHighSignalDetector.addSignalListener(this.handleAllSignals.bind(this));

    // Reset daily counter if it's a new day
    this.resetDailyCounterIfNeeded();

    // Set up periodic check for missing orders (every 2 minutes)
    this.setupPeriodicOrderCheck();

    this.isInitialized = true;
    console.log('✅ Automatic GTT Service initialized successfully');
  }

  // Configuration management
  updateConfig(newConfig: Partial<AutoGTTConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('⚙️ Auto GTT config updated:', this.config);
  }

  getConfig(): AutoGTTConfig {
    return { ...this.config };
  }

  // Load existing orders (from localStorage for now, can be replaced with API)
  private async loadExistingOrders(): Promise<void> {
    try {
      const stored = localStorage.getItem('autoGTTOrders');
      if (stored) {
        const parsedOrders = JSON.parse(stored);
        this.orders = parsedOrders.map((order: any) => ({
          ...order,
          createdAt: new Date(order.createdAt)
        }));
        console.log(`📂 Loaded ${this.orders.length} existing auto GTT orders`);
      }

      // Load daily counter
      const storedCounter = localStorage.getItem('autoGTTDailyCount');
      const storedDate = localStorage.getItem('autoGTTLastResetDate');
      
      if (storedCounter && storedDate === new Date().toDateString()) {
        this.dailyOrderCount = parseInt(storedCounter, 10);
      }
    } catch (error) {
      console.error('❌ Error loading existing orders:', error);
    }
  }

  // Save orders to localStorage
  private saveOrders(): void {
    try {
      localStorage.setItem('autoGTTOrders', JSON.stringify(this.orders));
      localStorage.setItem('autoGTTDailyCount', this.dailyOrderCount.toString());
      localStorage.setItem('autoGTTLastResetDate', this.lastResetDate);
    } catch (error) {
      console.error('❌ Error saving orders:', error);
    }
  }

  // Reset daily counter if it's a new day
  private resetDailyCounterIfNeeded(): void {
    const today = new Date().toDateString();
    if (this.lastResetDate !== today) {
      this.dailyOrderCount = 0;
      this.lastResetDate = today;
      console.log('🔄 Daily order counter reset for new day');
    }
  }

  // Check if we can create a new order
  private canCreateOrder(signal: WeeklyHighSignal): { canCreate: boolean; reason?: string } {
    // Check if service is enabled
    if (!this.config.enabled) {
      return { canCreate: false, reason: 'Automatic GTT service is disabled' };
    }

    // Check market hours if required
    if (this.config.onlyDuringMarketHours) {
      const now = new Date();
      const currentHour = now.getHours();
      const currentMinute = now.getMinutes();
      const isWeekday = now.getDay() >= 1 && now.getDay() <= 5;
      const isMarketOpen = isWeekday && 
                          (currentHour > 9 || (currentHour === 9 && currentMinute >= 15)) &&
                          (currentHour < 15 || (currentHour === 15 && currentMinute <= 30));
      
      if (!isMarketOpen) {
        return { canCreate: false, reason: 'Market is closed' };
      }
    }

    // Check daily limit
    this.resetDailyCounterIfNeeded();
    if (this.dailyOrderCount >= this.config.maxOrdersPerDay) {
      return { canCreate: false, reason: 'Daily order limit reached' };
    }

    // Check signal strength
    const strengthOrder = { 'WEAK': 1, 'MODERATE': 2, 'STRONG': 3 };
    if (strengthOrder[signal.signalStrength] < strengthOrder[this.config.minSignalStrength]) {
      return { canCreate: false, reason: `Signal strength ${signal.signalStrength} below minimum ${this.config.minSignalStrength}` };
    }

    // Check volume confirmation if required
    if (this.config.requireVolumeConfirmation && signal.volumeRatio < 1.2) {
      return { canCreate: false, reason: 'Insufficient volume confirmation' };
    }

    // Check for existing pending orders for this stock
    const existingOrder = this.orders.find(order => 
      order.symbol === signal.symbol && 
      order.status === 'PENDING' && 
      order.source === 'SIGNAL'
    );
    
    if (existingOrder) {
      return { canCreate: false, reason: 'Pending order already exists for this stock' };
    }

    // Check investment limits
    const existingInvestment = this.orders
      .filter(order => order.symbol === signal.symbol && order.status === 'PENDING')
      .reduce((sum, order) => sum + (order.triggerPrice * order.quantity), 0);
    
    const newInvestment = signal.suggestedBuyPrice * signal.suggestedGTTQuantity;
    
    if (existingInvestment + newInvestment > this.config.maxInvestmentPerStock) {
      return { canCreate: false, reason: 'Would exceed maximum investment per stock' };
    }

    // Check if quantity is valid
    if (signal.suggestedGTTQuantity <= 0) {
      return { canCreate: false, reason: 'Invalid quantity calculated' };
    }

    // Check if trigger price is reasonable
    if (signal.suggestedBuyPrice <= 0 || signal.suggestedBuyPrice > 5000) {
      return { canCreate: false, reason: 'Invalid trigger price' };
    }

    return { canCreate: true };
  }

  // Handle all current signals (check for missing orders)
  private async handleAllSignals(signals: WeeklyHighSignal[]): Promise<void> {
    console.log(`📊 Processing ${signals.length} current signals for automatic order creation...`);

    // Get existing pending signal orders
    const existingSymbols = new Set(
      this.orders
        .filter(order => order.source === 'SIGNAL' && order.status === 'PENDING')
        .map(order => order.symbol)
    );

    // Find signals that don't have existing orders
    const signalsWithoutOrders = signals.filter(signal => !existingSymbols.has(signal.symbol));

    console.log(`🔍 Found ${signalsWithoutOrders.length} signals without existing orders`);

    // Create orders for signals without existing orders
    for (const signal of signalsWithoutOrders) {
      const validation = this.canCreateOrder(signal);

      if (validation.canCreate) {
        try {
          await this.createAutoGTTOrder(signal);
          console.log(`🤖 Auto-created order for ${signal.symbol} from signal scan`);
        } catch (error) {
          console.error(`❌ Failed to create auto GTT order for ${signal.symbol}:`, error);
        }
      } else {
        console.log(`⏭️ Skipping order creation for ${signal.symbol}: ${validation.reason}`);
      }
    }
  }

  // Handle new signal detection (for immediate response to new signals)
  private async handleNewSignal(signal: WeeklyHighSignal): Promise<void> {
    console.log(`🔔 New signal received: ${signal.symbol} (${signal.signalStrength})`);

    const validation = this.canCreateOrder(signal);

    if (!validation.canCreate) {
      console.log(`⏭️ Skipping order creation for ${signal.symbol}: ${validation.reason}`);
      return;
    }

    try {
      await this.createAutoGTTOrder(signal);
    } catch (error) {
      console.error(`❌ Failed to create auto GTT order for ${signal.symbol}:`, error);
    }
  }

  // Create automatic GTT order
  private async createAutoGTTOrder(signal: WeeklyHighSignal): Promise<AutoGTTOrder> {
    console.log(`🤖 Creating automatic GTT order for ${signal.symbol}...`);

    const order: AutoGTTOrder = {
      id: `auto_gtt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      symbol: signal.symbol,
      name: signal.name,
      orderType: 'BUY',
      triggerPrice: signal.suggestedBuyPrice,
      quantity: signal.suggestedGTTQuantity,
      status: 'PENDING',
      createdAt: new Date(),
      source: 'SIGNAL',
      signalStrength: signal.signalStrength,
      autoCreated: true,
      originalSignal: signal
    };

    // Add to orders list
    this.orders.push(order);
    this.dailyOrderCount++;

    // Save to storage
    this.saveOrders();

    // Log the creation
    console.log(`✅ Auto GTT order created: ${order.symbol} - Trigger: ₹${order.triggerPrice.toFixed(2)}, Qty: ${order.quantity}`);
    console.log(`📊 Daily orders: ${this.dailyOrderCount}/${this.config.maxOrdersPerDay}`);

    // Notify listeners
    this.orderListeners.forEach(listener => {
      try {
        listener(order);
      } catch (error) {
        console.error('❌ Error in order listener:', error);
      }
    });

    return order;
  }

  // Get all orders
  getAllOrders(): AutoGTTOrder[] {
    return [...this.orders];
  }

  // Get orders by status
  getOrdersByStatus(status: AutoGTTOrder['status']): AutoGTTOrder[] {
    return this.orders.filter(order => order.status === status);
  }

  // Get orders by source
  getOrdersBySource(source: AutoGTTOrder['source']): AutoGTTOrder[] {
    return this.orders.filter(order => order.source === source);
  }

  // Cancel an order
  cancelOrder(orderId: string): boolean {
    const order = this.orders.find(o => o.id === orderId);
    if (order && order.status === 'PENDING') {
      order.status = 'CANCELLED';
      this.saveOrders();
      console.log(`❌ Order cancelled: ${order.symbol} (${orderId})`);
      return true;
    }
    return false;
  }

  // Update order status (for external triggers)
  updateOrderStatus(orderId: string, status: AutoGTTOrder['status']): boolean {
    const order = this.orders.find(o => o.id === orderId);
    if (order) {
      order.status = status;
      this.saveOrders();
      console.log(`🔄 Order status updated: ${order.symbol} -> ${status}`);
      return true;
    }
    return false;
  }

  // Add order listener
  addOrderListener(listener: (order: AutoGTTOrder) => void): void {
    this.orderListeners.add(listener);
  }

  // Remove order listener
  removeOrderListener(listener: (order: AutoGTTOrder) => void): void {
    this.orderListeners.delete(listener);
  }

  // Get service statistics
  getStatistics() {
    const today = new Date().toDateString();
    const todayOrders = this.orders.filter(order => 
      order.createdAt.toDateString() === today && order.autoCreated
    );

    return {
      totalOrders: this.orders.length,
      autoCreatedOrders: this.orders.filter(o => o.autoCreated).length,
      todayOrders: todayOrders.length,
      dailyLimit: this.config.maxOrdersPerDay,
      pendingOrders: this.orders.filter(o => o.status === 'PENDING').length,
      triggeredOrders: this.orders.filter(o => o.status === 'TRIGGERED').length,
      cancelledOrders: this.orders.filter(o => o.status === 'CANCELLED').length,
      isEnabled: this.config.enabled,
      isInitialized: this.isInitialized
    };
  }

  // Manual trigger for testing
  async testCreateOrder(symbol: string): Promise<AutoGTTOrder | null> {
    console.log(`🧪 Test order creation for ${symbol}`);
    
    // Get current signals
    const signals = await weeklyHighSignalDetector.triggerManualScan();
    const signal = signals.find(s => s.symbol === symbol);
    
    if (!signal) {
      console.log(`❌ No signal found for ${symbol}`);
      return null;
    }

    return await this.createAutoGTTOrder(signal);
  }

  // Set up periodic check for missing orders
  private setupPeriodicOrderCheck(): void {
    // Clear existing interval if any
    if (this.periodicCheckInterval) {
      clearInterval(this.periodicCheckInterval);
    }

    // Check every 2 minutes during market hours
    this.periodicCheckInterval = setInterval(async () => {
      if (!this.isMarketOpen()) {
        return;
      }

      try {
        console.log('🔄 Periodic check for missing GTT orders...');

        // Get current signals
        const currentSignals = await weeklyHighSignalDetector.triggerManualScan();

        // Process them through the handleAllSignals method
        await this.handleAllSignals(currentSignals);

      } catch (error) {
        console.error('❌ Error in periodic order check:', error);
      }
    }, 2 * 60 * 1000); // 2 minutes

    console.log('⏰ Periodic order check set up (every 2 minutes during market hours)');
  }

  // Check if market is open
  private isMarketOpen(): boolean {
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();

    const isWeekday = now.getDay() >= 1 && now.getDay() <= 5;
    const isAfterStart = currentHour > 9 || (currentHour === 9 && currentMinute >= 15);
    const isBeforeEnd = currentHour < 15 || (currentHour === 15 && currentMinute <= 30);

    return isWeekday && isAfterStart && isBeforeEnd;
  }

  // Start the service
  async start(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    // Start the signal detector if not already running
    weeklyHighSignalDetector.start();
    
    console.log('🚀 Automatic GTT Service started');
  }

  // Stop the service
  stop(): void {
    weeklyHighSignalDetector.stop();

    // Clear periodic check interval
    if (this.periodicCheckInterval) {
      clearInterval(this.periodicCheckInterval);
      this.periodicCheckInterval = null;
    }

    console.log('⏹️ Automatic GTT Service stopped');
  }
}

// Export singleton instance
export const automaticGTTService = new AutomaticGTTService();

// Auto-start the service when imported (with delay for DOM readiness)
if (typeof window !== 'undefined') {
  // Only run in browser environment, with a small delay to ensure DOM is ready
  setTimeout(() => {
    console.log('🤖 Auto-starting Automatic GTT Service...');

    automaticGTTService.start().then(() => {
      console.log('✅ Automatic GTT Service auto-started successfully');
    }).catch((error) => {
      console.error('❌ Automatic GTT Service auto-start failed:', error);
    });
  }, 200);
}
