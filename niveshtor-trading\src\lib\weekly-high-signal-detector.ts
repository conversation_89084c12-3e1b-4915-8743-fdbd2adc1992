// Weekly High Signal Detection Service
// Monitors for new Weekly High Signals and triggers automatic GTT order creation

import { yahooFinanceService } from './yahoo-finance';
import { stockNamesService } from './stock-names-service';
import { NIFTY_200_SYMBOLS, getYahooSymbol, addBOHEligibility, NiftyStock } from './nifty-stocks';
import { holdingsService } from './holdings-service';

export interface WeeklyHighSignal {
  symbol: string;
  name: string;
  currentPrice: number;
  lastWeekHighest: number;
  suggestedBuyPrice: number;
  percentDifference: number;
  suggestedGTTQuantity: number;
  isBOHEligible: boolean;
  inHoldings: boolean;
  signalStrength: 'STRONG' | 'MODERATE' | 'WEAK';
  detectedAt: Date;
  volume: number;
  avgVolume: number;
  volumeRatio: number;
}

export interface SignalDetectionConfig {
  enabled: boolean;
  pollingIntervalMinutes: number;
  marketStartHour: number; // 9 for 9:15 AM
  marketEndHour: number;   // 15 for 3:30 PM
  strongSignalThreshold: number; // % within weekly high
  moderateSignalThreshold: number;
  minVolumeRatio: number;
  maxInvestmentPerStock: number;
  investmentPerOrder: number;
}

class WeeklyHighSignalDetector {
  private config: SignalDetectionConfig = {
    enabled: true,
    pollingIntervalMinutes: 5,
    marketStartHour: 9,
    marketEndHour: 15,
    strongSignalThreshold: 2.0, // Within 2% of weekly high
    moderateSignalThreshold: 5.0, // Within 5% of weekly high
    minVolumeRatio: 1.2, // 20% above average volume
    maxInvestmentPerStock: 10000,
    investmentPerOrder: 2000
  };

  private isRunning = false;
  private pollingInterval: NodeJS.Timeout | null = null;
  private lastSignals: Map<string, WeeklyHighSignal> = new Map();
  private signalListeners = new Set<(signals: WeeklyHighSignal[]) => void>();
  private newSignalListeners = new Set<(signal: WeeklyHighSignal) => void>();

  constructor() {
    console.log('📡 Weekly High Signal Detector initialized');
  }

  // Configuration management
  updateConfig(newConfig: Partial<SignalDetectionConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('⚙️ Signal detector config updated:', this.config);
    
    if (this.isRunning) {
      this.stop();
      this.start();
    }
  }

  getConfig(): SignalDetectionConfig {
    return { ...this.config };
  }

  // Check if market is currently open
  private isMarketOpen(): boolean {
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    
    // Market hours: 9:15 AM to 3:30 PM (Monday to Friday)
    const isWeekday = now.getDay() >= 1 && now.getDay() <= 5;
    const isAfterStart = currentHour > this.config.marketStartHour || 
                       (currentHour === this.config.marketStartHour && currentMinute >= 15);
    const isBeforeEnd = currentHour < this.config.marketEndHour || 
                       (currentHour === this.config.marketEndHour && currentMinute <= 30);
    
    return isWeekday && isAfterStart && isBeforeEnd;
  }

  // Generate mock OHLC data for weekly high calculation
  private generateOHLCData(currentPrice: number) {
    const data = [];
    let price = currentPrice * 0.95; // Start 5% below current price
    
    for (let i = 0; i < 7; i++) {
      const open = price;
      const high = price * (1 + Math.random() * 0.08); // Up to 8% higher
      const low = price * (1 - Math.random() * 0.05); // Up to 5% lower
      const close = low + Math.random() * (high - low);
      
      data.push({ open, high, low, close });
      price = close;
    }
    
    return data;
  }

  // Calculate signal strength based on proximity to weekly high and volume
  private calculateSignalStrength(
    currentPrice: number, 
    weeklyHigh: number, 
    volumeRatio: number
  ): 'STRONG' | 'MODERATE' | 'WEAK' {
    const percentFromHigh = Math.abs((currentPrice - weeklyHigh) / weeklyHigh * 100);
    
    if (percentFromHigh <= this.config.strongSignalThreshold && volumeRatio >= this.config.minVolumeRatio) {
      return 'STRONG';
    } else if (percentFromHigh <= this.config.moderateSignalThreshold) {
      return 'MODERATE';
    } else {
      return 'WEAK';
    }
  }

  // Scan for Weekly High Signals
  async scanForSignals(): Promise<WeeklyHighSignal[]> {
    try {
      console.log('🔍 Scanning for Weekly High Signals...');

      // Fetch all Nifty 200 stocks with current prices
      const yahooSymbols = NIFTY_200_SYMBOLS.map(getYahooSymbol);
      const quotes = await yahooFinanceService.getMultipleQuotesWithCachedNames(yahooSymbols);
      
      console.log(`📊 Got quotes for ${quotes.length}/${yahooSymbols.length} symbols`);

      // Get current holdings
      const holdingSymbols = holdingsService.getAllHoldings().map(h => h.symbol);

      // Process each stock for signal detection
      const signals: WeeklyHighSignal[] = [];
      
      for (let i = 0; i < NIFTY_200_SYMBOLS.length; i++) {
        const nseSymbol = NIFTY_200_SYMBOLS[i];
        const yahooSymbol = getYahooSymbol(nseSymbol);
        const quote = quotes.find(q => q.symbol === yahooSymbol);
        
        if (!quote || quote.price <= 0) continue;

        const price = quote.price;
        const inHoldings = holdingSymbols.includes(nseSymbol);
        const isEligible = holdingsService.isStockEligibleForTrading(nseSymbol, price);

        // Create stock object for BOH eligibility check
        const stock: NiftyStock = {
          symbol: nseSymbol,
          name: quote.name,
          price,
          change: quote.change || 0,
          changePercent: quote.changePercent || 0,
          volume: quote.volume || 0,
          marketCap: quote.marketCap,
          high52Week: quote.high52Week,
          low52Week: quote.low52Week,
          high52WeekDate: quote.high52WeekDate,
          low52WeekDate: quote.low52WeekDate,
          isEligible,
          inHoldings
        };

        const stockWithBOH = addBOHEligibility(stock);
        
        // Only process BOH eligible stocks
        if (!stockWithBOH.isBOHEligible) continue;

        // Calculate weekly high data
        const ohlcData = this.generateOHLCData(price);
        const lastWeekHighest = Math.max(...ohlcData.map(d => d.high));
        const suggestedBuyPrice = lastWeekHighest + 0.05;
        const percentDifference = ((price - suggestedBuyPrice) / suggestedBuyPrice) * 100;
        const suggestedGTTQuantity = Math.floor(this.config.investmentPerOrder / suggestedBuyPrice);

        // Calculate volume metrics
        const avgVolume = quote.avgVolume || quote.volume || 1;
        const volumeRatio = quote.volume / avgVolume;

        // Calculate signal strength
        const signalStrength = this.calculateSignalStrength(price, lastWeekHighest, volumeRatio);

        // Only include signals that are MODERATE or STRONG
        if (signalStrength === 'WEAK') continue;

        const signal: WeeklyHighSignal = {
          symbol: nseSymbol,
          name: quote.name,
          currentPrice: price,
          lastWeekHighest,
          suggestedBuyPrice,
          percentDifference,
          suggestedGTTQuantity,
          isBOHEligible: true,
          inHoldings,
          signalStrength,
          detectedAt: new Date(),
          volume: quote.volume || 0,
          avgVolume,
          volumeRatio
        };

        signals.push(signal);
      }

      console.log(`✅ Found ${signals.length} Weekly High Signals`);
      return signals;

    } catch (error) {
      console.error('❌ Error scanning for signals:', error);
      return [];
    }
  }

  // Detect new signals by comparing with previous scan
  private detectNewSignals(currentSignals: WeeklyHighSignal[]): WeeklyHighSignal[] {
    const newSignals: WeeklyHighSignal[] = [];
    
    for (const signal of currentSignals) {
      const previousSignal = this.lastSignals.get(signal.symbol);
      
      // Consider it a new signal if:
      // 1. Stock wasn't in previous signals, OR
      // 2. Signal strength improved (MODERATE -> STRONG), OR
      // 3. Price moved significantly closer to weekly high
      const isNewSignal = !previousSignal ||
                         (signal.signalStrength === 'STRONG' && previousSignal.signalStrength !== 'STRONG') ||
                         (Math.abs(signal.percentDifference) < Math.abs(previousSignal.percentDifference) - 1);
      
      if (isNewSignal) {
        newSignals.push(signal);
        console.log(`🆕 New signal detected: ${signal.symbol} (${signal.signalStrength})`);
      }
    }
    
    return newSignals;
  }

  // Main polling function
  private async poll(): Promise<void> {
    if (!this.config.enabled) {
      console.log('⏸️ Signal detection is disabled');
      return;
    }

    if (!this.isMarketOpen()) {
      console.log('🕐 Market is closed, skipping signal detection');
      return;
    }

    try {
      console.log('🔄 Polling for Weekly High Signals...');
      
      const currentSignals = await this.scanForSignals();
      const newSignals = this.detectNewSignals(currentSignals);
      
      // Update last signals cache
      this.lastSignals.clear();
      currentSignals.forEach(signal => {
        this.lastSignals.set(signal.symbol, signal);
      });
      
      // Notify listeners about all current signals
      this.signalListeners.forEach(listener => {
        try {
          listener(currentSignals);
        } catch (error) {
          console.error('❌ Error in signal listener:', error);
        }
      });
      
      // Notify listeners about new signals
      if (newSignals.length > 0) {
        console.log(`🚨 ${newSignals.length} new signals detected!`);
        
        newSignals.forEach(signal => {
          this.newSignalListeners.forEach(listener => {
            try {
              listener(signal);
            } catch (error) {
              console.error('❌ Error in new signal listener:', error);
            }
          });
        });
      }
      
    } catch (error) {
      console.error('❌ Error in signal polling:', error);
    }
  }

  // Start the detection service
  start(): void {
    if (this.isRunning) {
      console.log('⚠️ Signal detector is already running');
      return;
    }

    console.log(`🚀 Starting Weekly High Signal Detector (polling every ${this.config.pollingIntervalMinutes} minutes)`);
    
    this.isRunning = true;
    
    // Initial scan
    this.poll();
    
    // Set up polling interval
    this.pollingInterval = setInterval(() => {
      this.poll();
    }, this.config.pollingIntervalMinutes * 60 * 1000);
  }

  // Stop the detection service
  stop(): void {
    if (!this.isRunning) {
      console.log('⚠️ Signal detector is not running');
      return;
    }

    console.log('⏹️ Stopping Weekly High Signal Detector');
    
    this.isRunning = false;
    
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
    }
  }

  // Add listener for all signals
  addSignalListener(listener: (signals: WeeklyHighSignal[]) => void): void {
    this.signalListeners.add(listener);
  }

  // Remove listener for all signals
  removeSignalListener(listener: (signals: WeeklyHighSignal[]) => void): void {
    this.signalListeners.delete(listener);
  }

  // Add listener for new signals only
  addNewSignalListener(listener: (signal: WeeklyHighSignal) => void): void {
    this.newSignalListeners.add(listener);
  }

  // Remove listener for new signals
  removeNewSignalListener(listener: (signal: WeeklyHighSignal) => void): void {
    this.newSignalListeners.delete(listener);
  }

  // Get current status
  getStatus() {
    return {
      isRunning: this.isRunning,
      isMarketOpen: this.isMarketOpen(),
      config: this.config,
      lastSignalCount: this.lastSignals.size,
      listenerCount: this.signalListeners.size + this.newSignalListeners.size
    };
  }

  // Manual trigger for testing
  async triggerManualScan(): Promise<WeeklyHighSignal[]> {
    console.log('🔧 Manual signal scan triggered');
    return await this.scanForSignals();
  }
}

// Export singleton instance
export const weeklyHighSignalDetector = new WeeklyHighSignalDetector();

// Auto-start the service when imported
if (typeof window !== 'undefined') {
  // Only run in browser environment
  console.log('📡 Auto-starting Weekly High Signal Detector...');

  weeklyHighSignalDetector.start();
  console.log('✅ Weekly High Signal Detector auto-started successfully');
}
