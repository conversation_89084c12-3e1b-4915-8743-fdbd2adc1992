{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/components/layout/dashboard-layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const DashboardLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call DashboardLayout() from the server but DashboardLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/dashboard-layout.tsx <module evaluation>\",\n    \"DashboardLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,4EACA", "debugId": null}}, {"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/components/layout/dashboard-layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const DashboardLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call DashboardLayout() from the server but DashboardLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/dashboard-layout.tsx\",\n    \"DashboardLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,wDACA", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/components/providers/BackgroundDataProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const BackgroundDataProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call BackgroundDataProvider() from the server but BackgroundDataProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/BackgroundDataProvider.tsx <module evaluation>\",\n    \"BackgroundDataProvider\",\n);\nexport const useBackgroundDataContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call useBackgroundDataContext() from the server but useBackgroundDataContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/BackgroundDataProvider.tsx <module evaluation>\",\n    \"useBackgroundDataContext\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,yBAAyB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,qFACA;AAEG,MAAM,2BAA2B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1D;IAAa,MAAM,IAAI,MAAM;AAAgQ,GAC7R,qFACA", "debugId": null}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/components/providers/BackgroundDataProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const BackgroundDataProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call BackgroundDataProvider() from the server but BackgroundDataProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/BackgroundDataProvider.tsx\",\n    \"BackgroundDataProvider\",\n);\nexport const useBackgroundDataContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call useBackgroundDataContext() from the server but useBackgroundDataContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/BackgroundDataProvider.tsx\",\n    \"useBackgroundDataContext\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,yBAAyB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,iEACA;AAEG,MAAM,2BAA2B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1D;IAAa,MAAM,IAAI,MAAM;AAAgQ,GAC7R,iEACA", "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/app/dashboard/layout.tsx"], "sourcesContent": ["import { DashboardLayout } from '@/components/layout/dashboard-layout';\nimport { BackgroundDataProvider } from '@/components/providers/BackgroundDataProvider';\n\nexport default function Layout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return (\n    <BackgroundDataProvider>\n      <DashboardLayout>{children}</DashboardLayout>\n    </BackgroundDataProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS,OAAO,EAC7B,QAAQ,EAGT;IACC,qBACE,8OAAC,yJAAA,CAAA,yBAAsB;kBACrB,cAAA,8OAAC,mJAAA,CAAA,kBAAe;sBAAE;;;;;;;;;;;AAGxB", "debugId": null}}]}