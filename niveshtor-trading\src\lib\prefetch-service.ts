// Prefetching service for optimizing navigation and data loading

import { cacheService, CacheKeys } from './cache-service';

class PrefetchService {
  private prefetchQueue: Set<string> = new Set();
  private isProcessing = false;

  // Prefetch data for likely next pages
  async prefetchPageData(route: string): Promise<void> {
    if (this.prefetchQueue.has(route) || this.isProcessing) {
      return;
    }

    this.prefetchQueue.add(route);
    
    // Process queue with delay to avoid overwhelming the API
    setTimeout(() => this.processQueue(), 100);
  }

  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.prefetchQueue.size === 0) {
      return;
    }

    this.isProcessing = true;

    try {
      const routes = Array.from(this.prefetchQueue);
      this.prefetchQueue.clear();

      // Process routes in parallel but with limits
      const batchSize = 3;
      for (let i = 0; i < routes.length; i += batchSize) {
        const batch = routes.slice(i, i + batchSize);
        await Promise.all(batch.map(route => this.prefetchRouteData(route)));
        
        // Small delay between batches
        if (i + batchSize < routes.length) {
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      }
    } finally {
      this.isProcessing = false;
    }
  }

  private async prefetchRouteData(route: string): Promise<void> {
    try {
      switch (route) {
        case '/dashboard/capital':
          await this.prefetchCapitalData();
          break;
        case '/dashboard/stocks':
          await this.prefetchStockData();
          break;
        case '/dashboard/holdings':
          await this.prefetchHoldingsData();
          break;
        default:
          console.log(`No prefetch strategy for route: ${route}`);
      }
    } catch (error) {
      console.warn(`Prefetch failed for ${route}:`, error);
    }
  }

  private async prefetchCapitalData(): Promise<void> {
    const userId = 'default-user';
    
    // Prefetch broker balance
    const balanceKey = CacheKeys.brokerBalance(userId);
    if (!cacheService.has(balanceKey)) {
      try {
        const response = await fetch('/api/broker/balance?userId=default-user');
        const data = await response.json();
        cacheService.set(balanceKey, data);
      } catch (error) {
        console.warn('Failed to prefetch broker balance:', error);
      }
    }

    // Prefetch fund allocation
    const allocationKey = CacheKeys.fundAllocation(userId, 'DARVAS_BOX');
    if (!cacheService.has(allocationKey)) {
      try {
        const response = await fetch('/api/fund-allocation?userId=default-user&strategy=DARVAS_BOX');
        const data = await response.json();
        cacheService.set(allocationKey, data);
      } catch (error) {
        console.warn('Failed to prefetch fund allocation:', error);
      }
    }

    // Prefetch portfolio summary
    const portfolioKey = CacheKeys.portfolioSummary(userId);
    if (!cacheService.has(portfolioKey)) {
      try {
        const response = await fetch('/api/portfolio/summary');
        const data = await response.json();
        cacheService.set(portfolioKey, data);
      } catch (error) {
        console.warn('Failed to prefetch portfolio summary:', error);
      }
    }
  }

  private async prefetchStockData(): Promise<void> {
    // Prefetch first batch of Nifty 200 stocks
    const stocksKey = CacheKeys.niftyStocks(0);
    if (!cacheService.has(stocksKey)) {
      try {
        const response = await fetch('/api/stocks/nifty200?batchIndex=0&batchSize=25');
        const data = await response.json();
        if (data.success) {
          cacheService.set(stocksKey, data.data);
        }
      } catch (error) {
        console.warn('Failed to prefetch stock data:', error);
      }
    }
  }

  private async prefetchHoldingsData(): Promise<void> {
    // Prefetch holdings data when available
    try {
      const response = await fetch('/api/holdings');
      const data = await response.json();
      if (data.success) {
        cacheService.set('holdings-data', data);
      }
    } catch (error) {
      console.warn('Failed to prefetch holdings data:', error);
    }
  }

  // Prefetch based on user navigation patterns
  async smartPrefetch(currentRoute: string): Promise<void> {
    const prefetchMap: { [key: string]: string[] } = {
      '/dashboard/capital': ['/dashboard/stocks', '/dashboard/holdings'],
      '/dashboard/stocks': ['/dashboard/capital', '/dashboard/boh-eligible'],
      '/dashboard/holdings': ['/dashboard/capital', '/dashboard/backtesting'],
      '/dashboard/boh-filter': ['/dashboard/stocks', '/dashboard/weekly-high'],
      '/dashboard/weekly-high': ['/dashboard/boh-filter', '/dashboard/gtt-orders'],
      '/dashboard/gtt-orders': ['/dashboard/holdings', '/dashboard/weekly-high'],
      '/dashboard/backtesting': ['/dashboard/holdings', '/dashboard/capital'],
      '/dashboard/broker': ['/dashboard/capital', '/dashboard/holdings']
    };

    const routesToPrefetch = prefetchMap[currentRoute] || [];
    
    // Prefetch likely next routes
    for (const route of routesToPrefetch) {
      this.prefetchPageData(route);
    }
  }

  // Prefetch critical resources on app start
  async prefetchCriticalResources(): Promise<void> {
    // Prefetch most commonly accessed data
    await Promise.all([
      this.prefetchCapitalData(),
      this.prefetchStockData()
    ]);
  }

  // Clear prefetch queue
  clearQueue(): void {
    this.prefetchQueue.clear();
  }

  // Get queue status
  getQueueStatus(): {
    queueSize: number;
    isProcessing: boolean;
    queuedRoutes: string[];
  } {
    return {
      queueSize: this.prefetchQueue.size,
      isProcessing: this.isProcessing,
      queuedRoutes: Array.from(this.prefetchQueue)
    };
  }
}

// Create singleton instance
export const prefetchService = new PrefetchService();

// Hook for using prefetch service in components
export function usePrefetch() {
  const prefetchPage = (route: string) => {
    prefetchService.prefetchPageData(route);
  };

  const smartPrefetch = (currentRoute: string) => {
    prefetchService.smartPrefetch(currentRoute);
  };

  return {
    prefetchPage,
    smartPrefetch,
    getQueueStatus: () => prefetchService.getQueueStatus()
  };
}

// Initialize critical resources prefetching
if (typeof window !== 'undefined') {
  // Prefetch critical resources after a short delay
  setTimeout(() => {
    prefetchService.prefetchCriticalResources();
  }, 1000);
}
