import { NextRequest, NextResponse } from 'next/server';
import { centralDataManager } from '@/lib/central-data-manager';
import { automaticGTTService } from '@/lib/automatic-gtt-service';
import { weeklyHighSignalDetector } from '@/lib/weekly-high-signal-detector';

// POST - Initialize the entire application with background services
export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Initializing Niveshtor Trading Application...');

    const initResults = {
      timestamp: new Date().toISOString(),
      services: [] as any[]
    };

    // Step 1: Initialize Central Data Manager
    console.log('📊 Step 1: Initializing Central Data Manager...');
    try {
      await centralDataManager.initialize();
      centralDataManager.start();
      
      initResults.services.push({
        name: 'Central Data Manager',
        status: 'SUCCESS',
        initialized: centralDataManager.getStatus().isInitialized,
        running: centralDataManager.getStatus().isRunning,
        cacheStatus: centralDataManager.getCacheStatus()
      });
      
      console.log('✅ Central Data Manager initialized and started');
    } catch (error) {
      initResults.services.push({
        name: 'Central Data Manager',
        status: 'ERROR',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      console.error('❌ Central Data Manager initialization failed:', error);
    }

    // Step 2: Initialize Automatic GTT Service
    console.log('🤖 Step 2: Initializing Automatic GTT Service...');
    try {
      await automaticGTTService.start();
      
      initResults.services.push({
        name: 'Automatic GTT Service',
        status: 'SUCCESS',
        statistics: automaticGTTService.getStatistics()
      });
      
      console.log('✅ Automatic GTT Service initialized and started');
    } catch (error) {
      initResults.services.push({
        name: 'Automatic GTT Service',
        status: 'ERROR',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      console.error('❌ Automatic GTT Service initialization failed:', error);
    }

    // Step 3: Initialize Weekly High Signal Detector
    console.log('📡 Step 3: Initializing Weekly High Signal Detector...');
    try {
      weeklyHighSignalDetector.start();
      
      initResults.services.push({
        name: 'Weekly High Signal Detector',
        status: 'SUCCESS',
        detectorStatus: weeklyHighSignalDetector.getStatus()
      });
      
      console.log('✅ Weekly High Signal Detector initialized and started');
    } catch (error) {
      initResults.services.push({
        name: 'Weekly High Signal Detector',
        status: 'ERROR',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      console.error('❌ Weekly High Signal Detector initialization failed:', error);
    }

    // Step 4: Wait for initial data loading
    console.log('⏳ Step 4: Waiting for initial data loading...');
    try {
      // Wait up to 30 seconds for data to load
      let attempts = 0;
      const maxAttempts = 30;
      
      while (attempts < maxAttempts) {
        const cacheStatus = centralDataManager.getCacheStatus();
        
        if (cacheStatus.nifty200Count > 0 && 
            cacheStatus.bohEligibleCount > 0 && 
            cacheStatus.weeklyHighSignalsCount > 0) {
          console.log('✅ Initial data loaded successfully');
          break;
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        attempts++;
      }
      
      const finalCacheStatus = centralDataManager.getCacheStatus();
      initResults.services.push({
        name: 'Initial Data Loading',
        status: finalCacheStatus.nifty200Count > 0 ? 'SUCCESS' : 'PARTIAL',
        cacheStatus: finalCacheStatus,
        loadingTime: `${attempts} seconds`
      });
      
    } catch (error) {
      initResults.services.push({
        name: 'Initial Data Loading',
        status: 'ERROR',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      console.error('❌ Initial data loading failed:', error);
    }

    // Calculate overall status
    const successfulServices = initResults.services.filter(s => s.status === 'SUCCESS').length;
    const totalServices = initResults.services.length;
    const overallStatus = successfulServices === totalServices ? 'SUCCESS' : 
                         successfulServices >= totalServices * 0.75 ? 'PARTIAL_SUCCESS' : 'FAILED';

    console.log(`🎉 Application initialization completed: ${successfulServices}/${totalServices} services successful`);

    return NextResponse.json({
      success: overallStatus !== 'FAILED',
      message: `Application initialized with ${overallStatus.toLowerCase().replace('_', ' ')}`,
      overallStatus,
      successfulServices,
      totalServices,
      results: initResults,
      readyForUse: overallStatus === 'SUCCESS' || overallStatus === 'PARTIAL_SUCCESS'
    });

  } catch (error) {
    console.error('❌ Application initialization failed:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Initialization failed',
        message: 'Application initialization failed'
      },
      { status: 500 }
    );
  }
}

// GET - Check application initialization status
export async function GET(request: NextRequest) {
  try {
    const status = {
      centralDataManager: centralDataManager.getStatus(),
      automaticGTTService: automaticGTTService.getStatistics(),
      weeklyHighSignalDetector: weeklyHighSignalDetector.getStatus(),
      timestamp: new Date().toISOString()
    };

    const isFullyInitialized = status.centralDataManager.isInitialized && 
                              status.centralDataManager.isRunning &&
                              status.automaticGTTService.isInitialized;

    return NextResponse.json({
      success: true,
      isFullyInitialized,
      status
    });

  } catch (error) {
    console.error('❌ Error checking initialization status:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Status check failed' 
      },
      { status: 500 }
    );
  }
}
