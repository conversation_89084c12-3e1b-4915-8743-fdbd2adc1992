// Real-time update status indicator
import React from 'react';
import { Wifi, WifiOff, Activity, Clock } from 'lucide-react';

interface RealTimeIndicatorProps {
  isActive: boolean;
  lastUpdate: Date | null;
  updateCount: number;
  error: Error | null;
  className?: string;
}

export function RealTimeIndicator({ 
  isActive, 
  lastUpdate, 
  updateCount, 
  error, 
  className = '' 
}: RealTimeIndicatorProps) {
  const formatLastUpdate = (date: Date | null) => {
    if (!date) return 'Never';
    
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const seconds = Math.floor(diff / 1000);
    
    if (seconds < 60) return `${seconds}s ago`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)}m ago`;
    return date.toLocaleTimeString();
  };

  const getStatusColor = () => {
    if (error) return 'text-red-500';
    if (isActive) return 'text-green-500';
    return 'text-gray-400';
  };

  const getStatusIcon = () => {
    if (error) return <WifiOff className="h-4 w-4" />;
    if (isActive) return <Wifi className="h-4 w-4" />;
    return <WifiOff className="h-4 w-4" />;
  };

  const getStatusText = () => {
    if (error) return 'Connection Error';
    if (isActive) return 'Live Updates';
    return 'Offline';
  };

  return (
    <div className={`flex items-center space-x-2 text-sm ${className}`}>
      {/* Status Icon and Text */}
      <div className={`flex items-center space-x-1 ${getStatusColor()}`}>
        {getStatusIcon()}
        <span className="font-medium">{getStatusText()}</span>
      </div>
      
      {/* Update Count */}
      {isActive && updateCount > 0 && (
        <div className="flex items-center space-x-1 text-gray-500">
          <Activity className="h-3 w-3" />
          <span>{updateCount} updates</span>
        </div>
      )}
      
      {/* Last Update Time */}
      <div className="flex items-center space-x-1 text-gray-500">
        <Clock className="h-3 w-3" />
        <span>{formatLastUpdate(lastUpdate)}</span>
      </div>
      
      {/* Error Message */}
      {error && (
        <div className="text-red-500 text-xs">
          {error.message}
        </div>
      )}
    </div>
  );
}

// Compact version for smaller spaces
export function CompactRealTimeIndicator({ 
  isActive, 
  lastUpdate, 
  error, 
  className = '' 
}: Omit<RealTimeIndicatorProps, 'updateCount'>) {
  const getStatusColor = () => {
    if (error) return 'text-red-500';
    if (isActive) return 'text-green-500';
    return 'text-gray-400';
  };

  const getStatusIcon = () => {
    if (error) return <WifiOff className="h-3 w-3" />;
    if (isActive) return <Wifi className="h-3 w-3" />;
    return <WifiOff className="h-3 w-3" />;
  };

  return (
    <div className={`flex items-center space-x-1 ${getStatusColor()} ${className}`}>
      {getStatusIcon()}
      {isActive && (
        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
      )}
    </div>
  );
}

// Hook for using real-time indicator data
export function useRealTimeIndicator() {
  const [isActive, setIsActive] = React.useState(false);
  const [lastUpdate, setLastUpdate] = React.useState<Date | null>(null);
  const [updateCount, setUpdateCount] = React.useState(0);
  const [error, setError] = React.useState<Error | null>(null);

  // Listen for real-time updates
  React.useEffect(() => {
    const handleRealTimeUpdate = (event: CustomEvent) => {
      setLastUpdate(new Date());
      setUpdateCount(prev => prev + 1);
      setError(null);
      setIsActive(true);
    };

    const handleError = (event: CustomEvent) => {
      setError(event.detail.error);
    };

    window.addEventListener('realTimePriceUpdate', handleRealTimeUpdate as EventListener);
    window.addEventListener('realTimeError', handleError as EventListener);

    return () => {
      window.removeEventListener('realTimePriceUpdate', handleRealTimeUpdate as EventListener);
      window.removeEventListener('realTimeError', handleError as EventListener);
    };
  }, []);

  return {
    isActive,
    lastUpdate,
    updateCount,
    error,
    clearError: () => setError(null)
  };
}
