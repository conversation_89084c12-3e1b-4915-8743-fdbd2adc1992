// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// User and Account Management
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Trading Account Details
  brokerAccounts BrokerAccount[]
  portfolios     Portfolio[]
  watchlists     Watchlist[]
  backtests      Backtest[]
  gttOrders      GTTOrder[]
  fundAllocations FundAllocation[]
  brokerBalances BrokerBalance[]

  @@map("users")
}

model BrokerAccount {
  id           String   @id @default(cuid())
  userId       String
  brokerName   String   // "SmartAPI", "Zerodha", etc.
  clientId     String
  apiKey       String?
  isActive     Boolean  @default(false)
  lastSyncAt   DateTime?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  balances     BrokerBalance[]

  @@map("broker_accounts")
}

// Portfolio and Holdings
model Portfolio {
  id           String   @id @default(cuid())
  userId       String
  name         String
  totalValue   Float    @default(0)
  totalInvested Float   @default(0)
  totalPnL     Float    @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  holdings     Holding[]

  @@map("portfolios")
}

model Holding {
  id           String   @id @default(cuid())
  portfolioId  String
  symbol       String
  quantity     Int
  avgPrice     Float
  currentPrice Float    @default(0)
  pnl          Float    @default(0)
  pnlPercent   Float    @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  portfolio    Portfolio @relation(fields: [portfolioId], references: [id], onDelete: Cascade)

  @@map("holdings")
}

// Stock Data and Watchlists
model Stock {
  id           String   @id @default(cuid())
  symbol       String   @unique
  name         String
  exchange     String
  sector       String?
  industry     String?
  marketCap    Float?
  lastPrice    Float?
  change       Float?
  changePercent Float?
  volume       Int?
  avgVolume    Int?
  high52Week   Float?
  low52Week    Float?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  watchlistItems WatchlistItem[]
  priceHistory   PriceHistory[]
  signals        Signal[]

  @@map("stocks")
}

model Watchlist {
  id        String   @id @default(cuid())
  userId    String
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  items     WatchlistItem[]

  @@map("watchlists")
}

model WatchlistItem {
  id          String   @id @default(cuid())
  watchlistId String
  stockId     String
  addedAt     DateTime @default(now())

  watchlist   Watchlist @relation(fields: [watchlistId], references: [id], onDelete: Cascade)
  stock       Stock     @relation(fields: [stockId], references: [id], onDelete: Cascade)

  @@unique([watchlistId, stockId])
  @@map("watchlist_items")
}

// Price History and Technical Data
model PriceHistory {
  id        String   @id @default(cuid())
  stockId   String
  date      DateTime
  open      Float
  high      Float
  low       Float
  close     Float
  volume    Int
  createdAt DateTime @default(now())

  stock     Stock    @relation(fields: [stockId], references: [id], onDelete: Cascade)

  @@unique([stockId, date])
  @@map("price_history")
}

// Trading Signals and Strategies
model Signal {
  id          String   @id @default(cuid())
  stockId     String
  signalType  String   // "BUY", "SELL", "HOLD"
  strategy    String   // "DARVAS_BOX", "WEEKLY_HIGH", "BOH_FILTER"
  price       Float
  confidence  Float    @default(0)
  isActive    Boolean  @default(true)
  triggeredAt DateTime @default(now())
  createdAt   DateTime @default(now())

  stock       Stock    @relation(fields: [stockId], references: [id], onDelete: Cascade)

  @@map("signals")
}

// GTT Orders
model GTTOrder {
  id              String   @id @default(cuid())
  userId          String
  symbol          String
  exchange        String
  transactionType String   // "BUY", "SELL"
  orderType       String   // "LIMIT", "MARKET", "SL", "SL-M"
  productType     String   // "INTRADAY", "DELIVERY", "MARGIN"
  quantity        Int
  price           Float?
  triggerPrice    Float?
  stopLoss        Float?
  target          Float?
  status          String   @default("PENDING") // "PENDING", "TRIGGERED", "CANCELLED", "EXPIRED"
  validTill       DateTime?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("gtt_orders")
}

// Backtesting
model Backtest {
  id          String   @id @default(cuid())
  userId      String
  name        String
  strategy    String   // "DARVAS_BOX", "WEEKLY_HIGH", etc.
  startDate   DateTime
  endDate     DateTime
  initialCapital Float
  finalCapital   Float   @default(0)
  totalTrades    Int     @default(0)
  winningTrades  Int     @default(0)
  losingTrades   Int     @default(0)
  maxDrawdown    Float   @default(0)
  sharpeRatio    Float   @default(0)
  status         String  @default("PENDING") // "PENDING", "RUNNING", "COMPLETED", "FAILED"
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  trades         BacktestTrade[]

  @@map("backtests")
}

model BacktestTrade {
  id          String   @id @default(cuid())
  backtestId  String
  symbol      String
  entryDate   DateTime
  exitDate    DateTime?
  entryPrice  Float
  exitPrice   Float?
  quantity    Int
  tradeType   String   // "BUY", "SELL"
  pnl         Float    @default(0)
  pnlPercent  Float    @default(0)
  status      String   @default("OPEN") // "OPEN", "CLOSED"
  createdAt   DateTime @default(now())

  backtest    Backtest @relation(fields: [backtestId], references: [id], onDelete: Cascade)

  @@map("backtest_trades")
}

// Strategy Parameters
model StrategyConfig {
  id          String   @id @default(cuid())
  userId      String?
  strategy    String   // "DARVAS_BOX", "WEEKLY_HIGH", "BOH_FILTER"
  parameters  String   // JSON string of strategy parameters
  isDefault   Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("strategy_configs")
}

// Fund Allocation Management
model FundAllocation {
  id                    String   @id @default(cuid())
  userId                String
  strategyName          String   // "DARVAS_BOX", "WEEKLY_HIGH", etc.
  totalAllocatedAmount  Float    @default(0)
  maxPerStock           Float    @default(10000)  // ₹10,000 per stock
  maxPerTrade           Float    @default(2000)   // ₹2,000 per trade
  isActive              Boolean  @default(true)
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  user                  User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  stockAllocations      StockAllocation[]

  @@unique([userId, strategyName])
  @@map("fund_allocations")
}

// Per-stock allocation tracking
model StockAllocation {
  id                String   @id @default(cuid())
  fundAllocationId  String
  symbol            String
  allocatedAmount   Float    @default(0)
  usedAmount        Float    @default(0)
  tradesCount       Int      @default(0)
  lastTradeAt       DateTime?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  fundAllocation    FundAllocation @relation(fields: [fundAllocationId], references: [id], onDelete: Cascade)

  @@unique([fundAllocationId, symbol])
  @@map("stock_allocations")
}

// Broker Account Balance
model BrokerBalance {
  id                String   @id @default(cuid())
  userId            String
  brokerAccountId   String
  availableCash     Float    @default(0)
  marginUsed        Float    @default(0)
  marginAvailable   Float    @default(0)
  totalBalance      Float    @default(0)
  lastSyncAt        DateTime @default(now())
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  brokerAccount     BrokerAccount @relation(fields: [brokerAccountId], references: [id], onDelete: Cascade)

  @@unique([userId, brokerAccountId])
  @@map("broker_balances")
}
