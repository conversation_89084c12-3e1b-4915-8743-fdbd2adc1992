'use client';

import { useState } from 'react';
import { 
  Plug, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  <PERSON>ting<PERSON>,
  RefreshCw,
  Eye,
  EyeOff,
  Shield
} from 'lucide-react';

interface BrokerConnection {
  id: string;
  name: string;
  logo: string;
  status: 'CONNECTED' | 'DISCONNECTED' | 'ERROR';
  lastSync: Date;
  apiKey?: string;
  clientId?: string;
  isConfigured: boolean;
}

export default function ConnectBrokerPage() {
  const [brokers, setBrokers] = useState<BrokerConnection[]>([
    {
      id: 'smartapi',
      name: 'Angel SmartAPI',
      logo: '/api/placeholder/40/40',
      status: 'DISCONNECTED',
      lastSync: new Date(),
      isConfigured: false
    },
    {
      id: 'zerodha',
      name: '<PERSON><PERSON> Kite',
      logo: '/api/placeholder/40/40',
      status: 'DISCONNECTED',
      lastSync: new Date(),
      isConfigured: false
    },
    {
      id: 'upstox',
      name: 'Upstox',
      logo: '/api/placeholder/40/40',
      status: 'DISCONNECTED',
      lastSync: new Date(),
      isConfigured: false
    }
  ]);

  const [selectedBroker, setSelectedBroker] = useState<string | null>(null);
  const [showCredentials, setShowCredentials] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionForm, setConnectionForm] = useState({
    apiKey: '',
    clientId: '',
    password: '',
    totpSecret: ''
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'CONNECTED': return 'text-green-600 bg-green-100';
      case 'DISCONNECTED': return 'text-gray-600 bg-gray-100';
      case 'ERROR': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'CONNECTED': return <CheckCircle className="h-4 w-4" />;
      case 'DISCONNECTED': return <XCircle className="h-4 w-4" />;
      case 'ERROR': return <AlertTriangle className="h-4 w-4" />;
      default: return <XCircle className="h-4 w-4" />;
    }
  };

  const handleConnect = async (brokerId: string) => {
    setIsConnecting(true);
    
    try {
      // Simulate API call to connect broker
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setBrokers(prev => prev.map(broker => 
        broker.id === brokerId 
          ? { ...broker, status: 'CONNECTED' as const, lastSync: new Date(), isConfigured: true }
          : broker
      ));
      
      setSelectedBroker(null);
      setConnectionForm({ apiKey: '', clientId: '', password: '', totpSecret: '' });
    } catch (error) {
      setBrokers(prev => prev.map(broker => 
        broker.id === brokerId 
          ? { ...broker, status: 'ERROR' as const }
          : broker
      ));
    } finally {
      setIsConnecting(false);
    }
  };

  const handleDisconnect = (brokerId: string) => {
    setBrokers(prev => prev.map(broker => 
      broker.id === brokerId 
        ? { ...broker, status: 'DISCONNECTED' as const, isConfigured: false }
        : broker
    ));
  };

  const BrokerCard = ({ broker }: { broker: BrokerConnection }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
            <Plug className="h-5 w-5 text-gray-600" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">{broker.name}</h3>
            <p className="text-sm text-gray-600">Trading API Integration</p>
          </div>
        </div>
        <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 ${getStatusColor(broker.status)}`}>
          {getStatusIcon(broker.status)}
          <span>{broker.status}</span>
        </span>
      </div>

      {broker.status === 'CONNECTED' && (
        <div className="mb-4 p-3 bg-green-50 rounded-lg">
          <div className="flex items-center space-x-2 text-green-800">
            <CheckCircle className="h-4 w-4" />
            <span className="text-sm font-medium">Connected Successfully</span>
          </div>
          <p className="text-xs text-green-600 mt-1">
            Last sync: {broker.lastSync.toLocaleString()}
          </p>
        </div>
      )}

      {broker.status === 'ERROR' && (
        <div className="mb-4 p-3 bg-red-50 rounded-lg">
          <div className="flex items-center space-x-2 text-red-800">
            <AlertTriangle className="h-4 w-4" />
            <span className="text-sm font-medium">Connection Failed</span>
          </div>
          <p className="text-xs text-red-600 mt-1">
            Please check your credentials and try again
          </p>
        </div>
      )}

      <div className="flex space-x-2">
        {broker.status === 'CONNECTED' ? (
          <>
            <button
              onClick={() => handleDisconnect(broker.id)}
              className="flex-1 px-4 py-2 border border-red-300 text-red-700 rounded-lg hover:bg-red-50 transition-colors"
            >
              Disconnect
            </button>
            <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
              <Settings className="h-4 w-4" />
            </button>
          </>
        ) : (
          <button
            onClick={() => setSelectedBroker(broker.id)}
            className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Configure & Connect
          </button>
        )}
      </div>
    </div>
  );

  const ConnectionModal = () => {
    const broker = brokers.find(b => b.id === selectedBroker);
    if (!broker) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-full max-w-md">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Connect {broker.name}</h3>
            <button
              onClick={() => setSelectedBroker(null)}
              className="text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          </div>
          
          <div className="mb-6 p-4 bg-yellow-50 rounded-lg">
            <div className="flex items-start space-x-2">
              <Shield className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-yellow-800">Security Notice</p>
                <p className="text-xs text-yellow-700 mt-1">
                  Your credentials are encrypted and stored securely. We never store your trading password.
                </p>
              </div>
            </div>
          </div>
          
          <form 
            onSubmit={(e) => {
              e.preventDefault();
              handleConnect(broker.id);
            }}
            className="space-y-4"
          >
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">API Key</label>
              <input
                type="text"
                value={connectionForm.apiKey}
                onChange={(e) => setConnectionForm(prev => ({ ...prev, apiKey: e.target.value }))}
                placeholder="Enter your API key"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Client ID</label>
              <input
                type="text"
                value={connectionForm.clientId}
                onChange={(e) => setConnectionForm(prev => ({ ...prev, clientId: e.target.value }))}
                placeholder="Enter your client ID"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Trading Password</label>
              <div className="relative">
                <input
                  type={showCredentials ? "text" : "password"}
                  value={connectionForm.password}
                  onChange={(e) => setConnectionForm(prev => ({ ...prev, password: e.target.value }))}
                  placeholder="Enter your trading password"
                  className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowCredentials(!showCredentials)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showCredentials ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">TOTP Secret</label>
              <input
                type="text"
                value={connectionForm.totpSecret}
                onChange={(e) => setConnectionForm(prev => ({ ...prev, totpSecret: e.target.value }))}
                placeholder="Enter your TOTP secret key"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
              <p className="text-xs text-gray-500 mt-1">
                Found in your broker&apos;s API settings or mobile app
              </p>
            </div>
            
            <div className="flex space-x-3 pt-4">
              <button
                type="button"
                onClick={() => setSelectedBroker(null)}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                disabled={isConnecting}
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isConnecting}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
              >
                {isConnecting ? (
                  <>
                    <RefreshCw className="h-4 w-4 animate-spin" />
                    <span>Connecting...</span>
                  </>
                ) : (
                  <span>Connect</span>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Connect Broker</h1>
          <p className="text-gray-600 mt-1">Manage broker connections and API integrations</p>
        </div>
        <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2">
          <RefreshCw className="h-4 w-4" />
          <span>Refresh Status</span>
        </button>
      </div>

      {/* Connection Status */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Connection Status</h3>
          <div className="flex items-center space-x-2">
            <div className="h-2 w-2 bg-red-500 rounded-full"></div>
            <span className="text-sm text-gray-600">No active connections</span>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900 mb-1">0</div>
            <div className="text-sm text-gray-600">Connected</div>
          </div>
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900 mb-1">3</div>
            <div className="text-sm text-gray-600">Available</div>
          </div>
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900 mb-1">0</div>
            <div className="text-sm text-gray-600">Errors</div>
          </div>
        </div>
      </div>

      {/* Available Brokers */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-6">Available Brokers</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {brokers.map((broker) => (
            <BrokerCard key={broker.id} broker={broker} />
          ))}
        </div>
      </div>

      {/* Setup Instructions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Setup Instructions</h3>
        
        <div className="space-y-4">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-xs font-medium text-blue-600">1</span>
            </div>
            <div>
              <h4 className="font-medium text-gray-900">Create API Credentials</h4>
              <p className="text-sm text-gray-600 mt-1">
                Log into your broker&apos;s website and navigate to API settings to generate your API key and secret.
              </p>
            </div>
          </div>
          
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-xs font-medium text-blue-600">2</span>
            </div>
            <div>
              <h4 className="font-medium text-gray-900">Enable TOTP</h4>
              <p className="text-sm text-gray-600 mt-1">
                Set up Two-Factor Authentication (TOTP) in your broker&apos;s mobile app and note down the secret key.
              </p>
            </div>
          </div>
          
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-xs font-medium text-blue-600">3</span>
            </div>
            <div>
              <h4 className="font-medium text-gray-900">Configure Connection</h4>
              <p className="text-sm text-gray-600 mt-1">
                Enter your credentials in the connection form and test the connection.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Connection Modal */}
      {selectedBroker && <ConnectionModal />}
    </div>
  );
}
