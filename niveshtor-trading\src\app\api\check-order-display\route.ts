import { NextRequest, NextResponse } from 'next/server';
import { automaticGTTService } from '@/lib/automatic-gtt-service';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Checking order display logic...');

    // Get all orders from the automatic GTT service
    const autoOrders = automaticGTTService.getAllOrders();
    
    // Simulate the conversion logic from the GTT Orders page
    const convertedOrders = autoOrders.map((autoOrder: any) => ({
      id: autoOrder.id,
      symbol: autoOrder.symbol,
      name: autoOrder.name,
      orderType: autoOrder.orderType,
      triggerPrice: autoOrder.triggerPrice,
      quantity: autoOrder.quantity,
      status: autoOrder.status,
      createdAt: new Date(autoOrder.createdAt),
      source: autoOrder.source,
      autoCreated: autoOrder.autoCreated,
      signalStrength: autoOrder.signalStrength
    }));

    // Filter by source like the page does
    const signalOrders = convertedOrders.filter(order => order.source === 'SIGNAL');
    const holdingOrders = convertedOrders.filter(order => order.source === 'HOLDING');
    const saleOrders = convertedOrders.filter(order => order.source === 'SALE');

    // Check for any potential issues
    const issues = [];
    
    if (autoOrders.length === 0) {
      issues.push('No orders found in automatic GTT service');
    }
    
    if (signalOrders.length === 0 && autoOrders.length > 0) {
      issues.push('Orders exist but none have source=SIGNAL');
    }

    const duplicateIds = convertedOrders
      .map(o => o.id)
      .filter((id, index, arr) => arr.indexOf(id) !== index);
    
    if (duplicateIds.length > 0) {
      issues.push(`Duplicate order IDs found: ${duplicateIds.join(', ')}`);
    }

    // Check order properties
    const invalidOrders = convertedOrders.filter(order => 
      !order.id || !order.symbol || !order.source || order.triggerPrice <= 0 || order.quantity <= 0
    );

    if (invalidOrders.length > 0) {
      issues.push(`${invalidOrders.length} orders have invalid properties`);
    }

    return NextResponse.json({
      success: true,
      message: 'Order display check completed',
      data: {
        totalAutoOrders: autoOrders.length,
        convertedOrders: convertedOrders.length,
        signalOrders: signalOrders.length,
        holdingOrders: holdingOrders.length,
        saleOrders: saleOrders.length,
        issues,
        orderBreakdown: {
          bySource: {
            SIGNAL: signalOrders.length,
            HOLDING: holdingOrders.length,
            SALE: saleOrders.length
          },
          byStatus: {
            PENDING: convertedOrders.filter(o => o.status === 'PENDING').length,
            TRIGGERED: convertedOrders.filter(o => o.status === 'TRIGGERED').length,
            CANCELLED: convertedOrders.filter(o => o.status === 'CANCELLED').length,
            EXPIRED: convertedOrders.filter(o => o.status === 'EXPIRED').length
          },
          autoCreated: convertedOrders.filter(o => o.autoCreated).length
        },
        sampleOrders: {
          signal: signalOrders.slice(0, 5).map(o => ({
            id: o.id,
            symbol: o.symbol,
            triggerPrice: o.triggerPrice,
            quantity: o.quantity,
            status: o.status,
            autoCreated: o.autoCreated
          })),
          all: convertedOrders.slice(0, 10).map(o => ({
            id: o.id,
            symbol: o.symbol,
            source: o.source,
            status: o.status,
            autoCreated: o.autoCreated
          }))
        }
      }
    });

  } catch (error) {
    console.error('❌ Order display check failed:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Check failed',
        message: 'Order display check failed'
      },
      { status: 500 }
    );
  }
}
