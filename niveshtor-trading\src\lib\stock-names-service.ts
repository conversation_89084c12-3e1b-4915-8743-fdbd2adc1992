// Stock names service for caching and managing stock company names
import { cacheService, CacheKeys } from './cache-service';

interface StockNameEntry {
  symbol: string;
  name: string;
  lastUpdated: number;
}

class StockNamesService {
  private readonly BATCH_SIZE = 50;
  private readonly MAX_RETRIES = 3;

  // Get stock name from cache or fetch if not available
  async getStockName(symbol: string): Promise<string> {
    // Try to get from cache first
    const cached = cacheService.get<string>(CacheKeys.stockName(symbol));
    if (cached) {
      return cached;
    }

    // If not in cache, fetch from Yahoo Finance
    try {
      const { yahooFinanceService } = await import('./yahoo-finance');
      const quote = await yahooFinanceService.getQuote(symbol);
      const name = quote?.name || symbol.replace('.NS', '');

      // Cache the name for 24 hours
      cacheService.set(CacheKeys.stockName(symbol), name);
      return name;
    } catch (error) {
      console.warn(`Failed to fetch name for ${symbol}:`, error);
      // Return symbol without .NS as fallback
      return symbol.replace('.NS', '');
    }
  }

  // Get multiple stock names efficiently
  async getStockNames(symbols: string[]): Promise<Map<string, string>> {
    const namesMap = new Map<string, string>();
    const uncachedSymbols: string[] = [];

    // Check cache for each symbol
    for (const symbol of symbols) {
      const cached = cacheService.get<string>(CacheKeys.stockName(symbol));
      if (cached) {
        namesMap.set(symbol, cached);
      } else {
        uncachedSymbols.push(symbol);
      }
    }

    // If all names are cached, return immediately
    if (uncachedSymbols.length === 0) {
      return namesMap;
    }

    console.log(`📝 Fetching names for ${uncachedSymbols.length} uncached stocks`);

    // Fetch uncached names in batches
    const batches = this.chunkArray(uncachedSymbols, this.BATCH_SIZE);
    
    for (const batch of batches) {
      try {
        const { yahooFinanceService } = await import('./yahoo-finance');
        const quotes = await yahooFinanceService.getMultipleQuotes(batch);
        
        for (const quote of quotes) {
          if (quote && quote.symbol) {
            // Use the name from the quote, or fallback to symbol without .NS
            const name = quote.name || quote.symbol.replace('.NS', '');
            namesMap.set(quote.symbol, name);

            // Cache the name for 24 hours
            cacheService.set(CacheKeys.stockName(quote.symbol), name);
          }
        }

        // Ensure all symbols in the batch have names (even if fallback)
        for (const symbol of batch) {
          if (!namesMap.has(symbol)) {
            const fallbackName = symbol.replace('.NS', '');
            namesMap.set(symbol, fallbackName);
            cacheService.set(CacheKeys.stockName(symbol), fallbackName);
            console.log(`📝 Using fallback name for ${symbol}: ${fallbackName}`);
          }
        }
        
        // Add delay between batches to avoid rate limiting
        if (batches.indexOf(batch) < batches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      } catch (error) {
        console.warn(`Failed to fetch names for batch:`, error);
        
        // Add fallback names for failed batch
        for (const symbol of batch) {
          if (!namesMap.has(symbol)) {
            const fallbackName = symbol.replace('.NS', '');
            namesMap.set(symbol, fallbackName);
            cacheService.set(CacheKeys.stockName(symbol), fallbackName);
          }
        }
      }
    }

    return namesMap;
  }

  // Get all cached stock names
  getCachedStockNames(): Map<string, string> {
    const cachedMap = cacheService.get<Map<string, string>>(CacheKeys.stockNamesMap());
    return cachedMap || new Map();
  }

  // Cache stock names map for quick access
  cacheStockNamesMap(namesMap: Map<string, string>): void {
    cacheService.set(CacheKeys.stockNamesMap(), namesMap);
  }

  // Preload stock names for given symbols
  async preloadStockNames(symbols: string[]): Promise<void> {
    console.log(`🚀 Preloading names for ${symbols.length} stocks`);
    
    try {
      const namesMap = await this.getStockNames(symbols);
      this.cacheStockNamesMap(namesMap);
      console.log(`✅ Preloaded ${namesMap.size} stock names`);
    } catch (error) {
      console.error('Failed to preload stock names:', error);
    }
  }

  // Check if stock name is cached
  isNameCached(symbol: string): boolean {
    return cacheService.has(CacheKeys.stockName(symbol));
  }

  // Get cache statistics for stock names
  getNamesCacheStats(): {
    cachedCount: number;
    totalRequested: number;
    hitRate: number;
  } {
    const stats = cacheService.getStats();
    const nameKeys = stats.keys.filter(key => key.includes('stock-name'));
    
    return {
      cachedCount: nameKeys.length,
      totalRequested: nameKeys.length, // Simplified for now
      hitRate: nameKeys.length > 0 ? 0.8 : 0 // Estimated hit rate
    };
  }

  // Clear all cached stock names
  clearNamesCache(): void {
    cacheService.invalidatePattern('stock-name');
    cacheService.delete(CacheKeys.stockNamesMap());
    console.log('🗑️ Cleared all cached stock names');
  }

  // Refresh stock names (force re-fetch)
  async refreshStockNames(symbols: string[]): Promise<Map<string, string>> {
    // Clear existing cache for these symbols
    for (const symbol of symbols) {
      cacheService.delete(CacheKeys.stockName(symbol));
    }
    
    // Fetch fresh names
    return await this.getStockNames(symbols);
  }

  // Utility function to chunk array into smaller arrays
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  // Get stock name with fallback
  getStockNameSync(symbol: string): string {
    const cached = cacheService.get<string>(CacheKeys.stockName(symbol));
    return cached || symbol.replace('.NS', '');
  }

  // Batch update stock names from quotes
  updateStockNamesFromQuotes(quotes: any[]): void {
    for (const quote of quotes) {
      if (quote && quote.symbol && quote.name) {
        cacheService.set(CacheKeys.stockName(quote.symbol), quote.name);
      }
    }
  }

  // Force refresh all stock names (clears cache and re-fetches)
  async forceRefreshAllNames(symbols: string[]): Promise<void> {
    console.log('🔄 Force refreshing all stock names...');
    this.clearNamesCache();
    await this.preloadStockNames(symbols);
  }
}

// Create singleton instance
export const stockNamesService = new StockNamesService();

// Export types
export type { StockNameEntry };
