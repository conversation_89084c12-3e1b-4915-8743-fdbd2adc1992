'use client';

import { useState, useEffect } from 'react';
import { 
  TrendingUp, 
  DollarSign, 
  Activity, 
  Users,
  ArrowUpRight,
  ArrowDownRight,
  RefreshCw
} from 'lucide-react';
import { useBackgroundData } from '@/hooks/useBackgroundData';

export default function DashboardPage() {
  const { isNamesReady, error: backgroundError } = useBackgroundData();
  const [stats, setStats] = useState({
    totalStocks: 200,
    bohEligible: 0,
    avgPrice: 0,
    marketCap: 0
  });

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-1">
            Welcome to Niveshtor Trading Platform
            {!isNamesReady && (
              <span className="text-blue-600 ml-2">(Initializing...)</span>
            )}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${isNamesReady ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
          <span className="text-sm text-gray-600">
            {isNamesReady ? 'Data Ready' : 'Loading...'}
          </span>
        </div>
      </div>

      {/* Error Display */}
      {backgroundError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="text-red-600">
              <p className="font-medium">Background Service Error</p>
              <p className="text-sm mt-1">{backgroundError.message}</p>
            </div>
          </div>
        </div>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <TrendingUp className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Stocks</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalStocks}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <Activity className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">BOH Eligible</p>
              <p className="text-2xl font-bold text-gray-900">{stats.bohEligible}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <DollarSign className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Avg Price</p>
              <p className="text-2xl font-bold text-gray-900">₹{stats.avgPrice.toFixed(0)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-orange-100 rounded-lg">
              <Users className="h-6 w-6 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Market Cap</p>
              <p className="text-2xl font-bold text-gray-900">₹{(stats.marketCap / 1000000).toFixed(0)}L Cr</p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="space-y-3">
            <a
              href="/dashboard/stocks"
              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <span className="text-gray-700">View All Stocks</span>
              <ArrowUpRight className="h-4 w-4 text-gray-500" />
            </a>
            <a
              href="/dashboard/boh-eligible"
              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <span className="text-gray-700">BOH Eligible Stocks</span>
              <ArrowUpRight className="h-4 w-4 text-gray-500" />
            </a>
            <a
              href="/dashboard/holdings"
              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <span className="text-gray-700">Current Holdings</span>
              <ArrowUpRight className="h-4 w-4 text-gray-500" />
            </a>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">System Status</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Background Service</span>
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${isNamesReady ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
                <span className="text-sm text-gray-700">
                  {isNamesReady ? 'Active' : 'Initializing'}
                </span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Stock Names Cache</span>
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${isNamesReady ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
                <span className="text-sm text-gray-700">
                  {isNamesReady ? 'Ready' : 'Loading'}
                </span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Price Updates</span>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 rounded-full bg-green-500"></div>
                <span className="text-sm text-gray-700">Every 30s</span>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 rounded-full bg-blue-500"></div>
              <span className="text-sm text-gray-600">Background service initialized</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 rounded-full bg-green-500"></div>
              <span className="text-sm text-gray-600">Stock names preloaded</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 rounded-full bg-purple-500"></div>
              <span className="text-sm text-gray-600">Price updates started</span>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Info */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-2">Performance Optimizations Active</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-blue-800">
          <div>
            <strong>Stock Names:</strong> Cached for 24 hours, instant loading
          </div>
          <div>
            <strong>Price Data:</strong> Auto-updates every 30 seconds
          </div>
          <div>
            <strong>Navigation:</strong> Zero-delay page transitions
          </div>
        </div>
      </div>
    </div>
  );
}
