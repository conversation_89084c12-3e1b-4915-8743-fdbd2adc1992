'use client';

import { useState, useEffect } from 'react';
import {
  Wallet,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Pie<PERSON>hart,
  AlertTriangle,
  Target,
  Shield,
  RefreshCw,
  Settings,
  Save,
  Plug,
  CheckCircle,
  XCircle,
  Calculator,
  BarChart3
} from 'lucide-react';
import { formatCurrency, formatPercentage, getChangeColor } from '@/lib/utils';
import { cacheService, CacheKeys } from '@/lib/cache-service';
import { PortfolioSummarySkeleton, InlineLoading, ButtonLoading } from '@/components/ui/LoadingStates';

interface BrokerBalance {
  id: string;
  availableCash: number;
  marginUsed: number;
  marginAvailable: number;
  totalBalance: number;
  lastSyncAt: string;
}

interface FundAllocation {
  id: string;
  strategyName: string;
  totalAllocatedAmount: number;
  maxPerStock: number;
  maxPerTrade: number;
  stockAllocations: StockAllocation[];
}

interface StockAllocation {
  id: string;
  symbol: string;
  allocatedAmount: number;
  usedAmount: number;
  tradesCount: number;
}

interface PortfolioSummary {
  totalValue: number;
  totalInvested: number;
  totalPnL: number;
  totalPnLPercent: number;
  availableCash: number;
  marginUsed: number;
  marginAvailable: number;
}

interface RiskMetrics {
  portfolioRisk: number;
  maxDrawdown: number;
  sharpeRatio: number;
  volatility: number;
}

export default function CapitalManagementPage() {
  // Broker Integration State
  const [brokerBalance, setBrokerBalance] = useState<BrokerBalance | null>(null);
  const [brokerConnected, setBrokerConnected] = useState(false);
  const [loadingBalance, setLoadingBalance] = useState(false);
  const [balanceError, setBalanceError] = useState<string | null>(null);

  // Fund Allocation State
  const [fundAllocation, setFundAllocation] = useState<FundAllocation | null>(null);
  const [loadingAllocation, setLoadingAllocation] = useState(false);
  const [allocationSettings, setAllocationSettings] = useState({
    totalAllocatedAmount: 0,
    maxPerStock: 10000,
    maxPerTrade: 2000
  });

  // UI State
  const [showAllocationSettings, setShowAllocationSettings] = useState(false);
  const [savingSettings, setSavingSettings] = useState(false);

  const [portfolioSummary, setPortfolioSummary] = useState<PortfolioSummary>({
    totalValue: 0,
    totalInvested: 0,
    totalPnL: 0,
    totalPnLPercent: 0,
    availableCash: 0,
    marginUsed: 0,
    marginAvailable: 0
  });
  const [loadingPortfolio, setLoadingPortfolio] = useState(false);

  const [riskMetrics, setRiskMetrics] = useState<RiskMetrics>({
    portfolioRisk: 15.5,
    maxDrawdown: -8.2,
    sharpeRatio: 1.45,
    volatility: 18.3
  });

  // Fetch broker balance with caching
  const fetchBrokerBalance = async (forceRefresh = false) => {
    setLoadingBalance(true);
    setBalanceError(null);

    try {
      const cacheKey = CacheKeys.brokerBalance('default-user');

      // Check cache first unless force refresh
      if (!forceRefresh) {
        const cached = cacheService.get<any>(cacheKey);
        if (cached) {
          setBrokerBalance(cached.data);
          setBrokerConnected(cached.success);

          if (cached.success) {
            setPortfolioSummary(prev => ({
              ...prev,
              availableCash: cached.data.availableCash,
              marginUsed: cached.data.marginUsed,
              marginAvailable: cached.data.marginAvailable
            }));
          }

          setLoadingBalance(false);
          return;
        }
      }

      const response = await fetch('/api/broker/balance?userId=default-user');
      const data = await response.json();

      // Cache the response
      cacheService.set(cacheKey, data);

      if (data.success) {
        setBrokerBalance(data.data);
        setBrokerConnected(true);
        setBalanceError(null);

        // Update portfolio summary with real broker data
        setPortfolioSummary(prev => ({
          ...prev,
          availableCash: data.data.availableCash,
          marginUsed: data.data.marginUsed,
          marginAvailable: data.data.marginAvailable
        }));
      } else {
        setBalanceError(data.error);
        setBrokerConnected(false);

        // Set fallback values when broker is not connected
        if (data.data) {
          setBrokerBalance(data.data);
          setPortfolioSummary(prev => ({
            ...prev,
            availableCash: data.data.availableCash,
            marginUsed: data.data.marginUsed,
            marginAvailable: data.data.marginAvailable
          }));
        }
      }
    } catch (error) {
      setBalanceError('Failed to connect to broker');
      setBrokerConnected(false);
    } finally {
      setLoadingBalance(false);
    }
  };

  // Fetch portfolio data
  const fetchPortfolioData = async () => {
    setLoadingPortfolio(true);

    try {
      const response = await fetch('/api/portfolio/summary');
      const data = await response.json();

      if (data.success) {
        const portfolio = data.data;
        setPortfolioSummary(prev => ({
          ...prev,
          totalValue: portfolio.totalValue,
          totalInvested: portfolio.totalInvested,
          totalPnL: portfolio.totalPnL,
          totalPnLPercent: portfolio.totalPnLPercent
        }));
      }
    } catch (error) {
      console.error('Failed to fetch portfolio data:', error);
    } finally {
      setLoadingPortfolio(false);
    }
  };

  // Fetch fund allocation
  const fetchFundAllocation = async () => {
    setLoadingAllocation(true);

    try {
      const response = await fetch('/api/fund-allocation?userId=default-user&strategy=DARVAS_BOX');
      const data = await response.json();

      if (data.success) {
        setFundAllocation(data.data);
        setAllocationSettings({
          totalAllocatedAmount: data.data.totalAllocatedAmount,
          maxPerStock: data.data.maxPerStock,
          maxPerTrade: data.data.maxPerTrade
        });
      }
    } catch (error) {
      console.error('Failed to fetch fund allocation:', error);
    } finally {
      setLoadingAllocation(false);
    }
  };

  // Save allocation settings
  const saveAllocationSettings = async () => {
    setSavingSettings(true);

    try {
      const response = await fetch('/api/fund-allocation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: 'default-user',
          strategyName: 'DARVAS_BOX',
          ...allocationSettings
        }),
      });

      const data = await response.json();

      if (data.success) {
        setFundAllocation(data.data);
        setShowAllocationSettings(false);
      }
    } catch (error) {
      console.error('Failed to save allocation settings:', error);
    } finally {
      setSavingSettings(false);
    }
  };

  // Calculate allocation metrics
  const calculateAllocationMetrics = () => {
    if (!fundAllocation || !brokerBalance) return null;

    const totalAllocated = fundAllocation.totalAllocatedAmount;
    const availableFunds = brokerBalance.availableCash;
    const allocationPercentage = availableFunds > 0 ? (totalAllocated / availableFunds) * 100 : 0;
    const maxTradesPerStock = Math.floor(fundAllocation.maxPerStock / fundAllocation.maxPerTrade);
    const totalPossibleStocks = totalAllocated > 0 ? Math.floor(totalAllocated / fundAllocation.maxPerStock) : 0;

    return {
      totalAllocated,
      availableFunds,
      allocationPercentage,
      maxTradesPerStock,
      totalPossibleStocks,
      remainingAllocation: Math.max(0, availableFunds - totalAllocated)
    };
  };

  // Refresh all data
  const refreshAllData = async () => {
    await Promise.all([
      fetchBrokerBalance(),
      fetchFundAllocation(),
      fetchPortfolioData()
    ]);
  };

  // Load data on component mount
  useEffect(() => {
    refreshAllData();
  }, []);

  const StatCard = ({
    title,
    value,
    change,
    icon: Icon,
    color = 'blue',
    loading = false,
    error = false
  }: {
    title: string;
    value: string;
    change?: string;
    icon: any;
    color?: string;
    loading?: boolean;
    error?: boolean;
  }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          {loading ? (
            <div className="mt-1 h-8 bg-gray-200 rounded animate-pulse"></div>
          ) : error ? (
            <p className="text-2xl font-bold text-red-500 mt-1">Error</p>
          ) : (
            <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
          )}
          {change && !loading && !error && (
            <p className={`text-sm mt-1 ${getChangeColor(parseFloat(change))}`}>
              {change.startsWith('-') ? '' : '+'}{change}
            </p>
          )}
        </div>
        <div className={`p-3 rounded-full bg-${color}-100`}>
          <Icon className={`h-6 w-6 text-${color}-600 ${loading ? 'animate-pulse' : ''}`} />
        </div>
      </div>
    </div>
  );

  const ProgressBar = ({
    value,
    max,
    label,
    color = 'blue'
  }: {
    value: number;
    max: number;
    label: string;
    color?: string;
  }) => {
    const percentage = max > 0 ? Math.min((value / max) * 100, 100) : 0;

    return (
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">{label}</span>
          <span className="font-medium">
            {formatCurrency(value)} / {formatCurrency(max)}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`bg-${color}-600 h-2 rounded-full transition-all duration-300`}
            style={{ width: `${percentage}%` }}
          ></div>
        </div>
        <div className="text-xs text-gray-500 text-right">
          {percentage.toFixed(1)}% utilized
        </div>
      </div>
    );
  };

  const allocationMetrics = calculateAllocationMetrics();

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Capital Management</h1>
          <p className="text-gray-600 mt-1">Portfolio overview and risk management with real-time broker integration</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={refreshAllData}
            disabled={loadingBalance || loadingAllocation || loadingPortfolio}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
          >
            <RefreshCw className={`h-4 w-4 ${(loadingBalance || loadingAllocation || loadingPortfolio) ? 'animate-spin' : ''}`} />
            <span>Refresh Data</span>
          </button>
          <button
            onClick={() => setShowAllocationSettings(!showAllocationSettings)}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2"
          >
            <Settings className="h-4 w-4" />
            <span>Settings</span>
          </button>
        </div>
      </div>

      {/* Broker Connection Status */}
      <div className={`p-4 rounded-lg border ${
        brokerConnected
          ? 'bg-green-50 border-green-200'
          : 'bg-red-50 border-red-200'
      }`}>
        <div className="flex items-center space-x-2">
          {brokerConnected ? (
            <>
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span className="text-green-800 font-medium">Broker Connected</span>
              {brokerBalance && (
                <span className="text-green-600 text-sm">
                  Last sync: {new Date(brokerBalance.lastSyncAt).toLocaleString()}
                </span>
              )}
            </>
          ) : (
            <>
              <XCircle className="h-5 w-5 text-red-600" />
              <span className="text-red-800 font-medium">Broker Not Connected</span>
              {balanceError && (
                <span className="text-red-600 text-sm">- {balanceError}</span>
              )}
            </>
          )}
        </div>
      </div>

      {/* Real-time Broker Balance Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Available Cash (Broker)"
          value={brokerBalance ? formatCurrency(brokerBalance.availableCash) : formatCurrency(portfolioSummary.availableCash)}
          icon={DollarSign}
          color="green"
          loading={loadingBalance}
          error={!brokerConnected}
        />
        <StatCard
          title="Margin Used"
          value={brokerBalance ? formatCurrency(brokerBalance.marginUsed) : formatCurrency(portfolioSummary.marginUsed)}
          icon={PieChart}
          color="orange"
          loading={loadingBalance}
          error={!brokerConnected}
        />
        <StatCard
          title="Margin Available"
          value={brokerBalance ? formatCurrency(brokerBalance.marginAvailable) : formatCurrency(portfolioSummary.marginAvailable)}
          icon={Wallet}
          color="blue"
          loading={loadingBalance}
          error={!brokerConnected}
        />
        <StatCard
          title="Total Balance"
          value={brokerBalance ? formatCurrency(brokerBalance.totalBalance) : formatCurrency(portfolioSummary.totalValue)}
          change={formatPercentage(portfolioSummary.totalPnLPercent)}
          icon={portfolioSummary.totalPnL >= 0 ? TrendingUp : TrendingDown}
          color={portfolioSummary.totalPnL >= 0 ? "green" : "red"}
          loading={loadingBalance}
          error={!brokerConnected}
        />
      </div>

      {/* Darvas Box Strategy Fund Allocation */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <Target className="h-6 w-6 text-blue-600" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Darvas Box Strategy Fund Allocation</h3>
              <p className="text-sm text-gray-600">Dedicated fund allocation for systematic trading</p>
            </div>
          </div>
          <button
            onClick={() => setShowAllocationSettings(!showAllocationSettings)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
          >
            <Calculator className="h-4 w-4" />
            <span>Configure</span>
          </button>
        </div>

        {loadingAllocation ? (
          <div className="space-y-4">
            <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2"></div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600 mb-1">
                  {formatCurrency(fundAllocation?.totalAllocatedAmount || 0)}
                </div>
                <div className="text-sm text-blue-800">Total Allocated</div>
                {allocationMetrics && (
                  <div className="text-xs text-blue-600 mt-1">
                    {allocationMetrics.allocationPercentage.toFixed(1)}% of available funds
                  </div>
                )}
              </div>

              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600 mb-1">
                  {formatCurrency(fundAllocation?.maxPerStock || 10000)}
                </div>
                <div className="text-sm text-green-800">Max Per Stock</div>
                <div className="text-xs text-green-600 mt-1">
                  Individual stock limit
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <div className="text-2xl font-bold text-orange-600 mb-1">
                  {formatCurrency(fundAllocation?.maxPerTrade || 2000)}
                </div>
                <div className="text-sm text-orange-800">Max Per Trade</div>
                <div className="text-xs text-orange-600 mt-1">
                  Single trade limit
                </div>
              </div>

              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600 mb-1">
                  {allocationMetrics?.maxTradesPerStock || 5}
                </div>
                <div className="text-sm text-purple-800">Trades Per Stock</div>
                <div className="text-xs text-purple-600 mt-1">
                  ₹{fundAllocation?.maxPerStock || 10000} ÷ ₹{fundAllocation?.maxPerTrade || 2000}
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-gray-600 mb-1">
                  {allocationMetrics?.totalPossibleStocks || 0}
                </div>
                <div className="text-sm text-gray-800">Possible Stocks</div>
                <div className="text-xs text-gray-600 mt-1">
                  Based on allocation
                </div>
              </div>

              <div className="text-center p-4 bg-indigo-50 rounded-lg">
                <div className="text-2xl font-bold text-indigo-600 mb-1">
                  {formatCurrency(allocationMetrics?.remainingAllocation || 0)}
                </div>
                <div className="text-sm text-indigo-800">Remaining Funds</div>
                <div className="text-xs text-indigo-600 mt-1">
                  Available for allocation
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Allocation Settings Modal */}
      {showAllocationSettings && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Fund Allocation Settings</h3>
            <button
              onClick={() => setShowAllocationSettings(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <XCircle className="h-6 w-6" />
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Total Strategy Allocation (₹)
              </label>
              <input
                type="number"
                value={allocationSettings.totalAllocatedAmount}
                onChange={(e) => setAllocationSettings(prev => ({
                  ...prev,
                  totalAllocatedAmount: Number(e.target.value)
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="100000"
              />
              <p className="text-xs text-gray-500 mt-1">
                Amount dedicated to Darvas Box strategy
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Per Stock (₹)
              </label>
              <input
                type="number"
                value={allocationSettings.maxPerStock}
                onChange={(e) => setAllocationSettings(prev => ({
                  ...prev,
                  maxPerStock: Number(e.target.value)
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="10000"
              />
              <p className="text-xs text-gray-500 mt-1">
                Maximum allocation per stock symbol
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Per Trade (₹)
              </label>
              <input
                type="number"
                value={allocationSettings.maxPerTrade}
                onChange={(e) => setAllocationSettings(prev => ({
                  ...prev,
                  maxPerTrade: Number(e.target.value)
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="2000"
              />
              <p className="text-xs text-gray-500 mt-1">
                Maximum amount per individual trade
              </p>
            </div>
          </div>

          <div className="mt-6 flex justify-end space-x-3">
            <button
              onClick={() => setShowAllocationSettings(false)}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={saveAllocationSettings}
              disabled={savingSettings}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
            >
              {savingSettings ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <Save className="h-4 w-4" />
              )}
              <span>{savingSettings ? 'Saving...' : 'Save Settings'}</span>
            </button>
          </div>
        </div>
      )}

      {/* Risk Management Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Position Sizing Rules */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Position Sizing Rules</h3>
            <Shield className="h-5 w-5 text-gray-400" />
          </div>

          <div className="space-y-6">
            {/* Per Stock Limit Visualization */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700">Per Stock Limit</span>
                <span className="text-sm text-gray-600">
                  {formatCurrency(fundAllocation?.maxPerStock || 10000)} max
                </span>
              </div>
              <ProgressBar
                value={8000} // Example current allocation
                max={fundAllocation?.maxPerStock || 10000}
                label="RELIANCE"
                color="blue"
              />
            </div>

            {/* Per Trade Limit Visualization */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700">Per Trade Limit</span>
                <span className="text-sm text-gray-600">
                  {formatCurrency(fundAllocation?.maxPerTrade || 2000)} max
                </span>
              </div>
              <div className="grid grid-cols-5 gap-2">
                {Array.from({ length: 5 }, (_, i) => (
                  <div
                    key={i}
                    className={`h-8 rounded flex items-center justify-center text-xs font-medium ${
                      i < 3
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-500'
                    }`}
                  >
                    Trade {i + 1}
                  </div>
                ))}
              </div>
              <div className="text-xs text-gray-500 mt-2 text-center">
                Up to 5 trades per stock (₹{fundAllocation?.maxPerStock || 10000} ÷ ₹{fundAllocation?.maxPerTrade || 2000} = 5 trades)
              </div>
            </div>

            {/* Risk Metrics */}
            <div className="pt-4 border-t border-gray-100">
              <div className="grid grid-cols-2 gap-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Portfolio Risk</span>
                  <span className="font-medium">{riskMetrics.portfolioRisk}%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Max Drawdown</span>
                  <span className="font-medium text-red-600">{riskMetrics.maxDrawdown}%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Sharpe Ratio</span>
                  <span className="font-medium">{riskMetrics.sharpeRatio}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Volatility</span>
                  <span className="font-medium">{riskMetrics.volatility}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Stock Allocation Tracking */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Stock Allocation Tracking</h3>
            <BarChart3 className="h-5 w-5 text-gray-400" />
          </div>

          {fundAllocation?.stockAllocations && fundAllocation.stockAllocations.length > 0 ? (
            <div className="space-y-4">
              {fundAllocation.stockAllocations.map((stock) => (
                <div key={stock.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium text-gray-900">{stock.symbol}</span>
                    <span className="text-sm text-gray-600">
                      {stock.tradesCount} / {Math.floor((fundAllocation?.maxPerStock || 10000) / (fundAllocation?.maxPerTrade || 2000))} trades
                    </span>
                  </div>

                  <ProgressBar
                    value={stock.allocatedAmount}
                    max={fundAllocation?.maxPerStock || 10000}
                    label={`Allocated: ${formatCurrency(stock.allocatedAmount)}`}
                    color="green"
                  />

                  <div className="mt-2 flex justify-between text-sm text-gray-600">
                    <span>Used: {formatCurrency(stock.usedAmount)}</span>
                    <span>Available: {formatCurrency(stock.allocatedAmount - stock.usedAmount)}</span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Target className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No stock allocations yet.</p>
              <p className="text-sm mt-1">Allocations will appear here when you start trading.</p>
            </div>
          )}
        </div>
      </div>

      {/* Fund Allocation Summary */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Fund Allocation Summary</h3>
          <button
            onClick={refreshAllData}
            disabled={loadingBalance || loadingAllocation || loadingPortfolio}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
          >
            <RefreshCw className={`h-4 w-4 ${(loadingBalance || loadingAllocation || loadingPortfolio) ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="w-20 h-20 mx-auto bg-blue-100 rounded-full flex items-center justify-center mb-3">
              <span className="text-lg font-bold text-blue-600">
                {allocationMetrics ? `${allocationMetrics.allocationPercentage.toFixed(0)}%` : '0%'}
              </span>
            </div>
            <h4 className="font-medium text-gray-900">Strategy Allocation</h4>
            <p className="text-sm text-gray-600">{formatCurrency(fundAllocation?.totalAllocatedAmount || 0)}</p>
          </div>

          <div className="text-center">
            <div className="w-20 h-20 mx-auto bg-green-100 rounded-full flex items-center justify-center mb-3">
              <span className="text-lg font-bold text-green-600">
                {allocationMetrics?.totalPossibleStocks || 0}
              </span>
            </div>
            <h4 className="font-medium text-gray-900">Possible Stocks</h4>
            <p className="text-sm text-gray-600">Based on limits</p>
          </div>

          <div className="text-center">
            <div className="w-20 h-20 mx-auto bg-orange-100 rounded-full flex items-center justify-center mb-3">
              <span className="text-lg font-bold text-orange-600">
                {allocationMetrics?.maxTradesPerStock || 5}
              </span>
            </div>
            <h4 className="font-medium text-gray-900">Trades Per Stock</h4>
            <p className="text-sm text-gray-600">Maximum allowed</p>
          </div>

          <div className="text-center">
            <div className="w-20 h-20 mx-auto bg-purple-100 rounded-full flex items-center justify-center mb-3">
              <span className="text-lg font-bold text-purple-600">
                {formatCurrency(allocationMetrics?.remainingAllocation || 0).replace('₹', '₹')}
              </span>
            </div>
            <h4 className="font-medium text-gray-900">Available</h4>
            <p className="text-sm text-gray-600">For allocation</p>
          </div>
        </div>
      </div>

      {/* Alerts and Notifications */}
      <div className="space-y-4">
        {!brokerConnected && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-start">
              <Plug className="h-5 w-5 text-red-600 mt-0.5 mr-3" />
              <div>
                <h4 className="text-sm font-medium text-red-800">Broker Connection Required</h4>
                <p className="text-sm text-red-700 mt-1">
                  Connect your broker account to access real-time balance and enable automated trading features.
                </p>
              </div>
            </div>
          </div>
        )}

        {allocationMetrics && allocationMetrics.allocationPercentage > 80 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start">
              <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 mr-3" />
              <div>
                <h4 className="text-sm font-medium text-yellow-800">High Allocation Warning</h4>
                <p className="text-sm text-yellow-700 mt-1">
                  You have allocated {allocationMetrics.allocationPercentage.toFixed(1)}% of your available funds to the Darvas Box strategy.
                  Consider maintaining some cash reserves for opportunities.
                </p>
              </div>
            </div>
          </div>
        )}

        {fundAllocation && fundAllocation.maxPerTrade > fundAllocation.maxPerStock / 3 && (
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <div className="flex items-start">
              <AlertTriangle className="h-5 w-5 text-orange-600 mt-0.5 mr-3" />
              <div>
                <h4 className="text-sm font-medium text-orange-800">Position Sizing Alert</h4>
                <p className="text-sm text-orange-700 mt-1">
                  Your per-trade limit is high relative to per-stock limit. This reduces diversification opportunities.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
