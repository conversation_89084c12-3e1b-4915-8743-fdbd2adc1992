'use client';

import { OptimizedSidebar } from './OptimizedSidebar';
import { Header } from './header';
import { Suspense } from 'react';
import { NavigationLoading } from '@/components/ui/LoadingStates';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  return (
    <div className="flex h-screen bg-gray-50">
      {/* Optimized Sidebar */}
      <OptimizedSidebar />

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden lg:ml-0">
        {/* Header */}
        <Header />

        {/* Page content with suspense boundary */}
        <main className="flex-1 overflow-y-auto p-6">
          <Suspense fallback={<NavigationLoading />}>
            {children}
          </Suspense>
        </main>
      </div>
    </div>
  );
}
