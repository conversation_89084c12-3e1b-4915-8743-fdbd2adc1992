// React hook for real-time stock data updates
import { useState, useEffect, useCallback, useRef } from 'react';
import { useBackgroundData } from './useBackgroundData';

interface UseRealTimeStocksOptions {
  onUpdate?: (quotes: any[]) => void;
  onError?: (error: Error) => void;
}

interface UseRealTimeStocksReturn {
  // Status
  isActive: boolean;
  lastUpdate: Date | null;
  updateCount: number;
  error: Error | null;
  
  // Methods
  start: () => void;
  stop: () => void;
  forceUpdate: () => Promise<void>;
  clearError: () => void;
}

export function useRealTimeStocks(options: UseRealTimeStocksOptions = {}): UseRealTimeStocksReturn {
  const { onUpdate, onError } = options;
  
  // State
  const [isActive, setIsActive] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [updateCount, setUpdateCount] = useState(0);
  const [error, setError] = useState<Error | null>(null);
  
  // Refs
  const mountedRef = useRef(true);
  const onUpdateRef = useRef(onUpdate);
  const onErrorRef = useRef(onError);
  
  // Update refs when callbacks change
  useEffect(() => {
    onUpdateRef.current = onUpdate;
    onErrorRef.current = onError;
  }, [onUpdate, onError]);
  
  // Real-time update handler
  const handleRealTimeUpdate = useCallback((quotes: any[]) => {
    if (!mountedRef.current) return;
    
    setLastUpdate(new Date());
    setUpdateCount(prev => prev + 1);
    setError(null);
    
    // Call custom callback if provided
    if (onUpdateRef.current) {
      onUpdateRef.current(quotes);
    }
  }, []);
  
  // Error handler
  const handleError = useCallback((err: Error) => {
    if (!mountedRef.current) return;
    
    setError(err);
    
    if (onErrorRef.current) {
      onErrorRef.current(err);
    }
  }, []);
  
  // Background data hook with real-time enabled
  const { forceUpdate: forceBackgroundUpdate } = useBackgroundData({
    enableRealTimeUpdates: isActive,
    onRealTimeUpdate: handleRealTimeUpdate,
    onError: handleError
  });
  
  // Start real-time updates
  const start = useCallback(() => {
    console.log('🚀 Starting real-time stock updates...');
    setIsActive(true);
    setError(null);
  }, []);
  
  // Stop real-time updates
  const stop = useCallback(() => {
    console.log('⏹️ Stopping real-time stock updates...');
    setIsActive(false);
  }, []);
  
  // Force immediate update
  const forceUpdate = useCallback(async () => {
    try {
      await forceBackgroundUpdate();
    } catch (err) {
      handleError(err as Error);
    }
  }, [forceBackgroundUpdate, handleError]);
  
  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);
  
  // Cleanup on unmount
  useEffect(() => {
    mountedRef.current = true;
    
    return () => {
      mountedRef.current = false;
    };
  }, []);
  
  return {
    isActive,
    lastUpdate,
    updateCount,
    error,
    start,
    stop,
    forceUpdate,
    clearError
  };
}

// Hook for listening to real-time updates via custom events
export function useRealTimeUpdates(callback: (quotes: any[]) => void) {
  const callbackRef = useRef(callback);
  
  // Update callback ref
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);
  
  useEffect(() => {
    const handleRealTimeUpdate = (event: CustomEvent) => {
      const { quotes } = event.detail;
      callbackRef.current(quotes);
    };
    
    window.addEventListener('realTimePriceUpdate', handleRealTimeUpdate as EventListener);
    
    return () => {
      window.removeEventListener('realTimePriceUpdate', handleRealTimeUpdate as EventListener);
    };
  }, []);
}

// Hook for seamless stock data with real-time updates
export function useSeamlessStockData() {
  const [stocks, setStocks] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  // Background data for names
  const { isNamesReady, getStockName } = useBackgroundData();
  
  // Real-time updates for prices
  const realTimeStocks = useRealTimeStocks({
    onUpdate: (quotes) => {
      // Update stocks with new price data
      setStocks(prevStocks => {
        const updatedStocks = [...prevStocks];
        
        quotes.forEach(quote => {
          const index = updatedStocks.findIndex(stock => stock.symbol === quote.symbol);
          if (index >= 0) {
            updatedStocks[index] = { ...updatedStocks[index], ...quote };
          } else {
            updatedStocks.push({
              ...quote,
              name: getStockName(quote.symbol)
            });
          }
        });
        
        return updatedStocks;
      });
      
      setIsLoading(false);
    }
  });
  
  // Start real-time updates when names are ready
  useEffect(() => {
    if (isNamesReady && !realTimeStocks.isActive) {
      realTimeStocks.start();
    }
  }, [isNamesReady, realTimeStocks]);
  
  return {
    stocks,
    isLoading: isLoading && !isNamesReady,
    isRealTimeActive: realTimeStocks.isActive,
    lastUpdate: realTimeStocks.lastUpdate,
    updateCount: realTimeStocks.updateCount,
    error: realTimeStocks.error,
    forceUpdate: realTimeStocks.forceUpdate,
    getStockName
  };
}
