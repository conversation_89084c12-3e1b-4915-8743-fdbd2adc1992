{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/hooks/useCentralData.ts"], "sourcesContent": ["// React Hook for Central Data Manager Integration\n// Provides real-time data access and automatic updates for all stock-related pages\n\nimport { useState, useEffect, useCallback, useRef } from 'react';\nimport { NiftyStock } from '@/lib/nifty-stocks';\nimport { WeeklyHighSignal } from '@/lib/weekly-high-signal-detector';\nimport { AutoGTTOrder } from '@/lib/automatic-gtt-service';\nimport { centralDataManager, automaticGTTService, weeklyHighSignalDetector } from '@/lib/service-initializer';\n\ntype DataType = 'nifty200' | 'bohEligible' | 'weeklyHighSignals' | 'gttOrders';\n\ninterface DataState<T> {\n  data: T[];\n  lastUpdated: Date | null;\n  isLoading: boolean;\n  error: string | null;\n}\n\ninterface CentralDataHook {\n  nifty200: DataState<NiftyStock>;\n  bohEligible: DataState<NiftyStock>;\n  weeklyHighSignals: DataState<WeeklyHighSignal>;\n  gttOrders: DataState<AutoGTTOrder>;\n  refreshData: (dataType?: DataType) => Promise<void>;\n  isInitialized: boolean;\n  isServiceRunning: boolean;\n}\n\nexport function useCentralData(): CentralDataHook {\n  const [nifty200, setNifty200] = useState<DataState<NiftyStock>>({\n    data: [],\n    lastUpdated: null,\n    isLoading: false,\n    error: null\n  });\n\n  const [bohEligible, setBohEligible] = useState<DataState<NiftyStock>>({\n    data: [],\n    lastUpdated: null,\n    isLoading: false,\n    error: null\n  });\n\n  const [weeklyHighSignals, setWeeklyHighSignals] = useState<DataState<WeeklyHighSignal>>({\n    data: [],\n    lastUpdated: null,\n    isLoading: false,\n    error: null\n  });\n\n  const [gttOrders, setGttOrders] = useState<DataState<AutoGTTOrder>>({\n    data: [],\n    lastUpdated: null,\n    isLoading: false,\n    error: null\n  });\n\n  const [isInitialized, setIsInitialized] = useState(false);\n  const [isServiceRunning, setIsServiceRunning] = useState(false);\n\n  const pollingIntervals = useRef<Map<DataType, NodeJS.Timeout>>(new Map());\n\n  // Fetch data directly from services\n  const fetchData = useCallback(async (dataType: DataType) => {\n    try {\n      let data: any[] = [];\n      let lastUpdated: Date | null = null;\n\n      switch (dataType) {\n        case 'nifty200':\n          data = centralDataManager.getNifty200Stocks();\n          lastUpdated = centralDataManager.getLastUpdated('nifty200');\n          setNifty200({\n            data,\n            lastUpdated,\n            isLoading: centralDataManager.isDataLoading('nifty200'),\n            error: null\n          });\n          break;\n        case 'bohEligible':\n          data = centralDataManager.getBOHEligibleStocks();\n          lastUpdated = centralDataManager.getLastUpdated('bohEligible');\n          setBohEligible({\n            data,\n            lastUpdated,\n            isLoading: centralDataManager.isDataLoading('bohEligible'),\n            error: null\n          });\n          break;\n        case 'weeklyHighSignals':\n          data = centralDataManager.getWeeklyHighSignals();\n          lastUpdated = centralDataManager.getLastUpdated('weeklyHighSignals');\n          setWeeklyHighSignals({\n            data,\n            lastUpdated,\n            isLoading: centralDataManager.isDataLoading('weeklyHighSignals'),\n            error: null\n          });\n          break;\n        case 'gttOrders':\n          data = centralDataManager.getGTTOrders();\n          lastUpdated = centralDataManager.getLastUpdated('gttOrders');\n          setGttOrders({\n            data,\n            lastUpdated,\n            isLoading: centralDataManager.isDataLoading('gttOrders'),\n            error: null\n          });\n          break;\n      }\n\n      console.log(`📊 Updated ${dataType}: ${data.length} items`);\n    } catch (error) {\n      console.error(`❌ Error fetching ${dataType}:`, error);\n\n      const errorState = {\n        data: [],\n        lastUpdated: null,\n        isLoading: false,\n        error: error instanceof Error ? error.message : 'Unknown error'\n      };\n\n      switch (dataType) {\n        case 'nifty200':\n          setNifty200(prev => ({ ...prev, ...errorState }));\n          break;\n        case 'bohEligible':\n          setBohEligible(prev => ({ ...prev, ...errorState }));\n          break;\n        case 'weeklyHighSignals':\n          setWeeklyHighSignals(prev => ({ ...prev, ...errorState }));\n          break;\n        case 'gttOrders':\n          setGttOrders(prev => ({ ...prev, ...errorState }));\n          break;\n      }\n    }\n  }, []);\n\n  // Check service status directly\n  const checkServiceStatus = useCallback(() => {\n    try {\n      const status = centralDataManager.getStatus();\n      setIsInitialized(status.isInitialized);\n      setIsServiceRunning(status.isRunning);\n\n      console.log('📊 Service status:', {\n        initialized: status.isInitialized,\n        running: status.isRunning,\n        marketOpen: status.isMarketOpen\n      });\n    } catch (error) {\n      console.error('❌ Error checking service status:', error);\n      setIsInitialized(false);\n      setIsServiceRunning(false);\n    }\n  }, []);\n\n  // Initialize service directly if not already initialized\n  const initializeService = useCallback(async () => {\n    try {\n      console.log('🚀 Initializing Central Data Manager directly...');\n\n      if (!centralDataManager.getStatus().isInitialized) {\n        await centralDataManager.initialize();\n        console.log('✅ Central Data Manager initialized');\n      }\n\n      if (!centralDataManager.getStatus().isRunning) {\n        centralDataManager.start();\n        console.log('✅ Central Data Manager started');\n      }\n\n      setIsInitialized(true);\n      setIsServiceRunning(true);\n    } catch (error) {\n      console.error('❌ Error initializing service:', error);\n      setIsInitialized(false);\n      setIsServiceRunning(false);\n    }\n  }, []);\n\n  // Refresh specific data type\n  const refreshData = useCallback(async (dataType?: DataType) => {\n    if (dataType) {\n      await fetchData(dataType);\n    } else {\n      // Refresh all data\n      await Promise.all([\n        fetchData('nifty200'),\n        fetchData('bohEligible'),\n        fetchData('weeklyHighSignals'),\n        fetchData('gttOrders')\n      ]);\n    }\n  }, [fetchData]);\n\n  // Set up polling for real-time updates\n  const setupPolling = useCallback(() => {\n    // Clear existing intervals\n    pollingIntervals.current.forEach(interval => clearInterval(interval));\n    pollingIntervals.current.clear();\n\n    // Set up new intervals\n    const intervals = {\n      nifty200: 30000, // 30 seconds\n      bohEligible: 60000, // 1 minute\n      weeklyHighSignals: 300000, // 5 minutes\n      gttOrders: 30000 // 30 seconds\n    };\n\n    Object.entries(intervals).forEach(([dataType, interval]) => {\n      const intervalId = setInterval(() => {\n        fetchData(dataType as DataType);\n      }, interval);\n      \n      pollingIntervals.current.set(dataType as DataType, intervalId);\n    });\n\n    console.log('⏰ Polling intervals set up for real-time updates');\n  }, [fetchData]);\n\n  // Set up data listeners for real-time updates\n  useEffect(() => {\n    console.log('🔗 Setting up Central Data Manager listeners...');\n\n    // Add listeners for each data type\n    const nifty200Listener = (data: any[], timestamp: Date) => {\n      setNifty200({\n        data,\n        lastUpdated: timestamp,\n        isLoading: false,\n        error: null\n      });\n      console.log(`📊 Nifty200 data updated: ${data.length} stocks`);\n    };\n\n    const bohEligibleListener = (data: any[], timestamp: Date) => {\n      setBohEligible({\n        data,\n        lastUpdated: timestamp,\n        isLoading: false,\n        error: null\n      });\n      console.log(`📊 BOH Eligible data updated: ${data.length} stocks`);\n    };\n\n    const weeklyHighSignalsListener = (data: any[], timestamp: Date) => {\n      setWeeklyHighSignals({\n        data,\n        lastUpdated: timestamp,\n        isLoading: false,\n        error: null\n      });\n      console.log(`📊 Weekly High Signals updated: ${data.length} signals`);\n    };\n\n    const gttOrdersListener = (data: any[], timestamp: Date) => {\n      setGttOrders({\n        data,\n        lastUpdated: timestamp,\n        isLoading: false,\n        error: null\n      });\n      console.log(`📊 GTT Orders updated: ${data.length} orders`);\n    };\n\n    // Add listeners to Central Data Manager\n    centralDataManager.addListener('nifty200', nifty200Listener);\n    centralDataManager.addListener('bohEligible', bohEligibleListener);\n    centralDataManager.addListener('weeklyHighSignals', weeklyHighSignalsListener);\n    centralDataManager.addListener('gttOrders', gttOrdersListener);\n\n    // Initialize and load initial data\n    const initialize = async () => {\n      checkServiceStatus();\n\n      if (!centralDataManager.getStatus().isInitialized) {\n        await initializeService();\n      }\n\n      // Load initial data\n      await refreshData();\n    };\n\n    initialize();\n\n    // Cleanup listeners on unmount\n    return () => {\n      centralDataManager.removeListener('nifty200', nifty200Listener);\n      centralDataManager.removeListener('bohEligible', bohEligibleListener);\n      centralDataManager.removeListener('weeklyHighSignals', weeklyHighSignalsListener);\n      centralDataManager.removeListener('gttOrders', gttOrdersListener);\n\n      pollingIntervals.current.forEach(interval => clearInterval(interval));\n      pollingIntervals.current.clear();\n    };\n  }, []);\n\n  // Re-setup polling when service status changes\n  useEffect(() => {\n    if (isServiceRunning) {\n      setupPolling();\n    }\n  }, [isServiceRunning, setupPolling]);\n\n  return {\n    nifty200,\n    bohEligible,\n    weeklyHighSignals,\n    gttOrders,\n    refreshData,\n    isInitialized,\n    isServiceRunning\n  };\n}\n\n// Specialized hooks for individual data types\nexport function useNifty200Stocks() {\n  const { nifty200, refreshData } = useCentralData();\n  \n  return {\n    stocks: nifty200.data,\n    lastUpdated: nifty200.lastUpdated,\n    isLoading: nifty200.isLoading,\n    error: nifty200.error,\n    refresh: () => refreshData('nifty200')\n  };\n}\n\nexport function useBOHEligibleStocks() {\n  const { bohEligible, refreshData } = useCentralData();\n  \n  return {\n    stocks: bohEligible.data,\n    lastUpdated: bohEligible.lastUpdated,\n    isLoading: bohEligible.isLoading,\n    error: bohEligible.error,\n    refresh: () => refreshData('bohEligible')\n  };\n}\n\nexport function useWeeklyHighSignals() {\n  const { weeklyHighSignals, refreshData } = useCentralData();\n  \n  return {\n    signals: weeklyHighSignals.data,\n    lastUpdated: weeklyHighSignals.lastUpdated,\n    isLoading: weeklyHighSignals.isLoading,\n    error: weeklyHighSignals.error,\n    refresh: () => refreshData('weeklyHighSignals')\n  };\n}\n\nexport function useGTTOrders() {\n  const { gttOrders, refreshData } = useCentralData();\n  \n  return {\n    orders: gttOrders.data,\n    lastUpdated: gttOrders.lastUpdated,\n    isLoading: gttOrders.isLoading,\n    error: gttOrders.error,\n    refresh: () => refreshData('gttOrders')\n  };\n}\n"], "names": [], "mappings": "AAAA,kDAAkD;AAClD,mFAAmF;;;;;;;;AAEnF;AAIA;AAAA;;;AAqBO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;QAC9D,MAAM,EAAE;QACR,aAAa;QACb,WAAW;QACX,OAAO;IACT;IAEA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;QACpE,MAAM,EAAE;QACR,aAAa;QACb,WAAW;QACX,OAAO;IACT;IAEA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+B;QACtF,MAAM,EAAE;QACR,aAAa;QACb,WAAW;QACX,OAAO;IACT;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;QAClE,MAAM,EAAE;QACR,aAAa;QACb,WAAW;QACX,OAAO;IACT;IAEA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAiC,IAAI;IAEnE,oCAAoC;IACpC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACnC,IAAI;YACF,IAAI,OAAc,EAAE;YACpB,IAAI,cAA2B;YAE/B,OAAQ;gBACN,KAAK;oBACH,OAAO,wIAAA,CAAA,qBAAkB,CAAC,iBAAiB;oBAC3C,cAAc,wIAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;oBAChD,YAAY;wBACV;wBACA;wBACA,WAAW,wIAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC;wBAC5C,OAAO;oBACT;oBACA;gBACF,KAAK;oBACH,OAAO,wIAAA,CAAA,qBAAkB,CAAC,oBAAoB;oBAC9C,cAAc,wIAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;oBAChD,eAAe;wBACb;wBACA;wBACA,WAAW,wIAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC;wBAC5C,OAAO;oBACT;oBACA;gBACF,KAAK;oBACH,OAAO,wIAAA,CAAA,qBAAkB,CAAC,oBAAoB;oBAC9C,cAAc,wIAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;oBAChD,qBAAqB;wBACnB;wBACA;wBACA,WAAW,wIAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC;wBAC5C,OAAO;oBACT;oBACA;gBACF,KAAK;oBACH,OAAO,wIAAA,CAAA,qBAAkB,CAAC,YAAY;oBACtC,cAAc,wIAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;oBAChD,aAAa;wBACX;wBACA;wBACA,WAAW,wIAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC;wBAC5C,OAAO;oBACT;oBACA;YACJ;YAEA,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE,KAAK,MAAM,CAAC,MAAM,CAAC;QAC5D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,iBAAiB,EAAE,SAAS,CAAC,CAAC,EAAE;YAE/C,MAAM,aAAa;gBACjB,MAAM,EAAE;gBACR,aAAa;gBACb,WAAW;gBACX,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;YAEA,OAAQ;gBACN,KAAK;oBACH,YAAY,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,GAAG,UAAU;wBAAC,CAAC;oBAC/C;gBACF,KAAK;oBACH,eAAe,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,GAAG,UAAU;wBAAC,CAAC;oBAClD;gBACF,KAAK;oBACH,qBAAqB,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,GAAG,UAAU;wBAAC,CAAC;oBACxD;gBACF,KAAK;oBACH,aAAa,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,GAAG,UAAU;wBAAC,CAAC;oBAChD;YACJ;QACF;IACF,GAAG,EAAE;IAEL,gCAAgC;IAChC,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI;YACF,MAAM,SAAS,wIAAA,CAAA,qBAAkB,CAAC,SAAS;YAC3C,iBAAiB,OAAO,aAAa;YACrC,oBAAoB,OAAO,SAAS;YAEpC,QAAQ,GAAG,CAAC,sBAAsB;gBAChC,aAAa,OAAO,aAAa;gBACjC,SAAS,OAAO,SAAS;gBACzB,YAAY,OAAO,YAAY;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,iBAAiB;YACjB,oBAAoB;QACtB;IACF,GAAG,EAAE;IAEL,yDAAyD;IACzD,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,IAAI,CAAC,wIAAA,CAAA,qBAAkB,CAAC,SAAS,GAAG,aAAa,EAAE;gBACjD,MAAM,wIAAA,CAAA,qBAAkB,CAAC,UAAU;gBACnC,QAAQ,GAAG,CAAC;YACd;YAEA,IAAI,CAAC,wIAAA,CAAA,qBAAkB,CAAC,SAAS,GAAG,SAAS,EAAE;gBAC7C,wIAAA,CAAA,qBAAkB,CAAC,KAAK;gBACxB,QAAQ,GAAG,CAAC;YACd;YAEA,iBAAiB;YACjB,oBAAoB;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,iBAAiB;YACjB,oBAAoB;QACtB;IACF,GAAG,EAAE;IAEL,6BAA6B;IAC7B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrC,IAAI,UAAU;YACZ,MAAM,UAAU;QAClB,OAAO;YACL,mBAAmB;YACnB,MAAM,QAAQ,GAAG,CAAC;gBAChB,UAAU;gBACV,UAAU;gBACV,UAAU;gBACV,UAAU;aACX;QACH;IACF,GAAG;QAAC;KAAU;IAEd,uCAAuC;IACvC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,2BAA2B;QAC3B,iBAAiB,OAAO,CAAC,OAAO,CAAC,CAAA,WAAY,cAAc;QAC3D,iBAAiB,OAAO,CAAC,KAAK;QAE9B,uBAAuB;QACvB,MAAM,YAAY;YAChB,UAAU;YACV,aAAa;YACb,mBAAmB;YACnB,WAAW,MAAM,aAAa;QAChC;QAEA,OAAO,OAAO,CAAC,WAAW,OAAO,CAAC,CAAC,CAAC,UAAU,SAAS;YACrD,MAAM,aAAa,YAAY;gBAC7B,UAAU;YACZ,GAAG;YAEH,iBAAiB,OAAO,CAAC,GAAG,CAAC,UAAsB;QACrD;QAEA,QAAQ,GAAG,CAAC;IACd,GAAG;QAAC;KAAU;IAEd,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC;QAEZ,mCAAmC;QACnC,MAAM,mBAAmB,CAAC,MAAa;YACrC,YAAY;gBACV;gBACA,aAAa;gBACb,WAAW;gBACX,OAAO;YACT;YACA,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,KAAK,MAAM,CAAC,OAAO,CAAC;QAC/D;QAEA,MAAM,sBAAsB,CAAC,MAAa;YACxC,eAAe;gBACb;gBACA,aAAa;gBACb,WAAW;gBACX,OAAO;YACT;YACA,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,KAAK,MAAM,CAAC,OAAO,CAAC;QACnE;QAEA,MAAM,4BAA4B,CAAC,MAAa;YAC9C,qBAAqB;gBACnB;gBACA,aAAa;gBACb,WAAW;gBACX,OAAO;YACT;YACA,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,KAAK,MAAM,CAAC,QAAQ,CAAC;QACtE;QAEA,MAAM,oBAAoB,CAAC,MAAa;YACtC,aAAa;gBACX;gBACA,aAAa;gBACb,WAAW;gBACX,OAAO;YACT;YACA,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,KAAK,MAAM,CAAC,OAAO,CAAC;QAC5D;QAEA,wCAAwC;QACxC,wIAAA,CAAA,qBAAkB,CAAC,WAAW,CAAC,YAAY;QAC3C,wIAAA,CAAA,qBAAkB,CAAC,WAAW,CAAC,eAAe;QAC9C,wIAAA,CAAA,qBAAkB,CAAC,WAAW,CAAC,qBAAqB;QACpD,wIAAA,CAAA,qBAAkB,CAAC,WAAW,CAAC,aAAa;QAE5C,mCAAmC;QACnC,MAAM,aAAa;YACjB;YAEA,IAAI,CAAC,wIAAA,CAAA,qBAAkB,CAAC,SAAS,GAAG,aAAa,EAAE;gBACjD,MAAM;YACR;YAEA,oBAAoB;YACpB,MAAM;QACR;QAEA;QAEA,+BAA+B;QAC/B,OAAO;YACL,wIAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC,YAAY;YAC9C,wIAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC,eAAe;YACjD,wIAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC,qBAAqB;YACvD,wIAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC,aAAa;YAE/C,iBAAiB,OAAO,CAAC,OAAO,CAAC,CAAA,WAAY,cAAc;YAC3D,iBAAiB,OAAO,CAAC,KAAK;QAChC;IACF,GAAG,EAAE;IAEL,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,kBAAkB;YACpB;QACF;IACF,GAAG;QAAC;QAAkB;KAAa;IAEnC,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAGO,SAAS;IACd,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG;IAElC,OAAO;QACL,QAAQ,SAAS,IAAI;QACrB,aAAa,SAAS,WAAW;QACjC,WAAW,SAAS,SAAS;QAC7B,OAAO,SAAS,KAAK;QACrB,SAAS,IAAM,YAAY;IAC7B;AACF;AAEO,SAAS;IACd,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG;IAErC,OAAO;QACL,QAAQ,YAAY,IAAI;QACxB,aAAa,YAAY,WAAW;QACpC,WAAW,YAAY,SAAS;QAChC,OAAO,YAAY,KAAK;QACxB,SAAS,IAAM,YAAY;IAC7B;AACF;AAEO,SAAS;IACd,MAAM,EAAE,iBAAiB,EAAE,WAAW,EAAE,GAAG;IAE3C,OAAO;QACL,SAAS,kBAAkB,IAAI;QAC/B,aAAa,kBAAkB,WAAW;QAC1C,WAAW,kBAAkB,SAAS;QACtC,OAAO,kBAAkB,KAAK;QAC9B,SAAS,IAAM,YAAY;IAC7B;AACF;AAEO,SAAS;IACd,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG;IAEnC,OAAO;QACL,QAAQ,UAAU,IAAI;QACtB,aAAa,UAAU,WAAW;QAClC,WAAW,UAAU,SAAS;QAC9B,OAAO,UAAU,KAAK;QACtB,SAAS,IAAM,YAAY;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 334, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/app/dashboard/weekly-high/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport {\n  ChevronDown,\n  ChevronUp,\n  RefreshCw,\n  TrendingUp,\n  Calendar,\n  Target,\n  Loader,\n  AlertCircle\n} from 'lucide-react';\nimport { formatCurrency, formatPercentage } from '@/lib/utils';\nimport { NiftyStock } from '@/lib/nifty-stocks';\nimport { holdingsService } from '@/lib/holdings-service';\nimport { yahooFinanceService } from '@/lib/yahoo-finance';\nimport { useWeeklyHighSignals } from '@/hooks/useCentralData';\n\ninterface OHLCData {\n  day: string;\n  date: string;\n  open: number;\n  high: number;\n  low: number;\n  close: number;\n}\n\ninterface WeeklyHighStock {\n  symbol: string;\n  name: string;\n  currentPrice: number;\n  lastWeekHighest: number;\n  suggestedBuyPrice: number;\n  percentDifference: number;\n  suggestedGTTQuantity: number;\n  ohlcData: OHLCData[];\n  isBOHEligible: boolean;\n  inHoldings: boolean;\n}\n\nexport default function WeeklyHighSignalPage() {\n  // Central Data Manager for real-time Weekly High Signals\n  const {\n    signals: weeklyHighSignals,\n    lastUpdated: signalsLastUpdated,\n    isLoading: signalsIsLoading,\n    error: signalsError,\n    refresh: refreshSignals\n  } = useWeeklyHighSignals();\n\n  const [stocks, setStocks] = useState<WeeklyHighStock[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [expandedStocks, setExpandedStocks] = useState<Set<string>>(new Set());\n  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);\n\n  // Toggle expanded state for a stock\n  const toggleExpanded = (symbol: string) => {\n    const newExpanded = new Set(expandedStocks);\n    if (newExpanded.has(symbol)) {\n      newExpanded.delete(symbol);\n    } else {\n      newExpanded.add(symbol);\n    }\n    setExpandedStocks(newExpanded);\n  };\n\n  // Generate mock OHLC data for last week\n  const generateOHLCData = (currentPrice: number): OHLCData[] => {\n    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];\n    const ohlcData: OHLCData[] = [];\n\n    // Get last week's dates\n    const today = new Date();\n    const lastFriday = new Date(today);\n    lastFriday.setDate(today.getDate() - ((today.getDay() + 2) % 7)); // Get last Friday\n\n    let basePrice = currentPrice * (0.95 + Math.random() * 0.1); // Start with price variation\n\n    for (let i = 0; i < 5; i++) {\n      const date = new Date(lastFriday);\n      date.setDate(lastFriday.getDate() - (4 - i)); // Monday to Friday of last week\n\n      const variation = 0.02 + Math.random() * 0.03; // 2-5% daily variation\n      const open = basePrice;\n      const high = open * (1 + variation);\n      const low = open * (1 - variation * 0.7);\n      const close = low + (high - low) * Math.random();\n\n      ohlcData.push({\n        day: days[i],\n        date: date.toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' }),\n        open: parseFloat(open.toFixed(2)),\n        high: parseFloat(high.toFixed(2)),\n        low: parseFloat(low.toFixed(2)),\n        close: parseFloat(close.toFixed(2))\n      });\n\n      basePrice = close; // Next day starts with previous close\n    }\n\n    return ohlcData;\n  };\n\n  // Calculate weekly high stock data\n  const calculateWeeklyHighData = (stock: NiftyStock): WeeklyHighStock => {\n    const ohlcData = generateOHLCData(stock.price);\n    const lastWeekHighest = Math.max(...ohlcData.map(d => d.high));\n    const suggestedBuyPrice = lastWeekHighest + 0.05;\n    const percentDifference = ((stock.price - suggestedBuyPrice) / suggestedBuyPrice) * 100;\n    const suggestedGTTQuantity = Math.floor(2000 / suggestedBuyPrice);\n\n    return {\n      symbol: stock.symbol,\n      name: stock.name,\n      currentPrice: stock.price,\n      lastWeekHighest,\n      suggestedBuyPrice,\n      percentDifference,\n      suggestedGTTQuantity,\n      ohlcData,\n      isBOHEligible: stock.isBOHEligible || false,\n      inHoldings: stock.inHoldings\n    };\n  };\n\n  // Load BOH eligible stocks\n  const loadBOHEligibleStocks = async () => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      console.log('🔍 Loading BOH eligible stocks for Weekly High signals...');\n\n      // Fetch BOH eligible stocks from the API\n      const response = await fetch('/api/stocks/nifty200?batchIndex=0&batchSize=200');\n      const data = await response.json();\n\n      if (!data.success) {\n        throw new Error(data.error || 'Failed to fetch stock data');\n      }\n\n      // Get current holdings\n      const holdingSymbols = holdingsService.getAllHoldings().map(h => h.symbol);\n\n      // Filter for BOH eligible stocks not in holdings\n      const bohEligibleStocks = data.data.stocks.filter((stock: NiftyStock) =>\n        stock.isBOHEligible && !holdingSymbols.includes(stock.symbol)\n      );\n\n      console.log(`✅ Found ${bohEligibleStocks.length} BOH eligible stocks not in holdings`);\n\n      // Calculate weekly high data for each stock\n      const weeklyHighStocks = bohEligibleStocks.map(calculateWeeklyHighData);\n\n      // Sort by percentage difference (closest to suggested buy price first)\n      weeklyHighStocks.sort((a, b) => Math.abs(a.percentDifference) - Math.abs(b.percentDifference));\n\n      setStocks(weeklyHighStocks);\n      setLastUpdate(new Date());\n\n    } catch (err) {\n      console.error('❌ Error loading BOH eligible stocks:', err);\n      setError(err instanceof Error ? err.message : 'Failed to load data');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Use Central Data Manager signals when available\n  useEffect(() => {\n    if (weeklyHighSignals.length > 0) {\n      console.log(`📊 Using ${weeklyHighSignals.length} signals from Central Data Manager`);\n\n      // Convert signals to WeeklyHighStock format\n      const convertedStocks: WeeklyHighStock[] = weeklyHighSignals.map(signal => ({\n        symbol: signal.symbol,\n        name: signal.name,\n        currentPrice: signal.currentPrice,\n        lastWeekHigh: signal.lastWeekHigh,\n        suggestedBuyPrice: signal.suggestedBuyPrice,\n        percentDifference: signal.percentDifference,\n        signalStrength: signal.signalStrength,\n        suggestedGTTQuantity: signal.suggestedGTTQuantity,\n        isEligible: true,\n        inHoldings: false\n      }));\n\n      setStocks(convertedStocks);\n      setLastUpdate(signalsLastUpdated || new Date());\n      setLoading(false);\n      setError(null);\n    } else if (signalsError) {\n      setError(signalsError);\n      setLoading(false);\n    } else if (!signalsIsLoading && weeklyHighSignals.length === 0) {\n      // Fallback to original loading method if no signals from Central Data Manager\n      loadBOHEligibleStocks();\n    }\n  }, [weeklyHighSignals, signalsLastUpdated, signalsError, signalsIsLoading]);\n\n  // Load data on component mount (fallback)\n  useEffect(() => {\n    if (weeklyHighSignals.length === 0 && !signalsIsLoading) {\n      loadBOHEligibleStocks();\n    }\n  }, []);\n\n  return (\n    <div className=\"p-6 max-w-7xl mx-auto space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Weekly High Signal</h1>\n          <p className=\"text-gray-600 mt-1\">\n            BOH Eligible Stocks (excluding currently held stocks) • {stocks.length} stocks found\n          </p>\n          {lastUpdate && (\n            <p className=\"text-sm text-gray-500 mt-1\">\n              Last updated: {lastUpdate.toLocaleTimeString()}\n            </p>\n          )}\n        </div>\n\n        <div className=\"flex items-center space-x-3\">\n          <button\n            onClick={() => {\n              console.log('🔄 Refreshing Weekly High Signals via Central Data Manager...');\n              refreshSignals();\n            }}\n            disabled={loading || signalsIsLoading}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center space-x-2\"\n          >\n            <RefreshCw className={`h-4 w-4 ${loading || signalsIsLoading ? 'animate-spin' : ''}`} />\n            <span>Refresh</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Summary Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">BOH Eligible Stocks</p>\n              <p className=\"text-2xl font-bold text-blue-600 mt-1\">{stocks.length}</p>\n            </div>\n            <Target className=\"h-8 w-8 text-blue-600\" />\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Near Buy Price</p>\n              <p className=\"text-2xl font-bold text-green-600 mt-1\">\n                {stocks.filter(s => Math.abs(s.percentDifference) <= 5).length}\n              </p>\n            </div>\n            <TrendingUp className=\"h-8 w-8 text-green-600\" />\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Avg GTT Quantity</p>\n              <p className=\"text-2xl font-bold text-gray-900 mt-1\">\n                {stocks.length > 0 ? Math.round(stocks.reduce((sum, s) => sum + s.suggestedGTTQuantity, 0) / stocks.length) : 0}\n              </p>\n            </div>\n            <Calendar className=\"h-8 w-8 text-gray-600\" />\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Total Investment</p>\n              <p className=\"text-2xl font-bold text-purple-600 mt-1\">\n                {formatCurrency(stocks.length * 2000)}\n              </p>\n            </div>\n            <Target className=\"h-8 w-8 text-purple-600\" />\n          </div>\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n        {error && (\n          <div className=\"p-6 border-b border-gray-200\">\n            <div className=\"flex items-center text-red-600\">\n              <AlertCircle className=\"h-5 w-5 mr-2\" />\n              <span>Error: {error}</span>\n            </div>\n          </div>\n        )}\n\n        {loading ? (\n          <div className=\"p-12 text-center\">\n            <Loader className=\"h-8 w-8 mx-auto mb-4 animate-spin text-blue-600\" />\n            <p className=\"text-gray-500\">Loading BOH eligible stocks...</p>\n            <p className=\"text-sm text-gray-400 mt-1\">Analyzing weekly high patterns</p>\n          </div>\n        ) : stocks.length === 0 ? (\n          <div className=\"p-12 text-center\">\n            <TrendingUp className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n            <p className=\"text-gray-500\">No BOH eligible stocks found</p>\n            <p className=\"text-sm text-gray-400 mt-1\">All BOH eligible stocks are currently in holdings</p>\n          </div>\n        ) : (\n          <div className=\"divide-y divide-gray-200\">\n            {/* Table Header */}\n            <div className=\"p-4 bg-gray-50 grid grid-cols-7 gap-4 text-sm font-medium text-gray-700\">\n              <div>Stock</div>\n              <div className=\"text-right\">Current Price</div>\n              <div className=\"text-right\">Last Week's High</div>\n              <div className=\"text-right\">Suggested Buy Price</div>\n              <div className=\"text-right\">% Difference</div>\n              <div className=\"text-right\">GTT Quantity</div>\n              <div className=\"text-center\">Details</div>\n            </div>\n\n            {/* Stock Rows */}\n            {stocks.map((stock) => (\n              <div key={stock.symbol}>\n                {/* Main Row */}\n                <div className=\"p-4 hover:bg-gray-50 grid grid-cols-7 gap-4 items-center\">\n                  {/* Stock Info */}\n                  <div>\n                    <div className=\"flex items-center space-x-2\">\n                      <h4 className=\"font-medium text-gray-900\">{stock.symbol}</h4>\n                      <span className=\"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full\">\n                        BOH\n                      </span>\n                    </div>\n                    <p className=\"text-sm text-gray-600 truncate\">{stock.name}</p>\n                  </div>\n\n                  {/* Current Price */}\n                  <div className=\"text-right\">\n                    <p className=\"font-medium text-gray-900\">{formatCurrency(stock.currentPrice)}</p>\n                  </div>\n\n                  {/* Last Week's High */}\n                  <div className=\"text-right\">\n                    <p className=\"font-medium text-gray-900\">{formatCurrency(stock.lastWeekHighest)}</p>\n                  </div>\n\n                  {/* Suggested Buy Price */}\n                  <div className=\"text-right\">\n                    <p className=\"font-medium text-green-600\">{formatCurrency(stock.suggestedBuyPrice)}</p>\n                    <p className=\"text-xs text-gray-500\">High + ₹0.05</p>\n                  </div>\n\n                  {/* % Difference */}\n                  <div className=\"text-right\">\n                    <p className={`font-medium ${\n                      stock.percentDifference >= 0\n                        ? 'text-green-600'\n                        : 'text-red-600'\n                    }`}>\n                      {formatPercentage(stock.percentDifference)}\n                    </p>\n                  </div>\n\n                  {/* GTT Quantity */}\n                  <div className=\"text-right\">\n                    <p className=\"font-medium text-gray-900\">{stock.suggestedGTTQuantity}</p>\n                    <p className=\"text-xs text-gray-500\">₹2,000 ÷ Buy Price</p>\n                  </div>\n\n                  {/* Expand Button */}\n                  <div className=\"text-center\">\n                    <button\n                      onClick={() => toggleExpanded(stock.symbol)}\n                      className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n                    >\n                      {expandedStocks.has(stock.symbol) ? (\n                        <ChevronUp className=\"h-4 w-4 text-gray-600\" />\n                      ) : (\n                        <ChevronDown className=\"h-4 w-4 text-gray-600\" />\n                      )}\n                    </button>\n                  </div>\n                </div>\n\n                {/* Expanded OHLC Table */}\n                {expandedStocks.has(stock.symbol) && (\n                  <div className=\"px-4 pb-4 bg-gray-50\">\n                    <div className=\"bg-white rounded-lg border border-gray-200 overflow-hidden\">\n                      <div className=\"px-4 py-3 bg-gray-100 border-b border-gray-200\">\n                        <h5 className=\"font-medium text-gray-900\">\n                          Last Week's OHLC Data - {stock.symbol}\n                        </h5>\n                      </div>\n\n                      <div className=\"overflow-x-auto\">\n                        <table className=\"w-full\">\n                          <thead className=\"bg-gray-50\">\n                            <tr>\n                              <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                Day\n                              </th>\n                              <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                Date\n                              </th>\n                              <th className=\"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                Open\n                              </th>\n                              <th className=\"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                High\n                              </th>\n                              <th className=\"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                Low\n                              </th>\n                              <th className=\"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                Close\n                              </th>\n                              <th className=\"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                Last Week's Highest\n                              </th>\n                            </tr>\n                          </thead>\n                          <tbody className=\"bg-white divide-y divide-gray-200\">\n                            {stock.ohlcData.map((dayData, index) => (\n                              <tr key={`${stock.symbol}-${index}`} className=\"hover:bg-gray-50\">\n                                <td className=\"px-4 py-3 text-sm font-medium text-gray-900\">\n                                  {dayData.day}\n                                </td>\n                                <td className=\"px-4 py-3 text-sm text-gray-600\">\n                                  {dayData.date}\n                                </td>\n                                <td className=\"px-4 py-3 text-sm text-gray-900 text-right\">\n                                  {formatCurrency(dayData.open)}\n                                </td>\n                                <td className=\"px-4 py-3 text-sm text-gray-900 text-right\">\n                                  <span className={dayData.high === stock.lastWeekHighest ? 'font-bold text-green-600' : ''}>\n                                    {formatCurrency(dayData.high)}\n                                  </span>\n                                </td>\n                                <td className=\"px-4 py-3 text-sm text-gray-900 text-right\">\n                                  {formatCurrency(dayData.low)}\n                                </td>\n                                <td className=\"px-4 py-3 text-sm text-gray-900 text-right\">\n                                  {formatCurrency(dayData.close)}\n                                </td>\n                                <td className=\"px-4 py-3 text-sm text-right\">\n                                  {index === stock.ohlcData.length - 1 && (\n                                    <span className=\"font-bold text-green-600\">\n                                      {formatCurrency(stock.lastWeekHighest)}\n                                    </span>\n                                  )}\n                                </td>\n                              </tr>\n                            ))}\n                          </tbody>\n                        </table>\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Signal Timing Info */}\n      <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n        <div className=\"flex items-start\">\n          <Calendar className=\"h-5 w-5 text-blue-600 mt-0.5 mr-3\" />\n          <div>\n            <h3 className=\"font-medium text-blue-900\">New Darvas Box Strategy - Signal Timing</h3>\n            <p className=\"text-blue-700 mt-1\">\n              The signal generation for the New Darvas Box Strategy runs every Friday at 8:00 PM.\n              This ensures fresh weekly high analysis for the upcoming trading week.\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAEA;AAEA;AAjBA;;;;;;;AAyCe,SAAS;IACtB,yDAAyD;IACzD,MAAM,EACJ,SAAS,iBAAiB,EAC1B,aAAa,kBAAkB,EAC/B,WAAW,gBAAgB,EAC3B,OAAO,YAAY,EACnB,SAAS,cAAc,EACxB,GAAG,CAAA,GAAA,8HAAA,CAAA,uBAAoB,AAAD;IAEvB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IACtE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAE1D,oCAAoC;IACpC,MAAM,iBAAiB,CAAC;QACtB,MAAM,cAAc,IAAI,IAAI;QAC5B,IAAI,YAAY,GAAG,CAAC,SAAS;YAC3B,YAAY,MAAM,CAAC;QACrB,OAAO;YACL,YAAY,GAAG,CAAC;QAClB;QACA,kBAAkB;IACpB;IAEA,wCAAwC;IACxC,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO;YAAC;YAAU;YAAW;YAAa;YAAY;SAAS;QACrE,MAAM,WAAuB,EAAE;QAE/B,wBAAwB;QACxB,MAAM,QAAQ,IAAI;QAClB,MAAM,aAAa,IAAI,KAAK;QAC5B,WAAW,OAAO,CAAC,MAAM,OAAO,KAAM,CAAC,MAAM,MAAM,KAAK,CAAC,IAAI,IAAK,kBAAkB;QAEpF,IAAI,YAAY,eAAe,CAAC,OAAO,KAAK,MAAM,KAAK,GAAG,GAAG,6BAA6B;QAE1F,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YAC1B,MAAM,OAAO,IAAI,KAAK;YACtB,KAAK,OAAO,CAAC,WAAW,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,gCAAgC;YAE9E,MAAM,YAAY,OAAO,KAAK,MAAM,KAAK,MAAM,uBAAuB;YACtE,MAAM,OAAO;YACb,MAAM,OAAO,OAAO,CAAC,IAAI,SAAS;YAClC,MAAM,MAAM,OAAO,CAAC,IAAI,YAAY,GAAG;YACvC,MAAM,QAAQ,MAAM,CAAC,OAAO,GAAG,IAAI,KAAK,MAAM;YAE9C,SAAS,IAAI,CAAC;gBACZ,KAAK,IAAI,CAAC,EAAE;gBACZ,MAAM,KAAK,kBAAkB,CAAC,SAAS;oBAAE,KAAK;oBAAW,OAAO;oBAAS,MAAM;gBAAU;gBACzF,MAAM,WAAW,KAAK,OAAO,CAAC;gBAC9B,MAAM,WAAW,KAAK,OAAO,CAAC;gBAC9B,KAAK,WAAW,IAAI,OAAO,CAAC;gBAC5B,OAAO,WAAW,MAAM,OAAO,CAAC;YAClC;YAEA,YAAY,OAAO,sCAAsC;QAC3D;QAEA,OAAO;IACT;IAEA,mCAAmC;IACnC,MAAM,0BAA0B,CAAC;QAC/B,MAAM,WAAW,iBAAiB,MAAM,KAAK;QAC7C,MAAM,kBAAkB,KAAK,GAAG,IAAI,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;QAC5D,MAAM,oBAAoB,kBAAkB;QAC5C,MAAM,oBAAoB,AAAC,CAAC,MAAM,KAAK,GAAG,iBAAiB,IAAI,oBAAqB;QACpF,MAAM,uBAAuB,KAAK,KAAK,CAAC,OAAO;QAE/C,OAAO;YACL,QAAQ,MAAM,MAAM;YACpB,MAAM,MAAM,IAAI;YAChB,cAAc,MAAM,KAAK;YACzB;YACA;YACA;YACA;YACA;YACA,eAAe,MAAM,aAAa,IAAI;YACtC,YAAY,MAAM,UAAU;QAC9B;IACF;IAEA,2BAA2B;IAC3B,MAAM,wBAAwB;QAC5B,WAAW;QACX,SAAS;QAET,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,yCAAyC;YACzC,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,KAAK,OAAO,EAAE;gBACjB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,uBAAuB;YACvB,MAAM,iBAAiB,iIAAA,CAAA,kBAAe,CAAC,cAAc,GAAG,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;YAEzE,iDAAiD;YACjD,MAAM,oBAAoB,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,QACjD,MAAM,aAAa,IAAI,CAAC,eAAe,QAAQ,CAAC,MAAM,MAAM;YAG9D,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,kBAAkB,MAAM,CAAC,oCAAoC,CAAC;YAErF,4CAA4C;YAC5C,MAAM,mBAAmB,kBAAkB,GAAG,CAAC;YAE/C,uEAAuE;YACvE,iBAAiB,IAAI,CAAC,CAAC,GAAG,IAAM,KAAK,GAAG,CAAC,EAAE,iBAAiB,IAAI,KAAK,GAAG,CAAC,EAAE,iBAAiB;YAE5F,UAAU;YACV,cAAc,IAAI;QAEpB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,wCAAwC;YACtD,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,kBAAkB,MAAM,GAAG,GAAG;YAChC,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,kBAAkB,MAAM,CAAC,kCAAkC,CAAC;YAEpF,4CAA4C;YAC5C,MAAM,kBAAqC,kBAAkB,GAAG,CAAC,CAAA,SAAU,CAAC;oBAC1E,QAAQ,OAAO,MAAM;oBACrB,MAAM,OAAO,IAAI;oBACjB,cAAc,OAAO,YAAY;oBACjC,cAAc,OAAO,YAAY;oBACjC,mBAAmB,OAAO,iBAAiB;oBAC3C,mBAAmB,OAAO,iBAAiB;oBAC3C,gBAAgB,OAAO,cAAc;oBACrC,sBAAsB,OAAO,oBAAoB;oBACjD,YAAY;oBACZ,YAAY;gBACd,CAAC;YAED,UAAU;YACV,cAAc,sBAAsB,IAAI;YACxC,WAAW;YACX,SAAS;QACX,OAAO,IAAI,cAAc;YACvB,SAAS;YACT,WAAW;QACb,OAAO,IAAI,CAAC,oBAAoB,kBAAkB,MAAM,KAAK,GAAG;YAC9D,8EAA8E;YAC9E;QACF;IACF,GAAG;QAAC;QAAmB;QAAoB;QAAc;KAAiB;IAE1E,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,kBAAkB,MAAM,KAAK,KAAK,CAAC,kBAAkB;YACvD;QACF;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;;oCAAqB;oCACyB,OAAO,MAAM;oCAAC;;;;;;;4BAExE,4BACC,8OAAC;gCAAE,WAAU;;oCAA6B;oCACzB,WAAW,kBAAkB;;;;;;;;;;;;;kCAKlD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS;gCACP,QAAQ,GAAG,CAAC;gCACZ;4BACF;4BACA,UAAU,WAAW;4BACrB,WAAU;;8CAEV,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAW,CAAC,QAAQ,EAAE,WAAW,mBAAmB,iBAAiB,IAAI;;;;;;8CACpF,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAyC,OAAO,MAAM;;;;;;;;;;;;8CAErE,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAItB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDACV,OAAO,MAAM,CAAC,CAAA,IAAK,KAAK,GAAG,CAAC,EAAE,iBAAiB,KAAK,GAAG,MAAM;;;;;;;;;;;;8CAGlE,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAI1B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDACV,OAAO,MAAM,GAAG,IAAI,KAAK,KAAK,CAAC,OAAO,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,oBAAoB,EAAE,KAAK,OAAO,MAAM,IAAI;;;;;;;;;;;;8CAGlH,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIxB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDACV,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,MAAM,GAAG;;;;;;;;;;;;8CAGpC,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMxB,8OAAC;gBAAI,WAAU;;oBACZ,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC;;wCAAK;wCAAQ;;;;;;;;;;;;;;;;;;oBAKnB,wBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;0CAC7B,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;+BAE1C,OAAO,MAAM,KAAK,kBACpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;0CAC7B,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;6CAG5C,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAI;;;;;;kDACL,8OAAC;wCAAI,WAAU;kDAAa;;;;;;kDAC5B,8OAAC;wCAAI,WAAU;kDAAa;;;;;;kDAC5B,8OAAC;wCAAI,WAAU;kDAAa;;;;;;kDAC5B,8OAAC;wCAAI,WAAU;kDAAa;;;;;;kDAC5B,8OAAC;wCAAI,WAAU;kDAAa;;;;;;kDAC5B,8OAAC;wCAAI,WAAU;kDAAc;;;;;;;;;;;;4BAI9B,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;;sDAEC,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAA6B,MAAM,MAAM;;;;;;8EACvD,8OAAC;oEAAK,WAAU;8EAA2D;;;;;;;;;;;;sEAI7E,8OAAC;4DAAE,WAAU;sEAAkC,MAAM,IAAI;;;;;;;;;;;;8DAI3D,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;kEAA6B,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,YAAY;;;;;;;;;;;8DAI7E,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;kEAA6B,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,eAAe;;;;;;;;;;;8DAIhF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAA8B,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,iBAAiB;;;;;;sEACjF,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAIvC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAW,CAAC,YAAY,EACzB,MAAM,iBAAiB,IAAI,IACvB,mBACA,gBACJ;kEACC,CAAA,GAAA,mHAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,iBAAiB;;;;;;;;;;;8DAK7C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAA6B,MAAM,oBAAoB;;;;;;sEACpE,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAIvC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,SAAS,IAAM,eAAe,MAAM,MAAM;wDAC1C,WAAU;kEAET,eAAe,GAAG,CAAC,MAAM,MAAM,kBAC9B,8OAAC,gNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;iFAErB,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;wCAO9B,eAAe,GAAG,CAAC,MAAM,MAAM,mBAC9B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAG,WAAU;;gEAA4B;gEACf,MAAM,MAAM;;;;;;;;;;;;kEAIzC,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAM,WAAU;;8EACf,8OAAC;oEAAM,WAAU;8EACf,cAAA,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAiF;;;;;;0FAG/F,8OAAC;gFAAG,WAAU;0FAAiF;;;;;;0FAG/F,8OAAC;gFAAG,WAAU;0FAAkF;;;;;;0FAGhG,8OAAC;gFAAG,WAAU;0FAAkF;;;;;;0FAGhG,8OAAC;gFAAG,WAAU;0FAAkF;;;;;;0FAGhG,8OAAC;gFAAG,WAAU;0FAAkF;;;;;;0FAGhG,8OAAC;gFAAG,WAAU;0FAAkF;;;;;;;;;;;;;;;;;8EAKpG,8OAAC;oEAAM,WAAU;8EACd,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC5B,8OAAC;4EAAoC,WAAU;;8FAC7C,8OAAC;oFAAG,WAAU;8FACX,QAAQ,GAAG;;;;;;8FAEd,8OAAC;oFAAG,WAAU;8FACX,QAAQ,IAAI;;;;;;8FAEf,8OAAC;oFAAG,WAAU;8FACX,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,IAAI;;;;;;8FAE9B,8OAAC;oFAAG,WAAU;8FACZ,cAAA,8OAAC;wFAAK,WAAW,QAAQ,IAAI,KAAK,MAAM,eAAe,GAAG,6BAA6B;kGACpF,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,IAAI;;;;;;;;;;;8FAGhC,8OAAC;oFAAG,WAAU;8FACX,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,GAAG;;;;;;8FAE7B,8OAAC;oFAAG,WAAU;8FACX,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,KAAK;;;;;;8FAE/B,8OAAC;oFAAG,WAAU;8FACX,UAAU,MAAM,QAAQ,CAAC,MAAM,GAAG,mBACjC,8OAAC;wFAAK,WAAU;kGACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,eAAe;;;;;;;;;;;;2EAxBpC,GAAG,MAAM,MAAM,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCArGzC,MAAM,MAAM;;;;;;;;;;;;;;;;;0BAgJ9B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA4B;;;;;;8CAC1C,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C", "debugId": null}}]}