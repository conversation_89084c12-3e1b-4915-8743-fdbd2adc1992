import { NextRequest, NextResponse } from 'next/server';
import { centralDataManager } from '@/lib/central-data-manager';
import { automaticGTTService } from '@/lib/automatic-gtt-service';
import { weeklyHighSignalDetector } from '@/lib/weekly-high-signal-detector';

export async function GET(request: NextRequest) {
  try {
    console.log('🎯 Testing complete end-to-end background automation...');

    const testResults = {
      timestamp: new Date().toISOString(),
      tests: [] as any[]
    };

    // Test 1: Background Services Status
    console.log('🔧 Test 1: Background Services Status...');
    const centralStatus = centralDataManager.getStatus();
    const gttStats = automaticGTTService.getStatistics();
    const signalStatus = weeklyHighSignalDetector.getStatus();

    const servicesRunning = centralStatus.isInitialized && centralStatus.isRunning && 
                           gttStats.isInitialized && signalStatus.isRunning;

    testResults.tests.push({
      name: 'Background Services Status',
      status: servicesRunning ? 'PASS' : 'FAIL',
      data: {
        centralDataManager: {
          initialized: centralStatus.isInitialized,
          running: centralStatus.isRunning,
          marketOpen: centralStatus.isMarketOpen
        },
        automaticGTTService: {
          initialized: gttStats.isInitialized,
          totalOrders: gttStats.totalOrders,
          todayOrders: gttStats.todayOrders
        },
        weeklyHighSignalDetector: {
          running: signalStatus.isRunning,
          lastSignalCount: signalStatus.lastSignalCount,
          listenerCount: signalStatus.listenerCount
        }
      }
    });

    // Test 2: Background Data Loading
    console.log('📊 Test 2: Background Data Loading...');
    const cacheStatus = centralDataManager.getCacheStatus();
    
    const dataLoaded = cacheStatus.nifty200Count > 0 && 
                      cacheStatus.bohEligibleCount > 0 && 
                      cacheStatus.weeklyHighSignalsCount > 0 && 
                      cacheStatus.gttOrdersCount > 0;

    testResults.tests.push({
      name: 'Background Data Loading',
      status: dataLoaded ? 'PASS' : 'FAIL',
      data: {
        nifty200Stocks: cacheStatus.nifty200Count,
        bohEligibleStocks: cacheStatus.bohEligibleCount,
        weeklyHighSignals: cacheStatus.weeklyHighSignalsCount,
        gttOrders: cacheStatus.gttOrdersCount,
        lastUpdated: cacheStatus.lastUpdated,
        listenerCounts: cacheStatus.listenerCounts
      }
    });

    // Test 3: Instant Page Navigation (API Response Times)
    console.log('⚡ Test 3: Instant Page Navigation...');
    const navigationTests = [];
    const requestUrl = new URL(request.url);
    const baseUrl = `${requestUrl.protocol}//${requestUrl.host}`;
    
    const endpoints = [
      { name: 'Stock Universe', endpoint: 'nifty200' },
      { name: 'BOH Eligible', endpoint: 'boh-eligible' },
      { name: 'Weekly High Signals', endpoint: 'weekly-high-signals' },
      { name: 'GTT Orders', endpoint: 'gtt-orders' }
    ];

    for (const { name, endpoint } of endpoints) {
      const startTime = performance.now();
      try {
        const response = await fetch(`${baseUrl}/api/data-manager?action=${endpoint}`);
        const endTime = performance.now();
        const responseTime = Math.round(endTime - startTime);
        
        const result = await response.json();
        
        navigationTests.push({
          page: name,
          responseTime,
          isInstant: responseTime < 200,
          dataCount: result.data?.length || 0,
          status: response.ok && responseTime < 200 ? 'PASS' : 'FAIL'
        });
      } catch (error) {
        navigationTests.push({
          page: name,
          responseTime: -1,
          isInstant: false,
          dataCount: 0,
          status: 'FAIL',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    const averageResponseTime = navigationTests.reduce((sum, test) => sum + (test.responseTime > 0 ? test.responseTime : 0), 0) / 
                               navigationTests.filter(test => test.responseTime > 0).length;
    const instantPages = navigationTests.filter(test => test.isInstant).length;

    testResults.tests.push({
      name: 'Instant Page Navigation',
      status: averageResponseTime < 200 && instantPages >= 3 ? 'PASS' : 'FAIL',
      data: {
        averageResponseTime: Math.round(averageResponseTime),
        instantPages,
        totalPages: navigationTests.length,
        target: '<200ms',
        pageTests: navigationTests
      }
    });

    // Test 4: Automatic Signal Detection
    console.log('📡 Test 4: Automatic Signal Detection...');
    const signals = await weeklyHighSignalDetector.triggerManualScan();
    
    testResults.tests.push({
      name: 'Automatic Signal Detection',
      status: signals.length > 0 ? 'PASS' : 'FAIL',
      data: {
        totalSignals: signals.length,
        strongSignals: signals.filter(s => s.signalStrength === 'STRONG').length,
        moderateSignals: signals.filter(s => s.signalStrength === 'MODERATE').length,
        detectorRunning: signalStatus.isRunning,
        scanInterval: '5 minutes during market hours'
      }
    });

    // Test 5: Automatic GTT Order Creation
    console.log('🤖 Test 5: Automatic GTT Order Creation...');
    const initialOrderCount = automaticGTTService.getAllOrders().length;
    
    // Find signals without existing orders
    const existingSymbols = new Set(
      automaticGTTService.getAllOrders()
        .filter(order => order.source === 'SIGNAL' && order.status === 'PENDING')
        .map(order => order.symbol)
    );
    
    const eligibleSignals = signals.filter(signal => !existingSymbols.has(signal.symbol));
    
    // Test creating one order if eligible signals exist
    let orderCreationWorking = false;
    let newOrderCreated = null;
    
    if (eligibleSignals.length > 0) {
      try {
        const testSignal = eligibleSignals[0];
        newOrderCreated = await automaticGTTService.testCreateOrder(testSignal.symbol);
        orderCreationWorking = newOrderCreated !== null;
      } catch (error) {
        console.error('Order creation test failed:', error);
      }
    }

    const finalOrderCount = automaticGTTService.getAllOrders().length;

    testResults.tests.push({
      name: 'Automatic GTT Order Creation',
      status: eligibleSignals.length === 0 ? 'SKIP' : (orderCreationWorking ? 'PASS' : 'FAIL'),
      data: {
        initialOrderCount,
        finalOrderCount,
        eligibleSignals: eligibleSignals.length,
        orderCreated: orderCreationWorking,
        newOrderId: newOrderCreated?.id || null,
        serviceInitialized: gttStats.isInitialized,
        listenerConnected: signalStatus.listenerCount > 0
      }
    });

    // Test 6: Real-time UI Synchronization
    console.log('🔄 Test 6: Real-time UI Synchronization...');
    
    // Check if Central Data Manager has the latest orders
    const centralOrders = centralDataManager.getGTTOrders();
    const serviceOrders = automaticGTTService.getAllOrders();
    const ordersSynced = centralOrders.length === serviceOrders.length;

    testResults.tests.push({
      name: 'Real-time UI Synchronization',
      status: ordersSynced ? 'PASS' : 'FAIL',
      data: {
        centralDataManagerOrders: centralOrders.length,
        automaticGTTServiceOrders: serviceOrders.length,
        ordersSynced,
        lastSyncTime: centralDataManager.getLastUpdated('gttOrders'),
        syncInterval: '30 seconds'
      }
    });

    // Test 7: Cross-Page Data Consistency
    console.log('🔗 Test 7: Cross-Page Data Consistency...');
    const nifty200Data = centralDataManager.getNifty200Stocks();
    const bohEligibleData = centralDataManager.getBOHEligibleStocks();
    const weeklyHighSignals = centralDataManager.getWeeklyHighSignals();
    const gttOrders = centralDataManager.getGTTOrders();

    // Check data relationships
    const bohSymbols = new Set(bohEligibleData.map(stock => stock.symbol));
    const niftySymbols = new Set(nifty200Data.map(stock => stock.symbol));
    const signalSymbols = new Set(weeklyHighSignals.map(signal => signal.symbol));
    
    const bohIsSubsetOfNifty = [...bohSymbols].every(symbol => niftySymbols.has(symbol));
    const signalsInBOH = [...signalSymbols].every(symbol => bohSymbols.has(symbol));

    testResults.tests.push({
      name: 'Cross-Page Data Consistency',
      status: bohIsSubsetOfNifty && signalsInBOH ? 'PASS' : 'FAIL',
      data: {
        nifty200Count: nifty200Data.length,
        bohEligibleCount: bohEligibleData.length,
        weeklyHighSignalsCount: weeklyHighSignals.length,
        gttOrdersCount: gttOrders.length,
        bohIsSubsetOfNifty,
        signalsInBOH,
        dataConsistency: bohIsSubsetOfNifty && signalsInBOH
      }
    });

    // Calculate overall results
    const passedTests = testResults.tests.filter(t => t.status === 'PASS').length;
    const skippedTests = testResults.tests.filter(t => t.status === 'SKIP').length;
    const totalTests = testResults.tests.length;
    const effectiveTests = totalTests - skippedTests;
    const overallStatus = passedTests >= effectiveTests ? 'SUCCESS' : 
                         passedTests >= effectiveTests * 0.8 ? 'MOSTLY_SUCCESS' : 'NEEDS_IMPROVEMENT';

    const summary = {
      overallStatus,
      passedTests,
      skippedTests,
      totalTests,
      effectiveTests,
      successRate: effectiveTests > 0 ? `${Math.round((passedTests / effectiveTests) * 100)}%` : '0%',
      
      // Key functionality status
      backgroundServicesRunning: testResults.tests.find(t => t.name === 'Background Services Status')?.status === 'PASS',
      backgroundDataLoading: testResults.tests.find(t => t.name === 'Background Data Loading')?.status === 'PASS',
      instantNavigation: testResults.tests.find(t => t.name === 'Instant Page Navigation')?.status === 'PASS',
      automaticSignalDetection: testResults.tests.find(t => t.name === 'Automatic Signal Detection')?.status === 'PASS',
      automaticOrderCreation: testResults.tests.find(t => t.name === 'Automatic GTT Order Creation')?.status === 'PASS' || 
                             testResults.tests.find(t => t.name === 'Automatic GTT Order Creation')?.status === 'SKIP',
      realTimeSync: testResults.tests.find(t => t.name === 'Real-time UI Synchronization')?.status === 'PASS',
      dataConsistency: testResults.tests.find(t => t.name === 'Cross-Page Data Consistency')?.status === 'PASS',
      
      // Performance metrics
      averageNavigationTime: testResults.tests.find(t => t.name === 'Instant Page Navigation')?.data?.averageResponseTime || 0,
      totalDataPoints: cacheStatus.nifty200Count + cacheStatus.bohEligibleCount + cacheStatus.weeklyHighSignalsCount + cacheStatus.gttOrdersCount,
      currentOrderCount: finalOrderCount
    };

    console.log(`🎉 End-to-end automation test: ${summary.successRate} success rate`);
    console.log(`🚀 System status: ${summary.overallStatus}`);

    return NextResponse.json({
      success: true,
      message: 'End-to-end background automation test completed',
      summary,
      testResults
    });

  } catch (error) {
    console.error('❌ End-to-end automation test failed:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'End-to-end test failed',
        message: 'End-to-end automation test failed'
      },
      { status: 500 }
    );
  }
}
