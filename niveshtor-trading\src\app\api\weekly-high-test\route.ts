import { NextResponse } from 'next/server';
import { weeklyHighSignalService } from '@/lib/weekly-high-service';

export async function GET() {
  try {
    console.log('🧪 Testing Weekly High Signal Service...');
    
    // Get current signals
    const currentSignals = weeklyHighSignalService.getSignals();
    const stats = weeklyHighSignalService.getSignalStats();
    
    // If no signals, trigger a scan
    if (currentSignals.length === 0) {
      console.log('🔄 No signals found, triggering scan...');
      await weeklyHighSignalService.scanForSignals();
    }
    
    const updatedSignals = weeklyHighSignalService.getSignals();
    const updatedStats = weeklyHighSignalService.getSignalStats();
    
    return NextResponse.json({
      success: true,
      message: 'Weekly High Signal Service is working',
      data: {
        signalCount: updatedSignals.length,
        stats: updatedStats,
        sampleSignals: updatedSignals.slice(0, 3).map(signal => ({
          symbol: signal.symbol,
          name: signal.name,
          currentPrice: signal.currentPrice,
          weeklyHigh: signal.weeklyHigh,
          strength: signal.strength,
          status: signal.status,
          daysFromHigh: signal.daysFromHigh,
          priceFromHighPercent: signal.priceFromHighPercent
        }))
      }
    });
    
  } catch (error) {
    console.error('❌ Weekly High Signal Service test error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
