// useInstantData Hook
// Provides instant access to preloaded static data and real-time dynamic updates
// Eliminates loading delays by using cached data with continuous background updates

import { useState, useEffect, useCallback } from 'react';
import { staticDataCache, StaticStockData } from '@/lib/static-data-cache';
import { dynamicDataUpdater } from '@/lib/dynamic-data-updater';
import { appDataPreloader } from '@/lib/app-data-preloader';

export interface InstantStockData extends StaticStockData {
  currentPrice: number;
  dayChange: number;
  dayChangePercent: number;
  marketValue?: number;
  lastUpdated: Date;
}

export interface InstantHoldingData {
  symbol: string;
  name: string;
  strategy: string;
  currentPrice: number;
  marketValue: number;
  pnl: number;
  pnlPercent: number;
  lastUpdated: Date;
}

export interface InstantGTTOrderData {
  id: string;
  symbol: string;
  name: string;
  orderType: 'BUY' | 'SELL';
  source: 'MANUAL' | 'SIGNAL' | 'HOLDING';
  status: 'PENDING' | 'TRIGGERED' | 'CANCELLED' | 'EXPIRED';
  triggerPrice: number;
  quantity: number;
  currentPrice: number;
  lastUpdated: Date;
}

export interface InstantWeeklyHighSignalData {
  symbol: string;
  name: string;
  signalStrength: 'STRONG' | 'MODERATE';
  percentDifference: number;
  lastWeekHigh: number;
  suggestedBuyPrice: number;
  currentPrice: number;
  lastUpdated: Date;
}

// Hook for instant stock universe data
export function useInstantStockUniverse() {
  const [stocks, setStocks] = useState<InstantStockData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const updateStockPrices = useCallback((pricesMap: Map<string, any>) => {
    const staticStocks = staticDataCache.getStockUniverse();
    
    const updatedStocks: InstantStockData[] = staticStocks.map(stock => {
      const priceData = pricesMap.get(stock.symbol);
      return {
        ...stock,
        currentPrice: priceData?.currentPrice || 0,
        dayChange: priceData?.dayChange || 0,
        dayChangePercent: priceData?.dayChangePercent || 0,
        lastUpdated: priceData?.lastUpdated || new Date()
      };
    });

    setStocks(updatedStocks);
    setLastUpdated(new Date());
  }, []);

  useEffect(() => {
    // Check if static data is ready
    if (!staticDataCache.isStaticCacheReady()) {
      console.log('⏳ Waiting for static cache to be ready...');
      const checkReady = () => {
        if (staticDataCache.isStaticCacheReady()) {
          initializeData();
        } else {
          setTimeout(checkReady, 100);
        }
      };
      checkReady();
      return;
    }

    initializeData();
  }, [updateStockPrices]);

  const initializeData = () => {
    // Get initial static data
    const staticStocks = staticDataCache.getStockUniverse();
    
    // Initialize with static data and zero prices
    const initialStocks: InstantStockData[] = staticStocks.map(stock => ({
      ...stock,
      currentPrice: 0,
      dayChange: 0,
      dayChangePercent: 0,
      lastUpdated: new Date()
    }));

    setStocks(initialStocks);
    setIsLoading(false);

    // Add listener for dynamic price updates
    dynamicDataUpdater.addListener('onStockPricesUpdate', updateStockPrices);

    // Get current prices if available
    const currentPrices = staticDataCache.getAllStockPrices();
    if (currentPrices.size > 0) {
      updateStockPrices(currentPrices);
    }
  };

  // Cleanup listener on unmount
  useEffect(() => {
    return () => {
      dynamicDataUpdater.removeListener('onStockPricesUpdate');
    };
  }, []);

  return {
    stocks,
    isLoading,
    lastUpdated,
    totalCount: stocks.length,
    isReady: !isLoading && staticDataCache.isStaticCacheReady()
  };
}

// Hook for instant BOH eligible stocks
export function useInstantBOHEligible() {
  const [stocks, setStocks] = useState<InstantStockData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const updateStockPrices = useCallback((pricesMap: Map<string, any>) => {
    const staticBOHStocks = staticDataCache.getBOHEligibleStocks();
    
    const updatedStocks: InstantStockData[] = staticBOHStocks.map(stock => {
      const priceData = pricesMap.get(stock.symbol);
      return {
        ...stock,
        currentPrice: priceData?.currentPrice || 0,
        dayChange: priceData?.dayChange || 0,
        dayChangePercent: priceData?.dayChangePercent || 0,
        lastUpdated: priceData?.lastUpdated || new Date()
      };
    });

    setStocks(updatedStocks);
    setLastUpdated(new Date());
  }, []);

  useEffect(() => {
    if (!staticDataCache.isStaticCacheReady()) {
      const checkReady = () => {
        if (staticDataCache.isStaticCacheReady()) {
          initializeData();
        } else {
          setTimeout(checkReady, 100);
        }
      };
      checkReady();
      return;
    }

    initializeData();
  }, [updateStockPrices]);

  const initializeData = () => {
    const staticBOHStocks = staticDataCache.getBOHEligibleStocks();
    
    const initialStocks: InstantStockData[] = staticBOHStocks.map(stock => ({
      ...stock,
      currentPrice: 0,
      dayChange: 0,
      dayChangePercent: 0,
      lastUpdated: new Date()
    }));

    setStocks(initialStocks);
    setIsLoading(false);

    dynamicDataUpdater.addListener('onStockPricesUpdate', updateStockPrices);

    const currentPrices = staticDataCache.getAllStockPrices();
    if (currentPrices.size > 0) {
      updateStockPrices(currentPrices);
    }
  };

  useEffect(() => {
    return () => {
      dynamicDataUpdater.removeListener('onStockPricesUpdate');
    };
  }, []);

  return {
    stocks,
    isLoading,
    lastUpdated,
    totalCount: stocks.length,
    isReady: !isLoading && staticDataCache.isStaticCacheReady()
  };
}

// Hook for instant weekly high signals
export function useInstantWeeklyHighSignals() {
  const [signals, setSignals] = useState<InstantWeeklyHighSignalData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const updateSignals = useCallback((signalsMap: Map<string, any>) => {
    const staticStocks = staticDataCache.getStockUniverse();
    const currentPrices = staticDataCache.getAllStockPrices();
    
    const updatedSignals: InstantWeeklyHighSignalData[] = [];
    
    signalsMap.forEach((signalData, symbol) => {
      const staticStock = staticStocks.find(s => s.symbol === symbol);
      const priceData = currentPrices.get(symbol);
      
      if (staticStock) {
        updatedSignals.push({
          symbol,
          name: staticStock.name,
          signalStrength: signalData.signalStrength,
          percentDifference: signalData.percentDifference,
          lastWeekHigh: signalData.lastWeekHigh,
          suggestedBuyPrice: signalData.suggestedBuyPrice,
          currentPrice: priceData?.currentPrice || 0,
          lastUpdated: signalData.lastUpdated
        });
      }
    });

    setSignals(updatedSignals);
    setLastUpdated(new Date());
  }, []);

  useEffect(() => {
    if (!staticDataCache.isStaticCacheReady()) {
      const checkReady = () => {
        if (staticDataCache.isStaticCacheReady()) {
          initializeData();
        } else {
          setTimeout(checkReady, 100);
        }
      };
      checkReady();
      return;
    }

    initializeData();
  }, [updateSignals]);

  const initializeData = () => {
    setSignals([]);
    setIsLoading(false);

    dynamicDataUpdater.addListener('onWeeklyHighSignalsUpdate', updateSignals);
  };

  useEffect(() => {
    return () => {
      dynamicDataUpdater.removeListener('onWeeklyHighSignalsUpdate');
    };
  }, []);

  return {
    signals,
    isLoading,
    lastUpdated,
    totalCount: signals.length,
    isReady: !isLoading && staticDataCache.isStaticCacheReady()
  };
}

// Hook for instant GTT orders
export function useInstantGTTOrders() {
  const [orders, setOrders] = useState<InstantGTTOrderData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const updateOrders = useCallback((ordersMap: Map<string, any>) => {
    const staticOrders = staticDataCache.getGTTOrdersStatic();
    const currentPrices = staticDataCache.getAllStockPrices();
    
    const updatedOrders: InstantGTTOrderData[] = staticOrders.map(staticOrder => {
      const dynamicData = ordersMap.get(staticOrder.id);
      const priceData = currentPrices.get(staticOrder.symbol);
      
      return {
        ...staticOrder,
        status: dynamicData?.status || 'PENDING',
        triggerPrice: dynamicData?.triggerPrice || 0,
        quantity: dynamicData?.quantity || 0,
        currentPrice: priceData?.currentPrice || 0,
        lastUpdated: dynamicData?.lastUpdated || new Date()
      };
    });

    setOrders(updatedOrders);
    setLastUpdated(new Date());
  }, []);

  useEffect(() => {
    if (!staticDataCache.isStaticCacheReady()) {
      const checkReady = () => {
        if (staticDataCache.isStaticCacheReady()) {
          initializeData();
        } else {
          setTimeout(checkReady, 100);
        }
      };
      checkReady();
      return;
    }

    initializeData();
  }, [updateOrders]);

  const initializeData = () => {
    const staticOrders = staticDataCache.getGTTOrdersStatic();
    
    const initialOrders: InstantGTTOrderData[] = staticOrders.map(order => ({
      ...order,
      status: 'PENDING' as const,
      triggerPrice: 0,
      quantity: 0,
      currentPrice: 0,
      lastUpdated: new Date()
    }));

    setOrders(initialOrders);
    setIsLoading(false);

    dynamicDataUpdater.addListener('onGTTOrdersUpdate', updateOrders);
  };

  useEffect(() => {
    return () => {
      dynamicDataUpdater.removeListener('onGTTOrdersUpdate');
    };
  }, []);

  return {
    orders,
    isLoading,
    lastUpdated,
    totalCount: orders.length,
    isReady: !isLoading && staticDataCache.isStaticCacheReady()
  };
}

// Hook to check if instant data system is ready
export function useInstantDataStatus() {
  const [isReady, setIsReady] = useState(false);
  const [preloadingStatus, setPreloadingStatus] = useState<any>(null);

  useEffect(() => {
    const checkStatus = () => {
      const ready = appDataPreloader.isReadyForInstantNavigation();
      const status = appDataPreloader.getStatus();
      
      setIsReady(ready);
      setPreloadingStatus(status);
    };

    checkStatus();
    const interval = setInterval(checkStatus, 1000);

    return () => clearInterval(interval);
  }, []);

  return {
    isReady,
    preloadingStatus,
    staticCacheReady: staticDataCache.isStaticCacheReady(),
    dynamicUpdaterRunning: dynamicDataUpdater.getStatus().isRunning
  };
}
