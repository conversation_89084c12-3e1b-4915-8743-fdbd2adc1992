const CHUNK_PUBLIC_PATH = "server/app/api/check-order-display/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/src_lib_yahoo-finance_ts_39b50dc8._.js");
runtime.loadChunk("server/chunks/node_modules_f629a332._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__ab2f1e70._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/check-order-display/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/check-order-display/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/check-order-display/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
