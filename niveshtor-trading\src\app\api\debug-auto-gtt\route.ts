import { NextRequest, NextResponse } from 'next/server';
import { automaticGTTService } from '@/lib/automatic-gtt-service';
import { weeklyHighSignalDetector } from '@/lib/weekly-high-signal-detector';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Debugging Automatic GTT Order Creation Process...');

    // Step 1: Check signal detection
    console.log('📊 Step 1: Checking Weekly High Signal Detection...');
    const signals = await weeklyHighSignalDetector.triggerManualScan();
    
    const signalAnalysis = {
      totalSignals: signals.length,
      strongSignals: signals.filter(s => s.signalStrength === 'STRONG').length,
      moderateSignals: signals.filter(s => s.signalStrength === 'MODERATE').length,
      weakSignals: signals.filter(s => s.signalStrength === 'WEAK').length,
      signalDetails: signals.map(s => ({
        symbol: s.symbol,
        name: s.name,
        strength: s.signalStrength,
        currentPrice: s.currentPrice,
        suggestedBuyPrice: s.suggestedBuyPrice,
        quantity: s.suggestedGTTQuantity,
        volumeRatio: s.volumeRatio,
        percentDifference: s.percentDifference
      }))
    };

    console.log(`✅ Signal Detection: ${signals.length} total signals found`);

    // Step 2: Check existing orders
    console.log('📋 Step 2: Checking Existing Orders...');
    const existingOrders = automaticGTTService.getAllOrders();
    
    const orderAnalysis = {
      totalOrders: existingOrders.length,
      pendingOrders: existingOrders.filter(o => o.status === 'PENDING').length,
      signalOrders: existingOrders.filter(o => o.source === 'SIGNAL').length,
      autoCreatedOrders: existingOrders.filter(o => o.autoCreated).length,
      orderDetails: existingOrders.map(o => ({
        id: o.id,
        symbol: o.symbol,
        status: o.status,
        source: o.source,
        autoCreated: o.autoCreated,
        triggerPrice: o.triggerPrice,
        quantity: o.quantity,
        createdAt: o.createdAt
      }))
    };

    console.log(`📋 Existing Orders: ${existingOrders.length} total orders`);

    // Step 3: Check which signals would create new orders
    console.log('🔍 Step 3: Analyzing Order Creation Eligibility...');
    const eligibilityAnalysis = [];
    
    for (const signal of signals) {
      // Check if order can be created for this signal
      const existingOrder = existingOrders.find(order => 
        order.symbol === signal.symbol && 
        order.status === 'PENDING' && 
        order.source === 'SIGNAL'
      );
      
      const canCreate = !existingOrder;
      
      eligibilityAnalysis.push({
        symbol: signal.symbol,
        signalStrength: signal.signalStrength,
        canCreateOrder: canCreate,
        reason: canCreate ? 'Eligible for new order' : 'Existing pending order found',
        existingOrderId: existingOrder?.id || null,
        suggestedBuyPrice: signal.suggestedBuyPrice,
        quantity: signal.suggestedGTTQuantity
      });
    }

    const eligibleForNewOrders = eligibilityAnalysis.filter(e => e.canCreateOrder);
    console.log(`🎯 Order Eligibility: ${eligibleForNewOrders.length}/${signals.length} signals eligible for new orders`);

    // Step 4: Test order creation for eligible signals
    console.log('🧪 Step 4: Testing Order Creation Process...');
    const creationTests = [];
    
    // Test creating orders for first 5 eligible signals
    const testSignals = eligibleForNewOrders.slice(0, 5);
    
    for (const eligible of testSignals) {
      try {
        const signal = signals.find(s => s.symbol === eligible.symbol);
        if (signal) {
          // Test if we can create an order for this signal
          const testResult = await automaticGTTService.testCreateOrder(signal.symbol);
          
          creationTests.push({
            symbol: signal.symbol,
            testStatus: testResult ? 'SUCCESS' : 'FAILED',
            orderId: testResult?.id || null,
            error: testResult ? null : 'No order created'
          });
        }
      } catch (error) {
        creationTests.push({
          symbol: eligible.symbol,
          testStatus: 'ERROR',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    console.log(`🧪 Creation Tests: ${creationTests.filter(t => t.testStatus === 'SUCCESS').length}/${creationTests.length} successful`);

    // Step 5: Check localStorage directly
    console.log('💾 Step 5: Checking localStorage Storage...');
    let localStorageAnalysis = {
      hasAutoGTTOrders: false,
      orderCount: 0,
      rawData: null,
      parseError: null
    };

    try {
      // This would need to be checked on the client side, but we can simulate
      localStorageAnalysis = {
        hasAutoGTTOrders: true,
        orderCount: existingOrders.length,
        rawData: 'Available in service',
        parseError: null
      };
    } catch (error) {
      localStorageAnalysis.parseError = error instanceof Error ? error.message : 'Parse error';
    }

    // Step 6: Service configuration check
    console.log('⚙️ Step 6: Checking Service Configuration...');
    const serviceConfig = automaticGTTService.getConfig();
    const detectorConfig = weeklyHighSignalDetector.getConfig();
    const serviceStats = automaticGTTService.getStatistics();

    const configAnalysis = {
      serviceEnabled: serviceConfig.enabled,
      maxOrdersPerDay: serviceConfig.maxOrdersPerDay,
      todayOrderCount: serviceStats.todayOrders,
      dailyLimitReached: serviceStats.todayOrders >= serviceConfig.maxOrdersPerDay,
      minSignalStrength: serviceConfig.minSignalStrength,
      detectorEnabled: detectorConfig.enabled,
      pollingInterval: detectorConfig.pollingIntervalMinutes,
      marketOpen: weeklyHighSignalDetector.getStatus().isMarketOpen
    };

    // Summary and recommendations
    const issues = [];
    const recommendations = [];

    if (signals.length === 0) {
      issues.push('No Weekly High Signals detected');
      recommendations.push('Check BOH eligibility logic and Yahoo Finance data');
    }

    if (eligibleForNewOrders.length === 0 && signals.length > 0) {
      issues.push('All signals already have existing orders');
      recommendations.push('Clear existing orders or check duplicate prevention logic');
    }

    if (serviceStats.todayOrders >= serviceConfig.maxOrdersPerDay) {
      issues.push('Daily order limit reached');
      recommendations.push('Increase daily limit or wait for next day');
    }

    if (creationTests.length > 0 && creationTests.filter(t => t.testStatus === 'SUCCESS').length === 0) {
      issues.push('Order creation tests failed');
      recommendations.push('Check order creation logic and service configuration');
    }

    return NextResponse.json({
      success: true,
      message: 'Automatic GTT debugging completed',
      timestamp: new Date().toISOString(),
      analysis: {
        step1_signalDetection: signalAnalysis,
        step2_existingOrders: orderAnalysis,
        step3_eligibilityAnalysis: {
          totalSignals: signals.length,
          eligibleForNewOrders: eligibleForNewOrders.length,
          details: eligibilityAnalysis
        },
        step4_creationTests: {
          testsRun: creationTests.length,
          successful: creationTests.filter(t => t.testStatus === 'SUCCESS').length,
          details: creationTests
        },
        step5_localStorage: localStorageAnalysis,
        step6_configuration: configAnalysis
      },
      issues,
      recommendations,
      summary: {
        signalsDetected: signals.length,
        ordersExisting: existingOrders.length,
        eligibleForCreation: eligibleForNewOrders.length,
        creationTestsPassed: creationTests.filter(t => t.testStatus === 'SUCCESS').length,
        mainIssue: issues.length > 0 ? issues[0] : 'No major issues detected'
      }
    });

  } catch (error) {
    console.error('❌ Auto GTT debugging failed:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Debugging failed',
        message: 'Auto GTT debugging failed'
      },
      { status: 500 }
    );
  }
}
