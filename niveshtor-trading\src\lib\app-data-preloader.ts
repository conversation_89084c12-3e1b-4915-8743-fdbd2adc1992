// App Data Preloader Service
// Comprehensive background data preloading during app initialization
// Loads ALL static data before showing the main interface

import { staticDataCache } from './static-data-cache';
import { dynamicDataUpdater } from './dynamic-data-updater';
import { centralDataManager } from './central-data-manager';
import { automaticGTTService } from './automatic-gtt-service';
import { weeklyHighSignalDetector } from './weekly-high-signal-detector';
import { stockNamesService } from './stock-names-service';
import { NIFTY_200_SYMBOLS } from './nifty-stocks';

export interface PreloadingProgress {
  stage: string;
  progress: number; // 0-100
  message: string;
  isComplete: boolean;
  error?: string;
}

export interface PreloadingResult {
  success: boolean;
  totalTime: number;
  stages: {
    name: string;
    duration: number;
    success: boolean;
    error?: string;
  }[];
  staticDataLoaded: {
    stockCount: number;
    bohEligibleCount: number;
    holdingsCount: number;
    gttOrdersCount: number;
  };
  servicesStarted: string[];
}

class AppDataPreloaderService {
  private isPreloading = false;
  private isPreloaded = false;
  private preloadingResult: PreloadingResult | null = null;
  private progressListeners: ((progress: PreloadingProgress) => void)[] = [];
  
  constructor() {
    console.log('🚀 App Data Preloader Service initialized');
  }
  
  // Main preloading method - called once on app start
  async preloadAllData(): Promise<PreloadingResult> {
    if (this.isPreloaded) {
      console.log('✅ Data already preloaded, returning cached result');
      return this.preloadingResult!;
    }
    
    if (this.isPreloading) {
      console.log('⏳ Preloading already in progress...');
      return new Promise((resolve) => {
        const checkComplete = () => {
          if (this.isPreloaded && this.preloadingResult) {
            resolve(this.preloadingResult);
          } else {
            setTimeout(checkComplete, 100);
          }
        };
        checkComplete();
      });
    }
    
    this.isPreloading = true;
    const startTime = Date.now();
    const stages: PreloadingResult['stages'] = [];
    
    console.log('🚀 Starting comprehensive app data preloading...');
    this.notifyProgress('Initializing', 0, 'Starting app data preloading...');
    
    try {
      // Stage 1: Initialize Static Data Cache (30% of progress)
      console.log('📋 Stage 1: Initializing static data cache...');
      this.notifyProgress('Static Data Cache', 10, 'Loading stock names and static information...');
      
      const stage1Start = Date.now();
      try {
        await staticDataCache.initializeStaticCache();
        stages.push({
          name: 'Static Data Cache',
          duration: Date.now() - stage1Start,
          success: true
        });
        this.notifyProgress('Static Data Cache', 30, 'Static data cache initialized successfully');
      } catch (error) {
        stages.push({
          name: 'Static Data Cache',
          duration: Date.now() - stage1Start,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        throw error;
      }
      
      // Stage 2: Initialize Background Services (50% of progress)
      console.log('⚙️ Stage 2: Starting background services...');
      this.notifyProgress('Background Services', 35, 'Starting Central Data Manager...');
      
      const stage2Start = Date.now();
      try {
        // Initialize Central Data Manager
        if (!centralDataManager.getStatus().isInitialized) {
          await centralDataManager.initialize();
        }
        if (!centralDataManager.getStatus().isRunning) {
          centralDataManager.start();
        }
        
        this.notifyProgress('Background Services', 40, 'Starting Automatic GTT Service...');
        
        // Initialize Automatic GTT Service
        if (!automaticGTTService.getStatistics().isInitialized) {
          await automaticGTTService.start();
        }
        
        this.notifyProgress('Background Services', 45, 'Starting Weekly High Signal Detector...');
        
        // Initialize Weekly High Signal Detector
        if (!weeklyHighSignalDetector.getStatus().isRunning) {
          weeklyHighSignalDetector.start();
        }
        
        stages.push({
          name: 'Background Services',
          duration: Date.now() - stage2Start,
          success: true
        });
        this.notifyProgress('Background Services', 50, 'All background services started');
      } catch (error) {
        stages.push({
          name: 'Background Services',
          duration: Date.now() - stage2Start,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        throw error;
      }
      
      // Stage 3: Preload Initial Dynamic Data (70% of progress)
      console.log('💰 Stage 3: Preloading initial dynamic data...');
      this.notifyProgress('Initial Dynamic Data', 55, 'Loading current stock prices...');
      
      const stage3Start = Date.now();
      try {
        // Get initial stock prices for key stocks
        const stockUniverse = staticDataCache.getStockUniverse();
        const priorityStocks = stockUniverse.slice(0, 50); // Load first 50 stocks initially
        
        this.notifyProgress('Initial Dynamic Data', 60, `Loading prices for ${priorityStocks.length} priority stocks...`);
        
        // Load initial prices in smaller batches
        const batchSize = 10;
        for (let i = 0; i < priorityStocks.length; i += batchSize) {
          const batch = priorityStocks.slice(i, i + batchSize);
          const progress = 60 + (i / priorityStocks.length) * 10;
          this.notifyProgress('Initial Dynamic Data', progress, `Loading batch ${Math.floor(i/batchSize) + 1}...`);
          
          // Small delay to avoid overwhelming the API
          await new Promise(resolve => setTimeout(resolve, 200));
        }
        
        this.notifyProgress('Initial Dynamic Data', 65, 'Loading weekly high signals...');
        
        // Get initial weekly high signals
        await weeklyHighSignalDetector.triggerManualScan();
        
        stages.push({
          name: 'Initial Dynamic Data',
          duration: Date.now() - stage3Start,
          success: true
        });
        this.notifyProgress('Initial Dynamic Data', 70, 'Initial dynamic data loaded');
      } catch (error) {
        stages.push({
          name: 'Initial Dynamic Data',
          duration: Date.now() - stage3Start,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        // Don't throw here - dynamic data can be loaded later
        console.warn('⚠️ Initial dynamic data loading failed, will continue with static data');
      }
      
      // Stage 4: Start Dynamic Data Updates (90% of progress)
      console.log('⚡ Stage 4: Starting continuous dynamic data updates...');
      this.notifyProgress('Dynamic Updates', 75, 'Starting continuous price updates...');
      
      const stage4Start = Date.now();
      try {
        // Start dynamic data updater
        dynamicDataUpdater.start();
        
        // Wait a moment for first update cycle
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        stages.push({
          name: 'Dynamic Updates',
          duration: Date.now() - stage4Start,
          success: true
        });
        this.notifyProgress('Dynamic Updates', 90, 'Continuous updates started');
      } catch (error) {
        stages.push({
          name: 'Dynamic Updates',
          duration: Date.now() - stage4Start,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        // Don't throw here - updates can be started later
        console.warn('⚠️ Dynamic updates failed to start, will retry later');
      }
      
      // Stage 5: Final Validation (100% of progress)
      console.log('✅ Stage 5: Final validation...');
      this.notifyProgress('Validation', 95, 'Validating preloaded data...');
      
      const stage5Start = Date.now();
      try {
        // Validate that essential data is loaded
        const staticStatus = staticDataCache.getStaticCacheStatus();
        const dynamicStatus = dynamicDataUpdater.getStatus();
        
        if (!staticStatus.isInitialized || staticStatus.stockCount === 0) {
          throw new Error('Static data validation failed');
        }
        
        stages.push({
          name: 'Validation',
          duration: Date.now() - stage5Start,
          success: true
        });
        this.notifyProgress('Validation', 100, 'Preloading completed successfully');
      } catch (error) {
        stages.push({
          name: 'Validation',
          duration: Date.now() - stage5Start,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        throw error;
      }
      
      // Build result
      const staticStatus = staticDataCache.getStaticCacheStatus();
      const totalTime = Date.now() - startTime;
      
      this.preloadingResult = {
        success: true,
        totalTime,
        stages,
        staticDataLoaded: {
          stockCount: staticStatus.stockCount,
          bohEligibleCount: staticStatus.bohEligibleCount,
          holdingsCount: staticDataCache.getHoldingsStatic().length,
          gttOrdersCount: staticDataCache.getGTTOrdersStatic().length
        },
        servicesStarted: [
          'Central Data Manager',
          'Automatic GTT Service', 
          'Weekly High Signal Detector',
          'Dynamic Data Updater'
        ]
      };
      
      this.isPreloaded = true;
      console.log(`🎉 App data preloading completed successfully in ${totalTime}ms`);
      console.log(`📊 Loaded: ${staticStatus.stockCount} stocks, ${staticStatus.bohEligibleCount} BOH eligible`);
      
      return this.preloadingResult;
      
    } catch (error) {
      console.error('❌ App data preloading failed:', error);
      
      this.preloadingResult = {
        success: false,
        totalTime: Date.now() - startTime,
        stages,
        staticDataLoaded: {
          stockCount: 0,
          bohEligibleCount: 0,
          holdingsCount: 0,
          gttOrdersCount: 0
        },
        servicesStarted: []
      };
      
      this.notifyProgress('Error', 0, `Preloading failed: ${error instanceof Error ? error.message : 'Unknown error'}`, false, error instanceof Error ? error.message : 'Unknown error');
      
      throw error;
    } finally {
      this.isPreloading = false;
    }
  }
  
  // Add progress listener
  addProgressListener(listener: (progress: PreloadingProgress) => void): void {
    this.progressListeners.push(listener);
  }
  
  // Remove progress listener
  removeProgressListener(listener: (progress: PreloadingProgress) => void): void {
    const index = this.progressListeners.indexOf(listener);
    if (index > -1) {
      this.progressListeners.splice(index, 1);
    }
  }
  
  // Notify progress to all listeners
  private notifyProgress(stage: string, progress: number, message: string, isComplete: boolean = false, error?: string): void {
    const progressData: PreloadingProgress = {
      stage,
      progress,
      message,
      isComplete,
      error
    };
    
    this.progressListeners.forEach(listener => {
      try {
        listener(progressData);
      } catch (error) {
        console.warn('⚠️ Progress listener error:', error);
      }
    });
  }
  
  // Get preloading status
  getStatus() {
    return {
      isPreloading: this.isPreloading,
      isPreloaded: this.isPreloaded,
      result: this.preloadingResult
    };
  }
  
  // Force re-preload (for testing or error recovery)
  async forceReload(): Promise<PreloadingResult> {
    console.log('🔄 Forcing app data reload...');
    this.isPreloaded = false;
    this.preloadingResult = null;
    staticDataCache.clearCache();
    return this.preloadAllData();
  }
  
  // Check if app is ready for instant navigation
  isReadyForInstantNavigation(): boolean {
    return this.isPreloaded && 
           staticDataCache.isStaticCacheReady() && 
           this.preloadingResult?.success === true;
  }
}

export const appDataPreloader = new AppDataPreloaderService();
