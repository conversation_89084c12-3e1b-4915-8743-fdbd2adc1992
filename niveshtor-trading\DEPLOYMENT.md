# Deployment Guide - Niveshtor Trading Platform

This guide covers different deployment options for the Niveshtor Trading Platform.

## 🚀 Quick Deployment Options

### Option 1: Vercel (Recommended for Frontend)

1. **Prepare Repository**
   ```bash
   git add .
   git commit -m "Initial commit"
   git push origin main
   ```

2. **Deploy to Vercel**
   - Visit [vercel.com](https://vercel.com)
   - Connect your GitHub repository
   - Configure environment variables:
     ```
     DATABASE_URL=file:./dev.db
     NEXTAUTH_SECRET=your-secret-key
     NEXTAUTH_URL=https://your-app.vercel.app
     ```
   - Deploy automatically

3. **Database Considerations**
   - For production, consider upgrading to PostgreSQL
   - Use Vercel Postgres or external database service

### Option 2: Railway (Full Stack)

1. **Create Railway Account**
   - Visit [railway.app](https://railway.app)
   - Connect GitHub repository

2. **Deploy Frontend**
   - Create new project from GitHub
   - Set build command: `npm run build`
   - Set start command: `npm start`

3. **Deploy Python Backend**
   - Create separate service for Python backend
   - Set start command: `python main.py`
   - Configure port: `8000`

### Option 3: Docker Deployment

1. **Create Dockerfile for Frontend**
   ```dockerfile
   FROM node:18-alpine
   WORKDIR /app
   COPY package*.json ./
   RUN npm install
   COPY . .
   RUN npx prisma generate
   RUN npm run build
   EXPOSE 3000
   CMD ["npm", "start"]
   ```

2. **Create Dockerfile for Backend**
   ```dockerfile
   FROM python:3.9-slim
   WORKDIR /app
   COPY python-backend/requirements.txt .
   RUN pip install -r requirements.txt
   COPY python-backend/ .
   EXPOSE 8000
   CMD ["python", "main.py"]
   ```

3. **Docker Compose**
   ```yaml
   version: '3.8'
   services:
     frontend:
       build: .
       ports:
         - "3000:3000"
       environment:
         - DATABASE_URL=file:./dev.db
       volumes:
         - ./data:/app/data
     
     backend:
       build:
         context: .
         dockerfile: Dockerfile.backend
       ports:
         - "8000:8000"
   ```

## 🔧 Environment Configuration

### Production Environment Variables

```env
# Database (Use PostgreSQL for production)
DATABASE_URL="postgresql://user:password@host:port/database"

# Security
NEXTAUTH_SECRET="your-production-secret-key"
NEXTAUTH_URL="https://your-domain.com"

# SmartAPI (Optional)
SMARTAPI_API_KEY="your_api_key"
SMARTAPI_CLIENT_ID="your_client_id"
SMARTAPI_PASSWORD="your_password"
SMARTAPI_TOTP_SECRET="your_totp_secret"

# Application
NODE_ENV="production"
```

### Database Migration for Production

```bash
# For PostgreSQL
npx prisma migrate deploy

# For SQLite (development only)
npx prisma db push
```

## 🛡️ Security Considerations

### 1. Environment Variables
- Never commit `.env` files to version control
- Use platform-specific environment variable management
- Rotate secrets regularly

### 2. Database Security
- Use connection pooling for production
- Enable SSL for database connections
- Regular backups and monitoring

### 3. API Security
- Implement rate limiting
- Use HTTPS in production
- Validate all user inputs

### 4. SmartAPI Security
- Store credentials securely
- Use encrypted storage for sensitive data
- Implement session management

## 📊 Performance Optimization

### 1. Frontend Optimization
```bash
# Build with optimization
npm run build

# Analyze bundle size
npm install -g @next/bundle-analyzer
ANALYZE=true npm run build
```

### 2. Database Optimization
- Add database indexes for frequently queried fields
- Use connection pooling
- Implement caching for static data

### 3. API Optimization
- Implement Redis caching
- Use CDN for static assets
- Optimize API response sizes

## 🔍 Monitoring and Logging

### 1. Application Monitoring
- Use Vercel Analytics for frontend monitoring
- Implement error tracking (Sentry)
- Set up uptime monitoring

### 2. Database Monitoring
- Monitor query performance
- Set up alerts for slow queries
- Regular backup verification

### 3. API Monitoring
- Monitor SmartAPI rate limits
- Track Yahoo Finance API usage
- Log trading activities

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Issues**
   ```bash
   # Check database URL
   npx prisma db pull
   
   # Reset database
   npx prisma migrate reset
   ```

2. **Build Failures**
   ```bash
   # Clear Next.js cache
   rm -rf .next
   
   # Reinstall dependencies
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **SmartAPI Connection Issues**
   - Verify API credentials
   - Check TOTP secret format
   - Ensure proper network connectivity

### Deployment Checklist

- [ ] Environment variables configured
- [ ] Database migrated
- [ ] SSL certificate installed
- [ ] Domain configured
- [ ] Monitoring set up
- [ ] Backup strategy implemented
- [ ] Security headers configured
- [ ] Performance optimized
- [ ] Error tracking enabled
- [ ] Documentation updated

## 📞 Support

For deployment issues:
1. Check the troubleshooting section
2. Review platform-specific documentation
3. Create an issue on GitHub
4. Contact support team

## 🔄 Continuous Deployment

### GitHub Actions Example
```yaml
name: Deploy to Production
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: npm install
      - run: npm run build
      - run: npx prisma generate
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v20
```

---

**Remember**: Always test deployments in a staging environment before production!
