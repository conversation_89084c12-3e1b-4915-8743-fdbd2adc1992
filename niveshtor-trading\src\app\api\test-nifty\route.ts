import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing Nifty 200 API endpoint...');
    
    // Test the internal API call
    const baseUrl = request.nextUrl.origin;
    const testUrl = `${baseUrl}/api/stocks/nifty200?batchIndex=0&batchSize=5`;
    
    console.log(`🔄 Testing URL: ${testUrl}`);
    
    const response = await fetch(testUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    console.log(`📊 Response status: ${response.status} ${response.statusText}`);
    
    if (!response.ok) {
      return NextResponse.json({
        success: false,
        error: `HTTP ${response.status}: ${response.statusText}`,
        url: testUrl
      });
    }
    
    const data = await response.json();
    
    return NextResponse.json({
      success: true,
      message: 'API endpoint is working',
      url: testUrl,
      responseStatus: response.status,
      dataReceived: {
        success: data.success,
        stockCount: data.data?.stocks?.length || 0,
        hasStocks: !!(data.data?.stocks && data.data.stocks.length > 0),
        firstStock: data.data?.stocks?.[0] || null
      }
    });
    
  } catch (error) {
    console.error('❌ Test API error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
