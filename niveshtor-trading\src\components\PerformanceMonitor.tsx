'use client';

import { useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';
import { Clock, Zap, AlertTriangle } from 'lucide-react';

interface PerformanceMetrics {
  navigationTime: number;
  dataLoadTime: number;
  totalTime: number;
  isInstant: boolean; // < 200ms
  timestamp: Date;
  page: string;
}

interface PerformanceStats {
  averageNavigationTime: number;
  instantNavigations: number;
  totalNavigations: number;
  slowestPage: string;
  fastestPage: string;
}

export function PerformanceMonitor() {
  const pathname = usePathname();
  const [metrics, setMetrics] = useState<PerformanceMetrics[]>([]);
  const [currentMetric, setCurrentMetric] = useState<PerformanceMetrics | null>(null);
  const [showMonitor, setShowMonitor] = useState(false);
  const [navigationStart, setNavigationStart] = useState<number>(0);

  // Track navigation performance
  useEffect(() => {
    const startTime = performance.now();
    setNavigationStart(startTime);

    // Measure when page is fully loaded
    const measurePerformance = () => {
      const endTime = performance.now();
      const totalTime = endTime - startTime;
      
      const metric: PerformanceMetrics = {
        navigationTime: totalTime,
        dataLoadTime: 0, // Will be updated by data loading hooks
        totalTime,
        isInstant: totalTime < 200,
        timestamp: new Date(),
        page: pathname
      };

      setCurrentMetric(metric);
      setMetrics(prev => [...prev.slice(-9), metric]); // Keep last 10 metrics

      console.log(`⚡ Navigation to ${pathname}: ${totalTime.toFixed(2)}ms ${totalTime < 200 ? '(INSTANT)' : '(SLOW)'}`);
    };

    // Measure after a short delay to allow for data loading
    const timer = setTimeout(measurePerformance, 100);

    return () => clearTimeout(timer);
  }, [pathname]);

  // Calculate performance stats
  const getPerformanceStats = (): PerformanceStats => {
    if (metrics.length === 0) {
      return {
        averageNavigationTime: 0,
        instantNavigations: 0,
        totalNavigations: 0,
        slowestPage: '',
        fastestPage: ''
      };
    }

    const totalTime = metrics.reduce((sum, m) => sum + m.navigationTime, 0);
    const instantCount = metrics.filter(m => m.isInstant).length;
    
    const sortedByTime = [...metrics].sort((a, b) => a.navigationTime - b.navigationTime);
    
    return {
      averageNavigationTime: totalTime / metrics.length,
      instantNavigations: instantCount,
      totalNavigations: metrics.length,
      slowestPage: sortedByTime[sortedByTime.length - 1]?.page || '',
      fastestPage: sortedByTime[0]?.page || ''
    };
  };

  const stats = getPerformanceStats();

  // Show/hide monitor with keyboard shortcut
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'P') {
        setShowMonitor(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, []);

  if (!showMonitor) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <button
          onClick={() => setShowMonitor(true)}
          className="bg-gray-800 text-white p-2 rounded-full shadow-lg hover:bg-gray-700 transition-colors"
          title="Show Performance Monitor (Ctrl+Shift+P)"
        >
          <Zap className="h-4 w-4" />
        </button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-white rounded-lg shadow-xl border border-gray-200 p-4 w-80">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <Zap className="h-4 w-4 text-blue-600" />
          <span className="font-semibold text-gray-900">Performance Monitor</span>
        </div>
        <button
          onClick={() => setShowMonitor(false)}
          className="text-gray-400 hover:text-gray-600"
        >
          ×
        </button>
      </div>

      {/* Current Page Performance */}
      {currentMetric && (
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-2 mb-2">
            <Clock className="h-4 w-4 text-gray-600" />
            <span className="text-sm font-medium text-gray-900">Current Page</span>
          </div>
          <div className="text-xs text-gray-600 mb-1">{currentMetric.page}</div>
          <div className={`text-lg font-bold ${currentMetric.isInstant ? 'text-green-600' : 'text-orange-600'}`}>
            {currentMetric.navigationTime.toFixed(0)}ms
            {currentMetric.isInstant ? (
              <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded">INSTANT</span>
            ) : (
              <span className="ml-2 text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded">SLOW</span>
            )}
          </div>
        </div>
      )}

      {/* Performance Stats */}
      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-600">Average Time:</span>
          <span className="font-medium">{stats.averageNavigationTime.toFixed(0)}ms</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Instant Navigation:</span>
          <span className="font-medium">
            {stats.instantNavigations}/{stats.totalNavigations}
            <span className="ml-1 text-xs text-gray-500">
              ({stats.totalNavigations > 0 ? Math.round((stats.instantNavigations / stats.totalNavigations) * 100) : 0}%)
            </span>
          </span>
        </div>
        {stats.fastestPage && (
          <div className="flex justify-between">
            <span className="text-gray-600">Fastest:</span>
            <span className="font-medium text-green-600 text-xs">{stats.fastestPage}</span>
          </div>
        )}
        {stats.slowestPage && (
          <div className="flex justify-between">
            <span className="text-gray-600">Slowest:</span>
            <span className="font-medium text-orange-600 text-xs">{stats.slowestPage}</span>
          </div>
        )}
      </div>

      {/* Recent Navigation Times */}
      {metrics.length > 0 && (
        <div className="mt-4 pt-3 border-t border-gray-200">
          <div className="text-xs font-medium text-gray-700 mb-2">Recent Navigations</div>
          <div className="space-y-1">
            {metrics.slice(-5).reverse().map((metric, index) => (
              <div key={index} className="flex justify-between items-center text-xs">
                <span className="text-gray-600 truncate flex-1 mr-2">
                  {metric.page.split('/').pop() || 'dashboard'}
                </span>
                <span className={`font-medium ${metric.isInstant ? 'text-green-600' : 'text-orange-600'}`}>
                  {metric.navigationTime.toFixed(0)}ms
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Performance Target Indicator */}
      <div className="mt-3 pt-3 border-t border-gray-200">
        <div className="flex items-center space-x-2 text-xs">
          {stats.averageNavigationTime < 200 ? (
            <>
              <Zap className="h-3 w-3 text-green-500" />
              <span className="text-green-700">Target: &lt;200ms ✓</span>
            </>
          ) : (
            <>
              <AlertTriangle className="h-3 w-3 text-orange-500" />
              <span className="text-orange-700">Target: &lt;200ms</span>
            </>
          )}
        </div>
      </div>

      <div className="mt-2 text-xs text-gray-500">
        Press Ctrl+Shift+P to toggle
      </div>
    </div>
  );
}

// Hook to report data loading times to the performance monitor
export function usePerformanceReporting() {
  const reportDataLoadTime = (loadTime: number, dataType: string) => {
    console.log(`📊 Data loading time for ${dataType}: ${loadTime.toFixed(2)}ms`);
  };

  return { reportDataLoadTime };
}
