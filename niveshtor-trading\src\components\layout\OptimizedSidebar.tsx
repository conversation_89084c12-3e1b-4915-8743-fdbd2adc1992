'use client';

import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';
import {
  BarChart3,
  Wallet,
  Search,
  Filter,
  TrendingUp,
  Clock,
  Briefcase,
  TestTube,
  Plug,
  Menu,
  X
} from 'lucide-react';
import { useState, useCallback, useMemo, useTransition } from 'react';

const navigation = [
  {
    name: 'Capital Management',
    href: '/dashboard/capital',
    icon: Wallet,
    description: 'Portfolio overview and risk management'
  },
  {
    name: 'Stock Universal',
    href: '/dashboard/stocks',
    icon: Search,
    description: 'Stock search and watchlist management'
  },
  {
    name: 'BOH Eligible',
    href: '/dashboard/boh-eligible',
    icon: Filter,
    description: 'Boom-Bust-Recovery pattern stocks'
  },
  {
    name: 'Weekly High Signal',
    href: '/dashboard/weekly-high',
    icon: TrendingUp,
    description: 'Weekly high breakout signals'
  },
  {
    name: 'GTT Order',
    href: '/dashboard/gtt-orders',
    icon: Clock,
    description: 'Good Till Triggered order management'
  },
  {
    name: 'Current Holdings',
    href: '/dashboard/holdings',
    icon: Briefcase,
    description: 'Current portfolio positions and performance'
  },
  {
    name: 'Back Testing',
    href: '/dashboard/backtesting',
    icon: TestTube,
    description: 'Strategy backtesting and analysis'
  },
  {
    name: 'Connect Broker',
    href: '/dashboard/broker',
    icon: Plug,
    description: 'Broker connection and authentication'
  }
];

interface SidebarProps {
  className?: string;
}

export function OptimizedSidebar({ className }: SidebarProps) {
  const pathname = usePathname();
  const router = useRouter();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isPending, startTransition] = useTransition();
  const [activeHref, setActiveHref] = useState(pathname);

  // Optimized navigation handler with immediate feedback
  const handleNavigation = useCallback((href: string, e: React.MouseEvent) => {
    e.preventDefault();
    
    // Skip if already on the same page
    if (href === pathname) {
      setIsMobileMenuOpen(false);
      return;
    }
    
    // Immediate visual feedback
    setActiveHref(href);
    setIsMobileMenuOpen(false);
    
    // Use transition for smooth navigation
    startTransition(() => {
      router.push(href);
    });
  }, [router, pathname]);

  // Memoize navigation items to prevent re-renders
  const navigationItems = useMemo(() => navigation, []);

  // Prefetch likely next pages on hover
  const handleMouseEnter = useCallback((href: string) => {
    if (href !== pathname) {
      router.prefetch(href);
    }
  }, [router, pathname]);

  // Close mobile menu on escape key
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setIsMobileMenuOpen(false);
    }
  }, []);

  return (
    <>
      {/* Mobile menu button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="p-2 rounded-md bg-white shadow-md border border-gray-200 hover:bg-gray-50 transition-colors duration-150"
          aria-label="Toggle navigation menu"
        >
          {isMobileMenuOpen ? (
            <X className="h-6 w-6" />
          ) : (
            <Menu className="h-6 w-6" />
          )}
        </button>
      </div>

      {/* Mobile overlay */}
      {isMobileMenuOpen && (
        <div
          className="lg:hidden fixed inset-0 z-40 bg-black bg-opacity-50 transition-opacity duration-300"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          'fixed inset-y-0 left-0 z-40 w-72 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0',
          isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full',
          className
        )}
        onKeyDown={handleKeyDown}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-center h-16 px-4 border-b border-gray-200 bg-white">
            <div className="flex items-center space-x-2">
              <BarChart3 className="h-8 w-8 text-blue-600" />
              <span className="text-xl font-bold text-gray-900">Niveshtor</span>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
            {navigationItems.map((item) => {
              const isActive = (activeHref || pathname) === item.href;
              const isLoading = isPending && isActive;
              
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  onClick={(e) => handleNavigation(item.href, e)}
                  onMouseEnter={() => handleMouseEnter(item.href)}
                  className={cn(
                    'group flex items-start p-3 rounded-lg text-sm font-medium transition-all duration-150 hover:scale-[1.02]',
                    isActive
                      ? 'bg-blue-50 text-blue-700 border border-blue-200 shadow-sm'
                      : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900 hover:shadow-sm',
                    isLoading && 'opacity-75 cursor-wait'
                  )}
                >
                  <item.icon
                    className={cn(
                      'flex-shrink-0 h-5 w-5 mt-0.5 mr-3 transition-colors duration-150',
                      isActive ? 'text-blue-600' : 'text-gray-400 group-hover:text-gray-500'
                    )}
                  />
                  <div className="flex-1 min-w-0">
                    <div className="font-medium">{item.name}</div>
                    <div className="text-xs text-gray-500 mt-1 leading-tight">
                      {item.description}
                    </div>
                  </div>
                  {isLoading && (
                    <div className="flex-shrink-0 ml-2">
                      <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                    </div>
                  )}
                </Link>
              );
            })}
          </nav>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200">
            <div className="text-xs text-gray-500 text-center">
              <div>Niveshtor Trading Platform</div>
              <div className="mt-1">v1.0.0</div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default OptimizedSidebar;
