import { NextRequest, NextResponse } from 'next/server';
import { automaticGTTService } from '@/lib/automatic-gtt-service';
import { weeklyHighSignalDetector } from '@/lib/weekly-high-signal-detector';

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing complete Automatic GTT System...');

    const requestUrl = new URL(request.url);
    const baseUrl = `${requestUrl.protocol}//${requestUrl.host}`;

    // Step 1: Initialize the service
    console.log('📊 Step 1: Initializing Automatic GTT Service...');
    await automaticGTTService.start();
    
    const initialStatus = {
      detector: weeklyHighSignalDetector.getStatus(),
      service: automaticGTTService.getStatistics()
    };

    console.log(`✅ Service initialized: ${initialStatus.service.isInitialized ? 'SUCCESS' : 'FAILED'}`);

    // Step 2: Test signal detection
    console.log('🔍 Step 2: Testing Weekly High Signal Detection...');
    const signals = await weeklyHighSignalDetector.triggerManualScan();
    
    const signalSummary = {
      total: signals.length,
      strong: signals.filter(s => s.signalStrength === 'STRONG').length,
      moderate: signals.filter(s => s.signalStrength === 'MODERATE').length,
      weak: signals.filter(s => s.signalStrength === 'WEAK').length
    };

    console.log(`✅ Signal detection: ${signals.length} signals found`);

    // Step 3: Test automatic order creation (simulate)
    console.log('🤖 Step 3: Testing Automatic Order Creation...');
    let testOrderResult = null;
    
    if (signals.length > 0) {
      const testSymbol = signals[0].symbol;
      try {
        testOrderResult = await automaticGTTService.testCreateOrder(testSymbol);
        console.log(`✅ Test order creation: ${testOrderResult ? 'SUCCESS' : 'NO_SIGNAL'}`);
      } catch (error) {
        console.log(`❌ Test order creation failed: ${error}`);
      }
    }

    // Step 4: Get final statistics
    console.log('📈 Step 4: Collecting Final Statistics...');
    const finalStats = {
      detector: weeklyHighSignalDetector.getStatus(),
      service: automaticGTTService.getStatistics()
    };

    const allOrders = automaticGTTService.getAllOrders();
    const orderStats = {
      total: allOrders.length,
      pending: allOrders.filter(o => o.status === 'PENDING').length,
      autoCreated: allOrders.filter(o => o.autoCreated).length,
      signalOrders: allOrders.filter(o => o.source === 'SIGNAL').length
    };

    // Step 5: Validate system health
    console.log('🏥 Step 5: System Health Check...');
    const healthCheck = {
      serviceInitialized: finalStats.service.isInitialized,
      detectorRunning: finalStats.detector.isRunning,
      signalsDetected: signals.length > 0,
      orderCreationWorking: testOrderResult !== null,
      marketStatusDetection: typeof finalStats.detector.isMarketOpen === 'boolean',
      configurationValid: finalStats.service.isEnabled !== undefined
    };

    const healthScore = Object.values(healthCheck).filter(Boolean).length;
    const maxScore = Object.keys(healthCheck).length;
    const isHealthy = healthScore >= maxScore - 1; // Allow 1 failure

    console.log(`🏥 Health Score: ${healthScore}/${maxScore} - ${isHealthy ? 'HEALTHY' : 'NEEDS_ATTENTION'}`);

    return NextResponse.json({
      success: true,
      message: 'Automatic GTT System test completed',
      results: {
        step1_initialization: {
          status: 'SUCCESS',
          serviceInitialized: initialStatus.service.isInitialized,
          detectorRunning: initialStatus.detector.isRunning,
          marketOpen: initialStatus.detector.isMarketOpen
        },
        step2_signal_detection: {
          status: 'SUCCESS',
          signalsFound: signals.length,
          signalBreakdown: signalSummary,
          sampleSignals: signals.slice(0, 3).map(s => ({
            symbol: s.symbol,
            strength: s.signalStrength,
            triggerPrice: s.suggestedBuyPrice,
            quantity: s.suggestedGTTQuantity
          }))
        },
        step3_order_creation: {
          status: testOrderResult ? 'SUCCESS' : 'NO_SIGNALS',
          testOrder: testOrderResult ? {
            symbol: testOrderResult.symbol,
            triggerPrice: testOrderResult.triggerPrice,
            quantity: testOrderResult.quantity,
            autoCreated: testOrderResult.autoCreated
          } : null
        },
        step4_statistics: {
          status: 'SUCCESS',
          detector: finalStats.detector,
          service: finalStats.service,
          orders: orderStats
        },
        step5_health_check: {
          status: isHealthy ? 'HEALTHY' : 'NEEDS_ATTENTION',
          score: `${healthScore}/${maxScore}`,
          checks: healthCheck,
          recommendations: isHealthy ? [] : [
            !healthCheck.serviceInitialized && 'Service initialization failed',
            !healthCheck.detectorRunning && 'Signal detector not running',
            !healthCheck.signalsDetected && 'No signals detected (may be normal)',
            !healthCheck.orderCreationWorking && 'Order creation not working',
            !healthCheck.marketStatusDetection && 'Market status detection failed',
            !healthCheck.configurationValid && 'Configuration validation failed'
          ].filter(Boolean)
        },
        overall: {
          status: isHealthy ? 'SUCCESS' : 'WARNING',
          readyForProduction: isHealthy && signals.length > 0,
          timestamp: new Date().toISOString()
        }
      }
    });

  } catch (error) {
    console.error('❌ Automatic GTT System test failed:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'System test failed',
        message: 'Automatic GTT System test failed'
      },
      { status: 500 }
    );
  }
}
