import { NextRequest, NextResponse } from 'next/server';
import { yahooFinanceService } from '@/lib/yahoo-finance';
import { getYahooSymbol } from '@/lib/nifty-stocks';

export async function GET(request: NextRequest) {
  try {
    // Test with a few known stocks
    const testSymbols = ['RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'ICICIBANK'];
    const yahooSymbols = testSymbols.map(getYahooSymbol);
    
    console.log('🧪 Testing BOH data fetching for:', yahooSymbols);
    
    // Test the optimized method
    const quotes = await yahooFinanceService.getMultipleQuotesWithCachedNames(yahooSymbols);
    
    const results = quotes.map(quote => ({
      symbol: quote.symbol,
      name: quote.name,
      price: quote.price,
      high52Week: quote.high52Week,
      low52Week: quote.low52Week,
      high52WeekDate: quote.high52WeekDate,
      low52WeekDate: quote.low52WeekDate,
      hasDates: !!(quote.high52WeekDate && quote.low52WeekDate),
      isBOHEligible: quote.high52WeekDate && quote.low52WeekDate ? 
        new Date(quote.low52WeekDate) > new Date(quote.high52WeekDate) : false
    }));
    
    return NextResponse.json({
      success: true,
      message: `Tested ${results.length} stocks`,
      data: results,
      summary: {
        total: results.length,
        withDates: results.filter(r => r.hasDates).length,
        bohEligible: results.filter(r => r.isBOHEligible).length
      }
    });
    
  } catch (error) {
    console.error('❌ Test BOH error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
