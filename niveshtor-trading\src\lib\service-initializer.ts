// Service Initializer
// Ensures all background services are imported and auto-started when the app loads

import { centralDataManager } from './central-data-manager';
import { automaticGTTService } from './automatic-gtt-service';
import { weeklyHighSignalDetector } from './weekly-high-signal-detector';

console.log('🚀 Service Initializer: Importing all background services...');

// Services will auto-start due to their auto-start code
// This file just ensures they are imported early in the app lifecycle

export const initializeAllServices = async () => {
  console.log('🔧 Initializing all background services...');
  
  try {
    // Wait a moment for auto-start to complete
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Check service status
    const centralStatus = centralDataManager.getStatus();
    const gttStats = automaticGTTService.getStatistics();
    const signalStatus = weeklyHighSignalDetector.getStatus();
    
    console.log('📊 Service Status Check:');
    console.log('- Central Data Manager:', centralStatus.isInitialized ? '✅ Running' : '❌ Not Running');
    console.log('- Automatic GTT Service:', gttStats.isInitialized ? '✅ Running' : '❌ Not Running');
    console.log('- Weekly High Signal Detector:', signalStatus.isRunning ? '✅ Running' : '❌ Not Running');
    
    return {
      centralDataManager: centralStatus.isInitialized,
      automaticGTTService: gttStats.isInitialized,
      weeklyHighSignalDetector: signalStatus.isRunning
    };
    
  } catch (error) {
    console.error('❌ Service initialization check failed:', error);
    return {
      centralDataManager: false,
      automaticGTTService: false,
      weeklyHighSignalDetector: false
    };
  }
};

// Export services for easy access
export {
  centralDataManager,
  automaticGTTService,
  weeklyHighSignalDetector
};
