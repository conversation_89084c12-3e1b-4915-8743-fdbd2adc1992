// React hook for weekly high signals
import { useState, useEffect, useCallback, useRef } from 'react';
import { weeklyHighSignalService, WeeklyHighSignal, WeeklyHighConfig } from '@/lib/weekly-high-service';

interface UseWeeklyHighSignalsOptions {
  autoRefresh?: boolean;
  refreshInterval?: number; // in milliseconds
  onError?: (error: Error) => void;
}

interface UseWeeklyHighSignalsReturn {
  // Data
  signals: WeeklyHighSignal[];
  stats: {
    total: number;
    active: number;
    triggered: number;
    expired: number;
    strong: number;
    moderate: number;
    weak: number;
    lastUpdate: Date | null;
  };
  
  // State
  isLoading: boolean;
  isRefreshing: boolean;
  error: Error | null;
  
  // Configuration
  config: WeeklyHighConfig;
  
  // Methods
  refreshSignals: () => Promise<void>;
  updateConfig: (newConfig: Partial<WeeklyHighConfig>) => void;
  getFilteredSignals: (status?: 'ACTIVE' | 'TRIGGERED' | 'EXPIRED') => WeeklyHighSignal[];
  clearError: () => void;
}

export function useWeeklyHighSignals(options: UseWeeklyHighSignalsOptions = {}): UseWeeklyHighSignalsReturn {
  const {
    autoRefresh = true,
    refreshInterval = 5 * 60 * 1000, // 5 minutes
    onError
  } = options;

  // State
  const [signals, setSignals] = useState<WeeklyHighSignal[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [config, setConfig] = useState<WeeklyHighConfig>(weeklyHighSignalService.getConfig());

  // Refs
  const mountedRef = useRef(true);
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Update signals from service
  const updateSignals = useCallback((newSignals: WeeklyHighSignal[]) => {
    if (!mountedRef.current) return;

    console.log(`🔄 Hook received ${newSignals.length} signals`);
    setSignals(newSignals);
    setIsLoading(false);
    setIsRefreshing(false);
    setError(null);
  }, []);

  // Handle errors
  const handleError = useCallback((err: Error) => {
    if (!mountedRef.current) return;
    
    console.error('❌ Weekly high signals error:', err);
    setError(err);
    setIsLoading(false);
    setIsRefreshing(false);
    
    if (onError) {
      onError(err);
    }
  }, [onError]);

  // Refresh signals
  const refreshSignals = useCallback(async () => {
    if (!mountedRef.current) return;
    
    try {
      setIsRefreshing(true);
      setError(null);
      
      const newSignals = await weeklyHighSignalService.refreshSignals();
      updateSignals(newSignals);
      
    } catch (err) {
      handleError(err as Error);
    }
  }, [updateSignals, handleError]);

  // Update configuration
  const updateConfig = useCallback((newConfig: Partial<WeeklyHighConfig>) => {
    weeklyHighSignalService.updateConfig(newConfig);
    setConfig(weeklyHighSignalService.getConfig());
  }, []);

  // Get filtered signals
  const getFilteredSignals = useCallback((status?: 'ACTIVE' | 'TRIGGERED' | 'EXPIRED') => {
    return weeklyHighSignalService.getFilteredSignals(status);
  }, []);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Get stats
  const stats = weeklyHighSignalService.getSignalStats();

  // Setup service listener
  useEffect(() => {
    mountedRef.current = true;

    console.log('🔗 Adding listener to weekly high signal service');

    // Add listener to service
    weeklyHighSignalService.addListener(updateSignals);

    // Get initial signals
    const initialSignals = weeklyHighSignalService.getSignals();
    console.log(`📊 Hook initialization: found ${initialSignals.length} initial signals`);

    if (initialSignals.length > 0) {
      updateSignals(initialSignals);
    } else {
      // Trigger initial scan if no signals
      console.log('🔄 No initial signals, triggering refresh');
      refreshSignals();
    }

    return () => {
      console.log('🔗 Removing listener from weekly high signal service');
      mountedRef.current = false;
      weeklyHighSignalService.removeListener(updateSignals);
    };
  }, [updateSignals, refreshSignals]);

  // Setup auto-refresh
  useEffect(() => {
    if (!autoRefresh) return;
    
    refreshIntervalRef.current = setInterval(() => {
      if (mountedRef.current && !isRefreshing) {
        refreshSignals();
      }
    }, refreshInterval);
    
    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    };
  }, [autoRefresh, refreshInterval, refreshSignals, isRefreshing]);

  return {
    signals,
    stats,
    isLoading,
    isRefreshing,
    error,
    config,
    refreshSignals,
    updateConfig,
    getFilteredSignals,
    clearError
  };
}

// Hook for listening to specific signal updates
export function useWeeklyHighSignalUpdates(callback: (signals: WeeklyHighSignal[]) => void) {
  const callbackRef = useRef(callback);
  
  // Update callback ref
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);
  
  useEffect(() => {
    const handleUpdate = (signals: WeeklyHighSignal[]) => {
      callbackRef.current(signals);
    };
    
    weeklyHighSignalService.addListener(handleUpdate);
    
    return () => {
      weeklyHighSignalService.removeListener(handleUpdate);
    };
  }, []);
}

// Hook for getting signal statistics
export function useWeeklyHighSignalStats() {
  const [stats, setStats] = useState(weeklyHighSignalService.getSignalStats());
  
  useEffect(() => {
    const updateStats = () => {
      setStats(weeklyHighSignalService.getSignalStats());
    };
    
    weeklyHighSignalService.addListener(updateStats);
    
    // Update stats immediately
    updateStats();
    
    return () => {
      weeklyHighSignalService.removeListener(updateStats);
    };
  }, []);
  
  return stats;
}
