{"name": "niveshtor-trading", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:studio": "prisma studio", "backend": "cd python-backend && python main.py", "setup": "npm install && npx prisma generate && npx prisma db push"}, "dependencies": {"@prisma/client": "^6.11.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.525.0", "next": "15.4.1", "prisma": "^6.11.1", "react": "19.1.0", "react-dom": "19.1.0", "recharts": "^3.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.1", "tailwindcss": "^4", "typescript": "^5"}}