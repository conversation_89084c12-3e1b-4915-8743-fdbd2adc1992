/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/stocks/nifty200/route";
exports.ids = ["app/api/stocks/nifty200/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstocks%2Fnifty200%2Froute&page=%2Fapi%2Fstocks%2Fnifty200%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstocks%2Fnifty200%2Froute.ts&appDir=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cniveshtor-trading%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cniveshtor-trading&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstocks%2Fnifty200%2Froute&page=%2Fapi%2Fstocks%2Fnifty200%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstocks%2Fnifty200%2Froute.ts&appDir=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cniveshtor-trading%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cniveshtor-trading&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var C_Users_mahesh_Documents_Niveshtor_niveshtor_trading_src_app_api_stocks_nifty200_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/stocks/nifty200/route.ts */ \"(rsc)/./src/app/api/stocks/nifty200/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/stocks/nifty200/route\",\n        pathname: \"/api/stocks/nifty200\",\n        filename: \"route\",\n        bundlePath: \"app/api/stocks/nifty200/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\niveshtor-trading\\\\src\\\\app\\\\api\\\\stocks\\\\nifty200\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_mahesh_Documents_Niveshtor_niveshtor_trading_src_app_api_stocks_nifty200_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/stocks/nifty200/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstocks%2Fnifty200%2Froute&page=%2Fapi%2Fstocks%2Fnifty200%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstocks%2Fnifty200%2Froute.ts&appDir=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cniveshtor-trading%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cniveshtor-trading&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/stocks/nifty200/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/stocks/nifty200/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_yahoo_finance__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/yahoo-finance */ \"(rsc)/./src/lib/yahoo-finance.ts\");\n/* harmony import */ var _lib_nifty_stocks__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/nifty-stocks */ \"(rsc)/./src/lib/nifty-stocks.ts\");\n/* harmony import */ var _lib_holdings_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/holdings-service */ \"(rsc)/./src/lib/holdings-service.ts\");\n/* harmony import */ var _lib_cache_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/cache-service */ \"(rsc)/./src/lib/cache-service.ts\");\n/* harmony import */ var _lib_stock_names_service__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/stock-names-service */ \"(rsc)/./src/lib/stock-names-service.ts\");\n\n\n\n\n\n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const batchSize = parseInt(searchParams.get('batchSize') || '25'); // Reduced batch size\n        const batchIndex = parseInt(searchParams.get('batchIndex') || '0');\n        // Check cache first\n        const cacheKey = _lib_cache_service__WEBPACK_IMPORTED_MODULE_4__.CacheKeys.niftyStocks(batchIndex);\n        const cached = _lib_cache_service__WEBPACK_IMPORTED_MODULE_4__.cacheService.get(cacheKey);\n        if (cached) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: cached,\n                cached: true\n            });\n        }\n        // Calculate batch range\n        const startIndex = batchIndex * batchSize;\n        const endIndex = Math.min(startIndex + batchSize, _lib_nifty_stocks__WEBPACK_IMPORTED_MODULE_2__.NIFTY_200_SYMBOLS.length);\n        const batchSymbols = _lib_nifty_stocks__WEBPACK_IMPORTED_MODULE_2__.NIFTY_200_SYMBOLS.slice(startIndex, endIndex);\n        console.log(`Fetching batch ${batchIndex}: symbols ${startIndex} to ${endIndex - 1} (${batchSymbols.length} symbols)`);\n        // Convert NSE symbols to Yahoo Finance format, filtering out delisted stocks\n        const yahooSymbols = batchSymbols.map(_lib_nifty_stocks__WEBPACK_IMPORTED_MODULE_2__.getYahooSymbol).filter((symbol)=>symbol !== null); // Remove delisted stocks\n        const validBatchSymbols = batchSymbols.filter((symbol)=>(0,_lib_nifty_stocks__WEBPACK_IMPORTED_MODULE_2__.getYahooSymbol)(symbol) !== null);\n        // Check if this is a force refresh request\n        const forceRefresh = searchParams.get('forceRefresh') === 'true';\n        // Preload stock names if not cached or force refresh\n        if (forceRefresh || !_lib_stock_names_service__WEBPACK_IMPORTED_MODULE_5__.stockNamesService.isNameCached(yahooSymbols[0])) {\n            console.log('🔄 Preloading stock names...');\n            await _lib_stock_names_service__WEBPACK_IMPORTED_MODULE_5__.stockNamesService.preloadStockNames(yahooSymbols);\n        }\n        // Fetch quotes using optimized method with cached names\n        const quotes = await Promise.race([\n            _lib_yahoo_finance__WEBPACK_IMPORTED_MODULE_1__.yahooFinanceService.getMultipleQuotesWithCachedNames(yahooSymbols),\n            new Promise((_, reject)=>setTimeout(()=>reject(new Error('Yahoo Finance API timeout')), 30000) // Increased to 30 seconds\n            )\n        ]);\n        console.log(`Yahoo Finance returned ${quotes.length} quotes for ${yahooSymbols.length} symbols`);\n        // Get current holdings\n        const holdingSymbols = _lib_holdings_service__WEBPACK_IMPORTED_MODULE_3__.holdingsService.getHoldingSymbols();\n        // Process and filter stocks\n        const processedStocks = batchSymbols.map((nseSymbol)=>{\n            const yahooSymbol = (0,_lib_nifty_stocks__WEBPACK_IMPORTED_MODULE_2__.getYahooSymbol)(nseSymbol);\n            const quote = quotes.find((q)=>q.symbol === yahooSymbol);\n            const price = quote?.price || 0;\n            const change = quote?.change || 0;\n            const changePercent = quote?.changePercent || 0;\n            const volume = quote?.volume || 0;\n            const marketCap = quote?.marketCap;\n            const high52Week = quote?.high52Week;\n            const low52Week = quote?.low52Week;\n            const high52WeekDate = quote?.high52WeekDate;\n            const low52WeekDate = quote?.low52WeekDate;\n            const inHoldings = holdingSymbols.includes(nseSymbol);\n            const isEligible = _lib_holdings_service__WEBPACK_IMPORTED_MODULE_3__.holdingsService.isStockEligibleForTrading(nseSymbol, price);\n            const stock = {\n                symbol: nseSymbol,\n                name: quote?.name || nseSymbol,\n                price,\n                change,\n                changePercent,\n                volume,\n                marketCap,\n                high52Week,\n                low52Week,\n                high52WeekDate,\n                low52WeekDate,\n                isEligible,\n                inHoldings\n            };\n            // Add BOH eligibility calculation\n            return (0,_lib_nifty_stocks__WEBPACK_IMPORTED_MODULE_2__.addBOHEligibility)(stock);\n        });\n        // Filter to show only eligible stocks (CMP < 2000 OR in holdings)\n        const eligibleStocks = processedStocks.filter((stock)=>stock.isEligible);\n        // Sort by market cap (descending) for better user experience\n        eligibleStocks.sort((a, b)=>(b.marketCap || 0) - (a.marketCap || 0));\n        console.log(`Processed ${processedStocks.length} stocks, ${eligibleStocks.length} eligible (CMP < 2000 or in holdings)`);\n        const totalBatches = Math.ceil(_lib_nifty_stocks__WEBPACK_IMPORTED_MODULE_2__.NIFTY_200_SYMBOLS.length / batchSize);\n        const hasMore = batchIndex < totalBatches - 1;\n        const responseData = {\n            stocks: eligibleStocks,\n            pagination: {\n                batchIndex,\n                batchSize,\n                totalSymbols: _lib_nifty_stocks__WEBPACK_IMPORTED_MODULE_2__.NIFTY_200_SYMBOLS.length,\n                totalBatches,\n                hasMore,\n                processedCount: endIndex,\n                eligibleCount: eligibleStocks.length\n            }\n        };\n        // Cache the response\n        _lib_cache_service__WEBPACK_IMPORTED_MODULE_4__.cacheService.set(cacheKey, responseData);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: responseData\n        });\n    } catch (error) {\n        console.error('Error fetching Nifty 200 stocks:', error);\n        // Try to return cached data even if stale\n        const cacheKey = _lib_cache_service__WEBPACK_IMPORTED_MODULE_4__.CacheKeys.niftyStocks(parseInt(new URL(request.url).searchParams.get('batchIndex') || '0'));\n        const staleData = _lib_cache_service__WEBPACK_IMPORTED_MODULE_4__.cacheService.get(cacheKey);\n        if (staleData) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: staleData,\n                stale: true,\n                warning: 'Using cached data due to API error'\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to fetch Nifty 200 stocks',\n            data: null\n        }, {\n            status: 500\n        });\n    }\n}\n// Get all eligible stocks (for search functionality)\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { searchQuery = '' } = body;\n        console.log('Fetching all Nifty 200 stocks for search...');\n        // Fetch all symbols in smaller batches to avoid timeout\n        const batchSize = 25;\n        const totalBatches = Math.ceil(_lib_nifty_stocks__WEBPACK_IMPORTED_MODULE_2__.NIFTY_200_SYMBOLS.length / batchSize);\n        const allStocks = [];\n        for(let i = 0; i < totalBatches; i++){\n            const startIndex = i * batchSize;\n            const endIndex = Math.min(startIndex + batchSize, _lib_nifty_stocks__WEBPACK_IMPORTED_MODULE_2__.NIFTY_200_SYMBOLS.length);\n            const batchSymbols = _lib_nifty_stocks__WEBPACK_IMPORTED_MODULE_2__.NIFTY_200_SYMBOLS.slice(startIndex, endIndex);\n            try {\n                const yahooSymbols = batchSymbols.map(_lib_nifty_stocks__WEBPACK_IMPORTED_MODULE_2__.getYahooSymbol);\n                // Use optimized method with cached names for batch processing\n                const quotes = await _lib_yahoo_finance__WEBPACK_IMPORTED_MODULE_1__.yahooFinanceService.getMultipleQuotesWithCachedNames(yahooSymbols);\n                const holdingSymbols = _lib_holdings_service__WEBPACK_IMPORTED_MODULE_3__.holdingsService.getHoldingSymbols();\n                const batchStocks = batchSymbols.map((nseSymbol)=>{\n                    const yahooSymbol = (0,_lib_nifty_stocks__WEBPACK_IMPORTED_MODULE_2__.getYahooSymbol)(nseSymbol);\n                    const quote = quotes.find((q)=>q.symbol === yahooSymbol);\n                    const price = quote?.price || 0;\n                    const inHoldings = holdingSymbols.includes(nseSymbol);\n                    const isEligible = _lib_holdings_service__WEBPACK_IMPORTED_MODULE_3__.holdingsService.isStockEligibleForTrading(nseSymbol, price);\n                    const stock = {\n                        symbol: nseSymbol,\n                        name: quote?.name || nseSymbol,\n                        price,\n                        change: quote?.change || 0,\n                        changePercent: quote?.changePercent || 0,\n                        volume: quote?.volume || 0,\n                        marketCap: quote?.marketCap,\n                        high52Week: quote?.high52Week,\n                        low52Week: quote?.low52Week,\n                        high52WeekDate: quote?.high52WeekDate,\n                        low52WeekDate: quote?.low52WeekDate,\n                        isEligible,\n                        inHoldings\n                    };\n                    return (0,_lib_nifty_stocks__WEBPACK_IMPORTED_MODULE_2__.addBOHEligibility)(stock);\n                });\n                allStocks.push(...batchStocks);\n                // Small delay to avoid overwhelming the API\n                await new Promise((resolve)=>setTimeout(resolve, 100));\n            } catch (batchError) {\n                console.error(`Error in batch ${i}:`, batchError);\n            // Continue with next batch even if one fails\n            }\n        }\n        // Filter eligible stocks\n        let eligibleStocks = allStocks.filter((stock)=>stock.isEligible);\n        // Apply search filter if provided\n        if (searchQuery.trim()) {\n            const query = searchQuery.toLowerCase();\n            eligibleStocks = eligibleStocks.filter((stock)=>stock.symbol.toLowerCase().includes(query) || stock.name.toLowerCase().includes(query));\n        }\n        // Sort by market cap (descending)\n        eligibleStocks.sort((a, b)=>(b.marketCap || 0) - (a.marketCap || 0));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                stocks: eligibleStocks,\n                totalCount: eligibleStocks.length,\n                searchQuery\n            }\n        });\n    } catch (error) {\n        console.error('Error searching Nifty 200 stocks:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to search Nifty 200 stocks'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/stocks/nifty200/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/cache-service.ts":
/*!**********************************!*\
  !*** ./src/lib/cache-service.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CacheKeys: () => (/* binding */ CacheKeys),\n/* harmony export */   cacheService: () => (/* binding */ cacheService)\n/* harmony export */ });\n// Cache service for optimizing data fetching and reducing API calls\nclass CacheService {\n    // Get TTL based on data type\n    getTTL(key) {\n        if (key.includes('stock-name') || key.includes('names-map')) {\n            return this.STOCK_NAMES_TTL;\n        }\n        if (key.includes('stock') || key.includes('nifty')) {\n            return this.STOCK_DATA_TTL;\n        }\n        if (key.includes('broker') || key.includes('balance')) {\n            return this.BROKER_DATA_TTL;\n        }\n        if (key.includes('portfolio') || key.includes('holdings')) {\n            return this.PORTFOLIO_DATA_TTL;\n        }\n        return this.DEFAULT_TTL;\n    }\n    // Set cache entry\n    set(key, data, customTTL) {\n        const ttl = customTTL || this.getTTL(key);\n        const now = Date.now();\n        this.cache.set(key, {\n            data,\n            timestamp: now,\n            expiresAt: now + ttl\n        });\n    }\n    // Get cache entry\n    get(key) {\n        const entry = this.cache.get(key);\n        if (!entry) {\n            return null;\n        }\n        // Check if expired\n        if (Date.now() > entry.expiresAt) {\n            this.cache.delete(key);\n            return null;\n        }\n        return entry.data;\n    }\n    // Check if key exists and is valid\n    has(key) {\n        const entry = this.cache.get(key);\n        if (!entry) {\n            return false;\n        }\n        // Check if expired\n        if (Date.now() > entry.expiresAt) {\n            this.cache.delete(key);\n            return false;\n        }\n        return true;\n    }\n    // Clear specific key\n    delete(key) {\n        this.cache.delete(key);\n    }\n    // Clear all cache\n    clear() {\n        this.cache.clear();\n    }\n    // Clear expired entries\n    cleanup() {\n        const now = Date.now();\n        for (const [key, entry] of this.cache.entries()){\n            if (now > entry.expiresAt) {\n                this.cache.delete(key);\n            }\n        }\n    }\n    // Get cache stats\n    getStats() {\n        return {\n            size: this.cache.size,\n            keys: Array.from(this.cache.keys()),\n            hitRate: 0 // TODO: Implement hit rate tracking\n        };\n    }\n    // Cached fetch wrapper\n    async cachedFetch(key, fetchFn, customTTL) {\n        // Try to get from cache first\n        const cached = this.get(key);\n        if (cached !== null) {\n            return cached;\n        }\n        // Fetch fresh data\n        try {\n            const data = await fetchFn();\n            this.set(key, data, customTTL);\n            return data;\n        } catch (error) {\n            // If fetch fails, try to return stale data if available\n            const staleEntry = this.cache.get(key);\n            if (staleEntry) {\n                console.warn(`Using stale data for ${key} due to fetch error:`, error);\n                return staleEntry.data;\n            }\n            throw error;\n        }\n    }\n    // Prefetch data in background\n    async prefetch(key, fetchFn, customTTL) {\n        // Only prefetch if not already cached\n        if (!this.has(key)) {\n            try {\n                const data = await fetchFn();\n                this.set(key, data, customTTL);\n            } catch (error) {\n                console.warn(`Prefetch failed for ${key}:`, error);\n            }\n        }\n    }\n    // Batch fetch with caching\n    async batchFetch(requests) {\n        const results = [];\n        const fetchPromises = [];\n        for (const request of requests){\n            const cached = this.get(request.key);\n            if (cached !== null) {\n                results.push(cached);\n            } else {\n                fetchPromises.push(request.fetchFn().then((data)=>{\n                    this.set(request.key, data, request.ttl);\n                    return data;\n                }));\n            }\n        }\n        // Wait for all fetches to complete\n        const fetchedResults = await Promise.all(fetchPromises);\n        results.push(...fetchedResults);\n        return results;\n    }\n    // Invalidate cache by pattern\n    invalidatePattern(pattern) {\n        const regex = new RegExp(pattern);\n        for (const key of this.cache.keys()){\n            if (regex.test(key)) {\n                this.cache.delete(key);\n            }\n        }\n    }\n    // Auto cleanup interval\n    startAutoCleanup(intervalMs = 5 * 60 * 1000) {\n        setInterval(()=>{\n            this.cleanup();\n        }, intervalMs);\n    }\n    constructor(){\n        this.cache = new Map();\n        this.DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes\n        this.STOCK_DATA_TTL = 30 * 1000; // 30 seconds for stock data\n        this.STOCK_NAMES_TTL = 24 * 60 * 60 * 1000; // 24 hours for stock names\n        this.BROKER_DATA_TTL = 10 * 1000; // 10 seconds for broker data\n        this.PORTFOLIO_DATA_TTL = 60 * 1000; // 1 minute for portfolio data\n    }\n}\n// Create singleton instance\nconst cacheService = new CacheService();\n// Start auto cleanup\nif (false) {}\n// Cache key generators\nconst CacheKeys = {\n    brokerBalance: (userId)=>`broker-balance-${userId}`,\n    fundAllocation: (userId, strategy)=>`fund-allocation-${userId}-${strategy}`,\n    portfolioSummary: (userId)=>`portfolio-summary-${userId}`,\n    niftyStocks: (batchIndex)=>`nifty-stocks-batch-${batchIndex}`,\n    stockQuote: (symbol)=>`stock-quote-${symbol}`,\n    stockName: (symbol)=>`stock-name-${symbol}`,\n    stockNamesMap: ()=>'stock-names-map',\n    stockPriceData: (symbol)=>`stock-price-data-${symbol}`,\n    stockSearch: (query)=>`stock-search-${query}`,\n    yahooQuotes: (symbols)=>`yahoo-quotes-${symbols.sort().join(',')}`\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/cache-service.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/holdings-service.ts":
/*!*************************************!*\
  !*** ./src/lib/holdings-service.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   holdingsService: () => (/* binding */ holdingsService)\n/* harmony export */ });\n// Holdings service to manage current holdings across strategies\nclass HoldingsService {\n    // Get all current holdings\n    getAllHoldings() {\n        return [\n            ...this.holdings\n        ];\n    }\n    // Get holdings for a specific strategy\n    getHoldingsByStrategy(strategy) {\n        return this.holdings.filter((holding)=>holding.strategy === strategy);\n    }\n    // Check if a stock is currently held in any strategy\n    isStockInHoldings(symbol) {\n        return this.holdings.some((holding)=>holding.symbol === symbol);\n    }\n    // Get all unique symbols in holdings\n    getHoldingSymbols() {\n        return [\n            ...new Set(this.holdings.map((holding)=>holding.symbol))\n        ];\n    }\n    // Add a new holding\n    addHolding(holding) {\n        const existingIndex = this.holdings.findIndex((h)=>h.symbol === holding.symbol && h.strategy === holding.strategy);\n        if (existingIndex >= 0) {\n            // Update existing holding (average price calculation)\n            const existing = this.holdings[existingIndex];\n            const totalQuantity = existing.quantity + holding.quantity;\n            const totalValue = existing.quantity * existing.avgPrice + holding.quantity * holding.avgPrice;\n            this.holdings[existingIndex] = {\n                ...existing,\n                quantity: totalQuantity,\n                avgPrice: totalValue / totalQuantity,\n                currentPrice: holding.currentPrice\n            };\n        } else {\n            // Add new holding\n            this.holdings.push({\n                ...holding,\n                purchaseDate: new Date()\n            });\n        }\n    }\n    // Remove a holding\n    removeHolding(symbol, strategy) {\n        this.holdings = this.holdings.filter((holding)=>!(holding.symbol === symbol && holding.strategy === strategy));\n    }\n    // Update current price for a holding\n    updateCurrentPrice(symbol, currentPrice) {\n        this.holdings.forEach((holding)=>{\n            if (holding.symbol === symbol) {\n                holding.currentPrice = currentPrice;\n            }\n        });\n    }\n    // Get stocks that were bought above ₹2000 and are still in holdings\n    getStocksAbove2000InHoldings() {\n        return this.holdings.filter((holding)=>holding.avgPrice > 2000 || holding.currentPrice > 2000).map((holding)=>holding.symbol);\n    }\n    // Check if a stock should be eligible for trading\n    // (CMP < 2000 OR currently in holdings)\n    isStockEligibleForTrading(symbol, currentPrice) {\n        return currentPrice < 2000 || this.isStockInHoldings(symbol);\n    }\n    constructor(){\n        this.holdings = [\n            // Sample holdings for demonstration - in real app, this would come from database\n            {\n                symbol: 'RELIANCE',\n                strategy: 'DARVAS_BOX',\n                quantity: 50,\n                avgPrice: 2200.00,\n                currentPrice: 2456.75,\n                purchaseDate: new Date('2024-01-15')\n            },\n            {\n                symbol: 'TCS',\n                strategy: 'DARVAS_BOX',\n                quantity: 25,\n                avgPrice: 3400.00,\n                currentPrice: 3234.50,\n                purchaseDate: new Date('2024-01-20')\n            },\n            {\n                symbol: 'HDFC',\n                strategy: 'WEEKLY_HIGH',\n                quantity: 40,\n                avgPrice: 1600.00,\n                currentPrice: 1678.90,\n                purchaseDate: new Date('2024-02-01')\n            },\n            {\n                symbol: 'INFY',\n                strategy: 'BOH_FILTER',\n                quantity: 60,\n                avgPrice: 1500.00,\n                currentPrice: 1456.80,\n                purchaseDate: new Date('2024-02-10')\n            }\n        ];\n    }\n}\nconst holdingsService = new HoldingsService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/holdings-service.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/nifty-stocks.ts":
/*!*********************************!*\
  !*** ./src/lib/nifty-stocks.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NIFTY_200_SYMBOLS: () => (/* binding */ NIFTY_200_SYMBOLS),\n/* harmony export */   addBOHEligibility: () => (/* binding */ addBOHEligibility),\n/* harmony export */   calculateBOHEligibility: () => (/* binding */ calculateBOHEligibility),\n/* harmony export */   getDisplaySymbol: () => (/* binding */ getDisplaySymbol),\n/* harmony export */   getYahooSymbol: () => (/* binding */ getYahooSymbol)\n/* harmony export */ });\n// Current Nifty 200 stock symbols - updated list as of 2025\nconst RAW_NIFTY_200_SYMBOLS = [\n    'NYKAA',\n    'MRF',\n    'MANKIND',\n    'CHOLAFIN',\n    'CONCOR',\n    'ICICIPRULI',\n    'PREMIERENE',\n    'BSE',\n    'BANDHANBNK',\n    'WAAREEENER',\n    'SHRIRAMFIN',\n    'SBICARD',\n    'DABUR',\n    'DIXON',\n    'GMRAIRPORT',\n    'VBL',\n    'BAJFINANCE',\n    'BHEL',\n    'BIOCON',\n    'INDHOTEL',\n    'COALINDIA',\n    'HYUNDAI',\n    'GODREJCP',\n    'HINDUNILVR',\n    'ADANIENSOL',\n    'PATANJALI',\n    'SHREECEM',\n    'VMM',\n    'CUMMINSIND',\n    'LODHA',\n    'ABB',\n    'COCHINSHIP',\n    'BRITANNIA',\n    'ULTRACEMCO',\n    'AUBANK',\n    'KALYANKJIL',\n    'BDL',\n    'DIVISLAB',\n    'INDIGO',\n    'POWERGRID',\n    'OIL',\n    'HEROMOTOCO',\n    'ASTRAL',\n    'ACC',\n    'BANKBARODA',\n    'BOSCHLTD',\n    'MOTILALOFS',\n    'TORNTPHARM',\n    'TATATECH',\n    'MAHABANK',\n    'M&M',\n    'ASIANPAINT',\n    'UNITDSPR',\n    'PIIND',\n    'ITC',\n    'ASHOKLEY',\n    'NESTLEIND',\n    'HDFCAMC',\n    'ADANIGREEN',\n    'MARICO',\n    'APOLLOTYRE',\n    'LTF',\n    'HDFCBANK',\n    'TVSMOTOR',\n    'ADANIPOWER',\n    'MARUTI',\n    'MOTHERSON',\n    'BAJAJHFL',\n    'NTPCGREEN',\n    'JIOFIN',\n    'BAJAJFINSV',\n    'JSWENERGY',\n    'TORNTPOWER',\n    'NTPC',\n    'FEDERALBNK',\n    'ALKEM',\n    'NHPC',\n    'BAJAJ-AUTO',\n    'EICHERMOT',\n    'M&MFIN',\n    'ETERNAL',\n    'MPHASIS',\n    'HUDCO',\n    'PETRONET',\n    'SUPREMEIND',\n    'HAL',\n    'CIPLA',\n    'IRCTC',\n    'KOTAKBANK',\n    'POLICYBZR',\n    'INDIANB',\n    'CANBK',\n    'AXISBANK',\n    'ONGC',\n    'LICI',\n    'SWIGGY',\n    'TATAMOTORS',\n    'IDEA',\n    'SOLARINDS',\n    'LICHSGFIN',\n    'MAZDOCK',\n    'TATAPOWER',\n    'IREDA',\n    'SRF',\n    'BAJAJHLDNG',\n    'SBIN',\n    'BHARTIHEXA',\n    'ZYDUSLIFE',\n    'VOLTAS',\n    'AMBUJACEM',\n    'MUTHOOTFIN',\n    'TITAN',\n    'ADANIPORTS',\n    'SBILIFE',\n    'ATGL',\n    'ADANIENT',\n    'YESBANK',\n    'INFY',\n    'TATACONSUM',\n    'EXIDEIND',\n    'AUROPHARMA',\n    'PAYTM',\n    'PFC',\n    'TATAELXSI',\n    'TATACOMM',\n    'SUNPHARMA',\n    'INDUSTOWER',\n    'JSWSTEEL',\n    'ESCORTS',\n    'IRFC',\n    'BHARTIARTL',\n    'LUPIN',\n    'RVNL',\n    'POLYCAB',\n    'CGPOWER',\n    'GLENMARK',\n    'HAVELLS',\n    'PIDILITIND',\n    'TCS',\n    'NMDC',\n    'LTIM',\n    'TRENT',\n    'SUZLON',\n    'DMART',\n    'JUBLFOOD',\n    'SAIL',\n    'COLPAL',\n    'LT',\n    'MFSL',\n    'SONACOMS',\n    'PRESTIGE',\n    'IDFCFIRSTB',\n    'ICICIBANK',\n    'SJVN',\n    'BEL',\n    'OFSS',\n    'WIPRO',\n    'ICICIGI',\n    'ABCAPITAL',\n    'COFORGE',\n    'JINDALSTEL',\n    'GRASIM',\n    'BANKINDIA',\n    'PAGEIND',\n    'ABFRL',\n    'TIINDIA',\n    'INDUSINDBK',\n    'PNB',\n    'RECLTD',\n    'KPITTECH',\n    'HDFCLIFE',\n    'RELIANCE',\n    'PERSISTENT',\n    'DRREDDY',\n    'UPL',\n    'OLAELEC',\n    'TECHM',\n    'OBEROIRLTY',\n    'APOLLOHOSP',\n    'BHARATFORG',\n    'NAUKRI',\n    'HINDPETRO',\n    'DLF',\n    'TATASTEEL',\n    'BPCL',\n    'HINDALCO',\n    'IRB',\n    'APLAPOLLO',\n    'NATIONALUM',\n    'HCLTECH',\n    'SIEMENS',\n    'IOC',\n    'GODREJPROP',\n    'IGL',\n    'HINDZINC',\n    'PHOENIXLTD',\n    'VEDL',\n    'UNIONBANK',\n    'MAXHEALTH',\n    'GAIL'\n];\n// Remove duplicates and create final list\nconst NIFTY_200_SYMBOLS = [\n    ...new Set(RAW_NIFTY_200_SYMBOLS)\n];\n// Validation: Log any duplicates found and final count\nconst duplicates = RAW_NIFTY_200_SYMBOLS.filter((symbol, index)=>RAW_NIFTY_200_SYMBOLS.indexOf(symbol) !== index);\nif (duplicates.length > 0) {\n    console.warn('Duplicate symbols found and removed:', duplicates);\n}\nconsole.log(`Nifty 200 symbols loaded: ${NIFTY_200_SYMBOLS.length} unique symbols`);\n// Function to get Yahoo Finance symbol format for Indian stocks\nfunction getYahooSymbol(nseSymbol) {\n    // Handle special cases for Yahoo Finance symbol mapping\n    const symbolMappings = {\n        'M&M': 'MM.NS',\n        'M&MFIN': 'MMFIN.NS',\n        'BAJAJ-AUTO': 'BAJAJ_AUTO.NS',\n        'L&T': 'LT.NS',\n        'LTF': 'LTFH.NS',\n        'BOSCHLTD': 'BOSCHLTD.NS',\n        'BSOFT': 'BSOFT.NS' // Birlasoft\n    };\n    // Handle merged/renamed stocks - map to their current equivalent\n    const renamedMappings = {\n        'CADILAHC': 'ZYDUSLIFE.NS'\n    };\n    // Handle merged stocks (for backward compatibility)\n    const delistedMappings = {\n        'HDFC': 'HDFCBANK.NS',\n        'MINDTREE': 'LTIM.NS',\n        'PVR': 'PVRINOX.NS',\n        ...renamedMappings\n    };\n    // Stocks that are completely delisted/suspended - these will be skipped\n    const delistedStocks = new Set([]);\n    // Stocks that might have issues - keep for now but monitor\n    const problematicStocks = new Set([\n        'WAAREEENER',\n        'PREMIERENE',\n        'GMRAIRPORT',\n        'ADANIENSOL',\n        'PATANJALI',\n        'VMM',\n        'KALYANKJIL',\n        'NTPCGREEN',\n        'JIOFIN',\n        'BHARTIHEXA',\n        'ATGL',\n        'IREDA',\n        'SWIGGY',\n        'SOLARINDS',\n        'OLAELEC',\n        'PHOENIXLTD',\n        'MAXHEALTH' // Check if available\n    ]);\n    // Check if stock is delisted/suspended - return null to skip\n    if (delistedStocks.has(nseSymbol)) {\n        console.log(`🚫 Skipping delisted/suspended stock: ${nseSymbol}`);\n        return null; // This will be handled in the API to skip the stock\n    }\n    // Check for renamed/merged stocks first\n    if (renamedMappings[nseSymbol]) {\n        console.log(`📝 Mapping renamed stock ${nseSymbol} to ${renamedMappings[nseSymbol]}`);\n        return renamedMappings[nseSymbol];\n    }\n    if (delistedMappings[nseSymbol]) {\n        console.log(`📝 Mapping merged stock ${nseSymbol} to ${delistedMappings[nseSymbol]}`);\n        return delistedMappings[nseSymbol];\n    }\n    if (symbolMappings[nseSymbol]) {\n        return symbolMappings[nseSymbol];\n    }\n    // Log if this is a potentially problematic stock\n    if (problematicStocks.has(nseSymbol)) {\n        console.log(`⚠️ Fetching potentially new/problematic stock: ${nseSymbol}`);\n    }\n    return `${nseSymbol}.NS`;\n}\n// Function to get display name from NSE symbol\nfunction getDisplaySymbol(nseSymbol) {\n    return nseSymbol;\n}\n// Function to calculate BOH (Boom-Bust-Recovery) eligibility\nfunction calculateBOHEligibility(stock) {\n    // BOH Eligible if 52-week low occurred AFTER 52-week high\n    // This indicates: High (boom) → Low (bust) → Recovery pattern\n    if (!stock.high52WeekDate || !stock.low52WeekDate) {\n        return false; // Cannot determine without dates\n    }\n    const highDate = new Date(stock.high52WeekDate);\n    const lowDate = new Date(stock.low52WeekDate);\n    // Return true if low date is after high date (boom → bust → recovery pattern)\n    return lowDate > highDate;\n}\n// Function to add BOH eligibility to stock data\nfunction addBOHEligibility(stock) {\n    return {\n        ...stock,\n        isBOHEligible: calculateBOHEligibility(stock)\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/nifty-stocks.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stock-names-service.ts":
/*!****************************************!*\
  !*** ./src/lib/stock-names-service.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stockNamesService: () => (/* binding */ stockNamesService)\n/* harmony export */ });\n/* harmony import */ var _cache_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./cache-service */ \"(rsc)/./src/lib/cache-service.ts\");\n// Stock names service for caching and managing stock company names\n\nclass StockNamesService {\n    // Get stock name from cache or fetch if not available\n    async getStockName(symbol) {\n        // Try to get from cache first\n        const cached = _cache_service__WEBPACK_IMPORTED_MODULE_0__.cacheService.get(_cache_service__WEBPACK_IMPORTED_MODULE_0__.CacheKeys.stockName(symbol));\n        if (cached) {\n            return cached;\n        }\n        // If not in cache, fetch from Yahoo Finance\n        try {\n            const { yahooFinanceService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./yahoo-finance */ \"(rsc)/./src/lib/yahoo-finance.ts\"));\n            const quote = await yahooFinanceService.getQuote(symbol);\n            const name = quote?.name || symbol.replace('.NS', '');\n            // Cache the name for 24 hours\n            _cache_service__WEBPACK_IMPORTED_MODULE_0__.cacheService.set(_cache_service__WEBPACK_IMPORTED_MODULE_0__.CacheKeys.stockName(symbol), name);\n            return name;\n        } catch (error) {\n            console.warn(`Failed to fetch name for ${symbol}:`, error);\n            // Return symbol without .NS as fallback\n            return symbol.replace('.NS', '');\n        }\n    }\n    // Get multiple stock names efficiently\n    async getStockNames(symbols) {\n        const namesMap = new Map();\n        const uncachedSymbols = [];\n        // Check cache for each symbol\n        for (const symbol of symbols){\n            const cached = _cache_service__WEBPACK_IMPORTED_MODULE_0__.cacheService.get(_cache_service__WEBPACK_IMPORTED_MODULE_0__.CacheKeys.stockName(symbol));\n            if (cached) {\n                namesMap.set(symbol, cached);\n            } else {\n                uncachedSymbols.push(symbol);\n            }\n        }\n        // If all names are cached, return immediately\n        if (uncachedSymbols.length === 0) {\n            return namesMap;\n        }\n        console.log(`📝 Fetching names for ${uncachedSymbols.length} uncached stocks`);\n        // Fetch uncached names in batches\n        const batches = this.chunkArray(uncachedSymbols, this.BATCH_SIZE);\n        for (const batch of batches){\n            try {\n                const { yahooFinanceService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./yahoo-finance */ \"(rsc)/./src/lib/yahoo-finance.ts\"));\n                const quotes = await yahooFinanceService.getMultipleQuotes(batch);\n                for (const quote of quotes){\n                    if (quote && quote.symbol) {\n                        // Use the name from the quote, or fallback to symbol without .NS\n                        const name = quote.name || quote.symbol.replace('.NS', '');\n                        namesMap.set(quote.symbol, name);\n                        // Cache the name for 24 hours\n                        _cache_service__WEBPACK_IMPORTED_MODULE_0__.cacheService.set(_cache_service__WEBPACK_IMPORTED_MODULE_0__.CacheKeys.stockName(quote.symbol), name);\n                    }\n                }\n                // Ensure all symbols in the batch have names (even if fallback)\n                for (const symbol of batch){\n                    if (!namesMap.has(symbol)) {\n                        const fallbackName = symbol.replace('.NS', '');\n                        namesMap.set(symbol, fallbackName);\n                        _cache_service__WEBPACK_IMPORTED_MODULE_0__.cacheService.set(_cache_service__WEBPACK_IMPORTED_MODULE_0__.CacheKeys.stockName(symbol), fallbackName);\n                        console.log(`📝 Using fallback name for ${symbol}: ${fallbackName}`);\n                    }\n                }\n                // Add delay between batches to avoid rate limiting\n                if (batches.indexOf(batch) < batches.length - 1) {\n                    await new Promise((resolve)=>setTimeout(resolve, 100));\n                }\n            } catch (error) {\n                console.warn(`Failed to fetch names for batch:`, error);\n                // Add fallback names for failed batch\n                for (const symbol of batch){\n                    if (!namesMap.has(symbol)) {\n                        const fallbackName = symbol.replace('.NS', '');\n                        namesMap.set(symbol, fallbackName);\n                        _cache_service__WEBPACK_IMPORTED_MODULE_0__.cacheService.set(_cache_service__WEBPACK_IMPORTED_MODULE_0__.CacheKeys.stockName(symbol), fallbackName);\n                    }\n                }\n            }\n        }\n        return namesMap;\n    }\n    // Get all cached stock names\n    getCachedStockNames() {\n        const cachedMap = _cache_service__WEBPACK_IMPORTED_MODULE_0__.cacheService.get(_cache_service__WEBPACK_IMPORTED_MODULE_0__.CacheKeys.stockNamesMap());\n        return cachedMap || new Map();\n    }\n    // Cache stock names map for quick access\n    cacheStockNamesMap(namesMap) {\n        _cache_service__WEBPACK_IMPORTED_MODULE_0__.cacheService.set(_cache_service__WEBPACK_IMPORTED_MODULE_0__.CacheKeys.stockNamesMap(), namesMap);\n    }\n    // Preload stock names for given symbols\n    async preloadStockNames(symbols) {\n        console.log(`🚀 Preloading names for ${symbols.length} stocks`);\n        try {\n            const namesMap = await this.getStockNames(symbols);\n            this.cacheStockNamesMap(namesMap);\n            console.log(`✅ Preloaded ${namesMap.size} stock names`);\n        } catch (error) {\n            console.error('Failed to preload stock names:', error);\n        }\n    }\n    // Check if stock name is cached\n    isNameCached(symbol) {\n        return _cache_service__WEBPACK_IMPORTED_MODULE_0__.cacheService.has(_cache_service__WEBPACK_IMPORTED_MODULE_0__.CacheKeys.stockName(symbol));\n    }\n    // Get cache statistics for stock names\n    getNamesCacheStats() {\n        const stats = _cache_service__WEBPACK_IMPORTED_MODULE_0__.cacheService.getStats();\n        const nameKeys = stats.keys.filter((key)=>key.includes('stock-name'));\n        return {\n            cachedCount: nameKeys.length,\n            totalRequested: nameKeys.length,\n            hitRate: nameKeys.length > 0 ? 0.8 : 0 // Estimated hit rate\n        };\n    }\n    // Clear all cached stock names\n    clearNamesCache() {\n        _cache_service__WEBPACK_IMPORTED_MODULE_0__.cacheService.invalidatePattern('stock-name');\n        _cache_service__WEBPACK_IMPORTED_MODULE_0__.cacheService.delete(_cache_service__WEBPACK_IMPORTED_MODULE_0__.CacheKeys.stockNamesMap());\n        console.log('🗑️ Cleared all cached stock names');\n    }\n    // Refresh stock names (force re-fetch)\n    async refreshStockNames(symbols) {\n        // Clear existing cache for these symbols\n        for (const symbol of symbols){\n            _cache_service__WEBPACK_IMPORTED_MODULE_0__.cacheService.delete(_cache_service__WEBPACK_IMPORTED_MODULE_0__.CacheKeys.stockName(symbol));\n        }\n        // Fetch fresh names\n        return await this.getStockNames(symbols);\n    }\n    // Utility function to chunk array into smaller arrays\n    chunkArray(array, chunkSize) {\n        const chunks = [];\n        for(let i = 0; i < array.length; i += chunkSize){\n            chunks.push(array.slice(i, i + chunkSize));\n        }\n        return chunks;\n    }\n    // Get stock name with fallback\n    getStockNameSync(symbol) {\n        const cached = _cache_service__WEBPACK_IMPORTED_MODULE_0__.cacheService.get(_cache_service__WEBPACK_IMPORTED_MODULE_0__.CacheKeys.stockName(symbol));\n        return cached || symbol.replace('.NS', '');\n    }\n    // Batch update stock names from quotes\n    updateStockNamesFromQuotes(quotes) {\n        for (const quote of quotes){\n            if (quote && quote.symbol && quote.name) {\n                _cache_service__WEBPACK_IMPORTED_MODULE_0__.cacheService.set(_cache_service__WEBPACK_IMPORTED_MODULE_0__.CacheKeys.stockName(quote.symbol), quote.name);\n            }\n        }\n    }\n    // Force refresh all stock names (clears cache and re-fetches)\n    async forceRefreshAllNames(symbols) {\n        console.log('🔄 Force refreshing all stock names...');\n        this.clearNamesCache();\n        await this.preloadStockNames(symbols);\n    }\n    constructor(){\n        this.BATCH_SIZE = 50;\n        this.MAX_RETRIES = 3;\n    }\n}\n// Create singleton instance\nconst stockNamesService = new StockNamesService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stock-names-service.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/yahoo-finance.ts":
/*!**********************************!*\
  !*** ./src/lib/yahoo-finance.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   yahooFinanceService: () => (/* binding */ yahooFinanceService)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _cache_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./cache-service */ \"(rsc)/./src/lib/cache-service.ts\");\n/* harmony import */ var _stock_names_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./stock-names-service */ \"(rsc)/./src/lib/stock-names-service.ts\");\n\n\n\n// Yahoo Finance API endpoints - using chart endpoint which is more reliable\nconst YAHOO_FINANCE_BASE_URL = 'https://query1.finance.yahoo.com/v8/finance/chart';\nconst YAHOO_SEARCH_URL = 'https://query1.finance.yahoo.com/v1/finance/search';\nconst YAHOO_QUOTE_URL = 'https://query1.finance.yahoo.com/v7/finance/quote';\nconst YAHOO_QUOTE_SUMMARY_URL = 'https://query2.finance.yahoo.com/v10/finance/quoteSummary';\n// Alternative endpoints for better reliability\nconst YAHOO_CHART_URL = 'https://query1.finance.yahoo.com/v8/finance/chart';\n// Known problematic stocks that often fail - handle with extra care\nconst PROBLEMATIC_STOCKS = new Set([\n    'BOSCHLTD.NS',\n    'BSOFT.NS',\n    'MINDTREE.NS',\n    'PVR.NS',\n    'HDFC.NS' // Merged stock\n]);\nclass YahooFinanceService {\n    // Start real-time updates for given symbols\n    startRealTimeUpdates(symbols, callback) {\n        console.log(`🔄 Starting real-time updates for ${symbols.length} symbols`);\n        this.currentSymbols = symbols;\n        this.updateListeners.add(callback);\n        if (!this.isRealTimeActive) {\n            this.isRealTimeActive = true;\n            this.scheduleNextUpdate();\n        }\n    }\n    // Stop real-time updates for a specific callback\n    stopRealTimeUpdates(callback) {\n        this.updateListeners.delete(callback);\n        if (this.updateListeners.size === 0) {\n            this.isRealTimeActive = false;\n            if (this.realTimeInterval) {\n                clearTimeout(this.realTimeInterval);\n                this.realTimeInterval = null;\n            }\n            console.log('⏹️ Stopped real-time updates - no active listeners');\n        }\n    }\n    // Schedule the next update\n    scheduleNextUpdate() {\n        if (!this.isRealTimeActive) return;\n        const now = Date.now();\n        const timeSinceLastUpdate = now - this.lastUpdateTime;\n        const timeUntilNextUpdate = Math.max(0, 30000 - timeSinceLastUpdate); // 30 seconds\n        this.realTimeInterval = setTimeout(()=>{\n            this.performRealTimeUpdate();\n        }, timeUntilNextUpdate);\n    }\n    // Perform the actual real-time update\n    async performRealTimeUpdate() {\n        if (!this.isRealTimeActive || this.currentSymbols.length === 0) return;\n        try {\n            console.log(`🔄 Performing real-time update for ${this.currentSymbols.length} symbols`);\n            const quotes = await this.getBatchQuotes(this.currentSymbols);\n            this.lastUpdateTime = Date.now();\n            // Notify all listeners\n            this.updateListeners.forEach((callback)=>{\n                try {\n                    callback(quotes);\n                } catch (error) {\n                    console.error('❌ Error in update listener:', error);\n                }\n            });\n            console.log(`✅ Real-time update completed: ${quotes.length} quotes updated`);\n        } catch (error) {\n            console.error('❌ Real-time update failed:', error);\n        }\n        // Schedule next update\n        this.scheduleNextUpdate();\n    }\n    async makeRequest(url, params = {}) {\n        try {\n            console.log(`🌐 Making request to: ${url}`, params);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(url, {\n                params,\n                headers: {\n                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',\n                    'Accept': 'application/json',\n                    'Accept-Language': 'en-US,en;q=0.9',\n                    'Cache-Control': 'no-cache',\n                    'Pragma': 'no-cache',\n                    'Referer': 'https://finance.yahoo.com/'\n                },\n                timeout: 15000\n            });\n            console.log(`✅ Request successful, status: ${response.status}`);\n            return response.data;\n        } catch (error) {\n            const errorDetails = {\n                url,\n                params,\n                message: error?.message || 'Unknown error',\n                status: error?.response?.status || 'No status',\n                data: error?.response?.data || 'No data'\n            };\n            console.error('❌ Yahoo Finance API error:', errorDetails);\n            // For historical data requests, return null instead of throwing\n            if (url.includes('/v8/finance/chart/')) {\n                console.warn(`⚠️ Historical data request failed, returning null`);\n                return null;\n            }\n            throw new Error(`Failed to fetch data from Yahoo Finance: ${error?.message || 'Unknown error'}`);\n        }\n    }\n    // Get price data only (without fetching names from API) - optimized for frequent updates\n    async getPriceDataOnly(symbol) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(`${YAHOO_CHART_URL}/${symbol}`, {\n                params: {\n                    interval: '1d',\n                    range: '1d',\n                    includePrePost: false\n                },\n                headers: {\n                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n                    'Accept': 'application/json',\n                    'Referer': 'https://finance.yahoo.com/'\n                },\n                timeout: 8000\n            });\n            const data = response.data;\n            if (data.chart?.result?.[0]) {\n                const result = data.chart.result[0];\n                const meta = result.meta;\n                const quote = result.indicators?.quote?.[0];\n                const currentPrice = meta.regularMarketPrice || quote?.close && quote.close[quote.close.length - 1] || meta.previousClose || 0;\n                const previousClose = meta.previousClose || meta.chartPreviousClose || currentPrice;\n                const change = meta.regularMarketChange || currentPrice - previousClose;\n                const changePercent = meta.regularMarketChangePercent || (previousClose > 0 ? change / previousClose * 100 : 0);\n                if (currentPrice > 0) {\n                    // Get 52-week high/low dates by analyzing historical data\n                    let high52WeekDate;\n                    let low52WeekDate;\n                    try {\n                        // Get 1-year historical data to find exact dates\n                        const historicalResponse = await axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(`${YAHOO_CHART_URL}/${symbol}`, {\n                            params: {\n                                interval: '1d',\n                                range: '1y',\n                                includePrePost: false\n                            },\n                            headers: {\n                                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n                                'Accept': 'application/json',\n                                'Referer': 'https://finance.yahoo.com/'\n                            },\n                            timeout: 10000\n                        });\n                        const historicalData = historicalResponse.data;\n                        if (historicalData.chart?.result?.[0]) {\n                            const historicalResult = historicalData.chart.result[0];\n                            const timestamps = historicalResult.timestamp;\n                            const historicalQuote = historicalResult.indicators?.quote?.[0];\n                            if (timestamps && historicalQuote?.high && historicalQuote?.low) {\n                                const highs = historicalQuote.high;\n                                const lows = historicalQuote.low;\n                                // Find 52-week high and low with their dates\n                                let maxHigh = -Infinity;\n                                let minLow = Infinity;\n                                let maxHighIndex = -1;\n                                let minLowIndex = -1;\n                                for(let i = 0; i < highs.length; i++){\n                                    if (highs[i] && highs[i] > maxHigh) {\n                                        maxHigh = highs[i];\n                                        maxHighIndex = i;\n                                    }\n                                    if (lows[i] && lows[i] < minLow) {\n                                        minLow = lows[i];\n                                        minLowIndex = i;\n                                    }\n                                }\n                                if (maxHighIndex >= 0 && minLowIndex >= 0) {\n                                    high52WeekDate = new Date(timestamps[maxHighIndex] * 1000).toISOString().split('T')[0];\n                                    low52WeekDate = new Date(timestamps[minLowIndex] * 1000).toISOString().split('T')[0];\n                                }\n                            }\n                        }\n                    } catch (historicalError) {\n                        console.warn(`⚠️ Could not fetch historical data for ${symbol}:`, {\n                            error: historicalError.message,\n                            status: historicalError.response?.status,\n                            timeout: historicalError.code === 'ECONNABORTED'\n                        });\n                    }\n                    return {\n                        symbol: symbol,\n                        price: parseFloat(currentPrice.toString()),\n                        change: parseFloat(change.toString()),\n                        changePercent: parseFloat(changePercent.toString()),\n                        volume: parseInt((meta.regularMarketVolume || quote?.volume?.[quote.volume.length - 1] || 0).toString()),\n                        marketCap: meta.marketCap,\n                        high52Week: parseFloat((meta.fiftyTwoWeekHigh || 0).toString()),\n                        low52Week: parseFloat((meta.fiftyTwoWeekLow || 0).toString()),\n                        high52WeekDate,\n                        low52WeekDate,\n                        avgVolume: parseInt((meta.averageDailyVolume3Month || 0).toString())\n                    };\n                }\n            }\n            return null;\n        } catch (error) {\n            console.warn(`Failed to get price data for ${symbol}:`, error);\n            return null;\n        }\n    }\n    // Get multiple price data only (batch operation)\n    async getMultiplePriceDataOnly(symbols) {\n        const results = [];\n        const batchSize = 25;\n        for(let i = 0; i < symbols.length; i += batchSize){\n            const batch = symbols.slice(i, i + batchSize);\n            const batchPromises = batch.map((symbol)=>this.getPriceDataOnly(symbol));\n            try {\n                const batchResults = await Promise.all(batchPromises);\n                results.push(...batchResults.filter((result)=>result !== null));\n                // Add delay between batches\n                if (i + batchSize < symbols.length) {\n                    await new Promise((resolve)=>setTimeout(resolve, 200));\n                }\n            } catch (error) {\n                console.error(`Batch error for symbols ${batch.join(', ')}:`, error);\n            }\n        }\n        return results;\n    }\n    // Get quote with cached name (optimized for frequent updates)\n    async getQuoteWithCachedName(symbol) {\n        try {\n            // Get price data only\n            const priceData = await this.getPriceDataOnly(symbol);\n            if (!priceData) return null;\n            // Get name from cache or use fallback\n            const name = _stock_names_service__WEBPACK_IMPORTED_MODULE_1__.stockNamesService.getStockNameSync(symbol);\n            return {\n                ...priceData,\n                name\n            };\n        } catch (error) {\n            console.warn(`Failed to get quote with cached name for ${symbol}:`, error);\n            return null;\n        }\n    }\n    // Get multiple quotes with cached names (batch operation)\n    async getMultipleQuotesWithCachedNames(symbols) {\n        try {\n            console.log(`📊 Fetching quotes with cached names for ${symbols.length} symbols`);\n            // Get price data for all symbols\n            const priceDataList = await this.getMultiplePriceDataOnly(symbols);\n            console.log(`💰 Got price data for ${priceDataList.length}/${symbols.length} symbols`);\n            // Get cached names for all symbols\n            const namesMap = await _stock_names_service__WEBPACK_IMPORTED_MODULE_1__.stockNamesService.getStockNames(symbols);\n            console.log(`📝 Got names for ${namesMap.size}/${symbols.length} symbols`);\n            // Combine price data with cached names\n            const quotes = [];\n            for (const priceData of priceDataList){\n                const name = namesMap.get(priceData.symbol) || priceData.symbol.replace('.NS', '');\n                const quote = {\n                    ...priceData,\n                    name\n                };\n                quotes.push(quote);\n                // Log BOH eligibility data for debugging\n                if (priceData.high52WeekDate && priceData.low52WeekDate) {\n                    const highDate = new Date(priceData.high52WeekDate);\n                    const lowDate = new Date(priceData.low52WeekDate);\n                    const isBOHEligible = lowDate > highDate;\n                    console.log(`🔍 ${priceData.symbol}: High=${priceData.high52WeekDate}, Low=${priceData.low52WeekDate}, BOH=${isBOHEligible}`);\n                }\n            }\n            console.log(`✅ Combined ${quotes.length} quotes with cached names`);\n            return quotes;\n        } catch (error) {\n            console.error('❌ Failed to get multiple quotes with cached names:', error);\n            return [];\n        }\n    }\n    async getQuote(symbol) {\n        try {\n            // Add .NS suffix for NSE stocks if not present\n            const formattedSymbol = symbol.includes('.') ? symbol : `${symbol}.NS`;\n            const data = await this.makeRequest(YAHOO_QUOTE_URL, {\n                symbols: formattedSymbol\n            });\n            const result = data.quoteResponse?.result?.[0];\n            if (!result) return null;\n            return {\n                symbol: result.symbol,\n                name: result.longName || result.shortName || symbol,\n                price: result.regularMarketPrice || 0,\n                change: result.regularMarketChange || 0,\n                changePercent: result.regularMarketChangePercent || 0,\n                volume: result.regularMarketVolume || 0,\n                marketCap: result.marketCap,\n                high52Week: result.fiftyTwoWeekHigh,\n                low52Week: result.fiftyTwoWeekLow,\n                avgVolume: result.averageDailyVolume3Month\n            };\n        } catch (error) {\n            console.error(`Error fetching quote for ${symbol}:`, error);\n            return null;\n        }\n    }\n    async getMultipleQuotes(symbols) {\n        console.log(`🔍 Yahoo Finance: Fetching quotes for ${symbols.length} symbols:`, symbols.slice(0, 5));\n        try {\n            // Format symbols for NSE - ensure .NS suffix\n            const formattedSymbols = symbols.map((symbol)=>{\n                const formatted = symbol.includes('.') ? symbol : `${symbol}.NS`;\n                return formatted;\n            });\n            console.log(`📝 Formatted symbols:`, formattedSymbols.slice(0, 5));\n            const allResults = [];\n            // Process each symbol individually using chart endpoint (more reliable)\n            for(let i = 0; i < formattedSymbols.length; i++){\n                const symbol = formattedSymbols[i];\n                try {\n                    console.log(`📊 Fetching data for ${symbol} (${i + 1}/${formattedSymbols.length})`);\n                    let stockQuote = null;\n                    // Try multiple approaches for better success rate\n                    // Approach 1: Chart endpoint (most reliable)\n                    try {\n                        const response = await axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(`${YAHOO_CHART_URL}/${symbol}`, {\n                            params: {\n                                interval: '1d',\n                                range: '1d',\n                                includePrePost: false\n                            },\n                            headers: {\n                                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',\n                                'Accept': 'application/json',\n                                'Accept-Language': 'en-US,en;q=0.9',\n                                'Referer': 'https://finance.yahoo.com/'\n                            },\n                            timeout: 8000\n                        });\n                        const data = response.data;\n                        if (data.chart?.result?.[0]) {\n                            const result = data.chart.result[0];\n                            const meta = result.meta;\n                            const quote = result.indicators?.quote?.[0];\n                            // Extract current price from meta or latest quote data\n                            const currentPrice = meta.regularMarketPrice || quote?.close && quote.close[quote.close.length - 1] || meta.previousClose || 0;\n                            const previousClose = meta.previousClose || meta.chartPreviousClose || currentPrice;\n                            // Calculate change and change percent\n                            const change = meta.regularMarketChange || currentPrice - previousClose;\n                            const changePercent = meta.regularMarketChangePercent || (previousClose > 0 ? change / previousClose * 100 : 0);\n                            if (currentPrice > 0) {\n                                // Get 52-week high/low dates by analyzing historical data\n                                let high52WeekDate;\n                                let low52WeekDate;\n                                try {\n                                    // Get 1-year historical data to find exact dates\n                                    const historicalResponse = await axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(`${YAHOO_CHART_URL}/${symbol}`, {\n                                        params: {\n                                            interval: '1d',\n                                            range: '1y',\n                                            includePrePost: false\n                                        },\n                                        headers: {\n                                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n                                            'Accept': 'application/json',\n                                            'Referer': 'https://finance.yahoo.com/'\n                                        },\n                                        timeout: 5000\n                                    });\n                                    const historicalData = historicalResponse.data;\n                                    if (historicalData.chart?.result?.[0]) {\n                                        const historicalResult = historicalData.chart.result[0];\n                                        const timestamps = historicalResult.timestamp;\n                                        const historicalQuote = historicalResult.indicators?.quote?.[0];\n                                        if (timestamps && historicalQuote?.high && historicalQuote?.low) {\n                                            const highs = historicalQuote.high;\n                                            const lows = historicalQuote.low;\n                                            // Find 52-week high and low with their dates\n                                            let maxHigh = -Infinity;\n                                            let minLow = Infinity;\n                                            let maxHighIndex = -1;\n                                            let minLowIndex = -1;\n                                            for(let i = 0; i < highs.length; i++){\n                                                if (highs[i] && highs[i] > maxHigh) {\n                                                    maxHigh = highs[i];\n                                                    maxHighIndex = i;\n                                                }\n                                                if (lows[i] && lows[i] < minLow) {\n                                                    minLow = lows[i];\n                                                    minLowIndex = i;\n                                                }\n                                            }\n                                            if (maxHighIndex >= 0 && minLowIndex >= 0) {\n                                                high52WeekDate = new Date(timestamps[maxHighIndex] * 1000).toISOString().split('T')[0];\n                                                low52WeekDate = new Date(timestamps[minLowIndex] * 1000).toISOString().split('T')[0];\n                                            }\n                                        }\n                                    }\n                                } catch (historicalError) {\n                                    console.log(`⚠️ Could not fetch historical data for ${symbol} dates`);\n                                }\n                                stockQuote = {\n                                    symbol: symbol,\n                                    name: meta.longName || meta.shortName || symbol.replace('.NS', ''),\n                                    price: parseFloat(currentPrice.toString()),\n                                    change: parseFloat(change.toString()),\n                                    changePercent: parseFloat(changePercent.toString()),\n                                    volume: parseInt((meta.regularMarketVolume || quote?.volume?.[quote.volume.length - 1] || 0).toString()),\n                                    marketCap: meta.marketCap,\n                                    high52Week: parseFloat((meta.fiftyTwoWeekHigh || 0).toString()),\n                                    low52Week: parseFloat((meta.fiftyTwoWeekLow || 0).toString()),\n                                    high52WeekDate,\n                                    low52WeekDate,\n                                    avgVolume: parseInt((meta.averageDailyVolume3Month || 0).toString())\n                                };\n                                console.log(`✅ Chart API success for ${symbol}: ₹${stockQuote.price}`);\n                            }\n                        }\n                    } catch (chartError) {\n                        const errorMsg = chartError.response?.data?.chart?.error?.description || chartError.message;\n                        if (errorMsg?.includes('delisted')) {\n                            console.log(`🚫 ${symbol} is delisted: ${errorMsg}`);\n                        } else {\n                            console.log(`⚠️ Chart API failed for ${symbol}: ${errorMsg}, trying quote API...`);\n                        }\n                    }\n                    // Approach 2: Quote endpoint (fallback)\n                    if (!stockQuote) {\n                        try {\n                            const response = await axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(YAHOO_QUOTE_URL, {\n                                params: {\n                                    symbols: symbol,\n                                    formatted: true\n                                },\n                                headers: {\n                                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n                                    'Accept': 'application/json',\n                                    'Referer': 'https://finance.yahoo.com/'\n                                },\n                                timeout: 8000\n                            });\n                            const data = response.data;\n                            const result = data.quoteResponse?.result?.[0];\n                            if (result && result.regularMarketPrice > 0) {\n                                stockQuote = {\n                                    symbol: symbol,\n                                    name: result.longName || result.shortName || symbol.replace('.NS', ''),\n                                    price: parseFloat(result.regularMarketPrice) || 0,\n                                    change: parseFloat(result.regularMarketChange) || 0,\n                                    changePercent: parseFloat(result.regularMarketChangePercent) || 0,\n                                    volume: parseInt(result.regularMarketVolume) || 0,\n                                    marketCap: result.marketCap,\n                                    high52Week: parseFloat(result.fiftyTwoWeekHigh) || 0,\n                                    low52Week: parseFloat(result.fiftyTwoWeekLow) || 0,\n                                    avgVolume: parseInt(result.averageDailyVolume3Month) || 0\n                                };\n                                console.log(`✅ Quote API success for ${symbol}: ₹${stockQuote.price}`);\n                            }\n                        } catch (quoteError) {\n                            console.log(`⚠️ Quote API also failed for ${symbol}`);\n                        }\n                    }\n                    // If we got valid data, add it to results\n                    if (stockQuote && stockQuote.price > 0) {\n                        allResults.push(stockQuote);\n                    } else {\n                        console.warn(`⚠️ All methods failed for ${symbol} - creating fallback entry`);\n                        // Create a fallback entry instead of skipping\n                        allResults.push({\n                            symbol: symbol,\n                            name: symbol.replace('.NS', ''),\n                            price: 0,\n                            change: 0,\n                            changePercent: 0,\n                            volume: 0,\n                            high52Week: 0,\n                            low52Week: 0,\n                            avgVolume: 0\n                        });\n                    }\n                    // Small delay to avoid rate limiting\n                    if (i < formattedSymbols.length - 1) {\n                        await new Promise((resolve)=>setTimeout(resolve, 150));\n                    }\n                } catch (symbolError) {\n                    console.warn(`⚠️ Critical error fetching ${symbol}:`, symbolError.message);\n                    // Create a fallback entry instead of skipping\n                    allResults.push({\n                        symbol: symbol,\n                        name: symbol.replace('.NS', ''),\n                        price: 0,\n                        change: 0,\n                        changePercent: 0,\n                        volume: 0,\n                        high52Week: 0,\n                        low52Week: 0,\n                        avgVolume: 0\n                    });\n                }\n            }\n            // Check if we have a reasonable success rate\n            const successRate = allResults.length / formattedSymbols.length;\n            console.log(`📊 Success rate: ${(successRate * 100).toFixed(1)}% (${allResults.length}/${formattedSymbols.length})`);\n            // If success rate is too low, try batch processing for remaining symbols\n            if (successRate < 0.8 && allResults.length < formattedSymbols.length) {\n                console.log(`⚠️ Low success rate, trying batch processing for remaining symbols...`);\n                const fetchedSymbols = new Set(allResults.map((r)=>r.symbol));\n                const remainingSymbols = formattedSymbols.filter((s)=>!fetchedSymbols.has(s));\n                if (remainingSymbols.length > 0) {\n                    try {\n                        // Try batch processing with smaller batches\n                        const batchSize = 5;\n                        for(let i = 0; i < remainingSymbols.length; i += batchSize){\n                            const batch = remainingSymbols.slice(i, i + batchSize);\n                            try {\n                                const response = await axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(YAHOO_QUOTE_URL, {\n                                    params: {\n                                        symbols: batch.join(','),\n                                        formatted: true\n                                    },\n                                    headers: {\n                                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n                                        'Accept': 'application/json',\n                                        'Referer': 'https://finance.yahoo.com/'\n                                    },\n                                    timeout: 10000\n                                });\n                                const data = response.data;\n                                const results = data.quoteResponse?.result || [];\n                                for (const result of results){\n                                    if (result && result.regularMarketPrice > 0) {\n                                        const batchQuote = {\n                                            symbol: result.symbol,\n                                            name: result.longName || result.shortName || result.symbol.replace('.NS', ''),\n                                            price: parseFloat(result.regularMarketPrice) || 0,\n                                            change: parseFloat(result.regularMarketChange) || 0,\n                                            changePercent: parseFloat(result.regularMarketChangePercent) || 0,\n                                            volume: parseInt(result.regularMarketVolume) || 0,\n                                            marketCap: result.marketCap,\n                                            high52Week: parseFloat(result.fiftyTwoWeekHigh) || 0,\n                                            low52Week: parseFloat(result.fiftyTwoWeekLow) || 0,\n                                            avgVolume: parseInt(result.averageDailyVolume3Month) || 0\n                                        };\n                                        allResults.push(batchQuote);\n                                        console.log(`✅ Batch recovery success for ${result.symbol}: ₹${batchQuote.price}`);\n                                    }\n                                }\n                                // Delay between batches\n                                await new Promise((resolve)=>setTimeout(resolve, 200));\n                            } catch (batchError) {\n                                console.error(`❌ Batch processing failed for batch:`, batch);\n                            }\n                        }\n                    } catch (error) {\n                        console.error(`❌ Batch recovery failed:`, error);\n                    }\n                }\n            }\n            console.log(`🎉 Final results: ${allResults.length} quotes fetched out of ${formattedSymbols.length} requested`);\n            console.log(`📊 Sample results:`, allResults.slice(0, 3).map((r)=>({\n                    symbol: r.symbol,\n                    price: r.price,\n                    name: r.name\n                })));\n            // Cache the results\n            const cacheKey = _cache_service__WEBPACK_IMPORTED_MODULE_0__.CacheKeys.yahooQuotes(symbols);\n            _cache_service__WEBPACK_IMPORTED_MODULE_0__.cacheService.set(cacheKey, allResults);\n            return allResults;\n        } catch (error) {\n            console.error('❌ Critical error in getMultipleQuotes:', error);\n            // Return fallback quotes for all symbols\n            return symbols.map((symbol)=>({\n                    symbol: symbol.includes('.') ? symbol : `${symbol}.NS`,\n                    name: symbol,\n                    price: 0,\n                    change: 0,\n                    changePercent: 0,\n                    volume: 0,\n                    marketCap: undefined,\n                    high52Week: 0,\n                    low52Week: 0,\n                    avgVolume: 0\n                }));\n        }\n    }\n    async searchStocks(query) {\n        try {\n            const data = await this.makeRequest(YAHOO_SEARCH_URL, {\n                q: query,\n                quotesCount: 10,\n                newsCount: 0\n            });\n            const quotes = data.quotes || [];\n            return quotes.filter((quote)=>quote.isYahooFinance && quote.symbol).map((quote)=>({\n                    symbol: quote.symbol,\n                    name: quote.longname || quote.shortname || quote.symbol,\n                    exchange: quote.exchange || 'NSE',\n                    type: quote.quoteType || 'EQUITY'\n                }));\n        } catch (error) {\n            console.error('Error searching stocks:', error);\n            return [];\n        }\n    }\n    // Helper method to get Indian stock symbols\n    getIndianStockSymbol(symbol) {\n        return symbol.includes('.') ? symbol : `${symbol}.NS`;\n    }\n    // Helper method to format Indian stock symbols for display\n    formatSymbolForDisplay(symbol) {\n        return symbol.replace('.NS', '').replace('.BO', '');\n    }\n    // Get historical data for a symbol using existing quote data\n    async getHistoricalData(symbol, days = 7) {\n        try {\n            console.log(`📊 Getting historical data for ${symbol} (${days} days)`);\n            // For now, use current quote data and simulate historical data\n            // This is a fallback approach since Yahoo Finance historical API is complex\n            const currentQuote = await this.getQuoteWithCachedName(symbol);\n            if (!currentQuote) {\n                console.warn(`No current quote found for ${symbol}`);\n                return null;\n            }\n            // Generate simulated historical data based on current price\n            // This is a simplified approach for weekly high calculation\n            const historicalData = [];\n            const basePrice = currentQuote.price;\n            const baseVolume = currentQuote.volume || 1000000;\n            for(let i = days - 1; i >= 0; i--){\n                const date = new Date();\n                date.setDate(date.getDate() - i);\n                // Simulate price variation (±5% from current price)\n                const variation = (Math.random() - 0.5) * 0.1; // ±5%\n                const dayPrice = basePrice * (1 + variation);\n                // Simulate intraday high/low (±2% from day price)\n                const intraVariation = Math.random() * 0.04; // 0-4%\n                const high = dayPrice * (1 + intraVariation);\n                const low = dayPrice * (1 - intraVariation);\n                // Simulate volume variation\n                const volumeVariation = (Math.random() - 0.5) * 0.4; // ±20%\n                const volume = Math.floor(baseVolume * (1 + volumeVariation));\n                historicalData.push({\n                    date,\n                    open: dayPrice,\n                    high: Math.max(high, dayPrice),\n                    low: Math.min(low, dayPrice),\n                    close: dayPrice,\n                    volume: Math.max(volume, 100000) // Minimum volume\n                });\n            }\n            // Ensure the most recent day has the current price as high\n            if (historicalData.length > 0) {\n                const lastDay = historicalData[historicalData.length - 1];\n                lastDay.high = Math.max(lastDay.high, currentQuote.price);\n                lastDay.close = currentQuote.price;\n                lastDay.volume = currentQuote.volume || lastDay.volume;\n            }\n            console.log(`✅ Generated ${historicalData.length} days of historical data for ${symbol}`);\n            return historicalData;\n        } catch (error) {\n            console.error(`❌ Error generating historical data for ${symbol}:`, error);\n            return null;\n        }\n    }\n    constructor(){\n        // Real-time update system\n        this.updateListeners = new Set();\n        this.isRealTimeActive = false;\n        this.realTimeInterval = null;\n        this.lastUpdateTime = 0;\n        this.currentSymbols = [];\n        this.cache = new Map();\n        this.CACHE_DURATION = 30 * 1000; // 30 seconds\n    }\n}\nconst yahooFinanceService = new YahooFinanceService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/yahoo-finance.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstocks%2Fnifty200%2Froute&page=%2Fapi%2Fstocks%2Fnifty200%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstocks%2Fnifty200%2Froute.ts&appDir=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cniveshtor-trading%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cniveshtor-trading&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();