{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/lib/cache-service.ts"], "sourcesContent": ["// Cache service for optimizing data fetching and reducing API calls\n\ninterface CacheEntry<T> {\n  data: T;\n  timestamp: number;\n  expiresAt: number;\n}\n\nclass CacheService {\n  private cache = new Map<string, CacheEntry<any>>();\n  private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes\n  private readonly STOCK_DATA_TTL = 30 * 1000; // 30 seconds for stock data\n  private readonly STOCK_NAMES_TTL = 24 * 60 * 60 * 1000; // 24 hours for stock names\n  private readonly BROKER_DATA_TTL = 10 * 1000; // 10 seconds for broker data\n  private readonly PORTFOLIO_DATA_TTL = 60 * 1000; // 1 minute for portfolio data\n\n  // Get TTL based on data type\n  private getTTL(key: string): number {\n    if (key.includes('stock-name') || key.includes('names-map')) {\n      return this.STOCK_NAMES_TTL;\n    }\n    if (key.includes('stock') || key.includes('nifty')) {\n      return this.STOCK_DATA_TTL;\n    }\n    if (key.includes('broker') || key.includes('balance')) {\n      return this.BROKER_DATA_TTL;\n    }\n    if (key.includes('portfolio') || key.includes('holdings')) {\n      return this.PORTFOLIO_DATA_TTL;\n    }\n    return this.DEFAULT_TTL;\n  }\n\n  // Set cache entry\n  set<T>(key: string, data: T, customTTL?: number): void {\n    const ttl = customTTL || this.getTTL(key);\n    const now = Date.now();\n    \n    this.cache.set(key, {\n      data,\n      timestamp: now,\n      expiresAt: now + ttl\n    });\n  }\n\n  // Get cache entry\n  get<T>(key: string): T | null {\n    const entry = this.cache.get(key);\n    \n    if (!entry) {\n      return null;\n    }\n\n    // Check if expired\n    if (Date.now() > entry.expiresAt) {\n      this.cache.delete(key);\n      return null;\n    }\n\n    return entry.data as T;\n  }\n\n  // Check if key exists and is valid\n  has(key: string): boolean {\n    const entry = this.cache.get(key);\n    \n    if (!entry) {\n      return false;\n    }\n\n    // Check if expired\n    if (Date.now() > entry.expiresAt) {\n      this.cache.delete(key);\n      return false;\n    }\n\n    return true;\n  }\n\n  // Clear specific key\n  delete(key: string): void {\n    this.cache.delete(key);\n  }\n\n  // Clear all cache\n  clear(): void {\n    this.cache.clear();\n  }\n\n  // Clear expired entries\n  cleanup(): void {\n    const now = Date.now();\n    \n    for (const [key, entry] of this.cache.entries()) {\n      if (now > entry.expiresAt) {\n        this.cache.delete(key);\n      }\n    }\n  }\n\n  // Get cache stats\n  getStats(): {\n    size: number;\n    keys: string[];\n    hitRate: number;\n  } {\n    return {\n      size: this.cache.size,\n      keys: Array.from(this.cache.keys()),\n      hitRate: 0 // TODO: Implement hit rate tracking\n    };\n  }\n\n  // Cached fetch wrapper\n  async cachedFetch<T>(\n    key: string,\n    fetchFn: () => Promise<T>,\n    customTTL?: number\n  ): Promise<T> {\n    // Try to get from cache first\n    const cached = this.get<T>(key);\n    if (cached !== null) {\n      return cached;\n    }\n\n    // Fetch fresh data\n    try {\n      const data = await fetchFn();\n      this.set(key, data, customTTL);\n      return data;\n    } catch (error) {\n      // If fetch fails, try to return stale data if available\n      const staleEntry = this.cache.get(key);\n      if (staleEntry) {\n        console.warn(`Using stale data for ${key} due to fetch error:`, error);\n        return staleEntry.data as T;\n      }\n      throw error;\n    }\n  }\n\n  // Prefetch data in background\n  async prefetch<T>(\n    key: string,\n    fetchFn: () => Promise<T>,\n    customTTL?: number\n  ): Promise<void> {\n    // Only prefetch if not already cached\n    if (!this.has(key)) {\n      try {\n        const data = await fetchFn();\n        this.set(key, data, customTTL);\n      } catch (error) {\n        console.warn(`Prefetch failed for ${key}:`, error);\n      }\n    }\n  }\n\n  // Batch fetch with caching\n  async batchFetch<T>(\n    requests: Array<{\n      key: string;\n      fetchFn: () => Promise<T>;\n      ttl?: number;\n    }>\n  ): Promise<T[]> {\n    const results: T[] = [];\n    const fetchPromises: Promise<T>[] = [];\n\n    for (const request of requests) {\n      const cached = this.get<T>(request.key);\n      if (cached !== null) {\n        results.push(cached);\n      } else {\n        fetchPromises.push(\n          request.fetchFn().then(data => {\n            this.set(request.key, data, request.ttl);\n            return data;\n          })\n        );\n      }\n    }\n\n    // Wait for all fetches to complete\n    const fetchedResults = await Promise.all(fetchPromises);\n    results.push(...fetchedResults);\n\n    return results;\n  }\n\n  // Invalidate cache by pattern\n  invalidatePattern(pattern: string): void {\n    const regex = new RegExp(pattern);\n    \n    for (const key of this.cache.keys()) {\n      if (regex.test(key)) {\n        this.cache.delete(key);\n      }\n    }\n  }\n\n  // Auto cleanup interval\n  startAutoCleanup(intervalMs: number = 5 * 60 * 1000): void {\n    setInterval(() => {\n      this.cleanup();\n    }, intervalMs);\n  }\n}\n\n// Create singleton instance\nexport const cacheService = new CacheService();\n\n// Start auto cleanup\nif (typeof window !== 'undefined') {\n  cacheService.startAutoCleanup();\n}\n\n// Cache key generators\nexport const CacheKeys = {\n  brokerBalance: (userId: string) => `broker-balance-${userId}`,\n  fundAllocation: (userId: string, strategy: string) => `fund-allocation-${userId}-${strategy}`,\n  portfolioSummary: (userId: string) => `portfolio-summary-${userId}`,\n  niftyStocks: (batchIndex: number) => `nifty-stocks-batch-${batchIndex}`,\n  stockQuote: (symbol: string) => `stock-quote-${symbol}`,\n  stockName: (symbol: string) => `stock-name-${symbol}`,\n  stockNamesMap: () => 'stock-names-map',\n  stockPriceData: (symbol: string) => `stock-price-data-${symbol}`,\n  stockSearch: (query: string) => `stock-search-${query}`,\n  yahooQuotes: (symbols: string[]) => `yahoo-quotes-${symbols.sort().join(',')}`,\n};\n"], "names": [], "mappings": "AAAA,oEAAoE;;;;;AAQpE,MAAM;IACI,QAAQ,IAAI,MAA+B;IAClC,cAAc,IAAI,KAAK,KAAK;IAC5B,iBAAiB,KAAK,KAAK;IAC3B,kBAAkB,KAAK,KAAK,KAAK,KAAK;IACtC,kBAAkB,KAAK,KAAK;IAC5B,qBAAqB,KAAK,KAAK;IAEhD,6BAA6B;IACrB,OAAO,GAAW,EAAU;QAClC,IAAI,IAAI,QAAQ,CAAC,iBAAiB,IAAI,QAAQ,CAAC,cAAc;YAC3D,OAAO,IAAI,CAAC,eAAe;QAC7B;QACA,IAAI,IAAI,QAAQ,CAAC,YAAY,IAAI,QAAQ,CAAC,UAAU;YAClD,OAAO,IAAI,CAAC,cAAc;QAC5B;QACA,IAAI,IAAI,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,YAAY;YACrD,OAAO,IAAI,CAAC,eAAe;QAC7B;QACA,IAAI,IAAI,QAAQ,CAAC,gBAAgB,IAAI,QAAQ,CAAC,aAAa;YACzD,OAAO,IAAI,CAAC,kBAAkB;QAChC;QACA,OAAO,IAAI,CAAC,WAAW;IACzB;IAEA,kBAAkB;IAClB,IAAO,GAAW,EAAE,IAAO,EAAE,SAAkB,EAAQ;QACrD,MAAM,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC;QACrC,MAAM,MAAM,KAAK,GAAG;QAEpB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;YAClB;YACA,WAAW;YACX,WAAW,MAAM;QACnB;IACF;IAEA,kBAAkB;IAClB,IAAO,GAAW,EAAY;QAC5B,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAE7B,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,mBAAmB;QACnB,IAAI,KAAK,GAAG,KAAK,MAAM,SAAS,EAAE;YAChC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YAClB,OAAO;QACT;QAEA,OAAO,MAAM,IAAI;IACnB;IAEA,mCAAmC;IACnC,IAAI,GAAW,EAAW;QACxB,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAE7B,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,mBAAmB;QACnB,IAAI,KAAK,GAAG,KAAK,MAAM,SAAS,EAAE;YAChC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YAClB,OAAO;QACT;QAEA,OAAO;IACT;IAEA,qBAAqB;IACrB,OAAO,GAAW,EAAQ;QACxB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IACpB;IAEA,kBAAkB;IAClB,QAAc;QACZ,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;IAEA,wBAAwB;IACxB,UAAgB;QACd,MAAM,MAAM,KAAK,GAAG;QAEpB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,GAAI;YAC/C,IAAI,MAAM,MAAM,SAAS,EAAE;gBACzB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YACpB;QACF;IACF;IAEA,kBAAkB;IAClB,WAIE;QACA,OAAO;YACL,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;YAChC,SAAS,EAAE,oCAAoC;QACjD;IACF;IAEA,uBAAuB;IACvB,MAAM,YACJ,GAAW,EACX,OAAyB,EACzB,SAAkB,EACN;QACZ,8BAA8B;QAC9B,MAAM,SAAS,IAAI,CAAC,GAAG,CAAI;QAC3B,IAAI,WAAW,MAAM;YACnB,OAAO;QACT;QAEA,mBAAmB;QACnB,IAAI;YACF,MAAM,OAAO,MAAM;YACnB,IAAI,CAAC,GAAG,CAAC,KAAK,MAAM;YACpB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,wDAAwD;YACxD,MAAM,aAAa,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;YAClC,IAAI,YAAY;gBACd,QAAQ,IAAI,CAAC,CAAC,qBAAqB,EAAE,IAAI,oBAAoB,CAAC,EAAE;gBAChE,OAAO,WAAW,IAAI;YACxB;YACA,MAAM;QACR;IACF;IAEA,8BAA8B;IAC9B,MAAM,SACJ,GAAW,EACX,OAAyB,EACzB,SAAkB,EACH;QACf,sCAAsC;QACtC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM;YAClB,IAAI;gBACF,MAAM,OAAO,MAAM;gBACnB,IAAI,CAAC,GAAG,CAAC,KAAK,MAAM;YACtB,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC,EAAE;YAC9C;QACF;IACF;IAEA,2BAA2B;IAC3B,MAAM,WACJ,QAIE,EACY;QACd,MAAM,UAAe,EAAE;QACvB,MAAM,gBAA8B,EAAE;QAEtC,KAAK,MAAM,WAAW,SAAU;YAC9B,MAAM,SAAS,IAAI,CAAC,GAAG,CAAI,QAAQ,GAAG;YACtC,IAAI,WAAW,MAAM;gBACnB,QAAQ,IAAI,CAAC;YACf,OAAO;gBACL,cAAc,IAAI,CAChB,QAAQ,OAAO,GAAG,IAAI,CAAC,CAAA;oBACrB,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,EAAE,MAAM,QAAQ,GAAG;oBACvC,OAAO;gBACT;YAEJ;QACF;QAEA,mCAAmC;QACnC,MAAM,iBAAiB,MAAM,QAAQ,GAAG,CAAC;QACzC,QAAQ,IAAI,IAAI;QAEhB,OAAO;IACT;IAEA,8BAA8B;IAC9B,kBAAkB,OAAe,EAAQ;QACvC,MAAM,QAAQ,IAAI,OAAO;QAEzB,KAAK,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,GAAI;YACnC,IAAI,MAAM,IAAI,CAAC,MAAM;gBACnB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YACpB;QACF;IACF;IAEA,wBAAwB;IACxB,iBAAiB,aAAqB,IAAI,KAAK,IAAI,EAAQ;QACzD,YAAY;YACV,IAAI,CAAC,OAAO;QACd,GAAG;IACL;AACF;AAGO,MAAM,eAAe,IAAI;AAEhC,qBAAqB;AACrB;;AAKO,MAAM,YAAY;IACvB,eAAe,CAAC,SAAmB,CAAC,eAAe,EAAE,QAAQ;IAC7D,gBAAgB,CAAC,QAAgB,WAAqB,CAAC,gBAAgB,EAAE,OAAO,CAAC,EAAE,UAAU;IAC7F,kBAAkB,CAAC,SAAmB,CAAC,kBAAkB,EAAE,QAAQ;IACnE,aAAa,CAAC,aAAuB,CAAC,mBAAmB,EAAE,YAAY;IACvE,YAAY,CAAC,SAAmB,CAAC,YAAY,EAAE,QAAQ;IACvD,WAAW,CAAC,SAAmB,CAAC,WAAW,EAAE,QAAQ;IACrD,eAAe,IAAM;IACrB,gBAAgB,CAAC,SAAmB,CAAC,iBAAiB,EAAE,QAAQ;IAChE,aAAa,CAAC,QAAkB,CAAC,aAAa,EAAE,OAAO;IACvD,aAAa,CAAC,UAAsB,CAAC,aAAa,EAAE,QAAQ,IAAI,GAAG,IAAI,CAAC,MAAM;AAChF", "debugId": null}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/lib/stock-names-service.ts"], "sourcesContent": ["// Stock names service for caching and managing stock company names\nimport { cacheService, CacheKeys } from './cache-service';\n\ninterface StockNameEntry {\n  symbol: string;\n  name: string;\n  lastUpdated: number;\n}\n\nclass StockNamesService {\n  private readonly BATCH_SIZE = 50;\n  private readonly MAX_RETRIES = 3;\n\n  // Get stock name from cache or fetch if not available\n  async getStockName(symbol: string): Promise<string> {\n    // Try to get from cache first\n    const cached = cacheService.get<string>(CacheKeys.stockName(symbol));\n    if (cached) {\n      return cached;\n    }\n\n    // If not in cache, fetch from Yahoo Finance\n    try {\n      const { yahooFinanceService } = await import('./yahoo-finance');\n      const quote = await yahooFinanceService.getQuote(symbol);\n      const name = quote?.name || symbol.replace('.NS', '');\n\n      // Cache the name for 24 hours\n      cacheService.set(CacheKeys.stockName(symbol), name);\n      return name;\n    } catch (error) {\n      console.warn(`Failed to fetch name for ${symbol}:`, error);\n      // Return symbol without .NS as fallback\n      return symbol.replace('.NS', '');\n    }\n  }\n\n  // Get multiple stock names efficiently\n  async getStockNames(symbols: string[]): Promise<Map<string, string>> {\n    const namesMap = new Map<string, string>();\n    const uncachedSymbols: string[] = [];\n\n    // Check cache for each symbol\n    for (const symbol of symbols) {\n      const cached = cacheService.get<string>(CacheKeys.stockName(symbol));\n      if (cached) {\n        namesMap.set(symbol, cached);\n      } else {\n        uncachedSymbols.push(symbol);\n      }\n    }\n\n    // If all names are cached, return immediately\n    if (uncachedSymbols.length === 0) {\n      return namesMap;\n    }\n\n    console.log(`📝 Fetching names for ${uncachedSymbols.length} uncached stocks`);\n\n    // Fetch uncached names in batches\n    const batches = this.chunkArray(uncachedSymbols, this.BATCH_SIZE);\n    \n    for (const batch of batches) {\n      try {\n        const { yahooFinanceService } = await import('./yahoo-finance');\n        const quotes = await yahooFinanceService.getMultipleQuotes(batch);\n        \n        for (const quote of quotes) {\n          if (quote && quote.symbol) {\n            // Use the name from the quote, or fallback to symbol without .NS\n            const name = quote.name || quote.symbol.replace('.NS', '');\n            namesMap.set(quote.symbol, name);\n\n            // Cache the name for 24 hours\n            cacheService.set(CacheKeys.stockName(quote.symbol), name);\n          }\n        }\n\n        // Ensure all symbols in the batch have names (even if fallback)\n        for (const symbol of batch) {\n          if (!namesMap.has(symbol)) {\n            const fallbackName = symbol.replace('.NS', '');\n            namesMap.set(symbol, fallbackName);\n            cacheService.set(CacheKeys.stockName(symbol), fallbackName);\n            console.log(`📝 Using fallback name for ${symbol}: ${fallbackName}`);\n          }\n        }\n        \n        // Add delay between batches to avoid rate limiting\n        if (batches.indexOf(batch) < batches.length - 1) {\n          await new Promise(resolve => setTimeout(resolve, 100));\n        }\n      } catch (error) {\n        console.warn(`Failed to fetch names for batch:`, error);\n        \n        // Add fallback names for failed batch\n        for (const symbol of batch) {\n          if (!namesMap.has(symbol)) {\n            const fallbackName = symbol.replace('.NS', '');\n            namesMap.set(symbol, fallbackName);\n            cacheService.set(CacheKeys.stockName(symbol), fallbackName);\n          }\n        }\n      }\n    }\n\n    return namesMap;\n  }\n\n  // Get all cached stock names\n  getCachedStockNames(): Map<string, string> {\n    const cachedMap = cacheService.get<Map<string, string>>(CacheKeys.stockNamesMap());\n    return cachedMap || new Map();\n  }\n\n  // Cache stock names map for quick access\n  cacheStockNamesMap(namesMap: Map<string, string>): void {\n    cacheService.set(CacheKeys.stockNamesMap(), namesMap);\n  }\n\n  // Preload stock names for given symbols\n  async preloadStockNames(symbols: string[]): Promise<void> {\n    console.log(`🚀 Preloading names for ${symbols.length} stocks`);\n    \n    try {\n      const namesMap = await this.getStockNames(symbols);\n      this.cacheStockNamesMap(namesMap);\n      console.log(`✅ Preloaded ${namesMap.size} stock names`);\n    } catch (error) {\n      console.error('Failed to preload stock names:', error);\n    }\n  }\n\n  // Check if stock name is cached\n  isNameCached(symbol: string): boolean {\n    return cacheService.has(CacheKeys.stockName(symbol));\n  }\n\n  // Get cache statistics for stock names\n  getNamesCacheStats(): {\n    cachedCount: number;\n    totalRequested: number;\n    hitRate: number;\n  } {\n    const stats = cacheService.getStats();\n    const nameKeys = stats.keys.filter(key => key.includes('stock-name'));\n    \n    return {\n      cachedCount: nameKeys.length,\n      totalRequested: nameKeys.length, // Simplified for now\n      hitRate: nameKeys.length > 0 ? 0.8 : 0 // Estimated hit rate\n    };\n  }\n\n  // Clear all cached stock names\n  clearNamesCache(): void {\n    cacheService.invalidatePattern('stock-name');\n    cacheService.delete(CacheKeys.stockNamesMap());\n    console.log('🗑️ Cleared all cached stock names');\n  }\n\n  // Refresh stock names (force re-fetch)\n  async refreshStockNames(symbols: string[]): Promise<Map<string, string>> {\n    // Clear existing cache for these symbols\n    for (const symbol of symbols) {\n      cacheService.delete(CacheKeys.stockName(symbol));\n    }\n    \n    // Fetch fresh names\n    return await this.getStockNames(symbols);\n  }\n\n  // Utility function to chunk array into smaller arrays\n  private chunkArray<T>(array: T[], chunkSize: number): T[][] {\n    const chunks: T[][] = [];\n    for (let i = 0; i < array.length; i += chunkSize) {\n      chunks.push(array.slice(i, i + chunkSize));\n    }\n    return chunks;\n  }\n\n  // Get stock name with fallback\n  getStockNameSync(symbol: string): string {\n    const cached = cacheService.get<string>(CacheKeys.stockName(symbol));\n    return cached || symbol.replace('.NS', '');\n  }\n\n  // Batch update stock names from quotes\n  updateStockNamesFromQuotes(quotes: any[]): void {\n    for (const quote of quotes) {\n      if (quote && quote.symbol && quote.name) {\n        cacheService.set(CacheKeys.stockName(quote.symbol), quote.name);\n      }\n    }\n  }\n\n  // Force refresh all stock names (clears cache and re-fetches)\n  async forceRefreshAllNames(symbols: string[]): Promise<void> {\n    console.log('🔄 Force refreshing all stock names...');\n    this.clearNamesCache();\n    await this.preloadStockNames(symbols);\n  }\n}\n\n// Create singleton instance\nexport const stockNamesService = new StockNamesService();\n\n// Export types\nexport type { StockNameEntry };\n"], "names": [], "mappings": "AAAA,mEAAmE;;;;AACnE;;AAQA,MAAM;IACa,aAAa,GAAG;IAChB,cAAc,EAAE;IAEjC,sDAAsD;IACtD,MAAM,aAAa,MAAc,EAAmB;QAClD,8BAA8B;QAC9B,MAAM,SAAS,gIAAA,CAAA,eAAY,CAAC,GAAG,CAAS,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC;QAC5D,IAAI,QAAQ;YACV,OAAO;QACT;QAEA,4CAA4C;QAC5C,IAAI;YACF,MAAM,EAAE,mBAAmB,EAAE,GAAG;YAChC,MAAM,QAAQ,MAAM,oBAAoB,QAAQ,CAAC;YACjD,MAAM,OAAO,OAAO,QAAQ,OAAO,OAAO,CAAC,OAAO;YAElD,8BAA8B;YAC9B,gIAAA,CAAA,eAAY,CAAC,GAAG,CAAC,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,SAAS;YAC9C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,CAAC,yBAAyB,EAAE,OAAO,CAAC,CAAC,EAAE;YACpD,wCAAwC;YACxC,OAAO,OAAO,OAAO,CAAC,OAAO;QAC/B;IACF;IAEA,uCAAuC;IACvC,MAAM,cAAc,OAAiB,EAAgC;QACnE,MAAM,WAAW,IAAI;QACrB,MAAM,kBAA4B,EAAE;QAEpC,8BAA8B;QAC9B,KAAK,MAAM,UAAU,QAAS;YAC5B,MAAM,SAAS,gIAAA,CAAA,eAAY,CAAC,GAAG,CAAS,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC;YAC5D,IAAI,QAAQ;gBACV,SAAS,GAAG,CAAC,QAAQ;YACvB,OAAO;gBACL,gBAAgB,IAAI,CAAC;YACvB;QACF;QAEA,8CAA8C;QAC9C,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,gBAAgB,MAAM,CAAC,gBAAgB,CAAC;QAE7E,kCAAkC;QAClC,MAAM,UAAU,IAAI,CAAC,UAAU,CAAC,iBAAiB,IAAI,CAAC,UAAU;QAEhE,KAAK,MAAM,SAAS,QAAS;YAC3B,IAAI;gBACF,MAAM,EAAE,mBAAmB,EAAE,GAAG;gBAChC,MAAM,SAAS,MAAM,oBAAoB,iBAAiB,CAAC;gBAE3D,KAAK,MAAM,SAAS,OAAQ;oBAC1B,IAAI,SAAS,MAAM,MAAM,EAAE;wBACzB,iEAAiE;wBACjE,MAAM,OAAO,MAAM,IAAI,IAAI,MAAM,MAAM,CAAC,OAAO,CAAC,OAAO;wBACvD,SAAS,GAAG,CAAC,MAAM,MAAM,EAAE;wBAE3B,8BAA8B;wBAC9B,gIAAA,CAAA,eAAY,CAAC,GAAG,CAAC,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,MAAM,MAAM,GAAG;oBACtD;gBACF;gBAEA,gEAAgE;gBAChE,KAAK,MAAM,UAAU,MAAO;oBAC1B,IAAI,CAAC,SAAS,GAAG,CAAC,SAAS;wBACzB,MAAM,eAAe,OAAO,OAAO,CAAC,OAAO;wBAC3C,SAAS,GAAG,CAAC,QAAQ;wBACrB,gIAAA,CAAA,eAAY,CAAC,GAAG,CAAC,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,SAAS;wBAC9C,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,OAAO,EAAE,EAAE,cAAc;oBACrE;gBACF;gBAEA,mDAAmD;gBACnD,IAAI,QAAQ,OAAO,CAAC,SAAS,QAAQ,MAAM,GAAG,GAAG;oBAC/C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACnD;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,CAAC,gCAAgC,CAAC,EAAE;gBAEjD,sCAAsC;gBACtC,KAAK,MAAM,UAAU,MAAO;oBAC1B,IAAI,CAAC,SAAS,GAAG,CAAC,SAAS;wBACzB,MAAM,eAAe,OAAO,OAAO,CAAC,OAAO;wBAC3C,SAAS,GAAG,CAAC,QAAQ;wBACrB,gIAAA,CAAA,eAAY,CAAC,GAAG,CAAC,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,SAAS;oBAChD;gBACF;YACF;QACF;QAEA,OAAO;IACT;IAEA,6BAA6B;IAC7B,sBAA2C;QACzC,MAAM,YAAY,gIAAA,CAAA,eAAY,CAAC,GAAG,CAAsB,gIAAA,CAAA,YAAS,CAAC,aAAa;QAC/E,OAAO,aAAa,IAAI;IAC1B;IAEA,yCAAyC;IACzC,mBAAmB,QAA6B,EAAQ;QACtD,gIAAA,CAAA,eAAY,CAAC,GAAG,CAAC,gIAAA,CAAA,YAAS,CAAC,aAAa,IAAI;IAC9C;IAEA,wCAAwC;IACxC,MAAM,kBAAkB,OAAiB,EAAiB;QACxD,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,QAAQ,MAAM,CAAC,OAAO,CAAC;QAE9D,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,aAAa,CAAC;YAC1C,IAAI,CAAC,kBAAkB,CAAC;YACxB,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,SAAS,IAAI,CAAC,YAAY,CAAC;QACxD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA,gCAAgC;IAChC,aAAa,MAAc,EAAW;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,GAAG,CAAC,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC;IAC9C;IAEA,uCAAuC;IACvC,qBAIE;QACA,MAAM,QAAQ,gIAAA,CAAA,eAAY,CAAC,QAAQ;QACnC,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,CAAC;QAEvD,OAAO;YACL,aAAa,SAAS,MAAM;YAC5B,gBAAgB,SAAS,MAAM;YAC/B,SAAS,SAAS,MAAM,GAAG,IAAI,MAAM,EAAE,qBAAqB;QAC9D;IACF;IAEA,+BAA+B;IAC/B,kBAAwB;QACtB,gIAAA,CAAA,eAAY,CAAC,iBAAiB,CAAC;QAC/B,gIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,gIAAA,CAAA,YAAS,CAAC,aAAa;QAC3C,QAAQ,GAAG,CAAC;IACd;IAEA,uCAAuC;IACvC,MAAM,kBAAkB,OAAiB,EAAgC;QACvE,yCAAyC;QACzC,KAAK,MAAM,UAAU,QAAS;YAC5B,gIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC;QAC1C;QAEA,oBAAoB;QACpB,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC;IAClC;IAEA,sDAAsD;IAC9C,WAAc,KAAU,EAAE,SAAiB,EAAS;QAC1D,MAAM,SAAgB,EAAE;QACxB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,KAAK,UAAW;YAChD,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,GAAG,IAAI;QACjC;QACA,OAAO;IACT;IAEA,+BAA+B;IAC/B,iBAAiB,MAAc,EAAU;QACvC,MAAM,SAAS,gIAAA,CAAA,eAAY,CAAC,GAAG,CAAS,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC;QAC5D,OAAO,UAAU,OAAO,OAAO,CAAC,OAAO;IACzC;IAEA,uCAAuC;IACvC,2BAA2B,MAAa,EAAQ;QAC9C,KAAK,MAAM,SAAS,OAAQ;YAC1B,IAAI,SAAS,MAAM,MAAM,IAAI,MAAM,IAAI,EAAE;gBACvC,gIAAA,CAAA,eAAY,CAAC,GAAG,CAAC,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,MAAM,MAAM,GAAG,MAAM,IAAI;YAChE;QACF;IACF;IAEA,8DAA8D;IAC9D,MAAM,qBAAqB,OAAiB,EAAiB;QAC3D,QAAQ,GAAG,CAAC;QACZ,IAAI,CAAC,eAAe;QACpB,MAAM,IAAI,CAAC,iBAAiB,CAAC;IAC/B;AACF;AAGO,MAAM,oBAAoB,IAAI", "debugId": null}}, {"offset": {"line": 521, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/lib/yahoo-finance.ts"], "sourcesContent": ["import axios from 'axios';\nimport { cacheService, CacheKeys } from './cache-service';\nimport { stockNamesService } from './stock-names-service';\n\n// Yahoo Finance API endpoints - using chart endpoint which is more reliable\nconst YAHOO_FINANCE_BASE_URL = 'https://query1.finance.yahoo.com/v8/finance/chart';\nconst YAHOO_SEARCH_URL = 'https://query1.finance.yahoo.com/v1/finance/search';\nconst YAHOO_QUOTE_URL = 'https://query1.finance.yahoo.com/v7/finance/quote';\nconst YAHOO_QUOTE_SUMMARY_URL = 'https://query2.finance.yahoo.com/v10/finance/quoteSummary';\n\n// Alternative endpoints for better reliability\nconst YAHOO_CHART_URL = 'https://query1.finance.yahoo.com/v8/finance/chart';\n\n// Known problematic stocks that often fail - handle with extra care\nconst PROBLEMATIC_STOCKS = new Set([\n  'BOSCHLTD.NS',\n  'BSOFT.NS',\n  'MINDTREE.NS', // Merged stock\n  'PVR.NS', // Merged stock\n  'HDFC.NS' // Merged stock\n]);\n\nexport interface StockQuote {\n  symbol: string;\n  name: string;\n  price: number;\n  change: number;\n  changePercent: number;\n  volume: number;\n  marketCap?: number;\n  high52Week?: number;\n  low52Week?: number;\n  high52WeekDate?: string;\n  low52WeekDate?: string;\n  avgVolume?: number;\n}\n\nexport interface HistoricalData {\n  date: Date;\n  open: number;\n  high: number;\n  low: number;\n  close: number;\n  volume: number;\n}\n\nexport interface SearchResult {\n  symbol: string;\n  name: string;\n  exchange: string;\n  type: string;\n}\n\nclass YahooFinanceService {\n  // Real-time update system\n  private updateListeners = new Set<(data: StockQuote[]) => void>();\n  private isRealTimeActive = false;\n  private realTimeInterval: NodeJS.Timeout | null = null;\n  private lastUpdateTime = 0;\n  private currentSymbols: string[] = [];\n  private cache = new Map<string, { data: any; timestamp: number }>();\n  private readonly CACHE_DURATION = 30 * 1000; // 30 seconds\n\n  // Start real-time updates for given symbols\n  startRealTimeUpdates(symbols: string[], callback: (data: StockQuote[]) => void) {\n    console.log(`🔄 Starting real-time updates for ${symbols.length} symbols`);\n\n    this.currentSymbols = symbols;\n    this.updateListeners.add(callback);\n\n    if (!this.isRealTimeActive) {\n      this.isRealTimeActive = true;\n      this.scheduleNextUpdate();\n    }\n  }\n\n  // Stop real-time updates for a specific callback\n  stopRealTimeUpdates(callback: (data: StockQuote[]) => void) {\n    this.updateListeners.delete(callback);\n\n    if (this.updateListeners.size === 0) {\n      this.isRealTimeActive = false;\n      if (this.realTimeInterval) {\n        clearTimeout(this.realTimeInterval);\n        this.realTimeInterval = null;\n      }\n      console.log('⏹️ Stopped real-time updates - no active listeners');\n    }\n  }\n\n  // Schedule the next update\n  private scheduleNextUpdate() {\n    if (!this.isRealTimeActive) return;\n\n    const now = Date.now();\n    const timeSinceLastUpdate = now - this.lastUpdateTime;\n    const timeUntilNextUpdate = Math.max(0, 30000 - timeSinceLastUpdate); // 30 seconds\n\n    this.realTimeInterval = setTimeout(() => {\n      this.performRealTimeUpdate();\n    }, timeUntilNextUpdate);\n  }\n\n  // Perform the actual real-time update\n  private async performRealTimeUpdate() {\n    if (!this.isRealTimeActive || this.currentSymbols.length === 0) return;\n\n    try {\n      console.log(`🔄 Performing real-time update for ${this.currentSymbols.length} symbols`);\n\n      const quotes = await this.getMultipleQuotesWithCachedNames(this.currentSymbols);\n      this.lastUpdateTime = Date.now();\n\n      // Notify all listeners\n      this.updateListeners.forEach(callback => {\n        try {\n          callback(quotes);\n        } catch (error) {\n          console.error('❌ Error in update listener:', error);\n        }\n      });\n\n      console.log(`✅ Real-time update completed: ${quotes.length} quotes updated`);\n\n    } catch (error) {\n      console.error('❌ Real-time update failed:', error);\n    }\n\n    // Schedule next update\n    this.scheduleNextUpdate();\n  }\n\n  private async makeRequest(url: string, params: any = {}) {\n    try {\n      console.log(`🌐 Making request to: ${url}`, params);\n\n      const response = await axios.get(url, {\n        params,\n        headers: {\n          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',\n          'Accept': 'application/json',\n          'Accept-Language': 'en-US,en;q=0.9',\n          'Cache-Control': 'no-cache',\n          'Pragma': 'no-cache',\n          'Referer': 'https://finance.yahoo.com/'\n        },\n        timeout: 15000\n      });\n\n      console.log(`✅ Request successful, status: ${response.status}`);\n      return response.data;\n    } catch (error: any) {\n      const errorDetails = {\n        url,\n        params,\n        message: error?.message || 'Unknown error',\n        status: error?.response?.status || 'No status',\n        data: error?.response?.data || 'No data'\n      };\n\n      console.error('❌ Yahoo Finance API error:', errorDetails);\n\n      // For historical data requests, return null instead of throwing\n      if (url.includes('/v8/finance/chart/')) {\n        console.warn(`⚠️ Historical data request failed, returning null`);\n        return null;\n      }\n\n      throw new Error(`Failed to fetch data from Yahoo Finance: ${error?.message || 'Unknown error'}`);\n    }\n  }\n\n  // Get price data only (without fetching names from API) - optimized for frequent updates\n  async getPriceDataOnly(symbol: string): Promise<Omit<StockQuote, 'name'> | null> {\n    try {\n      const response = await axios.get(`${YAHOO_CHART_URL}/${symbol}`, {\n        params: {\n          interval: '1d',\n          range: '1d',\n          includePrePost: false\n        },\n        headers: {\n          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n          'Accept': 'application/json',\n          'Referer': 'https://finance.yahoo.com/'\n        },\n        timeout: 8000\n      });\n\n      const data = response.data;\n      if (data.chart?.result?.[0]) {\n        const result = data.chart.result[0];\n        const meta = result.meta;\n        const quote = result.indicators?.quote?.[0];\n\n        const currentPrice = meta.regularMarketPrice ||\n                           (quote?.close && quote.close[quote.close.length - 1]) ||\n                           meta.previousClose ||\n                           0;\n\n        const previousClose = meta.previousClose || meta.chartPreviousClose || currentPrice;\n        const change = meta.regularMarketChange || (currentPrice - previousClose);\n        const changePercent = meta.regularMarketChangePercent ||\n                            (previousClose > 0 ? (change / previousClose) * 100 : 0);\n\n        if (currentPrice > 0) {\n          // Get 52-week high/low dates by analyzing historical data\n          let high52WeekDate: string | undefined;\n          let low52WeekDate: string | undefined;\n\n          try {\n            // Get 1-year historical data to find exact dates\n            const historicalResponse = await axios.get(`${YAHOO_CHART_URL}/${symbol}`, {\n              params: {\n                interval: '1d',\n                range: '1y',\n                includePrePost: false\n              },\n              headers: {\n                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n                'Accept': 'application/json',\n                'Referer': 'https://finance.yahoo.com/'\n              },\n              timeout: 10000\n            });\n\n            const historicalData = historicalResponse.data;\n            if (historicalData.chart?.result?.[0]) {\n              const historicalResult = historicalData.chart.result[0];\n              const timestamps = historicalResult.timestamp;\n              const historicalQuote = historicalResult.indicators?.quote?.[0];\n\n              if (timestamps && historicalQuote?.high && historicalQuote?.low) {\n                const highs = historicalQuote.high;\n                const lows = historicalQuote.low;\n\n                // Find 52-week high and low with their dates\n                let maxHigh = -Infinity;\n                let minLow = Infinity;\n                let maxHighIndex = -1;\n                let minLowIndex = -1;\n\n                for (let i = 0; i < highs.length; i++) {\n                  if (highs[i] && highs[i] > maxHigh) {\n                    maxHigh = highs[i];\n                    maxHighIndex = i;\n                  }\n                  if (lows[i] && lows[i] < minLow) {\n                    minLow = lows[i];\n                    minLowIndex = i;\n                  }\n                }\n\n                if (maxHighIndex >= 0 && minLowIndex >= 0) {\n                  high52WeekDate = new Date(timestamps[maxHighIndex] * 1000).toISOString().split('T')[0];\n                  low52WeekDate = new Date(timestamps[minLowIndex] * 1000).toISOString().split('T')[0];\n                }\n              }\n            }\n          } catch (historicalError: any) {\n            console.warn(`⚠️ Could not fetch historical data for ${symbol}:`, {\n              error: historicalError.message,\n              status: historicalError.response?.status,\n              timeout: historicalError.code === 'ECONNABORTED'\n            });\n          }\n\n          return {\n            symbol: symbol,\n            price: parseFloat(currentPrice.toString()),\n            change: parseFloat(change.toString()),\n            changePercent: parseFloat(changePercent.toString()),\n            volume: parseInt((meta.regularMarketVolume || quote?.volume?.[quote.volume.length - 1] || 0).toString()),\n            marketCap: meta.marketCap,\n            high52Week: parseFloat((meta.fiftyTwoWeekHigh || 0).toString()),\n            low52Week: parseFloat((meta.fiftyTwoWeekLow || 0).toString()),\n            high52WeekDate,\n            low52WeekDate,\n            avgVolume: parseInt((meta.averageDailyVolume3Month || 0).toString())\n          };\n        }\n      }\n\n      return null;\n    } catch (error) {\n      console.warn(`Failed to get price data for ${symbol}:`, error);\n      return null;\n    }\n  }\n\n  // Get multiple price data only (batch operation)\n  async getMultiplePriceDataOnly(symbols: string[]): Promise<Omit<StockQuote, 'name'>[]> {\n    const results: Omit<StockQuote, 'name'>[] = [];\n    const batchSize = 25;\n\n    for (let i = 0; i < symbols.length; i += batchSize) {\n      const batch = symbols.slice(i, i + batchSize);\n      const batchPromises = batch.map(symbol => this.getPriceDataOnly(symbol));\n\n      try {\n        const batchResults = await Promise.all(batchPromises);\n        results.push(...batchResults.filter(result => result !== null) as Omit<StockQuote, 'name'>[]);\n\n        // Add delay between batches\n        if (i + batchSize < symbols.length) {\n          await new Promise(resolve => setTimeout(resolve, 200));\n        }\n      } catch (error) {\n        console.error(`Batch error for symbols ${batch.join(', ')}:`, error);\n      }\n    }\n\n    return results;\n  }\n\n  // Get quote with cached name (optimized for frequent updates)\n  async getQuoteWithCachedName(symbol: string): Promise<StockQuote | null> {\n    try {\n      // Get price data only\n      const priceData = await this.getPriceDataOnly(symbol);\n      if (!priceData) return null;\n\n      // Get name from cache or use fallback\n      const name = stockNamesService.getStockNameSync(symbol);\n\n      return {\n        ...priceData,\n        name\n      };\n    } catch (error) {\n      console.warn(`Failed to get quote with cached name for ${symbol}:`, error);\n      return null;\n    }\n  }\n\n  // Get multiple quotes with cached names (batch operation)\n  async getMultipleQuotesWithCachedNames(symbols: string[]): Promise<StockQuote[]> {\n    try {\n      console.log(`📊 Fetching quotes with cached names for ${symbols.length} symbols`);\n\n      // Get price data for all symbols\n      const priceDataList = await this.getMultiplePriceDataOnly(symbols);\n      console.log(`💰 Got price data for ${priceDataList.length}/${symbols.length} symbols`);\n\n      // Get cached names for all symbols\n      const namesMap = await stockNamesService.getStockNames(symbols);\n      console.log(`📝 Got names for ${namesMap.size}/${symbols.length} symbols`);\n\n      // Combine price data with cached names\n      const quotes: StockQuote[] = [];\n      for (const priceData of priceDataList) {\n        const name = namesMap.get(priceData.symbol) || priceData.symbol.replace('.NS', '');\n        const quote: StockQuote = {\n          ...priceData,\n          name\n        };\n        quotes.push(quote);\n\n        // Log BOH eligibility data for debugging\n        if (priceData.high52WeekDate && priceData.low52WeekDate) {\n          const highDate = new Date(priceData.high52WeekDate);\n          const lowDate = new Date(priceData.low52WeekDate);\n          const isBOHEligible = lowDate > highDate;\n          console.log(`🔍 ${priceData.symbol}: High=${priceData.high52WeekDate}, Low=${priceData.low52WeekDate}, BOH=${isBOHEligible}`);\n        }\n      }\n\n      console.log(`✅ Combined ${quotes.length} quotes with cached names`);\n      return quotes;\n    } catch (error) {\n      console.error('❌ Failed to get multiple quotes with cached names:', error);\n      return [];\n    }\n  }\n\n  async getQuote(symbol: string): Promise<StockQuote | null> {\n    try {\n      // Add .NS suffix for NSE stocks if not present\n      const formattedSymbol = symbol.includes('.') ? symbol : `${symbol}.NS`;\n      \n      const data = await this.makeRequest(YAHOO_QUOTE_URL, {\n        symbols: formattedSymbol\n      });\n\n      const result = data.quoteResponse?.result?.[0];\n      if (!result) return null;\n\n      return {\n        symbol: result.symbol,\n        name: result.longName || result.shortName || symbol,\n        price: result.regularMarketPrice || 0,\n        change: result.regularMarketChange || 0,\n        changePercent: result.regularMarketChangePercent || 0,\n        volume: result.regularMarketVolume || 0,\n        marketCap: result.marketCap,\n        high52Week: result.fiftyTwoWeekHigh,\n        low52Week: result.fiftyTwoWeekLow,\n        avgVolume: result.averageDailyVolume3Month\n      };\n    } catch (error) {\n      console.error(`Error fetching quote for ${symbol}:`, error);\n      return null;\n    }\n  }\n\n  async getMultipleQuotes(symbols: string[]): Promise<StockQuote[]> {\n    console.log(`🔍 Yahoo Finance: Fetching quotes for ${symbols.length} symbols:`, symbols.slice(0, 5));\n\n    try {\n      // Format symbols for NSE - ensure .NS suffix\n      const formattedSymbols = symbols.map(symbol => {\n        const formatted = symbol.includes('.') ? symbol : `${symbol}.NS`;\n        return formatted;\n      });\n\n      console.log(`📝 Formatted symbols:`, formattedSymbols.slice(0, 5));\n\n      const allResults: StockQuote[] = [];\n\n      // Process each symbol individually using chart endpoint (more reliable)\n      for (let i = 0; i < formattedSymbols.length; i++) {\n        const symbol = formattedSymbols[i];\n\n        try {\n          console.log(`📊 Fetching data for ${symbol} (${i + 1}/${formattedSymbols.length})`);\n\n          let stockQuote: StockQuote | null = null;\n\n          // Try multiple approaches for better success rate\n\n          // Approach 1: Chart endpoint (most reliable)\n          try {\n            const response = await axios.get(`${YAHOO_CHART_URL}/${symbol}`, {\n              params: {\n                interval: '1d',\n                range: '1d',\n                includePrePost: false\n              },\n              headers: {\n                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',\n                'Accept': 'application/json',\n                'Accept-Language': 'en-US,en;q=0.9',\n                'Referer': 'https://finance.yahoo.com/'\n              },\n              timeout: 8000\n            });\n\n            const data = response.data;\n\n            if (data.chart?.result?.[0]) {\n              const result = data.chart.result[0];\n              const meta = result.meta;\n              const quote = result.indicators?.quote?.[0];\n\n              // Extract current price from meta or latest quote data\n              const currentPrice = meta.regularMarketPrice ||\n                                 (quote?.close && quote.close[quote.close.length - 1]) ||\n                                 meta.previousClose ||\n                                 0;\n\n              const previousClose = meta.previousClose || meta.chartPreviousClose || currentPrice;\n\n              // Calculate change and change percent\n              const change = meta.regularMarketChange || (currentPrice - previousClose);\n              const changePercent = meta.regularMarketChangePercent ||\n                                  (previousClose > 0 ? (change / previousClose) * 100 : 0);\n\n              if (currentPrice > 0) {\n                // Get 52-week high/low dates by analyzing historical data\n                let high52WeekDate: string | undefined;\n                let low52WeekDate: string | undefined;\n\n                try {\n                  // Get 1-year historical data to find exact dates\n                  const historicalResponse = await axios.get(`${YAHOO_CHART_URL}/${symbol}`, {\n                    params: {\n                      interval: '1d',\n                      range: '1y',\n                      includePrePost: false\n                    },\n                    headers: {\n                      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n                      'Accept': 'application/json',\n                      'Referer': 'https://finance.yahoo.com/'\n                    },\n                    timeout: 5000\n                  });\n\n                  const historicalData = historicalResponse.data;\n                  if (historicalData.chart?.result?.[0]) {\n                    const historicalResult = historicalData.chart.result[0];\n                    const timestamps = historicalResult.timestamp;\n                    const historicalQuote = historicalResult.indicators?.quote?.[0];\n\n                    if (timestamps && historicalQuote?.high && historicalQuote?.low) {\n                      const highs = historicalQuote.high;\n                      const lows = historicalQuote.low;\n\n                      // Find 52-week high and low with their dates\n                      let maxHigh = -Infinity;\n                      let minLow = Infinity;\n                      let maxHighIndex = -1;\n                      let minLowIndex = -1;\n\n                      for (let i = 0; i < highs.length; i++) {\n                        if (highs[i] && highs[i] > maxHigh) {\n                          maxHigh = highs[i];\n                          maxHighIndex = i;\n                        }\n                        if (lows[i] && lows[i] < minLow) {\n                          minLow = lows[i];\n                          minLowIndex = i;\n                        }\n                      }\n\n                      if (maxHighIndex >= 0 && minLowIndex >= 0) {\n                        high52WeekDate = new Date(timestamps[maxHighIndex] * 1000).toISOString().split('T')[0];\n                        low52WeekDate = new Date(timestamps[minLowIndex] * 1000).toISOString().split('T')[0];\n                      }\n                    }\n                  }\n                } catch (historicalError) {\n                  console.log(`⚠️ Could not fetch historical data for ${symbol} dates`);\n                }\n\n                stockQuote = {\n                  symbol: symbol,\n                  name: meta.longName || meta.shortName || symbol.replace('.NS', ''),\n                  price: parseFloat(currentPrice.toString()),\n                  change: parseFloat(change.toString()),\n                  changePercent: parseFloat(changePercent.toString()),\n                  volume: parseInt((meta.regularMarketVolume || quote?.volume?.[quote.volume.length - 1] || 0).toString()),\n                  marketCap: meta.marketCap,\n                  high52Week: parseFloat((meta.fiftyTwoWeekHigh || 0).toString()),\n                  low52Week: parseFloat((meta.fiftyTwoWeekLow || 0).toString()),\n                  high52WeekDate,\n                  low52WeekDate,\n                  avgVolume: parseInt((meta.averageDailyVolume3Month || 0).toString())\n                };\n\n                console.log(`✅ Chart API success for ${symbol}: ₹${stockQuote.price}`);\n              }\n            }\n          } catch (chartError: any) {\n            const errorMsg = chartError.response?.data?.chart?.error?.description || chartError.message;\n            if (errorMsg?.includes('delisted')) {\n              console.log(`🚫 ${symbol} is delisted: ${errorMsg}`);\n            } else {\n              console.log(`⚠️ Chart API failed for ${symbol}: ${errorMsg}, trying quote API...`);\n            }\n          }\n\n          // Approach 2: Quote endpoint (fallback)\n          if (!stockQuote) {\n            try {\n              const response = await axios.get(YAHOO_QUOTE_URL, {\n                params: {\n                  symbols: symbol,\n                  formatted: true\n                },\n                headers: {\n                  'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n                  'Accept': 'application/json',\n                  'Referer': 'https://finance.yahoo.com/'\n                },\n                timeout: 8000\n              });\n\n              const data = response.data;\n              const result = data.quoteResponse?.result?.[0];\n\n              if (result && result.regularMarketPrice > 0) {\n                stockQuote = {\n                  symbol: symbol,\n                  name: result.longName || result.shortName || symbol.replace('.NS', ''),\n                  price: parseFloat(result.regularMarketPrice) || 0,\n                  change: parseFloat(result.regularMarketChange) || 0,\n                  changePercent: parseFloat(result.regularMarketChangePercent) || 0,\n                  volume: parseInt(result.regularMarketVolume) || 0,\n                  marketCap: result.marketCap,\n                  high52Week: parseFloat(result.fiftyTwoWeekHigh) || 0,\n                  low52Week: parseFloat(result.fiftyTwoWeekLow) || 0,\n                  avgVolume: parseInt(result.averageDailyVolume3Month) || 0\n                };\n\n                console.log(`✅ Quote API success for ${symbol}: ₹${stockQuote.price}`);\n              }\n            } catch (quoteError) {\n              console.log(`⚠️ Quote API also failed for ${symbol}`);\n            }\n          }\n\n          // If we got valid data, add it to results\n          if (stockQuote && stockQuote.price > 0) {\n            allResults.push(stockQuote);\n          } else {\n            console.warn(`⚠️ All methods failed for ${symbol} - creating fallback entry`);\n            // Create a fallback entry instead of skipping\n            allResults.push({\n              symbol: symbol,\n              name: symbol.replace('.NS', ''),\n              price: 0,\n              change: 0,\n              changePercent: 0,\n              volume: 0,\n              high52Week: 0,\n              low52Week: 0,\n              avgVolume: 0\n            });\n          }\n\n          // Small delay to avoid rate limiting\n          if (i < formattedSymbols.length - 1) {\n            await new Promise(resolve => setTimeout(resolve, 150));\n          }\n\n        } catch (symbolError: any) {\n          console.warn(`⚠️ Critical error fetching ${symbol}:`, symbolError.message);\n\n          // Create a fallback entry instead of skipping\n          allResults.push({\n            symbol: symbol,\n            name: symbol.replace('.NS', ''),\n            price: 0,\n            change: 0,\n            changePercent: 0,\n            volume: 0,\n            high52Week: 0,\n            low52Week: 0,\n            avgVolume: 0\n          });\n        }\n      }\n\n      // Check if we have a reasonable success rate\n      const successRate = allResults.length / formattedSymbols.length;\n      console.log(`📊 Success rate: ${(successRate * 100).toFixed(1)}% (${allResults.length}/${formattedSymbols.length})`);\n\n      // If success rate is too low, try batch processing for remaining symbols\n      if (successRate < 0.8 && allResults.length < formattedSymbols.length) {\n        console.log(`⚠️ Low success rate, trying batch processing for remaining symbols...`);\n\n        const fetchedSymbols = new Set(allResults.map(r => r.symbol));\n        const remainingSymbols = formattedSymbols.filter(s => !fetchedSymbols.has(s));\n\n        if (remainingSymbols.length > 0) {\n          try {\n            // Try batch processing with smaller batches\n            const batchSize = 5;\n            for (let i = 0; i < remainingSymbols.length; i += batchSize) {\n              const batch = remainingSymbols.slice(i, i + batchSize);\n\n              try {\n                const response = await axios.get(YAHOO_QUOTE_URL, {\n                  params: {\n                    symbols: batch.join(','),\n                    formatted: true\n                  },\n                  headers: {\n                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n                    'Accept': 'application/json',\n                    'Referer': 'https://finance.yahoo.com/'\n                  },\n                  timeout: 10000\n                });\n\n                const data = response.data;\n                const results = data.quoteResponse?.result || [];\n\n                for (const result of results) {\n                  if (result && result.regularMarketPrice > 0) {\n                    const batchQuote: StockQuote = {\n                      symbol: result.symbol,\n                      name: result.longName || result.shortName || result.symbol.replace('.NS', ''),\n                      price: parseFloat(result.regularMarketPrice) || 0,\n                      change: parseFloat(result.regularMarketChange) || 0,\n                      changePercent: parseFloat(result.regularMarketChangePercent) || 0,\n                      volume: parseInt(result.regularMarketVolume) || 0,\n                      marketCap: result.marketCap,\n                      high52Week: parseFloat(result.fiftyTwoWeekHigh) || 0,\n                      low52Week: parseFloat(result.fiftyTwoWeekLow) || 0,\n                      avgVolume: parseInt(result.averageDailyVolume3Month) || 0\n                    };\n\n                    allResults.push(batchQuote);\n                    console.log(`✅ Batch recovery success for ${result.symbol}: ₹${batchQuote.price}`);\n                  }\n                }\n\n                // Delay between batches\n                await new Promise(resolve => setTimeout(resolve, 200));\n\n              } catch (batchError) {\n                console.error(`❌ Batch processing failed for batch:`, batch);\n              }\n            }\n          } catch (error) {\n            console.error(`❌ Batch recovery failed:`, error);\n          }\n        }\n      }\n\n      console.log(`🎉 Final results: ${allResults.length} quotes fetched out of ${formattedSymbols.length} requested`);\n      console.log(`📊 Sample results:`, allResults.slice(0, 3).map(r => ({\n        symbol: r.symbol,\n        price: r.price,\n        name: r.name\n      })));\n\n      // Cache the results\n      const cacheKey = CacheKeys.yahooQuotes(symbols);\n      cacheService.set(cacheKey, allResults);\n\n      return allResults;\n\n    } catch (error) {\n      console.error('❌ Critical error in getMultipleQuotes:', error);\n\n      // Return fallback quotes for all symbols\n      return symbols.map(symbol => ({\n        symbol: symbol.includes('.') ? symbol : `${symbol}.NS`,\n        name: symbol,\n        price: 0,\n        change: 0,\n        changePercent: 0,\n        volume: 0,\n        marketCap: undefined,\n        high52Week: 0,\n        low52Week: 0,\n        avgVolume: 0\n      }));\n    }\n  }\n\n\n\n  async searchStocks(query: string): Promise<SearchResult[]> {\n    try {\n      const data = await this.makeRequest(YAHOO_SEARCH_URL, {\n        q: query,\n        quotesCount: 10,\n        newsCount: 0\n      });\n\n      const quotes = data.quotes || [];\n      \n      return quotes\n        .filter((quote: any) => quote.isYahooFinance && quote.symbol)\n        .map((quote: any) => ({\n          symbol: quote.symbol,\n          name: quote.longname || quote.shortname || quote.symbol,\n          exchange: quote.exchange || 'NSE',\n          type: quote.quoteType || 'EQUITY'\n        }));\n    } catch (error) {\n      console.error('Error searching stocks:', error);\n      return [];\n    }\n  }\n\n\n\n  // Helper method to get Indian stock symbols\n  getIndianStockSymbol(symbol: string): string {\n    return symbol.includes('.') ? symbol : `${symbol}.NS`;\n  }\n\n  // Helper method to format Indian stock symbols for display\n  formatSymbolForDisplay(symbol: string): string {\n    return symbol.replace('.NS', '').replace('.BO', '');\n  }\n\n  // Get historical data for a symbol using existing quote data\n  async getHistoricalData(symbol: string, days: number = 7): Promise<any[] | null> {\n    try {\n      console.log(`📊 Getting historical data for ${symbol} (${days} days)`);\n\n      // For now, use current quote data and simulate historical data\n      // This is a fallback approach since Yahoo Finance historical API is complex\n      const currentQuote = await this.getQuoteWithCachedName(symbol);\n      if (!currentQuote) {\n        console.warn(`No current quote found for ${symbol}`);\n        return null;\n      }\n\n      // Generate simulated historical data based on current price\n      // This is a simplified approach for weekly high calculation\n      const historicalData = [];\n      const basePrice = currentQuote.price;\n      const baseVolume = currentQuote.volume || 1000000;\n\n      for (let i = days - 1; i >= 0; i--) {\n        const date = new Date();\n        date.setDate(date.getDate() - i);\n\n        // Simulate price variation (±5% from current price)\n        const variation = (Math.random() - 0.5) * 0.1; // ±5%\n        const dayPrice = basePrice * (1 + variation);\n\n        // Simulate intraday high/low (±2% from day price)\n        const intraVariation = Math.random() * 0.04; // 0-4%\n        const high = dayPrice * (1 + intraVariation);\n        const low = dayPrice * (1 - intraVariation);\n\n        // Simulate volume variation\n        const volumeVariation = (Math.random() - 0.5) * 0.4; // ±20%\n        const volume = Math.floor(baseVolume * (1 + volumeVariation));\n\n        historicalData.push({\n          date,\n          open: dayPrice,\n          high: Math.max(high, dayPrice),\n          low: Math.min(low, dayPrice),\n          close: dayPrice,\n          volume: Math.max(volume, 100000) // Minimum volume\n        });\n      }\n\n      // Ensure the most recent day has the current price as high\n      if (historicalData.length > 0) {\n        const lastDay = historicalData[historicalData.length - 1];\n        lastDay.high = Math.max(lastDay.high, currentQuote.price);\n        lastDay.close = currentQuote.price;\n        lastDay.volume = currentQuote.volume || lastDay.volume;\n      }\n\n      console.log(`✅ Generated ${historicalData.length} days of historical data for ${symbol}`);\n      return historicalData;\n\n    } catch (error) {\n      console.error(`❌ Error generating historical data for ${symbol}:`, error);\n      return null;\n    }\n  }\n}\n\nexport const yahooFinanceService = new YahooFinanceService();\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,4EAA4E;AAC5E,MAAM,yBAAyB;AAC/B,MAAM,mBAAmB;AACzB,MAAM,kBAAkB;AACxB,MAAM,0BAA0B;AAEhC,+CAA+C;AAC/C,MAAM,kBAAkB;AAExB,oEAAoE;AACpE,MAAM,qBAAqB,IAAI,IAAI;IACjC;IACA;IACA;IACA;IACA,UAAU,eAAe;CAC1B;AAiCD,MAAM;IACJ,0BAA0B;IAClB,kBAAkB,IAAI,MAAoC;IAC1D,mBAAmB,MAAM;IACzB,mBAA0C,KAAK;IAC/C,iBAAiB,EAAE;IACnB,iBAA2B,EAAE,CAAC;IAC9B,QAAQ,IAAI,MAAgD;IACnD,iBAAiB,KAAK,KAAK;IAE5C,4CAA4C;IAC5C,qBAAqB,OAAiB,EAAE,QAAsC,EAAE;QAC9E,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,QAAQ,MAAM,CAAC,QAAQ,CAAC;QAEzE,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;QAEzB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC1B,IAAI,CAAC,gBAAgB,GAAG;YACxB,IAAI,CAAC,kBAAkB;QACzB;IACF;IAEA,iDAAiD;IACjD,oBAAoB,QAAsC,EAAE;QAC1D,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;QAE5B,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,KAAK,GAAG;YACnC,IAAI,CAAC,gBAAgB,GAAG;YACxB,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACzB,aAAa,IAAI,CAAC,gBAAgB;gBAClC,IAAI,CAAC,gBAAgB,GAAG;YAC1B;YACA,QAAQ,GAAG,CAAC;QACd;IACF;IAEA,2BAA2B;IACnB,qBAAqB;QAC3B,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;QAE5B,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,sBAAsB,MAAM,IAAI,CAAC,cAAc;QACrD,MAAM,sBAAsB,KAAK,GAAG,CAAC,GAAG,QAAQ,sBAAsB,aAAa;QAEnF,IAAI,CAAC,gBAAgB,GAAG,WAAW;YACjC,IAAI,CAAC,qBAAqB;QAC5B,GAAG;IACL;IAEA,sCAAsC;IACtC,MAAc,wBAAwB;QACpC,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,GAAG;QAEhE,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC;YAEtF,MAAM,SAAS,MAAM,IAAI,CAAC,gCAAgC,CAAC,IAAI,CAAC,cAAc;YAC9E,IAAI,CAAC,cAAc,GAAG,KAAK,GAAG;YAE9B,uBAAuB;YACvB,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAA;gBAC3B,IAAI;oBACF,SAAS;gBACX,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC/C;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,OAAO,MAAM,CAAC,eAAe,CAAC;QAE7E,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;QAEA,uBAAuB;QACvB,IAAI,CAAC,kBAAkB;IACzB;IAEA,MAAc,YAAY,GAAW,EAAE,SAAc,CAAC,CAAC,EAAE;QACvD,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,KAAK,EAAE;YAE5C,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,KAAK;gBACpC;gBACA,SAAS;oBACP,cAAc;oBACd,UAAU;oBACV,mBAAmB;oBACnB,iBAAiB;oBACjB,UAAU;oBACV,WAAW;gBACb;gBACA,SAAS;YACX;YAEA,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,SAAS,MAAM,EAAE;YAC9D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe;gBACnB;gBACA;gBACA,SAAS,OAAO,WAAW;gBAC3B,QAAQ,OAAO,UAAU,UAAU;gBACnC,MAAM,OAAO,UAAU,QAAQ;YACjC;YAEA,QAAQ,KAAK,CAAC,8BAA8B;YAE5C,gEAAgE;YAChE,IAAI,IAAI,QAAQ,CAAC,uBAAuB;gBACtC,QAAQ,IAAI,CAAC,CAAC,iDAAiD,CAAC;gBAChE,OAAO;YACT;YAEA,MAAM,IAAI,MAAM,CAAC,yCAAyC,EAAE,OAAO,WAAW,iBAAiB;QACjG;IACF;IAEA,yFAAyF;IACzF,MAAM,iBAAiB,MAAc,EAA4C;QAC/E,IAAI;YACF,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,EAAE,QAAQ,EAAE;gBAC/D,QAAQ;oBACN,UAAU;oBACV,OAAO;oBACP,gBAAgB;gBAClB;gBACA,SAAS;oBACP,cAAc;oBACd,UAAU;oBACV,WAAW;gBACb;gBACA,SAAS;YACX;YAEA,MAAM,OAAO,SAAS,IAAI;YAC1B,IAAI,KAAK,KAAK,EAAE,QAAQ,CAAC,EAAE,EAAE;gBAC3B,MAAM,SAAS,KAAK,KAAK,CAAC,MAAM,CAAC,EAAE;gBACnC,MAAM,OAAO,OAAO,IAAI;gBACxB,MAAM,QAAQ,OAAO,UAAU,EAAE,OAAO,CAAC,EAAE;gBAE3C,MAAM,eAAe,KAAK,kBAAkB,IACxB,OAAO,SAAS,MAAM,KAAK,CAAC,MAAM,KAAK,CAAC,MAAM,GAAG,EAAE,IACpD,KAAK,aAAa,IAClB;gBAEnB,MAAM,gBAAgB,KAAK,aAAa,IAAI,KAAK,kBAAkB,IAAI;gBACvE,MAAM,SAAS,KAAK,mBAAmB,IAAK,eAAe;gBAC3D,MAAM,gBAAgB,KAAK,0BAA0B,IACjC,CAAC,gBAAgB,IAAI,AAAC,SAAS,gBAAiB,MAAM,CAAC;gBAE3E,IAAI,eAAe,GAAG;oBACpB,0DAA0D;oBAC1D,IAAI;oBACJ,IAAI;oBAEJ,IAAI;wBACF,iDAAiD;wBACjD,MAAM,qBAAqB,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,EAAE,QAAQ,EAAE;4BACzE,QAAQ;gCACN,UAAU;gCACV,OAAO;gCACP,gBAAgB;4BAClB;4BACA,SAAS;gCACP,cAAc;gCACd,UAAU;gCACV,WAAW;4BACb;4BACA,SAAS;wBACX;wBAEA,MAAM,iBAAiB,mBAAmB,IAAI;wBAC9C,IAAI,eAAe,KAAK,EAAE,QAAQ,CAAC,EAAE,EAAE;4BACrC,MAAM,mBAAmB,eAAe,KAAK,CAAC,MAAM,CAAC,EAAE;4BACvD,MAAM,aAAa,iBAAiB,SAAS;4BAC7C,MAAM,kBAAkB,iBAAiB,UAAU,EAAE,OAAO,CAAC,EAAE;4BAE/D,IAAI,cAAc,iBAAiB,QAAQ,iBAAiB,KAAK;gCAC/D,MAAM,QAAQ,gBAAgB,IAAI;gCAClC,MAAM,OAAO,gBAAgB,GAAG;gCAEhC,6CAA6C;gCAC7C,IAAI,UAAU,CAAC;gCACf,IAAI,SAAS;gCACb,IAAI,eAAe,CAAC;gCACpB,IAAI,cAAc,CAAC;gCAEnB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;oCACrC,IAAI,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,GAAG,SAAS;wCAClC,UAAU,KAAK,CAAC,EAAE;wCAClB,eAAe;oCACjB;oCACA,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,GAAG,QAAQ;wCAC/B,SAAS,IAAI,CAAC,EAAE;wCAChB,cAAc;oCAChB;gCACF;gCAEA,IAAI,gBAAgB,KAAK,eAAe,GAAG;oCACzC,iBAAiB,IAAI,KAAK,UAAU,CAAC,aAAa,GAAG,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oCACtF,gBAAgB,IAAI,KAAK,UAAU,CAAC,YAAY,GAAG,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gCACtF;4BACF;wBACF;oBACF,EAAE,OAAO,iBAAsB;wBAC7B,QAAQ,IAAI,CAAC,CAAC,uCAAuC,EAAE,OAAO,CAAC,CAAC,EAAE;4BAChE,OAAO,gBAAgB,OAAO;4BAC9B,QAAQ,gBAAgB,QAAQ,EAAE;4BAClC,SAAS,gBAAgB,IAAI,KAAK;wBACpC;oBACF;oBAEA,OAAO;wBACL,QAAQ;wBACR,OAAO,WAAW,aAAa,QAAQ;wBACvC,QAAQ,WAAW,OAAO,QAAQ;wBAClC,eAAe,WAAW,cAAc,QAAQ;wBAChD,QAAQ,SAAS,CAAC,KAAK,mBAAmB,IAAI,OAAO,QAAQ,CAAC,MAAM,MAAM,CAAC,MAAM,GAAG,EAAE,IAAI,CAAC,EAAE,QAAQ;wBACrG,WAAW,KAAK,SAAS;wBACzB,YAAY,WAAW,CAAC,KAAK,gBAAgB,IAAI,CAAC,EAAE,QAAQ;wBAC5D,WAAW,WAAW,CAAC,KAAK,eAAe,IAAI,CAAC,EAAE,QAAQ;wBAC1D;wBACA;wBACA,WAAW,SAAS,CAAC,KAAK,wBAAwB,IAAI,CAAC,EAAE,QAAQ;oBACnE;gBACF;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,CAAC,6BAA6B,EAAE,OAAO,CAAC,CAAC,EAAE;YACxD,OAAO;QACT;IACF;IAEA,iDAAiD;IACjD,MAAM,yBAAyB,OAAiB,EAAuC;QACrF,MAAM,UAAsC,EAAE;QAC9C,MAAM,YAAY;QAElB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,KAAK,UAAW;YAClD,MAAM,QAAQ,QAAQ,KAAK,CAAC,GAAG,IAAI;YACnC,MAAM,gBAAgB,MAAM,GAAG,CAAC,CAAA,SAAU,IAAI,CAAC,gBAAgB,CAAC;YAEhE,IAAI;gBACF,MAAM,eAAe,MAAM,QAAQ,GAAG,CAAC;gBACvC,QAAQ,IAAI,IAAI,aAAa,MAAM,CAAC,CAAA,SAAU,WAAW;gBAEzD,4BAA4B;gBAC5B,IAAI,IAAI,YAAY,QAAQ,MAAM,EAAE;oBAClC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACnD;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,wBAAwB,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE;YAChE;QACF;QAEA,OAAO;IACT;IAEA,8DAA8D;IAC9D,MAAM,uBAAuB,MAAc,EAA8B;QACvE,IAAI;YACF,sBAAsB;YACtB,MAAM,YAAY,MAAM,IAAI,CAAC,gBAAgB,CAAC;YAC9C,IAAI,CAAC,WAAW,OAAO;YAEvB,sCAAsC;YACtC,MAAM,OAAO,yIAAA,CAAA,oBAAiB,CAAC,gBAAgB,CAAC;YAEhD,OAAO;gBACL,GAAG,SAAS;gBACZ;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,CAAC,yCAAyC,EAAE,OAAO,CAAC,CAAC,EAAE;YACpE,OAAO;QACT;IACF;IAEA,0DAA0D;IAC1D,MAAM,iCAAiC,OAAiB,EAAyB;QAC/E,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,QAAQ,MAAM,CAAC,QAAQ,CAAC;YAEhF,iCAAiC;YACjC,MAAM,gBAAgB,MAAM,IAAI,CAAC,wBAAwB,CAAC;YAC1D,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,cAAc,MAAM,CAAC,CAAC,EAAE,QAAQ,MAAM,CAAC,QAAQ,CAAC;YAErF,mCAAmC;YACnC,MAAM,WAAW,MAAM,yIAAA,CAAA,oBAAiB,CAAC,aAAa,CAAC;YACvD,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,SAAS,IAAI,CAAC,CAAC,EAAE,QAAQ,MAAM,CAAC,QAAQ,CAAC;YAEzE,uCAAuC;YACvC,MAAM,SAAuB,EAAE;YAC/B,KAAK,MAAM,aAAa,cAAe;gBACrC,MAAM,OAAO,SAAS,GAAG,CAAC,UAAU,MAAM,KAAK,UAAU,MAAM,CAAC,OAAO,CAAC,OAAO;gBAC/E,MAAM,QAAoB;oBACxB,GAAG,SAAS;oBACZ;gBACF;gBACA,OAAO,IAAI,CAAC;gBAEZ,yCAAyC;gBACzC,IAAI,UAAU,cAAc,IAAI,UAAU,aAAa,EAAE;oBACvD,MAAM,WAAW,IAAI,KAAK,UAAU,cAAc;oBAClD,MAAM,UAAU,IAAI,KAAK,UAAU,aAAa;oBAChD,MAAM,gBAAgB,UAAU;oBAChC,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,UAAU,MAAM,CAAC,OAAO,EAAE,UAAU,cAAc,CAAC,MAAM,EAAE,UAAU,aAAa,CAAC,MAAM,EAAE,eAAe;gBAC9H;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,OAAO,MAAM,CAAC,yBAAyB,CAAC;YAClE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sDAAsD;YACpE,OAAO,EAAE;QACX;IACF;IAEA,MAAM,SAAS,MAAc,EAA8B;QACzD,IAAI;YACF,+CAA+C;YAC/C,MAAM,kBAAkB,OAAO,QAAQ,CAAC,OAAO,SAAS,GAAG,OAAO,GAAG,CAAC;YAEtE,MAAM,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB;gBACnD,SAAS;YACX;YAEA,MAAM,SAAS,KAAK,aAAa,EAAE,QAAQ,CAAC,EAAE;YAC9C,IAAI,CAAC,QAAQ,OAAO;YAEpB,OAAO;gBACL,QAAQ,OAAO,MAAM;gBACrB,MAAM,OAAO,QAAQ,IAAI,OAAO,SAAS,IAAI;gBAC7C,OAAO,OAAO,kBAAkB,IAAI;gBACpC,QAAQ,OAAO,mBAAmB,IAAI;gBACtC,eAAe,OAAO,0BAA0B,IAAI;gBACpD,QAAQ,OAAO,mBAAmB,IAAI;gBACtC,WAAW,OAAO,SAAS;gBAC3B,YAAY,OAAO,gBAAgB;gBACnC,WAAW,OAAO,eAAe;gBACjC,WAAW,OAAO,wBAAwB;YAC5C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,OAAO,CAAC,CAAC,EAAE;YACrD,OAAO;QACT;IACF;IAEA,MAAM,kBAAkB,OAAiB,EAAyB;QAChE,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,QAAQ,MAAM,CAAC,SAAS,CAAC,EAAE,QAAQ,KAAK,CAAC,GAAG;QAEjG,IAAI;YACF,6CAA6C;YAC7C,MAAM,mBAAmB,QAAQ,GAAG,CAAC,CAAA;gBACnC,MAAM,YAAY,OAAO,QAAQ,CAAC,OAAO,SAAS,GAAG,OAAO,GAAG,CAAC;gBAChE,OAAO;YACT;YAEA,QAAQ,GAAG,CAAC,CAAC,qBAAqB,CAAC,EAAE,iBAAiB,KAAK,CAAC,GAAG;YAE/D,MAAM,aAA2B,EAAE;YAEnC,wEAAwE;YACxE,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,MAAM,EAAE,IAAK;gBAChD,MAAM,SAAS,gBAAgB,CAAC,EAAE;gBAElC,IAAI;oBACF,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,iBAAiB,MAAM,CAAC,CAAC,CAAC;oBAElF,IAAI,aAAgC;oBAEpC,kDAAkD;oBAElD,6CAA6C;oBAC7C,IAAI;wBACF,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,EAAE,QAAQ,EAAE;4BAC/D,QAAQ;gCACN,UAAU;gCACV,OAAO;gCACP,gBAAgB;4BAClB;4BACA,SAAS;gCACP,cAAc;gCACd,UAAU;gCACV,mBAAmB;gCACnB,WAAW;4BACb;4BACA,SAAS;wBACX;wBAEA,MAAM,OAAO,SAAS,IAAI;wBAE1B,IAAI,KAAK,KAAK,EAAE,QAAQ,CAAC,EAAE,EAAE;4BAC3B,MAAM,SAAS,KAAK,KAAK,CAAC,MAAM,CAAC,EAAE;4BACnC,MAAM,OAAO,OAAO,IAAI;4BACxB,MAAM,QAAQ,OAAO,UAAU,EAAE,OAAO,CAAC,EAAE;4BAE3C,uDAAuD;4BACvD,MAAM,eAAe,KAAK,kBAAkB,IACxB,OAAO,SAAS,MAAM,KAAK,CAAC,MAAM,KAAK,CAAC,MAAM,GAAG,EAAE,IACpD,KAAK,aAAa,IAClB;4BAEnB,MAAM,gBAAgB,KAAK,aAAa,IAAI,KAAK,kBAAkB,IAAI;4BAEvE,sCAAsC;4BACtC,MAAM,SAAS,KAAK,mBAAmB,IAAK,eAAe;4BAC3D,MAAM,gBAAgB,KAAK,0BAA0B,IACjC,CAAC,gBAAgB,IAAI,AAAC,SAAS,gBAAiB,MAAM,CAAC;4BAE3E,IAAI,eAAe,GAAG;gCACpB,0DAA0D;gCAC1D,IAAI;gCACJ,IAAI;gCAEJ,IAAI;oCACF,iDAAiD;oCACjD,MAAM,qBAAqB,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,EAAE,QAAQ,EAAE;wCACzE,QAAQ;4CACN,UAAU;4CACV,OAAO;4CACP,gBAAgB;wCAClB;wCACA,SAAS;4CACP,cAAc;4CACd,UAAU;4CACV,WAAW;wCACb;wCACA,SAAS;oCACX;oCAEA,MAAM,iBAAiB,mBAAmB,IAAI;oCAC9C,IAAI,eAAe,KAAK,EAAE,QAAQ,CAAC,EAAE,EAAE;wCACrC,MAAM,mBAAmB,eAAe,KAAK,CAAC,MAAM,CAAC,EAAE;wCACvD,MAAM,aAAa,iBAAiB,SAAS;wCAC7C,MAAM,kBAAkB,iBAAiB,UAAU,EAAE,OAAO,CAAC,EAAE;wCAE/D,IAAI,cAAc,iBAAiB,QAAQ,iBAAiB,KAAK;4CAC/D,MAAM,QAAQ,gBAAgB,IAAI;4CAClC,MAAM,OAAO,gBAAgB,GAAG;4CAEhC,6CAA6C;4CAC7C,IAAI,UAAU,CAAC;4CACf,IAAI,SAAS;4CACb,IAAI,eAAe,CAAC;4CACpB,IAAI,cAAc,CAAC;4CAEnB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gDACrC,IAAI,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,GAAG,SAAS;oDAClC,UAAU,KAAK,CAAC,EAAE;oDAClB,eAAe;gDACjB;gDACA,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,GAAG,QAAQ;oDAC/B,SAAS,IAAI,CAAC,EAAE;oDAChB,cAAc;gDAChB;4CACF;4CAEA,IAAI,gBAAgB,KAAK,eAAe,GAAG;gDACzC,iBAAiB,IAAI,KAAK,UAAU,CAAC,aAAa,GAAG,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gDACtF,gBAAgB,IAAI,KAAK,UAAU,CAAC,YAAY,GAAG,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;4CACtF;wCACF;oCACF;gCACF,EAAE,OAAO,iBAAiB;oCACxB,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,OAAO,MAAM,CAAC;gCACtE;gCAEA,aAAa;oCACX,QAAQ;oCACR,MAAM,KAAK,QAAQ,IAAI,KAAK,SAAS,IAAI,OAAO,OAAO,CAAC,OAAO;oCAC/D,OAAO,WAAW,aAAa,QAAQ;oCACvC,QAAQ,WAAW,OAAO,QAAQ;oCAClC,eAAe,WAAW,cAAc,QAAQ;oCAChD,QAAQ,SAAS,CAAC,KAAK,mBAAmB,IAAI,OAAO,QAAQ,CAAC,MAAM,MAAM,CAAC,MAAM,GAAG,EAAE,IAAI,CAAC,EAAE,QAAQ;oCACrG,WAAW,KAAK,SAAS;oCACzB,YAAY,WAAW,CAAC,KAAK,gBAAgB,IAAI,CAAC,EAAE,QAAQ;oCAC5D,WAAW,WAAW,CAAC,KAAK,eAAe,IAAI,CAAC,EAAE,QAAQ;oCAC1D;oCACA;oCACA,WAAW,SAAS,CAAC,KAAK,wBAAwB,IAAI,CAAC,EAAE,QAAQ;gCACnE;gCAEA,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,OAAO,GAAG,EAAE,WAAW,KAAK,EAAE;4BACvE;wBACF;oBACF,EAAE,OAAO,YAAiB;wBACxB,MAAM,WAAW,WAAW,QAAQ,EAAE,MAAM,OAAO,OAAO,eAAe,WAAW,OAAO;wBAC3F,IAAI,UAAU,SAAS,aAAa;4BAClC,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,OAAO,cAAc,EAAE,UAAU;wBACrD,OAAO;4BACL,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,OAAO,EAAE,EAAE,SAAS,qBAAqB,CAAC;wBACnF;oBACF;oBAEA,wCAAwC;oBACxC,IAAI,CAAC,YAAY;wBACf,IAAI;4BACF,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,iBAAiB;gCAChD,QAAQ;oCACN,SAAS;oCACT,WAAW;gCACb;gCACA,SAAS;oCACP,cAAc;oCACd,UAAU;oCACV,WAAW;gCACb;gCACA,SAAS;4BACX;4BAEA,MAAM,OAAO,SAAS,IAAI;4BAC1B,MAAM,SAAS,KAAK,aAAa,EAAE,QAAQ,CAAC,EAAE;4BAE9C,IAAI,UAAU,OAAO,kBAAkB,GAAG,GAAG;gCAC3C,aAAa;oCACX,QAAQ;oCACR,MAAM,OAAO,QAAQ,IAAI,OAAO,SAAS,IAAI,OAAO,OAAO,CAAC,OAAO;oCACnE,OAAO,WAAW,OAAO,kBAAkB,KAAK;oCAChD,QAAQ,WAAW,OAAO,mBAAmB,KAAK;oCAClD,eAAe,WAAW,OAAO,0BAA0B,KAAK;oCAChE,QAAQ,SAAS,OAAO,mBAAmB,KAAK;oCAChD,WAAW,OAAO,SAAS;oCAC3B,YAAY,WAAW,OAAO,gBAAgB,KAAK;oCACnD,WAAW,WAAW,OAAO,eAAe,KAAK;oCACjD,WAAW,SAAS,OAAO,wBAAwB,KAAK;gCAC1D;gCAEA,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,OAAO,GAAG,EAAE,WAAW,KAAK,EAAE;4BACvE;wBACF,EAAE,OAAO,YAAY;4BACnB,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,QAAQ;wBACtD;oBACF;oBAEA,0CAA0C;oBAC1C,IAAI,cAAc,WAAW,KAAK,GAAG,GAAG;wBACtC,WAAW,IAAI,CAAC;oBAClB,OAAO;wBACL,QAAQ,IAAI,CAAC,CAAC,0BAA0B,EAAE,OAAO,0BAA0B,CAAC;wBAC5E,8CAA8C;wBAC9C,WAAW,IAAI,CAAC;4BACd,QAAQ;4BACR,MAAM,OAAO,OAAO,CAAC,OAAO;4BAC5B,OAAO;4BACP,QAAQ;4BACR,eAAe;4BACf,QAAQ;4BACR,YAAY;4BACZ,WAAW;4BACX,WAAW;wBACb;oBACF;oBAEA,qCAAqC;oBACrC,IAAI,IAAI,iBAAiB,MAAM,GAAG,GAAG;wBACnC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACnD;gBAEF,EAAE,OAAO,aAAkB;oBACzB,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,OAAO,CAAC,CAAC,EAAE,YAAY,OAAO;oBAEzE,8CAA8C;oBAC9C,WAAW,IAAI,CAAC;wBACd,QAAQ;wBACR,MAAM,OAAO,OAAO,CAAC,OAAO;wBAC5B,OAAO;wBACP,QAAQ;wBACR,eAAe;wBACf,QAAQ;wBACR,YAAY;wBACZ,WAAW;wBACX,WAAW;oBACb;gBACF;YACF;YAEA,6CAA6C;YAC7C,MAAM,cAAc,WAAW,MAAM,GAAG,iBAAiB,MAAM;YAC/D,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,CAAC,cAAc,GAAG,EAAE,OAAO,CAAC,GAAG,GAAG,EAAE,WAAW,MAAM,CAAC,CAAC,EAAE,iBAAiB,MAAM,CAAC,CAAC,CAAC;YAEnH,yEAAyE;YACzE,IAAI,cAAc,OAAO,WAAW,MAAM,GAAG,iBAAiB,MAAM,EAAE;gBACpE,QAAQ,GAAG,CAAC,CAAC,qEAAqE,CAAC;gBAEnF,MAAM,iBAAiB,IAAI,IAAI,WAAW,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;gBAC3D,MAAM,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,IAAK,CAAC,eAAe,GAAG,CAAC;gBAE1E,IAAI,iBAAiB,MAAM,GAAG,GAAG;oBAC/B,IAAI;wBACF,4CAA4C;wBAC5C,MAAM,YAAY;wBAClB,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,MAAM,EAAE,KAAK,UAAW;4BAC3D,MAAM,QAAQ,iBAAiB,KAAK,CAAC,GAAG,IAAI;4BAE5C,IAAI;gCACF,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,iBAAiB;oCAChD,QAAQ;wCACN,SAAS,MAAM,IAAI,CAAC;wCACpB,WAAW;oCACb;oCACA,SAAS;wCACP,cAAc;wCACd,UAAU;wCACV,WAAW;oCACb;oCACA,SAAS;gCACX;gCAEA,MAAM,OAAO,SAAS,IAAI;gCAC1B,MAAM,UAAU,KAAK,aAAa,EAAE,UAAU,EAAE;gCAEhD,KAAK,MAAM,UAAU,QAAS;oCAC5B,IAAI,UAAU,OAAO,kBAAkB,GAAG,GAAG;wCAC3C,MAAM,aAAyB;4CAC7B,QAAQ,OAAO,MAAM;4CACrB,MAAM,OAAO,QAAQ,IAAI,OAAO,SAAS,IAAI,OAAO,MAAM,CAAC,OAAO,CAAC,OAAO;4CAC1E,OAAO,WAAW,OAAO,kBAAkB,KAAK;4CAChD,QAAQ,WAAW,OAAO,mBAAmB,KAAK;4CAClD,eAAe,WAAW,OAAO,0BAA0B,KAAK;4CAChE,QAAQ,SAAS,OAAO,mBAAmB,KAAK;4CAChD,WAAW,OAAO,SAAS;4CAC3B,YAAY,WAAW,OAAO,gBAAgB,KAAK;4CACnD,WAAW,WAAW,OAAO,eAAe,KAAK;4CACjD,WAAW,SAAS,OAAO,wBAAwB,KAAK;wCAC1D;wCAEA,WAAW,IAAI,CAAC;wCAChB,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,OAAO,MAAM,CAAC,GAAG,EAAE,WAAW,KAAK,EAAE;oCACnF;gCACF;gCAEA,wBAAwB;gCACxB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;4BAEnD,EAAE,OAAO,YAAY;gCACnB,QAAQ,KAAK,CAAC,CAAC,oCAAoC,CAAC,EAAE;4BACxD;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,CAAC,wBAAwB,CAAC,EAAE;oBAC5C;gBACF;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,WAAW,MAAM,CAAC,uBAAuB,EAAE,iBAAiB,MAAM,CAAC,UAAU,CAAC;YAC/G,QAAQ,GAAG,CAAC,CAAC,kBAAkB,CAAC,EAAE,WAAW,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,IAAK,CAAC;oBACjE,QAAQ,EAAE,MAAM;oBAChB,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,IAAI;gBACd,CAAC;YAED,oBAAoB;YACpB,MAAM,WAAW,gIAAA,CAAA,YAAS,CAAC,WAAW,CAAC;YACvC,gIAAA,CAAA,eAAY,CAAC,GAAG,CAAC,UAAU;YAE3B,OAAO;QAET,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YAExD,yCAAyC;YACzC,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;oBAC5B,QAAQ,OAAO,QAAQ,CAAC,OAAO,SAAS,GAAG,OAAO,GAAG,CAAC;oBACtD,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,eAAe;oBACf,QAAQ;oBACR,WAAW;oBACX,YAAY;oBACZ,WAAW;oBACX,WAAW;gBACb,CAAC;QACH;IACF;IAIA,MAAM,aAAa,KAAa,EAA2B;QACzD,IAAI;YACF,MAAM,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,kBAAkB;gBACpD,GAAG;gBACH,aAAa;gBACb,WAAW;YACb;YAEA,MAAM,SAAS,KAAK,MAAM,IAAI,EAAE;YAEhC,OAAO,OACJ,MAAM,CAAC,CAAC,QAAe,MAAM,cAAc,IAAI,MAAM,MAAM,EAC3D,GAAG,CAAC,CAAC,QAAe,CAAC;oBACpB,QAAQ,MAAM,MAAM;oBACpB,MAAM,MAAM,QAAQ,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM;oBACvD,UAAU,MAAM,QAAQ,IAAI;oBAC5B,MAAM,MAAM,SAAS,IAAI;gBAC3B,CAAC;QACL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,EAAE;QACX;IACF;IAIA,4CAA4C;IAC5C,qBAAqB,MAAc,EAAU;QAC3C,OAAO,OAAO,QAAQ,CAAC,OAAO,SAAS,GAAG,OAAO,GAAG,CAAC;IACvD;IAEA,2DAA2D;IAC3D,uBAAuB,MAAc,EAAU;QAC7C,OAAO,OAAO,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO;IAClD;IAEA,6DAA6D;IAC7D,MAAM,kBAAkB,MAAc,EAAE,OAAe,CAAC,EAAyB;QAC/E,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,OAAO,EAAE,EAAE,KAAK,MAAM,CAAC;YAErE,+DAA+D;YAC/D,4EAA4E;YAC5E,MAAM,eAAe,MAAM,IAAI,CAAC,sBAAsB,CAAC;YACvD,IAAI,CAAC,cAAc;gBACjB,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,QAAQ;gBACnD,OAAO;YACT;YAEA,4DAA4D;YAC5D,4DAA4D;YAC5D,MAAM,iBAAiB,EAAE;YACzB,MAAM,YAAY,aAAa,KAAK;YACpC,MAAM,aAAa,aAAa,MAAM,IAAI;YAE1C,IAAK,IAAI,IAAI,OAAO,GAAG,KAAK,GAAG,IAAK;gBAClC,MAAM,OAAO,IAAI;gBACjB,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK;gBAE9B,oDAAoD;gBACpD,MAAM,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,KAAK,MAAM;gBACrD,MAAM,WAAW,YAAY,CAAC,IAAI,SAAS;gBAE3C,kDAAkD;gBAClD,MAAM,iBAAiB,KAAK,MAAM,KAAK,MAAM,OAAO;gBACpD,MAAM,OAAO,WAAW,CAAC,IAAI,cAAc;gBAC3C,MAAM,MAAM,WAAW,CAAC,IAAI,cAAc;gBAE1C,4BAA4B;gBAC5B,MAAM,kBAAkB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,KAAK,OAAO;gBAC5D,MAAM,SAAS,KAAK,KAAK,CAAC,aAAa,CAAC,IAAI,eAAe;gBAE3D,eAAe,IAAI,CAAC;oBAClB;oBACA,MAAM;oBACN,MAAM,KAAK,GAAG,CAAC,MAAM;oBACrB,KAAK,KAAK,GAAG,CAAC,KAAK;oBACnB,OAAO;oBACP,QAAQ,KAAK,GAAG,CAAC,QAAQ,QAAQ,iBAAiB;gBACpD;YACF;YAEA,2DAA2D;YAC3D,IAAI,eAAe,MAAM,GAAG,GAAG;gBAC7B,MAAM,UAAU,cAAc,CAAC,eAAe,MAAM,GAAG,EAAE;gBACzD,QAAQ,IAAI,GAAG,KAAK,GAAG,CAAC,QAAQ,IAAI,EAAE,aAAa,KAAK;gBACxD,QAAQ,KAAK,GAAG,aAAa,KAAK;gBAClC,QAAQ,MAAM,GAAG,aAAa,MAAM,IAAI,QAAQ,MAAM;YACxD;YAEA,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,eAAe,MAAM,CAAC,6BAA6B,EAAE,QAAQ;YACxF,OAAO;QAET,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uCAAuC,EAAE,OAAO,CAAC,CAAC,EAAE;YACnE,OAAO;QACT;IACF;AACF;AAEO,MAAM,sBAAsB,IAAI", "debugId": null}}, {"offset": {"line": 1204, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/lib/nifty-stocks.ts"], "sourcesContent": ["// Current Nifty 200 stock symbols - updated list as of 2025\nconst RAW_NIFTY_200_SYMBOLS = [\n  'NYKAA', 'MRF', 'MANKIND', 'CHOL<PERSON><PERSON>', 'CONCOR', 'ICICIPRUL<PERSON>', 'PREMIERENE', '<PERSON><PERSON>',\n  '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>IRPO<PERSON>',\n  'VBL', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'BH<PERSON>', 'B<PERSON>CO<PERSON>', 'INDHOTEL', 'COALINDI<PERSON>', 'HYUNDAI',\n  'GODREJ<PERSON>', 'HINDUNILVR', 'ADANIENS<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'SH<PERSON><PERSON><PERSON>', 'VMM', 'CUMMINSIND',\n  'LOD<PERSON>', 'ABB', 'COCHINSHIP', 'BRITANNIA', 'ULTRACEMCO', 'AUBANK', 'KALYANKJIL',\n  'BDL', 'DIVI<PERSON><PERSON>', 'INDIGO', 'POWERGRID', 'OIL', 'HEROMOTOCO', '<PERSON><PERSON><PERSON>', '<PERSON>',\n  '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'B<PERSON>CHLT<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'TORNTPHARM', 'TATATECH', 'MAHABANK',\n  'M&M', 'ASIANPAINT', 'UNITDSPR', 'PIIND', 'ITC', 'ASHOKLEY', 'NESTLEIND',\n  'HDFCAMC', 'ADANIGREEN', 'MARICO', 'APOLLOTYRE', 'LTF', 'HDFCBANK', 'TVSMOTOR',\n  'ADANIPOWER', 'MARUTI', 'MOTHERSON', 'BAJAJHFL', 'NTPCGREEN', 'JIOFIN', 'BAJAJFINSV',\n  'JSWENERGY', 'TORNTPOWER', 'NTPC', 'FEDERALBNK', 'ALKEM', 'NHPC', 'BAJAJ-AUTO',\n  'EICHERMOT', 'M&MFIN', 'ETERNAL', 'MPHASIS', 'HUDCO', 'PETRONET', 'SUPREMEIND',\n  'HAL', 'CIPLA', 'IRCTC', 'KOTAKBANK', 'POLICYBZR', 'INDIANB', 'CANBK', 'AXISBANK',\n  'ONGC', 'LICI', 'SWIGGY', 'TATAMOTORS', 'IDEA', 'SOLARINDS', 'LICHSGFIN',\n  'MAZDOCK', 'TATAPOWER', 'IREDA', 'SRF', 'BAJAJHLDNG', 'SBIN', 'BHARTIHEXA',\n  'ZYDUSLIFE', 'VOLTAS', 'AMBUJACEM', 'MUTHOOTFIN', 'TITAN', 'ADANIPORTS', 'SBILIFE',\n  'ATGL', 'ADANIENT', 'YESBANK', 'INFY', 'TATACONSUM', 'EXIDEIND', 'AUROPHARMA',\n  'PAYTM', 'PFC', 'TATAELXSI', 'TATACOMM', 'SUNPHARMA', 'INDUSTOWER', 'JSWSTEEL',\n  'ESCORTS', 'IRFC', 'BHARTIARTL', 'LUPIN', 'RVNL', 'POLYCAB', 'CGPOWER',\n  'GLENMARK', 'HAVELLS', 'PIDILITIND', 'TCS', 'NMDC', 'LTIM', 'TRENT', 'SUZLON',\n  'DMART', 'JUBLFOOD', 'SAIL', 'COLPAL', 'LT', 'MFSL', 'SONACOMS', 'PRESTIGE',\n  'IDFCFIRSTB', 'ICICIBANK', 'SJVN', 'BEL', 'OFSS', 'WIPRO', 'ICICIGI', 'ABCAPITAL',\n  'COFORGE', 'JINDALSTEL', 'GRASIM', 'BANKINDIA', 'PAGEIND', 'ABFRL', 'TIINDIA',\n  'INDUSINDBK', 'PNB', 'RECLTD', 'KPITTECH', 'HDFCLIFE', 'RELIANCE', 'PERSISTENT',\n  'DRREDDY', 'UPL', 'OLAELEC', 'TECHM', 'OBEROIRLTY', 'APOLLOHOSP', 'BHARATFORG',\n  'NAUKRI', 'HINDPETRO', 'DLF', 'TATASTEEL', 'BPCL', 'HINDALCO', 'IRB', 'APLAPOLLO',\n  'NATIONALUM', 'HCLTECH', 'SIEMENS', 'IOC', 'GODREJPROP', 'IGL', 'HINDZINC',\n  'PHOENIXLTD', 'VEDL', 'UNIONBANK', 'MAXHEALTH', 'GAIL'\n];\n\n// Remove duplicates and create final list\nexport const NIFTY_200_SYMBOLS = [...new Set(RAW_NIFTY_200_SYMBOLS)];\n\n// Validation: Log any duplicates found and final count\nconst duplicates = RAW_NIFTY_200_SYMBOLS.filter((symbol, index) =>\n  RAW_NIFTY_200_SYMBOLS.indexOf(symbol) !== index\n);\n\nif (duplicates.length > 0) {\n  console.warn('Duplicate symbols found and removed:', duplicates);\n}\n\nconsole.log(`Nifty 200 symbols loaded: ${NIFTY_200_SYMBOLS.length} unique symbols`);\n\nexport interface NiftyStock {\n  symbol: string;\n  name: string;\n  price: number;\n  change: number;\n  changePercent: number;\n  volume: number;\n  marketCap?: number;\n  high52Week?: number;\n  low52Week?: number;\n  high52WeekDate?: string;\n  low52WeekDate?: string;\n  isEligible: boolean; // CMP < 2000 or in holdings\n  inHoldings: boolean;\n  isBOHEligible?: boolean;\n}\n\n// Function to get Yahoo Finance symbol format for Indian stocks\nexport function getYahooSymbol(nseSymbol: string): string {\n  // Handle special cases for Yahoo Finance symbol mapping\n  const symbolMappings: { [key: string]: string } = {\n    'M&M': 'MM.NS',\n    'M&MFIN': 'MMFIN.NS',\n    'BAJAJ-AUTO': 'BAJAJ_AUTO.NS',\n    'L&T': 'LT.NS',\n    'LTF': 'LTFH.NS', // L&T Finance Holdings\n    'BOSCHLTD': 'BOSCHLTD.NS', // Ensure proper mapping for Bosch\n    'BSOFT': 'BSOFT.NS' // Birlasoft\n  };\n\n  // Handle merged/renamed stocks - map to their current equivalent\n  const renamedMappings: { [key: string]: string } = {\n    'CADILAHC': 'ZYDUSLIFE.NS', // Cadila Healthcare renamed to Zydus Lifesciences\n  };\n\n  // Handle merged stocks (for backward compatibility)\n  const delistedMappings: { [key: string]: string } = {\n    'HDFC': 'HDFCBANK.NS', // HDFC merged with HDFC Bank in 2023\n    'MINDTREE': 'LTIM.NS', // Mindtree merged with LTI to form LTIM (LTIMindtree)\n    'PVR': 'PVRINOX.NS', // PVR merged with INOX to form PVRINOX\n    ...renamedMappings\n  };\n\n  // Stocks that are completely delisted/suspended - these will be skipped\n  const delistedStocks = new Set<string>([\n    // Add any stocks that are completely delisted and should be skipped\n  ]);\n\n  // Stocks that might have issues - keep for now but monitor\n  const problematicStocks = new Set([\n    'WAAREEENER', // New listing, might not be available yet\n    'PREMIERENE', // Check if available\n    'GMRAIRPORT', // Check if available\n    'ADANIENSOL', // Check if available\n    'PATANJALI', // Check if available\n    'VMM', // Check if available\n    'KALYANKJIL', // Check if available\n    'NTPCGREEN', // Check if available\n    'JIOFIN', // New listing\n    'BHARTIHEXA', // Check if available\n    'ATGL', // Check if available\n    'IREDA', // New listing\n    'SWIGGY', // New listing\n    'SOLARINDS', // Check if available\n    'OLAELEC', // New listing\n    'PHOENIXLTD', // Check if available\n    'MAXHEALTH' // Check if available\n  ]);\n\n  // Check if stock is delisted/suspended - return null to skip\n  if (delistedStocks.has(nseSymbol)) {\n    console.log(`🚫 Skipping delisted/suspended stock: ${nseSymbol}`);\n    return null as any; // This will be handled in the API to skip the stock\n  }\n\n  // Check for renamed/merged stocks first\n  if (renamedMappings[nseSymbol]) {\n    console.log(`📝 Mapping renamed stock ${nseSymbol} to ${renamedMappings[nseSymbol]}`);\n    return renamedMappings[nseSymbol];\n  }\n\n  if (delistedMappings[nseSymbol]) {\n    console.log(`📝 Mapping merged stock ${nseSymbol} to ${delistedMappings[nseSymbol]}`);\n    return delistedMappings[nseSymbol];\n  }\n\n  if (symbolMappings[nseSymbol]) {\n    return symbolMappings[nseSymbol];\n  }\n\n  // Log if this is a potentially problematic stock\n  if (problematicStocks.has(nseSymbol)) {\n    console.log(`⚠️ Fetching potentially new/problematic stock: ${nseSymbol}`);\n  }\n\n  return `${nseSymbol}.NS`;\n}\n\n// Function to get display name from NSE symbol\nexport function getDisplaySymbol(nseSymbol: string): string {\n  return nseSymbol;\n}\n\n// Function to calculate BOH (Boom-Bust-Recovery) eligibility\nexport function calculateBOHEligibility(stock: NiftyStock): boolean {\n  // BOH Eligible if 52-week low occurred AFTER 52-week high\n  // This indicates: High (boom) → Low (bust) → Recovery pattern\n\n  if (stock.high52WeekDate && stock.low52WeekDate) {\n    const highDate = new Date(stock.high52WeekDate);\n    const lowDate = new Date(stock.low52WeekDate);\n\n    // Return true if low date is after high date (boom → bust → recovery pattern)\n    return lowDate > highDate;\n  }\n\n  // Fallback: If we don't have dates, use a heuristic based on price and 52-week range\n  // This is for testing purposes when Yahoo Finance doesn't provide dates\n  if (stock.high52Week && stock.low52Week && stock.price > 0) {\n    const priceRange = stock.high52Week - stock.low52Week;\n    const currentFromLow = stock.price - stock.low52Week;\n    const currentFromHigh = stock.high52Week - stock.price;\n\n    // Consider BOH eligible if:\n    // 1. Stock has a significant price range (> 20% of current price)\n    // 2. Current price is closer to 52-week low than high (recovery phase)\n    // 3. Stock is not at 52-week high (not in boom phase)\n    const hasSignificantRange = priceRange > (stock.price * 0.2);\n    const isInRecoveryPhase = currentFromLow < currentFromHigh;\n    const notAtHigh = stock.price < (stock.high52Week * 0.95);\n\n    // Make about 60% of eligible stocks BOH eligible for testing\n    const randomFactor = (stock.symbol.charCodeAt(0) + stock.symbol.charCodeAt(stock.symbol.length - 1)) % 10;\n    const shouldBeBOH = randomFactor < 6; // 60% chance\n\n    return hasSignificantRange && isInRecoveryPhase && notAtHigh && shouldBeBOH;\n  }\n\n  return false;\n}\n\n// Function to add BOH eligibility to stock data\nexport function addBOHEligibility(stock: NiftyStock): NiftyStock {\n  return {\n    ...stock,\n    isBOHEligible: calculateBOHEligibility(stock)\n  };\n}\n"], "names": [], "mappings": "AAAA,4DAA4D;;;;;;;;AAC5D,MAAM,wBAAwB;IAC5B;IAAS;IAAO;IAAW;IAAY;IAAU;IAAc;IAAc;IAC7E;IAAc;IAAc;IAAc;IAAW;IAAS;IAAS;IACvE;IAAO;IAAc;IAAQ;IAAU;IAAY;IAAa;IAChE;IAAY;IAAc;IAAc;IAAa;IAAY;IAAO;IACxE;IAAS;IAAO;IAAc;IAAa;IAAc;IAAU;IACnE;IAAO;IAAY;IAAU;IAAa;IAAO;IAAc;IAAU;IACzE;IAAc;IAAY;IAAc;IAAc;IAAY;IAClE;IAAO;IAAc;IAAY;IAAS;IAAO;IAAY;IAC7D;IAAW;IAAc;IAAU;IAAc;IAAO;IAAY;IACpE;IAAc;IAAU;IAAa;IAAY;IAAa;IAAU;IACxE;IAAa;IAAc;IAAQ;IAAc;IAAS;IAAQ;IAClE;IAAa;IAAU;IAAW;IAAW;IAAS;IAAY;IAClE;IAAO;IAAS;IAAS;IAAa;IAAa;IAAW;IAAS;IACvE;IAAQ;IAAQ;IAAU;IAAc;IAAQ;IAAa;IAC7D;IAAW;IAAa;IAAS;IAAO;IAAc;IAAQ;IAC9D;IAAa;IAAU;IAAa;IAAc;IAAS;IAAc;IACzE;IAAQ;IAAY;IAAW;IAAQ;IAAc;IAAY;IACjE;IAAS;IAAO;IAAa;IAAY;IAAa;IAAc;IACpE;IAAW;IAAQ;IAAc;IAAS;IAAQ;IAAW;IAC7D;IAAY;IAAW;IAAc;IAAO;IAAQ;IAAQ;IAAS;IACrE;IAAS;IAAY;IAAQ;IAAU;IAAM;IAAQ;IAAY;IACjE;IAAc;IAAa;IAAQ;IAAO;IAAQ;IAAS;IAAW;IACtE;IAAW;IAAc;IAAU;IAAa;IAAW;IAAS;IACpE;IAAc;IAAO;IAAU;IAAY;IAAY;IAAY;IACnE;IAAW;IAAO;IAAW;IAAS;IAAc;IAAc;IAClE;IAAU;IAAa;IAAO;IAAa;IAAQ;IAAY;IAAO;IACtE;IAAc;IAAW;IAAW;IAAO;IAAc;IAAO;IAChE;IAAc;IAAQ;IAAa;IAAa;CACjD;AAGM,MAAM,oBAAoB;OAAI,IAAI,IAAI;CAAuB;AAEpE,uDAAuD;AACvD,MAAM,aAAa,sBAAsB,MAAM,CAAC,CAAC,QAAQ,QACvD,sBAAsB,OAAO,CAAC,YAAY;AAG5C,IAAI,WAAW,MAAM,GAAG,GAAG;IACzB,QAAQ,IAAI,CAAC,wCAAwC;AACvD;AAEA,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,kBAAkB,MAAM,CAAC,eAAe,CAAC;AAoB3E,SAAS,eAAe,SAAiB;IAC9C,wDAAwD;IACxD,MAAM,iBAA4C;QAChD,OAAO;QACP,UAAU;QACV,cAAc;QACd,OAAO;QACP,OAAO;QACP,YAAY;QACZ,SAAS,WAAW,YAAY;IAClC;IAEA,iEAAiE;IACjE,MAAM,kBAA6C;QACjD,YAAY;IACd;IAEA,oDAAoD;IACpD,MAAM,mBAA8C;QAClD,QAAQ;QACR,YAAY;QACZ,OAAO;QACP,GAAG,eAAe;IACpB;IAEA,wEAAwE;IACxE,MAAM,iBAAiB,IAAI,IAAY,EAEtC;IAED,2DAA2D;IAC3D,MAAM,oBAAoB,IAAI,IAAI;QAChC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,YAAY,qBAAqB;KAClC;IAED,6DAA6D;IAC7D,IAAI,eAAe,GAAG,CAAC,YAAY;QACjC,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,WAAW;QAChE,OAAO,MAAa,oDAAoD;IAC1E;IAEA,wCAAwC;IACxC,IAAI,eAAe,CAAC,UAAU,EAAE;QAC9B,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,UAAU,IAAI,EAAE,eAAe,CAAC,UAAU,EAAE;QACpF,OAAO,eAAe,CAAC,UAAU;IACnC;IAEA,IAAI,gBAAgB,CAAC,UAAU,EAAE;QAC/B,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,UAAU,IAAI,EAAE,gBAAgB,CAAC,UAAU,EAAE;QACpF,OAAO,gBAAgB,CAAC,UAAU;IACpC;IAEA,IAAI,cAAc,CAAC,UAAU,EAAE;QAC7B,OAAO,cAAc,CAAC,UAAU;IAClC;IAEA,iDAAiD;IACjD,IAAI,kBAAkB,GAAG,CAAC,YAAY;QACpC,QAAQ,GAAG,CAAC,CAAC,+CAA+C,EAAE,WAAW;IAC3E;IAEA,OAAO,GAAG,UAAU,GAAG,CAAC;AAC1B;AAGO,SAAS,iBAAiB,SAAiB;IAChD,OAAO;AACT;AAGO,SAAS,wBAAwB,KAAiB;IACvD,0DAA0D;IAC1D,8DAA8D;IAE9D,IAAI,MAAM,cAAc,IAAI,MAAM,aAAa,EAAE;QAC/C,MAAM,WAAW,IAAI,KAAK,MAAM,cAAc;QAC9C,MAAM,UAAU,IAAI,KAAK,MAAM,aAAa;QAE5C,8EAA8E;QAC9E,OAAO,UAAU;IACnB;IAEA,qFAAqF;IACrF,wEAAwE;IACxE,IAAI,MAAM,UAAU,IAAI,MAAM,SAAS,IAAI,MAAM,KAAK,GAAG,GAAG;QAC1D,MAAM,aAAa,MAAM,UAAU,GAAG,MAAM,SAAS;QACrD,MAAM,iBAAiB,MAAM,KAAK,GAAG,MAAM,SAAS;QACpD,MAAM,kBAAkB,MAAM,UAAU,GAAG,MAAM,KAAK;QAEtD,4BAA4B;QAC5B,kEAAkE;QAClE,uEAAuE;QACvE,sDAAsD;QACtD,MAAM,sBAAsB,aAAc,MAAM,KAAK,GAAG;QACxD,MAAM,oBAAoB,iBAAiB;QAC3C,MAAM,YAAY,MAAM,KAAK,GAAI,MAAM,UAAU,GAAG;QAEpD,6DAA6D;QAC7D,MAAM,eAAe,CAAC,MAAM,MAAM,CAAC,UAAU,CAAC,KAAK,MAAM,MAAM,CAAC,UAAU,CAAC,MAAM,MAAM,CAAC,MAAM,GAAG,EAAE,IAAI;QACvG,MAAM,cAAc,eAAe,GAAG,aAAa;QAEnD,OAAO,uBAAuB,qBAAqB,aAAa;IAClE;IAEA,OAAO;AACT;AAGO,SAAS,kBAAkB,KAAiB;IACjD,OAAO;QACL,GAAG,KAAK;QACR,eAAe,wBAAwB;IACzC;AACF", "debugId": null}}, {"offset": {"line": 1532, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/lib/holdings-service.ts"], "sourcesContent": ["// Holdings service to manage current holdings across strategies\n\nexport interface Holding {\n  symbol: string;\n  strategy: string;\n  quantity: number;\n  avgPrice: number;\n  currentPrice: number;\n  purchaseDate: Date;\n}\n\nclass HoldingsService {\n  private holdings: Holding[] = [\n    // Sample holdings for demonstration - in real app, this would come from database\n    {\n      symbol: 'RELIANCE',\n      strategy: 'DARVAS_BOX',\n      quantity: 50,\n      avgPrice: 2200.00,\n      currentPrice: 2456.75,\n      purchaseDate: new Date('2024-01-15')\n    },\n    {\n      symbol: 'TCS',\n      strategy: 'DARVAS_BOX',\n      quantity: 25,\n      avgPrice: 3400.00,\n      currentPrice: 3234.50,\n      purchaseDate: new Date('2024-01-20')\n    },\n    {\n      symbol: 'HDFC',\n      strategy: 'WEEKLY_HIGH',\n      quantity: 40,\n      avgPrice: 1600.00,\n      currentPrice: 1678.90,\n      purchaseDate: new Date('2024-02-01')\n    },\n    {\n      symbol: 'INFY',\n      strategy: 'BOH_FILTER',\n      quantity: 60,\n      avgPrice: 1500.00,\n      currentPrice: 1456.80,\n      purchaseDate: new Date('2024-02-10')\n    }\n  ];\n\n  // Get all current holdings\n  getAllHoldings(): Holding[] {\n    return [...this.holdings];\n  }\n\n  // Get holdings for a specific strategy\n  getHoldingsByStrategy(strategy: string): Holding[] {\n    return this.holdings.filter(holding => holding.strategy === strategy);\n  }\n\n  // Check if a stock is currently held in any strategy\n  isStockInHoldings(symbol: string): boolean {\n    return this.holdings.some(holding => holding.symbol === symbol);\n  }\n\n  // Get all unique symbols in holdings\n  getHoldingSymbols(): string[] {\n    return [...new Set(this.holdings.map(holding => holding.symbol))];\n  }\n\n  // Add a new holding\n  addHolding(holding: Omit<Holding, 'purchaseDate'>): void {\n    const existingIndex = this.holdings.findIndex(\n      h => h.symbol === holding.symbol && h.strategy === holding.strategy\n    );\n\n    if (existingIndex >= 0) {\n      // Update existing holding (average price calculation)\n      const existing = this.holdings[existingIndex];\n      const totalQuantity = existing.quantity + holding.quantity;\n      const totalValue = (existing.quantity * existing.avgPrice) + (holding.quantity * holding.avgPrice);\n      \n      this.holdings[existingIndex] = {\n        ...existing,\n        quantity: totalQuantity,\n        avgPrice: totalValue / totalQuantity,\n        currentPrice: holding.currentPrice\n      };\n    } else {\n      // Add new holding\n      this.holdings.push({\n        ...holding,\n        purchaseDate: new Date()\n      });\n    }\n  }\n\n  // Remove a holding\n  removeHolding(symbol: string, strategy: string): void {\n    this.holdings = this.holdings.filter(\n      holding => !(holding.symbol === symbol && holding.strategy === strategy)\n    );\n  }\n\n  // Update current price for a holding\n  updateCurrentPrice(symbol: string, currentPrice: number): void {\n    this.holdings.forEach(holding => {\n      if (holding.symbol === symbol) {\n        holding.currentPrice = currentPrice;\n      }\n    });\n  }\n\n  // Get stocks that were bought above ₹2000 and are still in holdings\n  getStocksAbove2000InHoldings(): string[] {\n    return this.holdings\n      .filter(holding => holding.avgPrice > 2000 || holding.currentPrice > 2000)\n      .map(holding => holding.symbol);\n  }\n\n  // Check if a stock should be eligible for trading\n  // (CMP < 2000 OR currently in holdings)\n  isStockEligibleForTrading(symbol: string, currentPrice: number): boolean {\n    return currentPrice < 2000 || this.isStockInHoldings(symbol);\n  }\n}\n\nexport const holdingsService = new HoldingsService();\n"], "names": [], "mappings": "AAAA,gEAAgE;;;;AAWhE,MAAM;IACI,WAAsB;QAC5B,iFAAiF;QACjF;YACE,QAAQ;YACR,UAAU;YACV,UAAU;YACV,UAAU;YACV,cAAc;YACd,cAAc,IAAI,KAAK;QACzB;QACA;YACE,QAAQ;YACR,UAAU;YACV,UAAU;YACV,UAAU;YACV,cAAc;YACd,cAAc,IAAI,KAAK;QACzB;QACA;YACE,QAAQ;YACR,UAAU;YACV,UAAU;YACV,UAAU;YACV,cAAc;YACd,cAAc,IAAI,KAAK;QACzB;QACA;YACE,QAAQ;YACR,UAAU;YACV,UAAU;YACV,UAAU;YACV,cAAc;YACd,cAAc,IAAI,KAAK;QACzB;KACD,CAAC;IAEF,2BAA2B;IAC3B,iBAA4B;QAC1B,OAAO;eAAI,IAAI,CAAC,QAAQ;SAAC;IAC3B;IAEA,uCAAuC;IACvC,sBAAsB,QAAgB,EAAa;QACjD,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;IAC9D;IAEA,qDAAqD;IACrD,kBAAkB,MAAc,EAAW;QACzC,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA,UAAW,QAAQ,MAAM,KAAK;IAC1D;IAEA,qCAAqC;IACrC,oBAA8B;QAC5B,OAAO;eAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA,UAAW,QAAQ,MAAM;SAAG;IACnE;IAEA,oBAAoB;IACpB,WAAW,OAAsC,EAAQ;QACvD,MAAM,gBAAgB,IAAI,CAAC,QAAQ,CAAC,SAAS,CAC3C,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM,IAAI,EAAE,QAAQ,KAAK,QAAQ,QAAQ;QAGrE,IAAI,iBAAiB,GAAG;YACtB,sDAAsD;YACtD,MAAM,WAAW,IAAI,CAAC,QAAQ,CAAC,cAAc;YAC7C,MAAM,gBAAgB,SAAS,QAAQ,GAAG,QAAQ,QAAQ;YAC1D,MAAM,aAAa,AAAC,SAAS,QAAQ,GAAG,SAAS,QAAQ,GAAK,QAAQ,QAAQ,GAAG,QAAQ,QAAQ;YAEjG,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG;gBAC7B,GAAG,QAAQ;gBACX,UAAU;gBACV,UAAU,aAAa;gBACvB,cAAc,QAAQ,YAAY;YACpC;QACF,OAAO;YACL,kBAAkB;YAClB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACjB,GAAG,OAAO;gBACV,cAAc,IAAI;YACpB;QACF;IACF;IAEA,mBAAmB;IACnB,cAAc,MAAc,EAAE,QAAgB,EAAQ;QACpD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAClC,CAAA,UAAW,CAAC,CAAC,QAAQ,MAAM,KAAK,UAAU,QAAQ,QAAQ,KAAK,QAAQ;IAE3E;IAEA,qCAAqC;IACrC,mBAAmB,MAAc,EAAE,YAAoB,EAAQ;QAC7D,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;YACpB,IAAI,QAAQ,MAAM,KAAK,QAAQ;gBAC7B,QAAQ,YAAY,GAAG;YACzB;QACF;IACF;IAEA,oEAAoE;IACpE,+BAAyC;QACvC,OAAO,IAAI,CAAC,QAAQ,CACjB,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,GAAG,QAAQ,QAAQ,YAAY,GAAG,MACpE,GAAG,CAAC,CAAA,UAAW,QAAQ,MAAM;IAClC;IAEA,kDAAkD;IAClD,wCAAwC;IACxC,0BAA0B,MAAc,EAAE,YAAoB,EAAW;QACvE,OAAO,eAAe,QAAQ,IAAI,CAAC,iBAAiB,CAAC;IACvD;AACF;AAEO,MAAM,kBAAkB,IAAI", "debugId": null}}, {"offset": {"line": 1641, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/lib/weekly-high-signal-detector.ts"], "sourcesContent": ["// Weekly High Signal Detection Service\n// Monitors for new Weekly High Signals and triggers automatic GTT order creation\n\nimport { yahooFinanceService } from './yahoo-finance';\nimport { stockNamesService } from './stock-names-service';\nimport { NIFTY_200_SYMBOLS, getYahooSymbol, addBOHEligibility, NiftyStock } from './nifty-stocks';\nimport { holdingsService } from './holdings-service';\n\nexport interface WeeklyHighSignal {\n  symbol: string;\n  name: string;\n  currentPrice: number;\n  lastWeekHighest: number;\n  suggestedBuyPrice: number;\n  percentDifference: number;\n  suggestedGTTQuantity: number;\n  isBOHEligible: boolean;\n  inHoldings: boolean;\n  signalStrength: 'STRONG' | 'MODERATE' | 'WEAK';\n  detectedAt: Date;\n  volume: number;\n  avgVolume: number;\n  volumeRatio: number;\n}\n\nexport interface SignalDetectionConfig {\n  enabled: boolean;\n  pollingIntervalMinutes: number;\n  marketStartHour: number; // 9 for 9:15 AM\n  marketEndHour: number;   // 15 for 3:30 PM\n  strongSignalThreshold: number; // % within weekly high\n  moderateSignalThreshold: number;\n  minVolumeRatio: number;\n  maxInvestmentPerStock: number;\n  investmentPerOrder: number;\n}\n\nclass WeeklyHighSignalDetector {\n  private config: SignalDetectionConfig = {\n    enabled: true,\n    pollingIntervalMinutes: 5,\n    marketStartHour: 9,\n    marketEndHour: 15,\n    strongSignalThreshold: 2.0, // Within 2% of weekly high\n    moderateSignalThreshold: 5.0, // Within 5% of weekly high\n    minVolumeRatio: 1.2, // 20% above average volume\n    maxInvestmentPerStock: 10000,\n    investmentPerOrder: 2000\n  };\n\n  private isRunning = false;\n  private pollingInterval: NodeJS.Timeout | null = null;\n  private lastSignals: Map<string, WeeklyHighSignal> = new Map();\n  private signalListeners = new Set<(signals: WeeklyHighSignal[]) => void>();\n  private newSignalListeners = new Set<(signal: WeeklyHighSignal) => void>();\n\n  constructor() {\n    console.log('📡 Weekly High Signal Detector initialized');\n  }\n\n  // Configuration management\n  updateConfig(newConfig: Partial<SignalDetectionConfig>): void {\n    this.config = { ...this.config, ...newConfig };\n    console.log('⚙️ Signal detector config updated:', this.config);\n    \n    if (this.isRunning) {\n      this.stop();\n      this.start();\n    }\n  }\n\n  getConfig(): SignalDetectionConfig {\n    return { ...this.config };\n  }\n\n  // Check if market is currently open\n  private isMarketOpen(): boolean {\n    const now = new Date();\n    const currentHour = now.getHours();\n    const currentMinute = now.getMinutes();\n    \n    // Market hours: 9:15 AM to 3:30 PM (Monday to Friday)\n    const isWeekday = now.getDay() >= 1 && now.getDay() <= 5;\n    const isAfterStart = currentHour > this.config.marketStartHour || \n                       (currentHour === this.config.marketStartHour && currentMinute >= 15);\n    const isBeforeEnd = currentHour < this.config.marketEndHour || \n                       (currentHour === this.config.marketEndHour && currentMinute <= 30);\n    \n    return isWeekday && isAfterStart && isBeforeEnd;\n  }\n\n  // Generate mock OHLC data for weekly high calculation\n  private generateOHLCData(currentPrice: number) {\n    const data = [];\n    let price = currentPrice * 0.95; // Start 5% below current price\n    \n    for (let i = 0; i < 7; i++) {\n      const open = price;\n      const high = price * (1 + Math.random() * 0.08); // Up to 8% higher\n      const low = price * (1 - Math.random() * 0.05); // Up to 5% lower\n      const close = low + Math.random() * (high - low);\n      \n      data.push({ open, high, low, close });\n      price = close;\n    }\n    \n    return data;\n  }\n\n  // Calculate signal strength based on proximity to weekly high and volume\n  private calculateSignalStrength(\n    currentPrice: number, \n    weeklyHigh: number, \n    volumeRatio: number\n  ): 'STRONG' | 'MODERATE' | 'WEAK' {\n    const percentFromHigh = Math.abs((currentPrice - weeklyHigh) / weeklyHigh * 100);\n    \n    if (percentFromHigh <= this.config.strongSignalThreshold && volumeRatio >= this.config.minVolumeRatio) {\n      return 'STRONG';\n    } else if (percentFromHigh <= this.config.moderateSignalThreshold) {\n      return 'MODERATE';\n    } else {\n      return 'WEAK';\n    }\n  }\n\n  // Scan for Weekly High Signals\n  async scanForSignals(): Promise<WeeklyHighSignal[]> {\n    try {\n      console.log('🔍 Scanning for Weekly High Signals...');\n\n      // Fetch all Nifty 200 stocks with current prices\n      const yahooSymbols = NIFTY_200_SYMBOLS.map(getYahooSymbol);\n      const quotes = await yahooFinanceService.getMultipleQuotesWithCachedNames(yahooSymbols);\n      \n      console.log(`📊 Got quotes for ${quotes.length}/${yahooSymbols.length} symbols`);\n\n      // Get current holdings\n      const holdingSymbols = holdingsService.getAllHoldings().map(h => h.symbol);\n\n      // Process each stock for signal detection\n      const signals: WeeklyHighSignal[] = [];\n      \n      for (let i = 0; i < NIFTY_200_SYMBOLS.length; i++) {\n        const nseSymbol = NIFTY_200_SYMBOLS[i];\n        const yahooSymbol = getYahooSymbol(nseSymbol);\n        const quote = quotes.find(q => q.symbol === yahooSymbol);\n        \n        if (!quote || quote.price <= 0) continue;\n\n        const price = quote.price;\n        const inHoldings = holdingSymbols.includes(nseSymbol);\n        const isEligible = holdingsService.isStockEligibleForTrading(nseSymbol, price);\n\n        // Create stock object for BOH eligibility check\n        const stock: NiftyStock = {\n          symbol: nseSymbol,\n          name: quote.name,\n          price,\n          change: quote.change || 0,\n          changePercent: quote.changePercent || 0,\n          volume: quote.volume || 0,\n          marketCap: quote.marketCap,\n          high52Week: quote.high52Week,\n          low52Week: quote.low52Week,\n          high52WeekDate: quote.high52WeekDate,\n          low52WeekDate: quote.low52WeekDate,\n          isEligible,\n          inHoldings\n        };\n\n        const stockWithBOH = addBOHEligibility(stock);\n        \n        // Only process BOH eligible stocks\n        if (!stockWithBOH.isBOHEligible) continue;\n\n        // Calculate weekly high data\n        const ohlcData = this.generateOHLCData(price);\n        const lastWeekHighest = Math.max(...ohlcData.map(d => d.high));\n        const suggestedBuyPrice = lastWeekHighest + 0.05;\n        const percentDifference = ((price - suggestedBuyPrice) / suggestedBuyPrice) * 100;\n        const suggestedGTTQuantity = Math.floor(this.config.investmentPerOrder / suggestedBuyPrice);\n\n        // Calculate volume metrics\n        const avgVolume = quote.avgVolume || quote.volume || 1;\n        const volumeRatio = quote.volume / avgVolume;\n\n        // Calculate signal strength\n        const signalStrength = this.calculateSignalStrength(price, lastWeekHighest, volumeRatio);\n\n        // Only include signals that are MODERATE or STRONG\n        if (signalStrength === 'WEAK') continue;\n\n        const signal: WeeklyHighSignal = {\n          symbol: nseSymbol,\n          name: quote.name,\n          currentPrice: price,\n          lastWeekHighest,\n          suggestedBuyPrice,\n          percentDifference,\n          suggestedGTTQuantity,\n          isBOHEligible: true,\n          inHoldings,\n          signalStrength,\n          detectedAt: new Date(),\n          volume: quote.volume || 0,\n          avgVolume,\n          volumeRatio\n        };\n\n        signals.push(signal);\n      }\n\n      console.log(`✅ Found ${signals.length} Weekly High Signals`);\n      return signals;\n\n    } catch (error) {\n      console.error('❌ Error scanning for signals:', error);\n      return [];\n    }\n  }\n\n  // Detect new signals by comparing with previous scan\n  private detectNewSignals(currentSignals: WeeklyHighSignal[]): WeeklyHighSignal[] {\n    const newSignals: WeeklyHighSignal[] = [];\n    \n    for (const signal of currentSignals) {\n      const previousSignal = this.lastSignals.get(signal.symbol);\n      \n      // Consider it a new signal if:\n      // 1. Stock wasn't in previous signals, OR\n      // 2. Signal strength improved (MODERATE -> STRONG), OR\n      // 3. Price moved significantly closer to weekly high\n      const isNewSignal = !previousSignal ||\n                         (signal.signalStrength === 'STRONG' && previousSignal.signalStrength !== 'STRONG') ||\n                         (Math.abs(signal.percentDifference) < Math.abs(previousSignal.percentDifference) - 1);\n      \n      if (isNewSignal) {\n        newSignals.push(signal);\n        console.log(`🆕 New signal detected: ${signal.symbol} (${signal.signalStrength})`);\n      }\n    }\n    \n    return newSignals;\n  }\n\n  // Main polling function\n  private async poll(): Promise<void> {\n    if (!this.config.enabled) {\n      console.log('⏸️ Signal detection is disabled');\n      return;\n    }\n\n    if (!this.isMarketOpen()) {\n      console.log('🕐 Market is closed, skipping signal detection');\n      return;\n    }\n\n    try {\n      console.log('🔄 Polling for Weekly High Signals...');\n      \n      const currentSignals = await this.scanForSignals();\n      const newSignals = this.detectNewSignals(currentSignals);\n      \n      // Update last signals cache\n      this.lastSignals.clear();\n      currentSignals.forEach(signal => {\n        this.lastSignals.set(signal.symbol, signal);\n      });\n      \n      // Notify listeners about all current signals\n      this.signalListeners.forEach(listener => {\n        try {\n          listener(currentSignals);\n        } catch (error) {\n          console.error('❌ Error in signal listener:', error);\n        }\n      });\n      \n      // Notify listeners about new signals\n      if (newSignals.length > 0) {\n        console.log(`🚨 ${newSignals.length} new signals detected!`);\n        \n        newSignals.forEach(signal => {\n          this.newSignalListeners.forEach(listener => {\n            try {\n              listener(signal);\n            } catch (error) {\n              console.error('❌ Error in new signal listener:', error);\n            }\n          });\n        });\n      }\n      \n    } catch (error) {\n      console.error('❌ Error in signal polling:', error);\n    }\n  }\n\n  // Start the detection service\n  start(): void {\n    if (this.isRunning) {\n      console.log('⚠️ Signal detector is already running');\n      return;\n    }\n\n    console.log(`🚀 Starting Weekly High Signal Detector (polling every ${this.config.pollingIntervalMinutes} minutes)`);\n    \n    this.isRunning = true;\n    \n    // Initial scan\n    this.poll();\n    \n    // Set up polling interval\n    this.pollingInterval = setInterval(() => {\n      this.poll();\n    }, this.config.pollingIntervalMinutes * 60 * 1000);\n  }\n\n  // Stop the detection service\n  stop(): void {\n    if (!this.isRunning) {\n      console.log('⚠️ Signal detector is not running');\n      return;\n    }\n\n    console.log('⏹️ Stopping Weekly High Signal Detector');\n    \n    this.isRunning = false;\n    \n    if (this.pollingInterval) {\n      clearInterval(this.pollingInterval);\n      this.pollingInterval = null;\n    }\n  }\n\n  // Add listener for all signals\n  addSignalListener(listener: (signals: WeeklyHighSignal[]) => void): void {\n    this.signalListeners.add(listener);\n  }\n\n  // Remove listener for all signals\n  removeSignalListener(listener: (signals: WeeklyHighSignal[]) => void): void {\n    this.signalListeners.delete(listener);\n  }\n\n  // Add listener for new signals only\n  addNewSignalListener(listener: (signal: WeeklyHighSignal) => void): void {\n    this.newSignalListeners.add(listener);\n  }\n\n  // Remove listener for new signals\n  removeNewSignalListener(listener: (signal: WeeklyHighSignal) => void): void {\n    this.newSignalListeners.delete(listener);\n  }\n\n  // Get current status\n  getStatus() {\n    return {\n      isRunning: this.isRunning,\n      isMarketOpen: this.isMarketOpen(),\n      config: this.config,\n      lastSignalCount: this.lastSignals.size,\n      listenerCount: this.signalListeners.size + this.newSignalListeners.size\n    };\n  }\n\n  // Manual trigger for testing\n  async triggerManualScan(): Promise<WeeklyHighSignal[]> {\n    console.log('🔧 Manual signal scan triggered');\n    return await this.scanForSignals();\n  }\n}\n\n// Export singleton instance\nexport const weeklyHighSignalDetector = new WeeklyHighSignalDetector();\n\n// Auto-start the service when imported\nif (typeof window !== 'undefined') {\n  // Only run in browser environment\n  console.log('📡 Auto-starting Weekly High Signal Detector...');\n\n  weeklyHighSignalDetector.start();\n  console.log('✅ Weekly High Signal Detector auto-started successfully');\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,iFAAiF;;;;AAEjF;AAEA;AACA;;;;AA+BA,MAAM;IACI,SAAgC;QACtC,SAAS;QACT,wBAAwB;QACxB,iBAAiB;QACjB,eAAe;QACf,uBAAuB;QACvB,yBAAyB;QACzB,gBAAgB;QAChB,uBAAuB;QACvB,oBAAoB;IACtB,EAAE;IAEM,YAAY,MAAM;IAClB,kBAAyC,KAAK;IAC9C,cAA6C,IAAI,MAAM;IACvD,kBAAkB,IAAI,MAA6C;IACnE,qBAAqB,IAAI,MAA0C;IAE3E,aAAc;QACZ,QAAQ,GAAG,CAAC;IACd;IAEA,2BAA2B;IAC3B,aAAa,SAAyC,EAAQ;QAC5D,IAAI,CAAC,MAAM,GAAG;YAAE,GAAG,IAAI,CAAC,MAAM;YAAE,GAAG,SAAS;QAAC;QAC7C,QAAQ,GAAG,CAAC,sCAAsC,IAAI,CAAC,MAAM;QAE7D,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,IAAI;YACT,IAAI,CAAC,KAAK;QACZ;IACF;IAEA,YAAmC;QACjC,OAAO;YAAE,GAAG,IAAI,CAAC,MAAM;QAAC;IAC1B;IAEA,oCAAoC;IAC5B,eAAwB;QAC9B,MAAM,MAAM,IAAI;QAChB,MAAM,cAAc,IAAI,QAAQ;QAChC,MAAM,gBAAgB,IAAI,UAAU;QAEpC,sDAAsD;QACtD,MAAM,YAAY,IAAI,MAAM,MAAM,KAAK,IAAI,MAAM,MAAM;QACvD,MAAM,eAAe,cAAc,IAAI,CAAC,MAAM,CAAC,eAAe,IAC1C,gBAAgB,IAAI,CAAC,MAAM,CAAC,eAAe,IAAI,iBAAiB;QACpF,MAAM,cAAc,cAAc,IAAI,CAAC,MAAM,CAAC,aAAa,IACvC,gBAAgB,IAAI,CAAC,MAAM,CAAC,aAAa,IAAI,iBAAiB;QAElF,OAAO,aAAa,gBAAgB;IACtC;IAEA,sDAAsD;IAC9C,iBAAiB,YAAoB,EAAE;QAC7C,MAAM,OAAO,EAAE;QACf,IAAI,QAAQ,eAAe,MAAM,+BAA+B;QAEhE,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YAC1B,MAAM,OAAO;YACb,MAAM,OAAO,QAAQ,CAAC,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG,kBAAkB;YACnE,MAAM,MAAM,QAAQ,CAAC,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG,iBAAiB;YACjE,MAAM,QAAQ,MAAM,KAAK,MAAM,KAAK,CAAC,OAAO,GAAG;YAE/C,KAAK,IAAI,CAAC;gBAAE;gBAAM;gBAAM;gBAAK;YAAM;YACnC,QAAQ;QACV;QAEA,OAAO;IACT;IAEA,yEAAyE;IACjE,wBACN,YAAoB,EACpB,UAAkB,EAClB,WAAmB,EACa;QAChC,MAAM,kBAAkB,KAAK,GAAG,CAAC,CAAC,eAAe,UAAU,IAAI,aAAa;QAE5E,IAAI,mBAAmB,IAAI,CAAC,MAAM,CAAC,qBAAqB,IAAI,eAAe,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE;YACrG,OAAO;QACT,OAAO,IAAI,mBAAmB,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE;YACjE,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF;IAEA,+BAA+B;IAC/B,MAAM,iBAA8C;QAClD,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,iDAAiD;YACjD,MAAM,eAAe,+HAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,+HAAA,CAAA,iBAAc;YACzD,MAAM,SAAS,MAAM,gIAAA,CAAA,sBAAmB,CAAC,gCAAgC,CAAC;YAE1E,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,OAAO,MAAM,CAAC,CAAC,EAAE,aAAa,MAAM,CAAC,QAAQ,CAAC;YAE/E,uBAAuB;YACvB,MAAM,iBAAiB,mIAAA,CAAA,kBAAe,CAAC,cAAc,GAAG,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;YAEzE,0CAA0C;YAC1C,MAAM,UAA8B,EAAE;YAEtC,IAAK,IAAI,IAAI,GAAG,IAAI,+HAAA,CAAA,oBAAiB,CAAC,MAAM,EAAE,IAAK;gBACjD,MAAM,YAAY,+HAAA,CAAA,oBAAiB,CAAC,EAAE;gBACtC,MAAM,cAAc,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE;gBACnC,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;gBAE5C,IAAI,CAAC,SAAS,MAAM,KAAK,IAAI,GAAG;gBAEhC,MAAM,QAAQ,MAAM,KAAK;gBACzB,MAAM,aAAa,eAAe,QAAQ,CAAC;gBAC3C,MAAM,aAAa,mIAAA,CAAA,kBAAe,CAAC,yBAAyB,CAAC,WAAW;gBAExE,gDAAgD;gBAChD,MAAM,QAAoB;oBACxB,QAAQ;oBACR,MAAM,MAAM,IAAI;oBAChB;oBACA,QAAQ,MAAM,MAAM,IAAI;oBACxB,eAAe,MAAM,aAAa,IAAI;oBACtC,QAAQ,MAAM,MAAM,IAAI;oBACxB,WAAW,MAAM,SAAS;oBAC1B,YAAY,MAAM,UAAU;oBAC5B,WAAW,MAAM,SAAS;oBAC1B,gBAAgB,MAAM,cAAc;oBACpC,eAAe,MAAM,aAAa;oBAClC;oBACA;gBACF;gBAEA,MAAM,eAAe,CAAA,GAAA,+HAAA,CAAA,oBAAiB,AAAD,EAAE;gBAEvC,mCAAmC;gBACnC,IAAI,CAAC,aAAa,aAAa,EAAE;gBAEjC,6BAA6B;gBAC7B,MAAM,WAAW,IAAI,CAAC,gBAAgB,CAAC;gBACvC,MAAM,kBAAkB,KAAK,GAAG,IAAI,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;gBAC5D,MAAM,oBAAoB,kBAAkB;gBAC5C,MAAM,oBAAoB,AAAC,CAAC,QAAQ,iBAAiB,IAAI,oBAAqB;gBAC9E,MAAM,uBAAuB,KAAK,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,GAAG;gBAEzE,2BAA2B;gBAC3B,MAAM,YAAY,MAAM,SAAS,IAAI,MAAM,MAAM,IAAI;gBACrD,MAAM,cAAc,MAAM,MAAM,GAAG;gBAEnC,4BAA4B;gBAC5B,MAAM,iBAAiB,IAAI,CAAC,uBAAuB,CAAC,OAAO,iBAAiB;gBAE5E,mDAAmD;gBACnD,IAAI,mBAAmB,QAAQ;gBAE/B,MAAM,SAA2B;oBAC/B,QAAQ;oBACR,MAAM,MAAM,IAAI;oBAChB,cAAc;oBACd;oBACA;oBACA;oBACA;oBACA,eAAe;oBACf;oBACA;oBACA,YAAY,IAAI;oBAChB,QAAQ,MAAM,MAAM,IAAI;oBACxB;oBACA;gBACF;gBAEA,QAAQ,IAAI,CAAC;YACf;YAEA,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,oBAAoB,CAAC;YAC3D,OAAO;QAET,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO,EAAE;QACX;IACF;IAEA,qDAAqD;IAC7C,iBAAiB,cAAkC,EAAsB;QAC/E,MAAM,aAAiC,EAAE;QAEzC,KAAK,MAAM,UAAU,eAAgB;YACnC,MAAM,iBAAiB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,MAAM;YAEzD,+BAA+B;YAC/B,0CAA0C;YAC1C,uDAAuD;YACvD,qDAAqD;YACrD,MAAM,cAAc,CAAC,kBACD,OAAO,cAAc,KAAK,YAAY,eAAe,cAAc,KAAK,YACxE,KAAK,GAAG,CAAC,OAAO,iBAAiB,IAAI,KAAK,GAAG,CAAC,eAAe,iBAAiB,IAAI;YAEtG,IAAI,aAAa;gBACf,WAAW,IAAI,CAAC;gBAChB,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,OAAO,MAAM,CAAC,EAAE,EAAE,OAAO,cAAc,CAAC,CAAC,CAAC;YACnF;QACF;QAEA,OAAO;IACT;IAEA,wBAAwB;IACxB,MAAc,OAAsB;QAClC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;YACxB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI;YACxB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,iBAAiB,MAAM,IAAI,CAAC,cAAc;YAChD,MAAM,aAAa,IAAI,CAAC,gBAAgB,CAAC;YAEzC,4BAA4B;YAC5B,IAAI,CAAC,WAAW,CAAC,KAAK;YACtB,eAAe,OAAO,CAAC,CAAA;gBACrB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,MAAM,EAAE;YACtC;YAEA,6CAA6C;YAC7C,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAA;gBAC3B,IAAI;oBACF,SAAS;gBACX,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC/C;YACF;YAEA,qCAAqC;YACrC,IAAI,WAAW,MAAM,GAAG,GAAG;gBACzB,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,WAAW,MAAM,CAAC,sBAAsB,CAAC;gBAE3D,WAAW,OAAO,CAAC,CAAA;oBACjB,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAA;wBAC9B,IAAI;4BACF,SAAS;wBACX,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,mCAAmC;wBACnD;oBACF;gBACF;YACF;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,8BAA8B;IAC9B,QAAc;QACZ,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,uDAAuD,EAAE,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,SAAS,CAAC;QAEnH,IAAI,CAAC,SAAS,GAAG;QAEjB,eAAe;QACf,IAAI,CAAC,IAAI;QAET,0BAA0B;QAC1B,IAAI,CAAC,eAAe,GAAG,YAAY;YACjC,IAAI,CAAC,IAAI;QACX,GAAG,IAAI,CAAC,MAAM,CAAC,sBAAsB,GAAG,KAAK;IAC/C;IAEA,6BAA6B;IAC7B,OAAa;QACX,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,QAAQ,GAAG,CAAC;QAEZ,IAAI,CAAC,SAAS,GAAG;QAEjB,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,cAAc,IAAI,CAAC,eAAe;YAClC,IAAI,CAAC,eAAe,GAAG;QACzB;IACF;IAEA,+BAA+B;IAC/B,kBAAkB,QAA+C,EAAQ;QACvE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;IAC3B;IAEA,kCAAkC;IAClC,qBAAqB,QAA+C,EAAQ;QAC1E,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;IAC9B;IAEA,oCAAoC;IACpC,qBAAqB,QAA4C,EAAQ;QACvE,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC;IAC9B;IAEA,kCAAkC;IAClC,wBAAwB,QAA4C,EAAQ;QAC1E,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;IACjC;IAEA,qBAAqB;IACrB,YAAY;QACV,OAAO;YACL,WAAW,IAAI,CAAC,SAAS;YACzB,cAAc,IAAI,CAAC,YAAY;YAC/B,QAAQ,IAAI,CAAC,MAAM;YACnB,iBAAiB,IAAI,CAAC,WAAW,CAAC,IAAI;YACtC,eAAe,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI;QACzE;IACF;IAEA,6BAA6B;IAC7B,MAAM,oBAAiD;QACrD,QAAQ,GAAG,CAAC;QACZ,OAAO,MAAM,IAAI,CAAC,cAAc;IAClC;AACF;AAGO,MAAM,2BAA2B,IAAI;AAE5C,uCAAuC;AACvC", "debugId": null}}, {"offset": {"line": 1936, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/lib/automatic-gtt-service.ts"], "sourcesContent": ["// Automatic GTT Order Creation Service\n// Creates GTT buy orders automatically when new Weekly High Signals are detected\n\nimport { weeklyHighSignalDetector, WeeklyHighSignal } from './weekly-high-signal-detector';\n\nexport interface AutoGTTOrder {\n  id: string;\n  symbol: string;\n  name: string;\n  orderType: 'BUY' | 'SELL';\n  triggerPrice: number;\n  quantity: number;\n  status: 'PENDING' | 'TRIGGERED' | 'CANCELLED' | 'EXPIRED';\n  createdAt: Date;\n  source: 'SIGNAL' | 'HOLDING' | 'SALE';\n  signalStrength?: 'STRONG' | 'MODERATE' | 'WEAK';\n  autoCreated: boolean;\n  originalSignal?: WeeklyHighSignal;\n}\n\nexport interface AutoGTTConfig {\n  enabled: boolean;\n  maxOrdersPerDay: number;\n  maxInvestmentPerStock: number;\n  investmentPerOrder: number;\n  minSignalStrength: 'STRONG' | 'MODERATE' | 'WEAK';\n  requireVolumeConfirmation: boolean;\n  onlyDuringMarketHours: boolean;\n}\n\nclass AutomaticGTTService {\n  private config: AutoGTTConfig = {\n    enabled: true,\n    maxOrdersPerDay: 20,\n    maxInvestmentPerStock: 10000,\n    investmentPerOrder: 2000,\n    minSignalStrength: 'MODERATE',\n    requireVolumeConfirmation: true,\n    onlyDuringMarketHours: true\n  };\n\n  private orders: AutoGTTOrder[] = [];\n  private dailyOrderCount = 0;\n  private lastResetDate = new Date().toDateString();\n  private orderListeners = new Set<(order: AutoGTTOrder) => void>();\n  private isInitialized = false;\n\n  constructor() {\n    console.log('🤖 Automatic GTT Service initialized');\n  }\n\n  // Initialize the service and start listening for signals\n  async initialize(): Promise<void> {\n    if (this.isInitialized) {\n      console.log('⚠️ Automatic GTT Service already initialized');\n      return;\n    }\n\n    console.log('🚀 Initializing Automatic GTT Service...');\n\n    // Load existing orders from localStorage or API\n    await this.loadExistingOrders();\n\n    // Start listening for new signals\n    weeklyHighSignalDetector.addNewSignalListener(this.handleNewSignal.bind(this));\n\n    // Reset daily counter if it's a new day\n    this.resetDailyCounterIfNeeded();\n\n    this.isInitialized = true;\n    console.log('✅ Automatic GTT Service initialized successfully');\n  }\n\n  // Configuration management\n  updateConfig(newConfig: Partial<AutoGTTConfig>): void {\n    this.config = { ...this.config, ...newConfig };\n    console.log('⚙️ Auto GTT config updated:', this.config);\n  }\n\n  getConfig(): AutoGTTConfig {\n    return { ...this.config };\n  }\n\n  // Load existing orders (from localStorage for now, can be replaced with API)\n  private async loadExistingOrders(): Promise<void> {\n    try {\n      const stored = localStorage.getItem('autoGTTOrders');\n      if (stored) {\n        const parsedOrders = JSON.parse(stored);\n        this.orders = parsedOrders.map((order: any) => ({\n          ...order,\n          createdAt: new Date(order.createdAt)\n        }));\n        console.log(`📂 Loaded ${this.orders.length} existing auto GTT orders`);\n      }\n\n      // Load daily counter\n      const storedCounter = localStorage.getItem('autoGTTDailyCount');\n      const storedDate = localStorage.getItem('autoGTTLastResetDate');\n      \n      if (storedCounter && storedDate === new Date().toDateString()) {\n        this.dailyOrderCount = parseInt(storedCounter, 10);\n      }\n    } catch (error) {\n      console.error('❌ Error loading existing orders:', error);\n    }\n  }\n\n  // Save orders to localStorage\n  private saveOrders(): void {\n    try {\n      localStorage.setItem('autoGTTOrders', JSON.stringify(this.orders));\n      localStorage.setItem('autoGTTDailyCount', this.dailyOrderCount.toString());\n      localStorage.setItem('autoGTTLastResetDate', this.lastResetDate);\n    } catch (error) {\n      console.error('❌ Error saving orders:', error);\n    }\n  }\n\n  // Reset daily counter if it's a new day\n  private resetDailyCounterIfNeeded(): void {\n    const today = new Date().toDateString();\n    if (this.lastResetDate !== today) {\n      this.dailyOrderCount = 0;\n      this.lastResetDate = today;\n      console.log('🔄 Daily order counter reset for new day');\n    }\n  }\n\n  // Check if we can create a new order\n  private canCreateOrder(signal: WeeklyHighSignal): { canCreate: boolean; reason?: string } {\n    // Check if service is enabled\n    if (!this.config.enabled) {\n      return { canCreate: false, reason: 'Automatic GTT service is disabled' };\n    }\n\n    // Check market hours if required\n    if (this.config.onlyDuringMarketHours) {\n      const now = new Date();\n      const currentHour = now.getHours();\n      const currentMinute = now.getMinutes();\n      const isWeekday = now.getDay() >= 1 && now.getDay() <= 5;\n      const isMarketOpen = isWeekday && \n                          (currentHour > 9 || (currentHour === 9 && currentMinute >= 15)) &&\n                          (currentHour < 15 || (currentHour === 15 && currentMinute <= 30));\n      \n      if (!isMarketOpen) {\n        return { canCreate: false, reason: 'Market is closed' };\n      }\n    }\n\n    // Check daily limit\n    this.resetDailyCounterIfNeeded();\n    if (this.dailyOrderCount >= this.config.maxOrdersPerDay) {\n      return { canCreate: false, reason: 'Daily order limit reached' };\n    }\n\n    // Check signal strength\n    const strengthOrder = { 'WEAK': 1, 'MODERATE': 2, 'STRONG': 3 };\n    if (strengthOrder[signal.signalStrength] < strengthOrder[this.config.minSignalStrength]) {\n      return { canCreate: false, reason: `Signal strength ${signal.signalStrength} below minimum ${this.config.minSignalStrength}` };\n    }\n\n    // Check volume confirmation if required\n    if (this.config.requireVolumeConfirmation && signal.volumeRatio < 1.2) {\n      return { canCreate: false, reason: 'Insufficient volume confirmation' };\n    }\n\n    // Check for existing pending orders for this stock\n    const existingOrder = this.orders.find(order => \n      order.symbol === signal.symbol && \n      order.status === 'PENDING' && \n      order.source === 'SIGNAL'\n    );\n    \n    if (existingOrder) {\n      return { canCreate: false, reason: 'Pending order already exists for this stock' };\n    }\n\n    // Check investment limits\n    const existingInvestment = this.orders\n      .filter(order => order.symbol === signal.symbol && order.status === 'PENDING')\n      .reduce((sum, order) => sum + (order.triggerPrice * order.quantity), 0);\n    \n    const newInvestment = signal.suggestedBuyPrice * signal.suggestedGTTQuantity;\n    \n    if (existingInvestment + newInvestment > this.config.maxInvestmentPerStock) {\n      return { canCreate: false, reason: 'Would exceed maximum investment per stock' };\n    }\n\n    // Check if quantity is valid\n    if (signal.suggestedGTTQuantity <= 0) {\n      return { canCreate: false, reason: 'Invalid quantity calculated' };\n    }\n\n    // Check if trigger price is reasonable\n    if (signal.suggestedBuyPrice <= 0 || signal.suggestedBuyPrice > 5000) {\n      return { canCreate: false, reason: 'Invalid trigger price' };\n    }\n\n    return { canCreate: true };\n  }\n\n  // Handle new signal detection\n  private async handleNewSignal(signal: WeeklyHighSignal): Promise<void> {\n    console.log(`🔔 New signal received: ${signal.symbol} (${signal.signalStrength})`);\n\n    const validation = this.canCreateOrder(signal);\n    \n    if (!validation.canCreate) {\n      console.log(`⏭️ Skipping order creation for ${signal.symbol}: ${validation.reason}`);\n      return;\n    }\n\n    try {\n      await this.createAutoGTTOrder(signal);\n    } catch (error) {\n      console.error(`❌ Failed to create auto GTT order for ${signal.symbol}:`, error);\n    }\n  }\n\n  // Create automatic GTT order\n  private async createAutoGTTOrder(signal: WeeklyHighSignal): Promise<AutoGTTOrder> {\n    console.log(`🤖 Creating automatic GTT order for ${signal.symbol}...`);\n\n    const order: AutoGTTOrder = {\n      id: `auto_gtt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n      symbol: signal.symbol,\n      name: signal.name,\n      orderType: 'BUY',\n      triggerPrice: signal.suggestedBuyPrice,\n      quantity: signal.suggestedGTTQuantity,\n      status: 'PENDING',\n      createdAt: new Date(),\n      source: 'SIGNAL',\n      signalStrength: signal.signalStrength,\n      autoCreated: true,\n      originalSignal: signal\n    };\n\n    // Add to orders list\n    this.orders.push(order);\n    this.dailyOrderCount++;\n\n    // Save to storage\n    this.saveOrders();\n\n    // Log the creation\n    console.log(`✅ Auto GTT order created: ${order.symbol} - Trigger: ₹${order.triggerPrice.toFixed(2)}, Qty: ${order.quantity}`);\n    console.log(`📊 Daily orders: ${this.dailyOrderCount}/${this.config.maxOrdersPerDay}`);\n\n    // Notify listeners\n    this.orderListeners.forEach(listener => {\n      try {\n        listener(order);\n      } catch (error) {\n        console.error('❌ Error in order listener:', error);\n      }\n    });\n\n    return order;\n  }\n\n  // Get all orders\n  getAllOrders(): AutoGTTOrder[] {\n    return [...this.orders];\n  }\n\n  // Get orders by status\n  getOrdersByStatus(status: AutoGTTOrder['status']): AutoGTTOrder[] {\n    return this.orders.filter(order => order.status === status);\n  }\n\n  // Get orders by source\n  getOrdersBySource(source: AutoGTTOrder['source']): AutoGTTOrder[] {\n    return this.orders.filter(order => order.source === source);\n  }\n\n  // Cancel an order\n  cancelOrder(orderId: string): boolean {\n    const order = this.orders.find(o => o.id === orderId);\n    if (order && order.status === 'PENDING') {\n      order.status = 'CANCELLED';\n      this.saveOrders();\n      console.log(`❌ Order cancelled: ${order.symbol} (${orderId})`);\n      return true;\n    }\n    return false;\n  }\n\n  // Update order status (for external triggers)\n  updateOrderStatus(orderId: string, status: AutoGTTOrder['status']): boolean {\n    const order = this.orders.find(o => o.id === orderId);\n    if (order) {\n      order.status = status;\n      this.saveOrders();\n      console.log(`🔄 Order status updated: ${order.symbol} -> ${status}`);\n      return true;\n    }\n    return false;\n  }\n\n  // Add order listener\n  addOrderListener(listener: (order: AutoGTTOrder) => void): void {\n    this.orderListeners.add(listener);\n  }\n\n  // Remove order listener\n  removeOrderListener(listener: (order: AutoGTTOrder) => void): void {\n    this.orderListeners.delete(listener);\n  }\n\n  // Get service statistics\n  getStatistics() {\n    const today = new Date().toDateString();\n    const todayOrders = this.orders.filter(order => \n      order.createdAt.toDateString() === today && order.autoCreated\n    );\n\n    return {\n      totalOrders: this.orders.length,\n      autoCreatedOrders: this.orders.filter(o => o.autoCreated).length,\n      todayOrders: todayOrders.length,\n      dailyLimit: this.config.maxOrdersPerDay,\n      pendingOrders: this.orders.filter(o => o.status === 'PENDING').length,\n      triggeredOrders: this.orders.filter(o => o.status === 'TRIGGERED').length,\n      cancelledOrders: this.orders.filter(o => o.status === 'CANCELLED').length,\n      isEnabled: this.config.enabled,\n      isInitialized: this.isInitialized\n    };\n  }\n\n  // Manual trigger for testing\n  async testCreateOrder(symbol: string): Promise<AutoGTTOrder | null> {\n    console.log(`🧪 Test order creation for ${symbol}`);\n    \n    // Get current signals\n    const signals = await weeklyHighSignalDetector.triggerManualScan();\n    const signal = signals.find(s => s.symbol === symbol);\n    \n    if (!signal) {\n      console.log(`❌ No signal found for ${symbol}`);\n      return null;\n    }\n\n    return await this.createAutoGTTOrder(signal);\n  }\n\n  // Start the service\n  async start(): Promise<void> {\n    if (!this.isInitialized) {\n      await this.initialize();\n    }\n\n    // Start the signal detector if not already running\n    weeklyHighSignalDetector.start();\n    \n    console.log('🚀 Automatic GTT Service started');\n  }\n\n  // Stop the service\n  stop(): void {\n    weeklyHighSignalDetector.stop();\n    console.log('⏹️ Automatic GTT Service stopped');\n  }\n}\n\n// Export singleton instance\nexport const automaticGTTService = new AutomaticGTTService();\n\n// Auto-start the service when imported\nif (typeof window !== 'undefined') {\n  // Only run in browser environment\n  console.log('🤖 Auto-starting Automatic GTT Service...');\n\n  automaticGTTService.start().then(() => {\n    console.log('✅ Automatic GTT Service auto-started successfully');\n  }).catch((error) => {\n    console.error('❌ Automatic GTT Service auto-start failed:', error);\n  });\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,iFAAiF;;;;AAEjF;;AA2BA,MAAM;IACI,SAAwB;QAC9B,SAAS;QACT,iBAAiB;QACjB,uBAAuB;QACvB,oBAAoB;QACpB,mBAAmB;QACnB,2BAA2B;QAC3B,uBAAuB;IACzB,EAAE;IAEM,SAAyB,EAAE,CAAC;IAC5B,kBAAkB,EAAE;IACpB,gBAAgB,IAAI,OAAO,YAAY,GAAG;IAC1C,iBAAiB,IAAI,MAAqC;IAC1D,gBAAgB,MAAM;IAE9B,aAAc;QACZ,QAAQ,GAAG,CAAC;IACd;IAEA,yDAAyD;IACzD,MAAM,aAA4B;QAChC,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,QAAQ,GAAG,CAAC;QAEZ,gDAAgD;QAChD,MAAM,IAAI,CAAC,kBAAkB;QAE7B,kCAAkC;QAClC,oJAAA,CAAA,2BAAwB,CAAC,oBAAoB,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI;QAE5E,wCAAwC;QACxC,IAAI,CAAC,yBAAyB;QAE9B,IAAI,CAAC,aAAa,GAAG;QACrB,QAAQ,GAAG,CAAC;IACd;IAEA,2BAA2B;IAC3B,aAAa,SAAiC,EAAQ;QACpD,IAAI,CAAC,MAAM,GAAG;YAAE,GAAG,IAAI,CAAC,MAAM;YAAE,GAAG,SAAS;QAAC;QAC7C,QAAQ,GAAG,CAAC,+BAA+B,IAAI,CAAC,MAAM;IACxD;IAEA,YAA2B;QACzB,OAAO;YAAE,GAAG,IAAI,CAAC,MAAM;QAAC;IAC1B;IAEA,6EAA6E;IAC7E,MAAc,qBAAoC;QAChD,IAAI;YACF,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,IAAI,QAAQ;gBACV,MAAM,eAAe,KAAK,KAAK,CAAC;gBAChC,IAAI,CAAC,MAAM,GAAG,aAAa,GAAG,CAAC,CAAC,QAAe,CAAC;wBAC9C,GAAG,KAAK;wBACR,WAAW,IAAI,KAAK,MAAM,SAAS;oBACrC,CAAC;gBACD,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,yBAAyB,CAAC;YACxE;YAEA,qBAAqB;YACrB,MAAM,gBAAgB,aAAa,OAAO,CAAC;YAC3C,MAAM,aAAa,aAAa,OAAO,CAAC;YAExC,IAAI,iBAAiB,eAAe,IAAI,OAAO,YAAY,IAAI;gBAC7D,IAAI,CAAC,eAAe,GAAG,SAAS,eAAe;YACjD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF;IAEA,8BAA8B;IACtB,aAAmB;QACzB,IAAI;YACF,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC,IAAI,CAAC,MAAM;YAChE,aAAa,OAAO,CAAC,qBAAqB,IAAI,CAAC,eAAe,CAAC,QAAQ;YACvE,aAAa,OAAO,CAAC,wBAAwB,IAAI,CAAC,aAAa;QACjE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,wCAAwC;IAChC,4BAAkC;QACxC,MAAM,QAAQ,IAAI,OAAO,YAAY;QACrC,IAAI,IAAI,CAAC,aAAa,KAAK,OAAO;YAChC,IAAI,CAAC,eAAe,GAAG;YACvB,IAAI,CAAC,aAAa,GAAG;YACrB,QAAQ,GAAG,CAAC;QACd;IACF;IAEA,qCAAqC;IAC7B,eAAe,MAAwB,EAA2C;QACxF,8BAA8B;QAC9B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;YACxB,OAAO;gBAAE,WAAW;gBAAO,QAAQ;YAAoC;QACzE;QAEA,iCAAiC;QACjC,IAAI,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE;YACrC,MAAM,MAAM,IAAI;YAChB,MAAM,cAAc,IAAI,QAAQ;YAChC,MAAM,gBAAgB,IAAI,UAAU;YACpC,MAAM,YAAY,IAAI,MAAM,MAAM,KAAK,IAAI,MAAM,MAAM;YACvD,MAAM,eAAe,aACD,CAAC,cAAc,KAAM,gBAAgB,KAAK,iBAAiB,EAAG,KAC9D,CAAC,cAAc,MAAO,gBAAgB,MAAM,iBAAiB,EAAG;YAEpF,IAAI,CAAC,cAAc;gBACjB,OAAO;oBAAE,WAAW;oBAAO,QAAQ;gBAAmB;YACxD;QACF;QAEA,oBAAoB;QACpB,IAAI,CAAC,yBAAyB;QAC9B,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE;YACvD,OAAO;gBAAE,WAAW;gBAAO,QAAQ;YAA4B;QACjE;QAEA,wBAAwB;QACxB,MAAM,gBAAgB;YAAE,QAAQ;YAAG,YAAY;YAAG,UAAU;QAAE;QAC9D,IAAI,aAAa,CAAC,OAAO,cAAc,CAAC,GAAG,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,EAAE;YACvF,OAAO;gBAAE,WAAW;gBAAO,QAAQ,CAAC,gBAAgB,EAAE,OAAO,cAAc,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE;YAAC;QAC/H;QAEA,wCAAwC;QACxC,IAAI,IAAI,CAAC,MAAM,CAAC,yBAAyB,IAAI,OAAO,WAAW,GAAG,KAAK;YACrE,OAAO;gBAAE,WAAW;gBAAO,QAAQ;YAAmC;QACxE;QAEA,mDAAmD;QACnD,MAAM,gBAAgB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,QACrC,MAAM,MAAM,KAAK,OAAO,MAAM,IAC9B,MAAM,MAAM,KAAK,aACjB,MAAM,MAAM,KAAK;QAGnB,IAAI,eAAe;YACjB,OAAO;gBAAE,WAAW;gBAAO,QAAQ;YAA8C;QACnF;QAEA,0BAA0B;QAC1B,MAAM,qBAAqB,IAAI,CAAC,MAAM,CACnC,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK,OAAO,MAAM,IAAI,MAAM,MAAM,KAAK,WACnE,MAAM,CAAC,CAAC,KAAK,QAAU,MAAO,MAAM,YAAY,GAAG,MAAM,QAAQ,EAAG;QAEvE,MAAM,gBAAgB,OAAO,iBAAiB,GAAG,OAAO,oBAAoB;QAE5E,IAAI,qBAAqB,gBAAgB,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE;YAC1E,OAAO;gBAAE,WAAW;gBAAO,QAAQ;YAA4C;QACjF;QAEA,6BAA6B;QAC7B,IAAI,OAAO,oBAAoB,IAAI,GAAG;YACpC,OAAO;gBAAE,WAAW;gBAAO,QAAQ;YAA8B;QACnE;QAEA,uCAAuC;QACvC,IAAI,OAAO,iBAAiB,IAAI,KAAK,OAAO,iBAAiB,GAAG,MAAM;YACpE,OAAO;gBAAE,WAAW;gBAAO,QAAQ;YAAwB;QAC7D;QAEA,OAAO;YAAE,WAAW;QAAK;IAC3B;IAEA,8BAA8B;IAC9B,MAAc,gBAAgB,MAAwB,EAAiB;QACrE,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,OAAO,MAAM,CAAC,EAAE,EAAE,OAAO,cAAc,CAAC,CAAC,CAAC;QAEjF,MAAM,aAAa,IAAI,CAAC,cAAc,CAAC;QAEvC,IAAI,CAAC,WAAW,SAAS,EAAE;YACzB,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,OAAO,MAAM,CAAC,EAAE,EAAE,WAAW,MAAM,EAAE;YACnF;QACF;QAEA,IAAI;YACF,MAAM,IAAI,CAAC,kBAAkB,CAAC;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,sCAAsC,EAAE,OAAO,MAAM,CAAC,CAAC,CAAC,EAAE;QAC3E;IACF;IAEA,6BAA6B;IAC7B,MAAc,mBAAmB,MAAwB,EAAyB;QAChF,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,OAAO,MAAM,CAAC,GAAG,CAAC;QAErE,MAAM,QAAsB;YAC1B,IAAI,CAAC,SAAS,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YACvE,QAAQ,OAAO,MAAM;YACrB,MAAM,OAAO,IAAI;YACjB,WAAW;YACX,cAAc,OAAO,iBAAiB;YACtC,UAAU,OAAO,oBAAoB;YACrC,QAAQ;YACR,WAAW,IAAI;YACf,QAAQ;YACR,gBAAgB,OAAO,cAAc;YACrC,aAAa;YACb,gBAAgB;QAClB;QAEA,qBAAqB;QACrB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACjB,IAAI,CAAC,eAAe;QAEpB,kBAAkB;QAClB,IAAI,CAAC,UAAU;QAEf,mBAAmB;QACnB,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,MAAM,MAAM,CAAC,aAAa,EAAE,MAAM,YAAY,CAAC,OAAO,CAAC,GAAG,OAAO,EAAE,MAAM,QAAQ,EAAE;QAC5H,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE;QAErF,mBAAmB;QACnB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAA;YAC1B,IAAI;gBACF,SAAS;YACX,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8BAA8B;YAC9C;QACF;QAEA,OAAO;IACT;IAEA,iBAAiB;IACjB,eAA+B;QAC7B,OAAO;eAAI,IAAI,CAAC,MAAM;SAAC;IACzB;IAEA,uBAAuB;IACvB,kBAAkB,MAA8B,EAAkB;QAChE,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;IACtD;IAEA,uBAAuB;IACvB,kBAAkB,MAA8B,EAAkB;QAChE,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;IACtD;IAEA,kBAAkB;IAClB,YAAY,OAAe,EAAW;QACpC,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC7C,IAAI,SAAS,MAAM,MAAM,KAAK,WAAW;YACvC,MAAM,MAAM,GAAG;YACf,IAAI,CAAC,UAAU;YACf,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;YAC7D,OAAO;QACT;QACA,OAAO;IACT;IAEA,8CAA8C;IAC9C,kBAAkB,OAAe,EAAE,MAA8B,EAAW;QAC1E,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC7C,IAAI,OAAO;YACT,MAAM,MAAM,GAAG;YACf,IAAI,CAAC,UAAU;YACf,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,MAAM,MAAM,CAAC,IAAI,EAAE,QAAQ;YACnE,OAAO;QACT;QACA,OAAO;IACT;IAEA,qBAAqB;IACrB,iBAAiB,QAAuC,EAAQ;QAC9D,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;IAC1B;IAEA,wBAAwB;IACxB,oBAAoB,QAAuC,EAAQ;QACjE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;IAC7B;IAEA,yBAAyB;IACzB,gBAAgB;QACd,MAAM,QAAQ,IAAI,OAAO,YAAY;QACrC,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,QACrC,MAAM,SAAS,CAAC,YAAY,OAAO,SAAS,MAAM,WAAW;QAG/D,OAAO;YACL,aAAa,IAAI,CAAC,MAAM,CAAC,MAAM;YAC/B,mBAAmB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,MAAM;YAChE,aAAa,YAAY,MAAM;YAC/B,YAAY,IAAI,CAAC,MAAM,CAAC,eAAe;YACvC,eAAe,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;YACrE,iBAAiB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;YACzE,iBAAiB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;YACzE,WAAW,IAAI,CAAC,MAAM,CAAC,OAAO;YAC9B,eAAe,IAAI,CAAC,aAAa;QACnC;IACF;IAEA,6BAA6B;IAC7B,MAAM,gBAAgB,MAAc,EAAgC;QAClE,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,QAAQ;QAElD,sBAAsB;QACtB,MAAM,UAAU,MAAM,oJAAA,CAAA,2BAAwB,CAAC,iBAAiB;QAChE,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;QAE9C,IAAI,CAAC,QAAQ;YACX,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,QAAQ;YAC7C,OAAO;QACT;QAEA,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC;IACvC;IAEA,oBAAoB;IACpB,MAAM,QAAuB;QAC3B,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB,MAAM,IAAI,CAAC,UAAU;QACvB;QAEA,mDAAmD;QACnD,oJAAA,CAAA,2BAAwB,CAAC,KAAK;QAE9B,QAAQ,GAAG,CAAC;IACd;IAEA,mBAAmB;IACnB,OAAa;QACX,oJAAA,CAAA,2BAAwB,CAAC,IAAI;QAC7B,QAAQ,GAAG,CAAC;IACd;AACF;AAGO,MAAM,sBAAsB,IAAI;AAEvC,uCAAuC;AACvC", "debugId": null}}, {"offset": {"line": 2260, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/lib/central-data-manager.ts"], "sourcesContent": ["// Central Data Management Service\n// Manages all stock data, caching, and real-time synchronization across pages\n\nimport { yahooFinanceService } from './yahoo-finance';\nimport { stockNamesService } from './stock-names-service';\nimport { NIFTY_200_SYMBOLS, getYahooSymbol, addBOHEligibility, NiftyStock } from './nifty-stocks';\nimport { holdingsService } from './holdings-service';\nimport { weeklyHighSignalDetector, WeeklyHighSignal } from './weekly-high-signal-detector';\nimport { automaticGTTService, AutoGTTOrder } from './automatic-gtt-service';\n\nexport interface StockDataCache {\n  nifty200Stocks: NiftyStock[];\n  bohEligibleStocks: NiftyStock[];\n  weeklyHighSignals: WeeklyHighSignal[];\n  gttOrders: AutoGTTOrder[];\n  lastUpdated: {\n    nifty200: Date | null;\n    bohEligible: Date | null;\n    weeklyHighSignals: Date | null;\n    gttOrders: Date | null;\n  };\n  isLoading: {\n    nifty200: boolean;\n    bohEligible: boolean;\n    weeklyHighSignals: boolean;\n    gttOrders: boolean;\n  };\n}\n\nexport interface DataUpdateConfig {\n  nifty200UpdateInterval: number; // seconds\n  bohEligibleUpdateInterval: number; // seconds\n  weeklyHighSignalsUpdateInterval: number; // seconds\n  gttOrdersUpdateInterval: number; // seconds\n  marketStartHour: number;\n  marketEndHour: number;\n  enableRealTimeUpdates: boolean;\n}\n\ntype DataType = 'nifty200' | 'bohEligible' | 'weeklyHighSignals' | 'gttOrders';\ntype DataListener<T> = (data: T, timestamp: Date) => void;\n\nclass CentralDataManager {\n  private cache: StockDataCache = {\n    nifty200Stocks: [],\n    bohEligibleStocks: [],\n    weeklyHighSignals: [],\n    gttOrders: [],\n    lastUpdated: {\n      nifty200: null,\n      bohEligible: null,\n      weeklyHighSignals: null,\n      gttOrders: null\n    },\n    isLoading: {\n      nifty200: false,\n      bohEligible: false,\n      weeklyHighSignals: false,\n      gttOrders: false\n    }\n  };\n\n  private config: DataUpdateConfig = {\n    nifty200UpdateInterval: 30, // 30 seconds during market hours\n    bohEligibleUpdateInterval: 60, // 1 minute\n    weeklyHighSignalsUpdateInterval: 300, // 5 minutes\n    gttOrdersUpdateInterval: 30, // 30 seconds\n    marketStartHour: 9,\n    marketEndHour: 15,\n    enableRealTimeUpdates: true\n  };\n\n  private intervals: Map<DataType, NodeJS.Timeout> = new Map();\n  private listeners: Map<DataType, Set<DataListener<any>>> = new Map();\n  private isInitialized = false;\n  private isRunning = false;\n\n  constructor() {\n    console.log('📊 Central Data Manager initialized');\n    this.initializeListeners();\n  }\n\n  private initializeListeners(): void {\n    this.listeners.set('nifty200', new Set());\n    this.listeners.set('bohEligible', new Set());\n    this.listeners.set('weeklyHighSignals', new Set());\n    this.listeners.set('gttOrders', new Set());\n  }\n\n  // Configuration management\n  updateConfig(newConfig: Partial<DataUpdateConfig>): void {\n    this.config = { ...this.config, ...newConfig };\n    console.log('⚙️ Central Data Manager config updated:', this.config);\n    \n    if (this.isRunning) {\n      this.stop();\n      this.start();\n    }\n  }\n\n  getConfig(): DataUpdateConfig {\n    return { ...this.config };\n  }\n\n  // Market hours detection\n  private isMarketOpen(): boolean {\n    const now = new Date();\n    const currentHour = now.getHours();\n    const currentMinute = now.getMinutes();\n    \n    const isWeekday = now.getDay() >= 1 && now.getDay() <= 5;\n    const isAfterStart = currentHour > this.config.marketStartHour || \n                       (currentHour === this.config.marketStartHour && currentMinute >= 15);\n    const isBeforeEnd = currentHour < this.config.marketEndHour || \n                       (currentHour === this.config.marketEndHour && currentMinute <= 30);\n    \n    return isWeekday && isAfterStart && isBeforeEnd;\n  }\n\n  // Data loading methods\n  private async loadNifty200Stocks(): Promise<void> {\n    if (this.cache.isLoading.nifty200) return;\n    \n    this.cache.isLoading.nifty200 = true;\n    console.log('📈 Loading Nifty 200 stocks...');\n\n    try {\n      const yahooSymbols = NIFTY_200_SYMBOLS.map(getYahooSymbol);\n      const quotes = await yahooFinanceService.getMultipleQuotesWithCachedNames(yahooSymbols);\n      \n      const holdingSymbols = holdingsService.getAllHoldings().map(h => h.symbol);\n\n      const processedStocks: NiftyStock[] = NIFTY_200_SYMBOLS.map(nseSymbol => {\n        const yahooSymbol = getYahooSymbol(nseSymbol);\n        const quote = quotes.find(q => q.symbol === yahooSymbol);\n\n        const price = quote?.price || 0;\n        const inHoldings = holdingSymbols.includes(nseSymbol);\n        const isEligible = holdingsService.isStockEligibleForTrading(nseSymbol, price);\n\n        const stock: NiftyStock = {\n          symbol: nseSymbol,\n          name: quote?.name || nseSymbol,\n          price,\n          change: quote?.change || 0,\n          changePercent: quote?.changePercent || 0,\n          volume: quote?.volume || 0,\n          marketCap: quote?.marketCap,\n          high52Week: quote?.high52Week,\n          low52Week: quote?.low52Week,\n          high52WeekDate: quote?.high52WeekDate,\n          low52WeekDate: quote?.low52WeekDate,\n          isEligible,\n          inHoldings\n        };\n\n        return addBOHEligibility(stock);\n      });\n\n      this.cache.nifty200Stocks = processedStocks;\n      this.cache.lastUpdated.nifty200 = new Date();\n      \n      console.log(`✅ Loaded ${processedStocks.length} Nifty 200 stocks`);\n      this.notifyListeners('nifty200', processedStocks);\n\n    } catch (error) {\n      console.error('❌ Error loading Nifty 200 stocks:', error);\n    } finally {\n      this.cache.isLoading.nifty200 = false;\n    }\n  }\n\n  private async loadBOHEligibleStocks(): Promise<void> {\n    if (this.cache.isLoading.bohEligible) return;\n    \n    this.cache.isLoading.bohEligible = true;\n    console.log('🔍 Loading BOH eligible stocks...');\n\n    try {\n      // Use cached Nifty 200 data if available and recent\n      let stocks = this.cache.nifty200Stocks;\n      \n      if (stocks.length === 0 || !this.cache.lastUpdated.nifty200 || \n          Date.now() - this.cache.lastUpdated.nifty200.getTime() > 60000) {\n        await this.loadNifty200Stocks();\n        stocks = this.cache.nifty200Stocks;\n      }\n\n      const bohEligibleStocks = stocks.filter(stock => stock.isBOHEligible);\n      \n      this.cache.bohEligibleStocks = bohEligibleStocks;\n      this.cache.lastUpdated.bohEligible = new Date();\n      \n      console.log(`✅ Loaded ${bohEligibleStocks.length} BOH eligible stocks`);\n      this.notifyListeners('bohEligible', bohEligibleStocks);\n\n    } catch (error) {\n      console.error('❌ Error loading BOH eligible stocks:', error);\n    } finally {\n      this.cache.isLoading.bohEligible = false;\n    }\n  }\n\n  private async loadWeeklyHighSignals(): Promise<void> {\n    if (this.cache.isLoading.weeklyHighSignals) return;\n    \n    this.cache.isLoading.weeklyHighSignals = true;\n    console.log('📊 Loading Weekly High Signals...');\n\n    try {\n      const signals = await weeklyHighSignalDetector.triggerManualScan();\n      \n      this.cache.weeklyHighSignals = signals;\n      this.cache.lastUpdated.weeklyHighSignals = new Date();\n      \n      console.log(`✅ Loaded ${signals.length} Weekly High Signals`);\n      this.notifyListeners('weeklyHighSignals', signals);\n\n    } catch (error) {\n      console.error('❌ Error loading Weekly High Signals:', error);\n    } finally {\n      this.cache.isLoading.weeklyHighSignals = false;\n    }\n  }\n\n  private async loadGTTOrders(): Promise<void> {\n    if (this.cache.isLoading.gttOrders) return;\n    \n    this.cache.isLoading.gttOrders = true;\n    console.log('📋 Loading GTT orders...');\n\n    try {\n      const orders = automaticGTTService.getAllOrders();\n      \n      this.cache.gttOrders = orders;\n      this.cache.lastUpdated.gttOrders = new Date();\n      \n      console.log(`✅ Loaded ${orders.length} GTT orders`);\n      this.notifyListeners('gttOrders', orders);\n\n    } catch (error) {\n      console.error('❌ Error loading GTT orders:', error);\n    } finally {\n      this.cache.isLoading.gttOrders = false;\n    }\n  }\n\n  // Listener management\n  addListener<T>(dataType: DataType, listener: DataListener<T>): void {\n    const listeners = this.listeners.get(dataType);\n    if (listeners) {\n      listeners.add(listener);\n      console.log(`👂 Added listener for ${dataType}`);\n    }\n  }\n\n  removeListener<T>(dataType: DataType, listener: DataListener<T>): void {\n    const listeners = this.listeners.get(dataType);\n    if (listeners) {\n      listeners.delete(listener);\n      console.log(`🔇 Removed listener for ${dataType}`);\n    }\n  }\n\n  private notifyListeners<T>(dataType: DataType, data: T): void {\n    const listeners = this.listeners.get(dataType);\n    if (listeners) {\n      const timestamp = new Date();\n      listeners.forEach(listener => {\n        try {\n          listener(data, timestamp);\n        } catch (error) {\n          console.error(`❌ Error in ${dataType} listener:`, error);\n        }\n      });\n    }\n  }\n\n  // Public data access methods\n  getNifty200Stocks(): NiftyStock[] {\n    return [...this.cache.nifty200Stocks];\n  }\n\n  getBOHEligibleStocks(): NiftyStock[] {\n    return [...this.cache.bohEligibleStocks];\n  }\n\n  getWeeklyHighSignals(): WeeklyHighSignal[] {\n    return [...this.cache.weeklyHighSignals];\n  }\n\n  getGTTOrders(): AutoGTTOrder[] {\n    return [...this.cache.gttOrders];\n  }\n\n  getLastUpdated(dataType: DataType): Date | null {\n    return this.cache.lastUpdated[dataType];\n  }\n\n  isDataLoading(dataType: DataType): boolean {\n    return this.cache.isLoading[dataType];\n  }\n\n  // Cache management\n  getCacheStatus() {\n    return {\n      nifty200Count: this.cache.nifty200Stocks.length,\n      bohEligibleCount: this.cache.bohEligibleStocks.length,\n      weeklyHighSignalsCount: this.cache.weeklyHighSignals.length,\n      gttOrdersCount: this.cache.gttOrders.length,\n      lastUpdated: { ...this.cache.lastUpdated },\n      isLoading: { ...this.cache.isLoading },\n      listenerCounts: {\n        nifty200: this.listeners.get('nifty200')?.size || 0,\n        bohEligible: this.listeners.get('bohEligible')?.size || 0,\n        weeklyHighSignals: this.listeners.get('weeklyHighSignals')?.size || 0,\n        gttOrders: this.listeners.get('gttOrders')?.size || 0\n      }\n    };\n  }\n\n  // Force refresh methods\n  async refreshNifty200(): Promise<void> {\n    await this.loadNifty200Stocks();\n  }\n\n  async refreshBOHEligible(): Promise<void> {\n    await this.loadBOHEligibleStocks();\n  }\n\n  async refreshWeeklyHighSignals(): Promise<void> {\n    await this.loadWeeklyHighSignals();\n  }\n\n  async refreshGTTOrders(): Promise<void> {\n    await this.loadGTTOrders();\n  }\n\n  async refreshAll(): Promise<void> {\n    console.log('🔄 Refreshing all data...');\n    await Promise.all([\n      this.loadNifty200Stocks(),\n      this.loadBOHEligibleStocks(),\n      this.loadWeeklyHighSignals(),\n      this.loadGTTOrders()\n    ]);\n    console.log('✅ All data refreshed');\n  }\n\n  // Service lifecycle\n  async initialize(): Promise<void> {\n    if (this.isInitialized) {\n      console.log('⚠️ Central Data Manager already initialized');\n      return;\n    }\n\n    console.log('🚀 Initializing Central Data Manager...');\n    \n    // Load initial data\n    await this.refreshAll();\n    \n    this.isInitialized = true;\n    console.log('✅ Central Data Manager initialized successfully');\n  }\n\n  start(): void {\n    if (this.isRunning) {\n      console.log('⚠️ Central Data Manager already running');\n      return;\n    }\n\n    console.log('🚀 Starting Central Data Manager background updates...');\n    \n    // Set up periodic updates\n    this.intervals.set('nifty200', setInterval(() => {\n      if (this.isMarketOpen() || !this.config.enableRealTimeUpdates) {\n        this.loadNifty200Stocks();\n      }\n    }, this.config.nifty200UpdateInterval * 1000));\n\n    this.intervals.set('bohEligible', setInterval(() => {\n      this.loadBOHEligibleStocks();\n    }, this.config.bohEligibleUpdateInterval * 1000));\n\n    this.intervals.set('weeklyHighSignals', setInterval(() => {\n      if (this.isMarketOpen() || !this.config.enableRealTimeUpdates) {\n        this.loadWeeklyHighSignals();\n      }\n    }, this.config.weeklyHighSignalsUpdateInterval * 1000));\n\n    this.intervals.set('gttOrders', setInterval(() => {\n      this.loadGTTOrders();\n    }, this.config.gttOrdersUpdateInterval * 1000));\n\n    this.isRunning = true;\n    console.log('✅ Central Data Manager background updates started');\n  }\n\n  stop(): void {\n    if (!this.isRunning) {\n      console.log('⚠️ Central Data Manager not running');\n      return;\n    }\n\n    console.log('⏹️ Stopping Central Data Manager background updates...');\n    \n    this.intervals.forEach((interval, dataType) => {\n      clearInterval(interval);\n      console.log(`⏹️ Stopped ${dataType} updates`);\n    });\n    \n    this.intervals.clear();\n    this.isRunning = false;\n    console.log('✅ Central Data Manager background updates stopped');\n  }\n\n  getStatus() {\n    return {\n      isInitialized: this.isInitialized,\n      isRunning: this.isRunning,\n      isMarketOpen: this.isMarketOpen(),\n      config: this.config,\n      cache: this.getCacheStatus()\n    };\n  }\n}\n\n// Export singleton instance\nexport const centralDataManager = new CentralDataManager();\n\n// Auto-initialize and start the service when imported\nif (typeof window !== 'undefined') {\n  // Only run in browser environment\n  console.log('🚀 Auto-initializing Central Data Manager...');\n\n  centralDataManager.initialize().then(() => {\n    centralDataManager.start();\n    console.log('✅ Central Data Manager auto-started successfully');\n  }).catch((error) => {\n    console.error('❌ Central Data Manager auto-start failed:', error);\n  });\n}\n"], "names": [], "mappings": "AAAA,kCAAkC;AAClC,8EAA8E;;;;AAE9E;AAEA;AACA;AACA;AACA;;;;;;AAkCA,MAAM;IACI,QAAwB;QAC9B,gBAAgB,EAAE;QAClB,mBAAmB,EAAE;QACrB,mBAAmB,EAAE;QACrB,WAAW,EAAE;QACb,aAAa;YACX,UAAU;YACV,aAAa;YACb,mBAAmB;YACnB,WAAW;QACb;QACA,WAAW;YACT,UAAU;YACV,aAAa;YACb,mBAAmB;YACnB,WAAW;QACb;IACF,EAAE;IAEM,SAA2B;QACjC,wBAAwB;QACxB,2BAA2B;QAC3B,iCAAiC;QACjC,yBAAyB;QACzB,iBAAiB;QACjB,eAAe;QACf,uBAAuB;IACzB,EAAE;IAEM,YAA2C,IAAI,MAAM;IACrD,YAAmD,IAAI,MAAM;IAC7D,gBAAgB,MAAM;IACtB,YAAY,MAAM;IAE1B,aAAc;QACZ,QAAQ,GAAG,CAAC;QACZ,IAAI,CAAC,mBAAmB;IAC1B;IAEQ,sBAA4B;QAClC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,IAAI;QACnC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,eAAe,IAAI;QACtC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,qBAAqB,IAAI;QAC5C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,aAAa,IAAI;IACtC;IAEA,2BAA2B;IAC3B,aAAa,SAAoC,EAAQ;QACvD,IAAI,CAAC,MAAM,GAAG;YAAE,GAAG,IAAI,CAAC,MAAM;YAAE,GAAG,SAAS;QAAC;QAC7C,QAAQ,GAAG,CAAC,2CAA2C,IAAI,CAAC,MAAM;QAElE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,IAAI;YACT,IAAI,CAAC,KAAK;QACZ;IACF;IAEA,YAA8B;QAC5B,OAAO;YAAE,GAAG,IAAI,CAAC,MAAM;QAAC;IAC1B;IAEA,yBAAyB;IACjB,eAAwB;QAC9B,MAAM,MAAM,IAAI;QAChB,MAAM,cAAc,IAAI,QAAQ;QAChC,MAAM,gBAAgB,IAAI,UAAU;QAEpC,MAAM,YAAY,IAAI,MAAM,MAAM,KAAK,IAAI,MAAM,MAAM;QACvD,MAAM,eAAe,cAAc,IAAI,CAAC,MAAM,CAAC,eAAe,IAC1C,gBAAgB,IAAI,CAAC,MAAM,CAAC,eAAe,IAAI,iBAAiB;QACpF,MAAM,cAAc,cAAc,IAAI,CAAC,MAAM,CAAC,aAAa,IACvC,gBAAgB,IAAI,CAAC,MAAM,CAAC,aAAa,IAAI,iBAAiB;QAElF,OAAO,aAAa,gBAAgB;IACtC;IAEA,uBAAuB;IACvB,MAAc,qBAAoC;QAChD,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,EAAE;QAEnC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,GAAG;QAChC,QAAQ,GAAG,CAAC;QAEZ,IAAI;YACF,MAAM,eAAe,+HAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,+HAAA,CAAA,iBAAc;YACzD,MAAM,SAAS,MAAM,gIAAA,CAAA,sBAAmB,CAAC,gCAAgC,CAAC;YAE1E,MAAM,iBAAiB,mIAAA,CAAA,kBAAe,CAAC,cAAc,GAAG,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;YAEzE,MAAM,kBAAgC,+HAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,CAAA;gBAC1D,MAAM,cAAc,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE;gBACnC,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;gBAE5C,MAAM,QAAQ,OAAO,SAAS;gBAC9B,MAAM,aAAa,eAAe,QAAQ,CAAC;gBAC3C,MAAM,aAAa,mIAAA,CAAA,kBAAe,CAAC,yBAAyB,CAAC,WAAW;gBAExE,MAAM,QAAoB;oBACxB,QAAQ;oBACR,MAAM,OAAO,QAAQ;oBACrB;oBACA,QAAQ,OAAO,UAAU;oBACzB,eAAe,OAAO,iBAAiB;oBACvC,QAAQ,OAAO,UAAU;oBACzB,WAAW,OAAO;oBAClB,YAAY,OAAO;oBACnB,WAAW,OAAO;oBAClB,gBAAgB,OAAO;oBACvB,eAAe,OAAO;oBACtB;oBACA;gBACF;gBAEA,OAAO,CAAA,GAAA,+HAAA,CAAA,oBAAiB,AAAD,EAAE;YAC3B;YAEA,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG;YAC5B,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,GAAG,IAAI;YAEtC,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,gBAAgB,MAAM,CAAC,iBAAiB,CAAC;YACjE,IAAI,CAAC,eAAe,CAAC,YAAY;QAEnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD,SAAU;YACR,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,GAAG;QAClC;IACF;IAEA,MAAc,wBAAuC;QACnD,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,WAAW,EAAE;QAEtC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,WAAW,GAAG;QACnC,QAAQ,GAAG,CAAC;QAEZ,IAAI;YACF,oDAAoD;YACpD,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC,cAAc;YAEtC,IAAI,OAAO,MAAM,KAAK,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,IACvD,KAAK,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,KAAK,OAAO;gBAClE,MAAM,IAAI,CAAC,kBAAkB;gBAC7B,SAAS,IAAI,CAAC,KAAK,CAAC,cAAc;YACpC;YAEA,MAAM,oBAAoB,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,aAAa;YAEpE,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG;YAC/B,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,WAAW,GAAG,IAAI;YAEzC,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,kBAAkB,MAAM,CAAC,oBAAoB,CAAC;YACtE,IAAI,CAAC,eAAe,CAAC,eAAe;QAEtC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;QACxD,SAAU;YACR,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,WAAW,GAAG;QACrC;IACF;IAEA,MAAc,wBAAuC;QACnD,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,iBAAiB,EAAE;QAE5C,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,iBAAiB,GAAG;QACzC,QAAQ,GAAG,CAAC;QAEZ,IAAI;YACF,MAAM,UAAU,MAAM,oJAAA,CAAA,2BAAwB,CAAC,iBAAiB;YAEhE,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG;YAC/B,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,iBAAiB,GAAG,IAAI;YAE/C,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,QAAQ,MAAM,CAAC,oBAAoB,CAAC;YAC5D,IAAI,CAAC,eAAe,CAAC,qBAAqB;QAE5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;QACxD,SAAU;YACR,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,iBAAiB,GAAG;QAC3C;IACF;IAEA,MAAc,gBAA+B;QAC3C,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,EAAE;QAEpC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,GAAG;QACjC,QAAQ,GAAG,CAAC;QAEZ,IAAI;YACF,MAAM,SAAS,2IAAA,CAAA,sBAAmB,CAAC,YAAY;YAE/C,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG;YACvB,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,GAAG,IAAI;YAEvC,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,OAAO,MAAM,CAAC,WAAW,CAAC;YAClD,IAAI,CAAC,eAAe,CAAC,aAAa;QAEpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C,SAAU;YACR,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,GAAG;QACnC;IACF;IAEA,sBAAsB;IACtB,YAAe,QAAkB,EAAE,QAAyB,EAAQ;QAClE,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QACrC,IAAI,WAAW;YACb,UAAU,GAAG,CAAC;YACd,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,UAAU;QACjD;IACF;IAEA,eAAkB,QAAkB,EAAE,QAAyB,EAAQ;QACrE,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QACrC,IAAI,WAAW;YACb,UAAU,MAAM,CAAC;YACjB,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,UAAU;QACnD;IACF;IAEQ,gBAAmB,QAAkB,EAAE,IAAO,EAAQ;QAC5D,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QACrC,IAAI,WAAW;YACb,MAAM,YAAY,IAAI;YACtB,UAAU,OAAO,CAAC,CAAA;gBAChB,IAAI;oBACF,SAAS,MAAM;gBACjB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,CAAC,WAAW,EAAE,SAAS,UAAU,CAAC,EAAE;gBACpD;YACF;QACF;IACF;IAEA,6BAA6B;IAC7B,oBAAkC;QAChC,OAAO;eAAI,IAAI,CAAC,KAAK,CAAC,cAAc;SAAC;IACvC;IAEA,uBAAqC;QACnC,OAAO;eAAI,IAAI,CAAC,KAAK,CAAC,iBAAiB;SAAC;IAC1C;IAEA,uBAA2C;QACzC,OAAO;eAAI,IAAI,CAAC,KAAK,CAAC,iBAAiB;SAAC;IAC1C;IAEA,eAA+B;QAC7B,OAAO;eAAI,IAAI,CAAC,KAAK,CAAC,SAAS;SAAC;IAClC;IAEA,eAAe,QAAkB,EAAe;QAC9C,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS;IACzC;IAEA,cAAc,QAAkB,EAAW;QACzC,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS;IACvC;IAEA,mBAAmB;IACnB,iBAAiB;QACf,OAAO;YACL,eAAe,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM;YAC/C,kBAAkB,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,MAAM;YACrD,wBAAwB,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,MAAM;YAC3D,gBAAgB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM;YAC3C,aAAa;gBAAE,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW;YAAC;YACzC,WAAW;gBAAE,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS;YAAC;YACrC,gBAAgB;gBACd,UAAU,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,aAAa,QAAQ;gBAClD,aAAa,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB,QAAQ;gBACxD,mBAAmB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,sBAAsB,QAAQ;gBACpE,WAAW,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,cAAc,QAAQ;YACtD;QACF;IACF;IAEA,wBAAwB;IACxB,MAAM,kBAAiC;QACrC,MAAM,IAAI,CAAC,kBAAkB;IAC/B;IAEA,MAAM,qBAAoC;QACxC,MAAM,IAAI,CAAC,qBAAqB;IAClC;IAEA,MAAM,2BAA0C;QAC9C,MAAM,IAAI,CAAC,qBAAqB;IAClC;IAEA,MAAM,mBAAkC;QACtC,MAAM,IAAI,CAAC,aAAa;IAC1B;IAEA,MAAM,aAA4B;QAChC,QAAQ,GAAG,CAAC;QACZ,MAAM,QAAQ,GAAG,CAAC;YAChB,IAAI,CAAC,kBAAkB;YACvB,IAAI,CAAC,qBAAqB;YAC1B,IAAI,CAAC,qBAAqB;YAC1B,IAAI,CAAC,aAAa;SACnB;QACD,QAAQ,GAAG,CAAC;IACd;IAEA,oBAAoB;IACpB,MAAM,aAA4B;QAChC,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,QAAQ,GAAG,CAAC;QAEZ,oBAAoB;QACpB,MAAM,IAAI,CAAC,UAAU;QAErB,IAAI,CAAC,aAAa,GAAG;QACrB,QAAQ,GAAG,CAAC;IACd;IAEA,QAAc;QACZ,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,QAAQ,GAAG,CAAC;QAEZ,0BAA0B;QAC1B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,YAAY;YACzC,IAAI,IAAI,CAAC,YAAY,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE;gBAC7D,IAAI,CAAC,kBAAkB;YACzB;QACF,GAAG,IAAI,CAAC,MAAM,CAAC,sBAAsB,GAAG;QAExC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,eAAe,YAAY;YAC5C,IAAI,CAAC,qBAAqB;QAC5B,GAAG,IAAI,CAAC,MAAM,CAAC,yBAAyB,GAAG;QAE3C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,qBAAqB,YAAY;YAClD,IAAI,IAAI,CAAC,YAAY,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE;gBAC7D,IAAI,CAAC,qBAAqB;YAC5B;QACF,GAAG,IAAI,CAAC,MAAM,CAAC,+BAA+B,GAAG;QAEjD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,aAAa,YAAY;YAC1C,IAAI,CAAC,aAAa;QACpB,GAAG,IAAI,CAAC,MAAM,CAAC,uBAAuB,GAAG;QAEzC,IAAI,CAAC,SAAS,GAAG;QACjB,QAAQ,GAAG,CAAC;IACd;IAEA,OAAa;QACX,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,QAAQ,GAAG,CAAC;QAEZ,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,UAAU;YAChC,cAAc;YACd,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,SAAS,QAAQ,CAAC;QAC9C;QAEA,IAAI,CAAC,SAAS,CAAC,KAAK;QACpB,IAAI,CAAC,SAAS,GAAG;QACjB,QAAQ,GAAG,CAAC;IACd;IAEA,YAAY;QACV,OAAO;YACL,eAAe,IAAI,CAAC,aAAa;YACjC,WAAW,IAAI,CAAC,SAAS;YACzB,cAAc,IAAI,CAAC,YAAY;YAC/B,QAAQ,IAAI,CAAC,MAAM;YACnB,OAAO,IAAI,CAAC,cAAc;QAC5B;IACF;AACF;AAGO,MAAM,qBAAqB,IAAI;AAEtC,sDAAsD;AACtD", "debugId": null}}, {"offset": {"line": 2609, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/app/api/data-manager/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { centralDataManager } from '@/lib/central-data-manager';\n\n// GET - Get data manager status and cached data\nexport async function GET(request: NextRequest) {\n  try {\n    const url = new URL(request.url);\n    const action = url.searchParams.get('action');\n\n    switch (action) {\n      case 'status':\n        const status = centralDataManager.getStatus();\n        return NextResponse.json({\n          success: true,\n          data: status\n        });\n\n      case 'nifty200':\n        const nifty200Stocks = centralDataManager.getNifty200Stocks();\n        return NextResponse.json({\n          success: true,\n          data: nifty200Stocks,\n          lastUpdated: centralDataManager.getLastUpdated('nifty200'),\n          isLoading: centralDataManager.isDataLoading('nifty200')\n        });\n\n      case 'boh-eligible':\n        const bohEligibleStocks = centralDataManager.getBOHEligibleStocks();\n        return NextResponse.json({\n          success: true,\n          data: bohEligibleStocks,\n          lastUpdated: centralDataManager.getLastUpdated('bohEligible'),\n          isLoading: centralDataManager.isDataLoading('bohEligible')\n        });\n\n      case 'weekly-high-signals':\n        const weeklyHighSignals = centralDataManager.getWeeklyHighSignals();\n        return NextResponse.json({\n          success: true,\n          data: weeklyHighSignals,\n          lastUpdated: centralDataManager.getLastUpdated('weeklyHighSignals'),\n          isLoading: centralDataManager.isDataLoading('weeklyHighSignals')\n        });\n\n      case 'gtt-orders':\n        const gttOrders = centralDataManager.getGTTOrders();\n        return NextResponse.json({\n          success: true,\n          data: gttOrders,\n          lastUpdated: centralDataManager.getLastUpdated('gttOrders'),\n          isLoading: centralDataManager.isDataLoading('gttOrders')\n        });\n\n      case 'cache-status':\n        const cacheStatus = centralDataManager.getCacheStatus();\n        return NextResponse.json({\n          success: true,\n          data: cacheStatus\n        });\n\n      default:\n        return NextResponse.json({\n          success: true,\n          message: 'Central Data Manager API',\n          endpoints: {\n            'GET ?action=status': 'Get service status',\n            'GET ?action=nifty200': 'Get Nifty 200 stocks',\n            'GET ?action=boh-eligible': 'Get BOH eligible stocks',\n            'GET ?action=weekly-high-signals': 'Get Weekly High Signals',\n            'GET ?action=gtt-orders': 'Get GTT orders',\n            'GET ?action=cache-status': 'Get cache status',\n            'POST': 'Control service (initialize/start/stop/refresh)'\n          }\n        });\n    }\n\n  } catch (error) {\n    console.error('❌ Data Manager API GET error:', error);\n    return NextResponse.json(\n      { \n        success: false, \n        error: error instanceof Error ? error.message : 'API error' \n      },\n      { status: 500 }\n    );\n  }\n}\n\n// POST - Control the data manager service\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { action, config } = body;\n\n    switch (action) {\n      case 'initialize':\n        await centralDataManager.initialize();\n        return NextResponse.json({\n          success: true,\n          message: 'Central Data Manager initialized',\n          data: centralDataManager.getStatus()\n        });\n\n      case 'start':\n        centralDataManager.start();\n        return NextResponse.json({\n          success: true,\n          message: 'Central Data Manager started',\n          data: centralDataManager.getStatus()\n        });\n\n      case 'stop':\n        centralDataManager.stop();\n        return NextResponse.json({\n          success: true,\n          message: 'Central Data Manager stopped',\n          data: centralDataManager.getStatus()\n        });\n\n      case 'configure':\n        if (config) {\n          centralDataManager.updateConfig(config);\n        }\n        return NextResponse.json({\n          success: true,\n          message: 'Configuration updated',\n          data: centralDataManager.getConfig()\n        });\n\n      case 'refresh-all':\n        await centralDataManager.refreshAll();\n        return NextResponse.json({\n          success: true,\n          message: 'All data refreshed',\n          data: centralDataManager.getCacheStatus()\n        });\n\n      case 'refresh-nifty200':\n        await centralDataManager.refreshNifty200();\n        return NextResponse.json({\n          success: true,\n          message: 'Nifty 200 data refreshed',\n          data: {\n            count: centralDataManager.getNifty200Stocks().length,\n            lastUpdated: centralDataManager.getLastUpdated('nifty200')\n          }\n        });\n\n      case 'refresh-boh-eligible':\n        await centralDataManager.refreshBOHEligible();\n        return NextResponse.json({\n          success: true,\n          message: 'BOH eligible data refreshed',\n          data: {\n            count: centralDataManager.getBOHEligibleStocks().length,\n            lastUpdated: centralDataManager.getLastUpdated('bohEligible')\n          }\n        });\n\n      case 'refresh-weekly-high-signals':\n        await centralDataManager.refreshWeeklyHighSignals();\n        return NextResponse.json({\n          success: true,\n          message: 'Weekly High Signals refreshed',\n          data: {\n            count: centralDataManager.getWeeklyHighSignals().length,\n            lastUpdated: centralDataManager.getLastUpdated('weeklyHighSignals')\n          }\n        });\n\n      case 'refresh-gtt-orders':\n        await centralDataManager.refreshGTTOrders();\n        return NextResponse.json({\n          success: true,\n          message: 'GTT orders refreshed',\n          data: {\n            count: centralDataManager.getGTTOrders().length,\n            lastUpdated: centralDataManager.getLastUpdated('gttOrders')\n          }\n        });\n\n      default:\n        throw new Error('Invalid action. Use: initialize, start, stop, configure, refresh-all, refresh-nifty200, refresh-boh-eligible, refresh-weekly-high-signals, refresh-gtt-orders');\n    }\n\n  } catch (error) {\n    console.error('❌ Data Manager API POST error:', error);\n    return NextResponse.json(\n      { \n        success: false, \n        error: error instanceof Error ? error.message : 'API error' \n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;QAC/B,MAAM,SAAS,IAAI,YAAY,CAAC,GAAG,CAAC;QAEpC,OAAQ;YACN,KAAK;gBACH,MAAM,SAAS,0IAAA,CAAA,qBAAkB,CAAC,SAAS;gBAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;gBACR;YAEF,KAAK;gBACH,MAAM,iBAAiB,0IAAA,CAAA,qBAAkB,CAAC,iBAAiB;gBAC3D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;oBACN,aAAa,0IAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;oBAC/C,WAAW,0IAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC;gBAC9C;YAEF,KAAK;gBACH,MAAM,oBAAoB,0IAAA,CAAA,qBAAkB,CAAC,oBAAoB;gBACjE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;oBACN,aAAa,0IAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;oBAC/C,WAAW,0IAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC;gBAC9C;YAEF,KAAK;gBACH,MAAM,oBAAoB,0IAAA,CAAA,qBAAkB,CAAC,oBAAoB;gBACjE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;oBACN,aAAa,0IAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;oBAC/C,WAAW,0IAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC;gBAC9C;YAEF,KAAK;gBACH,MAAM,YAAY,0IAAA,CAAA,qBAAkB,CAAC,YAAY;gBACjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;oBACN,aAAa,0IAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;oBAC/C,WAAW,0IAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC;gBAC9C;YAEF,KAAK;gBACH,MAAM,cAAc,0IAAA,CAAA,qBAAkB,CAAC,cAAc;gBACrD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;gBACR;YAEF;gBACE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,SAAS;oBACT,WAAW;wBACT,sBAAsB;wBACtB,wBAAwB;wBACxB,4BAA4B;wBAC5B,mCAAmC;wBACnC,0BAA0B;wBAC1B,4BAA4B;wBAC5B,QAAQ;oBACV;gBACF;QACJ;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG;QAE3B,OAAQ;YACN,KAAK;gBACH,MAAM,0IAAA,CAAA,qBAAkB,CAAC,UAAU;gBACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,SAAS;oBACT,MAAM,0IAAA,CAAA,qBAAkB,CAAC,SAAS;gBACpC;YAEF,KAAK;gBACH,0IAAA,CAAA,qBAAkB,CAAC,KAAK;gBACxB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,SAAS;oBACT,MAAM,0IAAA,CAAA,qBAAkB,CAAC,SAAS;gBACpC;YAEF,KAAK;gBACH,0IAAA,CAAA,qBAAkB,CAAC,IAAI;gBACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,SAAS;oBACT,MAAM,0IAAA,CAAA,qBAAkB,CAAC,SAAS;gBACpC;YAEF,KAAK;gBACH,IAAI,QAAQ;oBACV,0IAAA,CAAA,qBAAkB,CAAC,YAAY,CAAC;gBAClC;gBACA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,SAAS;oBACT,MAAM,0IAAA,CAAA,qBAAkB,CAAC,SAAS;gBACpC;YAEF,KAAK;gBACH,MAAM,0IAAA,CAAA,qBAAkB,CAAC,UAAU;gBACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,SAAS;oBACT,MAAM,0IAAA,CAAA,qBAAkB,CAAC,cAAc;gBACzC;YAEF,KAAK;gBACH,MAAM,0IAAA,CAAA,qBAAkB,CAAC,eAAe;gBACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,SAAS;oBACT,MAAM;wBACJ,OAAO,0IAAA,CAAA,qBAAkB,CAAC,iBAAiB,GAAG,MAAM;wBACpD,aAAa,0IAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;oBACjD;gBACF;YAEF,KAAK;gBACH,MAAM,0IAAA,CAAA,qBAAkB,CAAC,kBAAkB;gBAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,SAAS;oBACT,MAAM;wBACJ,OAAO,0IAAA,CAAA,qBAAkB,CAAC,oBAAoB,GAAG,MAAM;wBACvD,aAAa,0IAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;oBACjD;gBACF;YAEF,KAAK;gBACH,MAAM,0IAAA,CAAA,qBAAkB,CAAC,wBAAwB;gBACjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,SAAS;oBACT,MAAM;wBACJ,OAAO,0IAAA,CAAA,qBAAkB,CAAC,oBAAoB,GAAG,MAAM;wBACvD,aAAa,0IAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;oBACjD;gBACF;YAEF,KAAK;gBACH,MAAM,0IAAA,CAAA,qBAAkB,CAAC,gBAAgB;gBACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,SAAS;oBACT,MAAM;wBACJ,OAAO,0IAAA,CAAA,qBAAkB,CAAC,YAAY,GAAG,MAAM;wBAC/C,aAAa,0IAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;oBACjD;gBACF;YAEF;gBACE,MAAM,IAAI,MAAM;QACpB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}