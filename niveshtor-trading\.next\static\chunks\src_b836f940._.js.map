{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/components/ui/RealTimeIndicator.tsx"], "sourcesContent": ["// Real-time update status indicator\nimport React from 'react';\nimport { Wifi, WifiOff, Activity, Clock } from 'lucide-react';\n\ninterface RealTimeIndicatorProps {\n  isActive: boolean;\n  lastUpdate: Date | null;\n  updateCount: number;\n  error: Error | null;\n  className?: string;\n}\n\nexport function RealTimeIndicator({ \n  isActive, \n  lastUpdate, \n  updateCount, \n  error, \n  className = '' \n}: RealTimeIndicatorProps) {\n  const formatLastUpdate = (date: Date | null) => {\n    if (!date) return 'Never';\n    \n    const now = new Date();\n    const diff = now.getTime() - date.getTime();\n    const seconds = Math.floor(diff / 1000);\n    \n    if (seconds < 60) return `${seconds}s ago`;\n    if (seconds < 3600) return `${Math.floor(seconds / 60)}m ago`;\n    return date.toLocaleTimeString();\n  };\n\n  const getStatusColor = () => {\n    if (error) return 'text-red-500';\n    if (isActive) return 'text-green-500';\n    return 'text-gray-400';\n  };\n\n  const getStatusIcon = () => {\n    if (error) return <WifiOff className=\"h-4 w-4\" />;\n    if (isActive) return <Wifi className=\"h-4 w-4\" />;\n    return <WifiOff className=\"h-4 w-4\" />;\n  };\n\n  const getStatusText = () => {\n    if (error) return 'Connection Error';\n    if (isActive) return 'Live Updates';\n    return 'Offline';\n  };\n\n  return (\n    <div className={`flex items-center space-x-2 text-sm ${className}`}>\n      {/* Status Icon and Text */}\n      <div className={`flex items-center space-x-1 ${getStatusColor()}`}>\n        {getStatusIcon()}\n        <span className=\"font-medium\">{getStatusText()}</span>\n      </div>\n      \n      {/* Update Count */}\n      {isActive && updateCount > 0 && (\n        <div className=\"flex items-center space-x-1 text-gray-500\">\n          <Activity className=\"h-3 w-3\" />\n          <span>{updateCount} updates</span>\n        </div>\n      )}\n      \n      {/* Last Update Time */}\n      <div className=\"flex items-center space-x-1 text-gray-500\">\n        <Clock className=\"h-3 w-3\" />\n        <span>{formatLastUpdate(lastUpdate)}</span>\n      </div>\n      \n      {/* Error Message */}\n      {error && (\n        <div className=\"text-red-500 text-xs\">\n          {error.message}\n        </div>\n      )}\n    </div>\n  );\n}\n\n// Compact version for smaller spaces\nexport function CompactRealTimeIndicator({ \n  isActive, \n  lastUpdate, \n  error, \n  className = '' \n}: Omit<RealTimeIndicatorProps, 'updateCount'>) {\n  const getStatusColor = () => {\n    if (error) return 'text-red-500';\n    if (isActive) return 'text-green-500';\n    return 'text-gray-400';\n  };\n\n  const getStatusIcon = () => {\n    if (error) return <WifiOff className=\"h-3 w-3\" />;\n    if (isActive) return <Wifi className=\"h-3 w-3\" />;\n    return <WifiOff className=\"h-3 w-3\" />;\n  };\n\n  return (\n    <div className={`flex items-center space-x-1 ${getStatusColor()} ${className}`}>\n      {getStatusIcon()}\n      {isActive && (\n        <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\" />\n      )}\n    </div>\n  );\n}\n\n// Hook for using real-time indicator data\nexport function useRealTimeIndicator() {\n  const [isActive, setIsActive] = React.useState(false);\n  const [lastUpdate, setLastUpdate] = React.useState<Date | null>(null);\n  const [updateCount, setUpdateCount] = React.useState(0);\n  const [error, setError] = React.useState<Error | null>(null);\n\n  // Listen for real-time updates\n  React.useEffect(() => {\n    const handleRealTimeUpdate = (event: CustomEvent) => {\n      setLastUpdate(new Date());\n      setUpdateCount(prev => prev + 1);\n      setError(null);\n      setIsActive(true);\n    };\n\n    const handleError = (event: CustomEvent) => {\n      setError(event.detail.error);\n    };\n\n    window.addEventListener('realTimePriceUpdate', handleRealTimeUpdate as EventListener);\n    window.addEventListener('realTimeError', handleError as EventListener);\n\n    return () => {\n      window.removeEventListener('realTimePriceUpdate', handleRealTimeUpdate as EventListener);\n      window.removeEventListener('realTimeError', handleError as EventListener);\n    };\n  }, []);\n\n  return {\n    isActive,\n    lastUpdate,\n    updateCount,\n    error,\n    clearError: () => setError(null)\n  };\n}\n"], "names": [], "mappings": "AAAA,oCAAoC;;;;;;;AACpC;AACA;AAAA;AAAA;AAAA;;;;;AAUO,SAAS,kBAAkB,KAMT;QANS,EAChC,QAAQ,EACR,UAAU,EACV,WAAW,EACX,KAAK,EACL,YAAY,EAAE,EACS,GANS;IAOhC,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,MAAM,IAAI;QAChB,MAAM,OAAO,IAAI,OAAO,KAAK,KAAK,OAAO;QACzC,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO;QAElC,IAAI,UAAU,IAAI,OAAO,AAAC,GAAU,OAAR,SAAQ;QACpC,IAAI,UAAU,MAAM,OAAO,AAAC,GAA2B,OAAzB,KAAK,KAAK,CAAC,UAAU,KAAI;QACvD,OAAO,KAAK,kBAAkB;IAChC;IAEA,MAAM,iBAAiB;QACrB,IAAI,OAAO,OAAO;QAClB,IAAI,UAAU,OAAO;QACrB,OAAO;IACT;IAEA,MAAM,gBAAgB;QACpB,IAAI,OAAO,qBAAO,6LAAC,+MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QACrC,IAAI,UAAU,qBAAO,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QACrC,qBAAO,6LAAC,+MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;IAC5B;IAEA,MAAM,gBAAgB;QACpB,IAAI,OAAO,OAAO;QAClB,IAAI,UAAU,OAAO;QACrB,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,uCAAgD,OAAV;;0BAErD,6LAAC;gBAAI,WAAW,AAAC,+BAA+C,OAAjB;;oBAC5C;kCACD,6LAAC;wBAAK,WAAU;kCAAe;;;;;;;;;;;;YAIhC,YAAY,cAAc,mBACzB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC;;4BAAM;4BAAY;;;;;;;;;;;;;0BAKvB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,6LAAC;kCAAM,iBAAiB;;;;;;;;;;;;YAIzB,uBACC,6LAAC;gBAAI,WAAU;0BACZ,MAAM,OAAO;;;;;;;;;;;;AAKxB;KAnEgB;AAsET,SAAS,yBAAyB,KAKK;QALL,EACvC,QAAQ,EACR,UAAU,EACV,KAAK,EACL,YAAY,EAAE,EAC8B,GALL;IAMvC,MAAM,iBAAiB;QACrB,IAAI,OAAO,OAAO;QAClB,IAAI,UAAU,OAAO;QACrB,OAAO;IACT;IAEA,MAAM,gBAAgB;QACpB,IAAI,OAAO,qBAAO,6LAAC,+MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QACrC,IAAI,UAAU,qBAAO,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QACrC,qBAAO,6LAAC,+MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;IAC5B;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,+BAAkD,OAApB,kBAAiB,KAAa,OAAV;;YAChE;YACA,0BACC,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAIvB;MA1BgB;AA6BT,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAc;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACrD,MAAM,CAAC,OAAO,SAAS,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAe;IAEvD,+BAA+B;IAC/B,6JAAA,CAAA,UAAK,CAAC,SAAS;0CAAC;YACd,MAAM;uEAAuB,CAAC;oBAC5B,cAAc,IAAI;oBAClB;+EAAe,CAAA,OAAQ,OAAO;;oBAC9B,SAAS;oBACT,YAAY;gBACd;;YAEA,MAAM;8DAAc,CAAC;oBACnB,SAAS,MAAM,MAAM,CAAC,KAAK;gBAC7B;;YAEA,OAAO,gBAAgB,CAAC,uBAAuB;YAC/C,OAAO,gBAAgB,CAAC,iBAAiB;YAEzC;kDAAO;oBACL,OAAO,mBAAmB,CAAC,uBAAuB;oBAClD,OAAO,mBAAmB,CAAC,iBAAiB;gBAC9C;;QACF;yCAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;QACA,YAAY,IAAM,SAAS;IAC7B;AACF;GAnCgB", "debugId": null}}, {"offset": {"line": 257, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/hooks/useBackgroundData.ts"], "sourcesContent": ["// React hook for seamless background data integration\nimport { useState, useEffect, useCallback, useRef } from 'react';\nimport { backgroundDataService, BackgroundUpdateListener, RealTimeUpdateListener } from '@/lib/background-data-service';\nimport { stockNamesService } from '@/lib/stock-names-service';\nimport { NiftyStock } from '@/lib/nifty-stocks';\n\ninterface UseBackgroundDataOptions {\n  enablePriceUpdates?: boolean;\n  enableNameUpdates?: boolean;\n  enableRealTimeUpdates?: boolean;\n  onError?: (error: Error) => void;\n  onRealTimeUpdate?: (quotes: any[]) => void;\n}\n\ninterface UseBackgroundDataReturn {\n  // Status\n  isNamesReady: boolean;\n  isUpdating: boolean;\n  lastUpdate: Date | null;\n  error: Error | null;\n  \n  // Data\n  stockNames: Map<string, string>;\n  \n  // Methods\n  getStockName: (symbol: string) => string;\n  forceUpdate: () => Promise<void>;\n  clearError: () => void;\n}\n\nexport function useBackgroundData(options: UseBackgroundDataOptions = {}): UseBackgroundDataReturn {\n  const {\n    enablePriceUpdates = true,\n    enableNameUpdates = true,\n    enableRealTimeUpdates = false,\n    onError,\n    onRealTimeUpdate\n  } = options;\n\n  // State\n  const [isNamesReady, setIsNamesReady] = useState(false);\n  const [isUpdating, setIsUpdating] = useState(false);\n  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);\n  const [error, setError] = useState<Error | null>(null);\n  const [stockNames, setStockNames] = useState<Map<string, string>>(new Map());\n  \n  // Refs\n  const listenerRef = useRef<BackgroundUpdateListener | null>(null);\n  const realTimeListenerRef = useRef<RealTimeUpdateListener | null>(null);\n  const mountedRef = useRef(true);\n\n  // Create listener\n  const createListener = useCallback((): BackgroundUpdateListener => ({\n    onPriceUpdate: (data: any[]) => {\n      if (!mountedRef.current || !enablePriceUpdates) return;\n      \n      console.log(`🔄 Received background price update: ${data.length} stocks`);\n      setLastUpdate(new Date());\n      setIsUpdating(false);\n      \n      // Trigger custom event for other components to listen\n      window.dispatchEvent(new CustomEvent('backgroundPriceUpdate', {\n        detail: { data, timestamp: new Date() }\n      }));\n    },\n    \n    onNamesUpdate: (namesMap: Map<string, string>) => {\n      if (!mountedRef.current || !enableNameUpdates) return;\n      \n      console.log(`📝 Received background names update: ${namesMap.size} names`);\n      setStockNames(new Map(namesMap));\n      setIsNamesReady(true);\n      setError(null);\n      \n      // Trigger custom event for other components to listen\n      window.dispatchEvent(new CustomEvent('backgroundNamesUpdate', {\n        detail: { namesMap, timestamp: new Date() }\n      }));\n    },\n    \n    onError: (err: Error) => {\n      if (!mountedRef.current) return;\n      \n      console.error('❌ Background data error:', err);\n      setError(err);\n      setIsUpdating(false);\n      \n      if (onError) {\n        onError(err);\n      }\n    }\n  }), [enablePriceUpdates, enableNameUpdates, onError]);\n\n  // Create real-time listener\n  const createRealTimeListener = useCallback((): RealTimeUpdateListener => ({\n    onRealTimeUpdate: (quotes: any[]) => {\n      if (!mountedRef.current || !enableRealTimeUpdates) return;\n\n      console.log(`⚡ Received real-time update: ${quotes.length} quotes`);\n      setLastUpdate(new Date());\n\n      // Call custom callback if provided\n      if (onRealTimeUpdate) {\n        onRealTimeUpdate(quotes);\n      }\n\n      // Trigger custom event for components to listen\n      window.dispatchEvent(new CustomEvent('realTimePriceUpdate', {\n        detail: { quotes, timestamp: new Date() }\n      }));\n    },\n\n    onError: (err: Error) => {\n      if (!mountedRef.current) return;\n\n      console.error('❌ Real-time update error:', err);\n      setError(err);\n\n      if (onError) {\n        onError(err);\n      }\n    }\n  }), [enableRealTimeUpdates, onRealTimeUpdate, onError]);\n\n  // Initialize and setup listener\n  useEffect(() => {\n    mountedRef.current = true;\n    \n    const initializeData = async () => {\n      try {\n        // Check if names are already ready\n        const namesReady = backgroundDataService.areNamesReady();\n        setIsNamesReady(namesReady);\n\n        if (namesReady) {\n          // Load existing cached names\n          const cachedNames = stockNamesService.getCachedStockNames();\n          setStockNames(cachedNames);\n        }\n\n        // Create and register listener\n        const listener = createListener();\n        listenerRef.current = listener;\n        backgroundDataService.addListener(listener);\n\n        // Create and register real-time listener if enabled\n        if (enableRealTimeUpdates) {\n          const realTimeListener = createRealTimeListener();\n          realTimeListenerRef.current = realTimeListener;\n          backgroundDataService.addRealTimeListener(realTimeListener);\n        }\n\n        // Initialize background service if not already done\n        await backgroundDataService.initialize();\n\n      } catch (err) {\n        console.error('❌ Failed to initialize background data hook:', err);\n        setError(err as Error);\n        // Set names ready to true to allow pages to work even if background service fails\n        setIsNamesReady(true);\n      }\n    };\n    \n    initializeData();\n    \n    // Cleanup\n    return () => {\n      mountedRef.current = false;\n      if (listenerRef.current) {\n        backgroundDataService.removeListener(listenerRef.current);\n      }\n      if (realTimeListenerRef.current) {\n        backgroundDataService.removeRealTimeListener(realTimeListenerRef.current);\n      }\n    };\n  }, [createListener, createRealTimeListener, enableRealTimeUpdates]);\n\n  // Get stock name with fallback\n  const getStockName = useCallback((symbol: string): string => {\n    // First try from local state\n    const name = stockNames.get(symbol);\n    if (name) return name;\n\n    // Fallback to sync method\n    try {\n      return stockNamesService.getStockNameSync(symbol);\n    } catch (error) {\n      // Ultimate fallback - just return symbol without .NS\n      return symbol.replace('.NS', '');\n    }\n  }, [stockNames]);\n\n  // Force update\n  const forceUpdate = useCallback(async (): Promise<void> => {\n    setIsUpdating(true);\n    setError(null);\n    \n    try {\n      await backgroundDataService.forceUpdate();\n    } catch (err) {\n      setError(err as Error);\n      throw err;\n    }\n  }, []);\n\n  // Clear error\n  const clearError = useCallback(() => {\n    setError(null);\n  }, []);\n\n  return {\n    // Status\n    isNamesReady,\n    isUpdating,\n    lastUpdate,\n    error,\n    \n    // Data\n    stockNames,\n    \n    // Methods\n    getStockName,\n    forceUpdate,\n    clearError\n  };\n}\n\n// Hook for listening to background price updates\nexport function useBackgroundPriceUpdates(callback: (data: any[]) => void) {\n  useEffect(() => {\n    const handlePriceUpdate = (event: CustomEvent) => {\n      callback(event.detail.data);\n    };\n\n    window.addEventListener('backgroundPriceUpdate', handlePriceUpdate as EventListener);\n    \n    return () => {\n      window.removeEventListener('backgroundPriceUpdate', handlePriceUpdate as EventListener);\n    };\n  }, [callback]);\n}\n\n// Hook for listening to background name updates\nexport function useBackgroundNameUpdates(callback: (namesMap: Map<string, string>) => void) {\n  useEffect(() => {\n    const handleNameUpdate = (event: CustomEvent) => {\n      callback(event.detail.namesMap);\n    };\n\n    window.addEventListener('backgroundNamesUpdate', handleNameUpdate as EventListener);\n    \n    return () => {\n      window.removeEventListener('backgroundNamesUpdate', handleNameUpdate as EventListener);\n    };\n  }, [callback]);\n}\n"], "names": [], "mappings": "AAAA,sDAAsD;;;;;;AACtD;AACA;AACA;;;;;AA2BO,SAAS;QAAkB,UAAA,iEAAoC,CAAC;;IACrE,MAAM,EACJ,qBAAqB,IAAI,EACzB,oBAAoB,IAAI,EACxB,wBAAwB,KAAK,EAC7B,OAAO,EACP,gBAAgB,EACjB,GAAG;IAEJ,QAAQ;IACR,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC1D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,IAAI;IAEtE,OAAO;IACP,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAmC;IAC5D,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiC;IAClE,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE1B,kBAAkB;IAClB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,IAAgC,CAAC;gBAClE,aAAa;qEAAE,CAAC;wBACd,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC,oBAAoB;wBAEhD,QAAQ,GAAG,CAAC,AAAC,wCAAmD,OAAZ,KAAK,MAAM,EAAC;wBAChE,cAAc,IAAI;wBAClB,cAAc;wBAEd,sDAAsD;wBACtD,OAAO,aAAa,CAAC,IAAI,YAAY,yBAAyB;4BAC5D,QAAQ;gCAAE;gCAAM,WAAW,IAAI;4BAAO;wBACxC;oBACF;;gBAEA,aAAa;qEAAE,CAAC;wBACd,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC,mBAAmB;wBAE/C,QAAQ,GAAG,CAAC,AAAC,wCAAqD,OAAd,SAAS,IAAI,EAAC;wBAClE,cAAc,IAAI,IAAI;wBACtB,gBAAgB;wBAChB,SAAS;wBAET,sDAAsD;wBACtD,OAAO,aAAa,CAAC,IAAI,YAAY,yBAAyB;4BAC5D,QAAQ;gCAAE;gCAAU,WAAW,IAAI;4BAAO;wBAC5C;oBACF;;gBAEA,OAAO;qEAAE,CAAC;wBACR,IAAI,CAAC,WAAW,OAAO,EAAE;wBAEzB,QAAQ,KAAK,CAAC,4BAA4B;wBAC1C,SAAS;wBACT,cAAc;wBAEd,IAAI,SAAS;4BACX,QAAQ;wBACV;oBACF;;YACF,CAAC;wDAAG;QAAC;QAAoB;QAAmB;KAAQ;IAEpD,4BAA4B;IAC5B,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iEAAE,IAA8B,CAAC;gBACxE,gBAAgB;6EAAE,CAAC;wBACjB,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC,uBAAuB;wBAEnD,QAAQ,GAAG,CAAC,AAAC,gCAA6C,OAAd,OAAO,MAAM,EAAC;wBAC1D,cAAc,IAAI;wBAElB,mCAAmC;wBACnC,IAAI,kBAAkB;4BACpB,iBAAiB;wBACnB;wBAEA,gDAAgD;wBAChD,OAAO,aAAa,CAAC,IAAI,YAAY,uBAAuB;4BAC1D,QAAQ;gCAAE;gCAAQ,WAAW,IAAI;4BAAO;wBAC1C;oBACF;;gBAEA,OAAO;6EAAE,CAAC;wBACR,IAAI,CAAC,WAAW,OAAO,EAAE;wBAEzB,QAAQ,KAAK,CAAC,6BAA6B;wBAC3C,SAAS;wBAET,IAAI,SAAS;4BACX,QAAQ;wBACV;oBACF;;YACF,CAAC;gEAAG;QAAC;QAAuB;QAAkB;KAAQ;IAEtD,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,WAAW,OAAO,GAAG;YAErB,MAAM;8DAAiB;oBACrB,IAAI;wBACF,mCAAmC;wBACnC,MAAM,aAAa,8IAAA,CAAA,wBAAqB,CAAC,aAAa;wBACtD,gBAAgB;wBAEhB,IAAI,YAAY;4BACd,6BAA6B;4BAC7B,MAAM,cAAc,0IAAA,CAAA,oBAAiB,CAAC,mBAAmB;4BACzD,cAAc;wBAChB;wBAEA,+BAA+B;wBAC/B,MAAM,WAAW;wBACjB,YAAY,OAAO,GAAG;wBACtB,8IAAA,CAAA,wBAAqB,CAAC,WAAW,CAAC;wBAElC,oDAAoD;wBACpD,IAAI,uBAAuB;4BACzB,MAAM,mBAAmB;4BACzB,oBAAoB,OAAO,GAAG;4BAC9B,8IAAA,CAAA,wBAAqB,CAAC,mBAAmB,CAAC;wBAC5C;wBAEA,oDAAoD;wBACpD,MAAM,8IAAA,CAAA,wBAAqB,CAAC,UAAU;oBAExC,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,gDAAgD;wBAC9D,SAAS;wBACT,kFAAkF;wBAClF,gBAAgB;oBAClB;gBACF;;YAEA;YAEA,UAAU;YACV;+CAAO;oBACL,WAAW,OAAO,GAAG;oBACrB,IAAI,YAAY,OAAO,EAAE;wBACvB,8IAAA,CAAA,wBAAqB,CAAC,cAAc,CAAC,YAAY,OAAO;oBAC1D;oBACA,IAAI,oBAAoB,OAAO,EAAE;wBAC/B,8IAAA,CAAA,wBAAqB,CAAC,sBAAsB,CAAC,oBAAoB,OAAO;oBAC1E;gBACF;;QACF;sCAAG;QAAC;QAAgB;QAAwB;KAAsB;IAElE,+BAA+B;IAC/B,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC;YAChC,6BAA6B;YAC7B,MAAM,OAAO,WAAW,GAAG,CAAC;YAC5B,IAAI,MAAM,OAAO;YAEjB,0BAA0B;YAC1B,IAAI;gBACF,OAAO,0IAAA,CAAA,oBAAiB,CAAC,gBAAgB,CAAC;YAC5C,EAAE,OAAO,OAAO;gBACd,qDAAqD;gBACrD,OAAO,OAAO,OAAO,CAAC,OAAO;YAC/B;QACF;sDAAG;QAAC;KAAW;IAEf,eAAe;IACf,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YAC9B,cAAc;YACd,SAAS;YAET,IAAI;gBACF,MAAM,8IAAA,CAAA,wBAAqB,CAAC,WAAW;YACzC,EAAE,OAAO,KAAK;gBACZ,SAAS;gBACT,MAAM;YACR;QACF;qDAAG,EAAE;IAEL,cAAc;IACd,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE;YAC7B,SAAS;QACX;oDAAG,EAAE;IAEL,OAAO;QACL,SAAS;QACT;QACA;QACA;QACA;QAEA,OAAO;QACP;QAEA,UAAU;QACV;QACA;QACA;IACF;AACF;GAnMgB;AAsMT,SAAS,0BAA0B,QAA+B;;IACvE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+CAAE;YACR,MAAM;yEAAoB,CAAC;oBACzB,SAAS,MAAM,MAAM,CAAC,IAAI;gBAC5B;;YAEA,OAAO,gBAAgB,CAAC,yBAAyB;YAEjD;uDAAO;oBACL,OAAO,mBAAmB,CAAC,yBAAyB;gBACtD;;QACF;8CAAG;QAAC;KAAS;AACf;IAZgB;AAeT,SAAS,yBAAyB,QAAiD;;IACxF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8CAAE;YACR,MAAM;uEAAmB,CAAC;oBACxB,SAAS,MAAM,MAAM,CAAC,QAAQ;gBAChC;;YAEA,OAAO,gBAAgB,CAAC,yBAAyB;YAEjD;sDAAO;oBACL,OAAO,mBAAmB,CAAC,yBAAyB;gBACtD;;QACF;6CAAG;QAAC;KAAS;AACf;IAZgB", "debugId": null}}, {"offset": {"line": 528, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/hooks/useRealTimeStocks.ts"], "sourcesContent": ["// React hook for real-time stock data updates\nimport { useState, useEffect, useCallback, useRef } from 'react';\nimport { useBackgroundData } from './useBackgroundData';\n\ninterface UseRealTimeStocksOptions {\n  onUpdate?: (quotes: any[]) => void;\n  onError?: (error: Error) => void;\n}\n\ninterface UseRealTimeStocksReturn {\n  // Status\n  isActive: boolean;\n  lastUpdate: Date | null;\n  updateCount: number;\n  error: Error | null;\n  \n  // Methods\n  start: () => void;\n  stop: () => void;\n  forceUpdate: () => Promise<void>;\n  clearError: () => void;\n}\n\nexport function useRealTimeStocks(options: UseRealTimeStocksOptions = {}): UseRealTimeStocksReturn {\n  const { onUpdate, onError } = options;\n  \n  // State\n  const [isActive, setIsActive] = useState(false);\n  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);\n  const [updateCount, setUpdateCount] = useState(0);\n  const [error, setError] = useState<Error | null>(null);\n  \n  // Refs\n  const mountedRef = useRef(true);\n  const onUpdateRef = useRef(onUpdate);\n  const onErrorRef = useRef(onError);\n  \n  // Update refs when callbacks change\n  useEffect(() => {\n    onUpdateRef.current = onUpdate;\n    onErrorRef.current = onError;\n  }, [onUpdate, onError]);\n  \n  // Real-time update handler\n  const handleRealTimeUpdate = useCallback((quotes: any[]) => {\n    if (!mountedRef.current) return;\n    \n    setLastUpdate(new Date());\n    setUpdateCount(prev => prev + 1);\n    setError(null);\n    \n    // Call custom callback if provided\n    if (onUpdateRef.current) {\n      onUpdateRef.current(quotes);\n    }\n  }, []);\n  \n  // Error handler\n  const handleError = useCallback((err: Error) => {\n    if (!mountedRef.current) return;\n    \n    setError(err);\n    \n    if (onErrorRef.current) {\n      onErrorRef.current(err);\n    }\n  }, []);\n  \n  // Background data hook with real-time enabled\n  const { forceUpdate: forceBackgroundUpdate } = useBackgroundData({\n    enableRealTimeUpdates: isActive,\n    onRealTimeUpdate: handleRealTimeUpdate,\n    onError: handleError\n  });\n  \n  // Start real-time updates\n  const start = useCallback(() => {\n    console.log('🚀 Starting real-time stock updates...');\n    setIsActive(true);\n    setError(null);\n  }, []);\n  \n  // Stop real-time updates\n  const stop = useCallback(() => {\n    console.log('⏹️ Stopping real-time stock updates...');\n    setIsActive(false);\n  }, []);\n  \n  // Force immediate update\n  const forceUpdate = useCallback(async () => {\n    try {\n      await forceBackgroundUpdate();\n    } catch (err) {\n      handleError(err as Error);\n    }\n  }, [forceBackgroundUpdate, handleError]);\n  \n  // Clear error\n  const clearError = useCallback(() => {\n    setError(null);\n  }, []);\n  \n  // Cleanup on unmount\n  useEffect(() => {\n    mountedRef.current = true;\n    \n    return () => {\n      mountedRef.current = false;\n    };\n  }, []);\n  \n  return {\n    isActive,\n    lastUpdate,\n    updateCount,\n    error,\n    start,\n    stop,\n    forceUpdate,\n    clearError\n  };\n}\n\n// Hook for listening to real-time updates via custom events\nexport function useRealTimeUpdates(callback: (quotes: any[]) => void) {\n  const callbackRef = useRef(callback);\n  \n  // Update callback ref\n  useEffect(() => {\n    callbackRef.current = callback;\n  }, [callback]);\n  \n  useEffect(() => {\n    const handleRealTimeUpdate = (event: CustomEvent) => {\n      const { quotes } = event.detail;\n      callbackRef.current(quotes);\n    };\n    \n    window.addEventListener('realTimePriceUpdate', handleRealTimeUpdate as EventListener);\n    \n    return () => {\n      window.removeEventListener('realTimePriceUpdate', handleRealTimeUpdate as EventListener);\n    };\n  }, []);\n}\n\n// Hook for seamless stock data with real-time updates\nexport function useSeamlessStockData() {\n  const [stocks, setStocks] = useState<any[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  \n  // Background data for names\n  const { isNamesReady, getStockName } = useBackgroundData();\n  \n  // Real-time updates for prices\n  const realTimeStocks = useRealTimeStocks({\n    onUpdate: (quotes) => {\n      // Update stocks with new price data\n      setStocks(prevStocks => {\n        const updatedStocks = [...prevStocks];\n        \n        quotes.forEach(quote => {\n          const index = updatedStocks.findIndex(stock => stock.symbol === quote.symbol);\n          if (index >= 0) {\n            updatedStocks[index] = { ...updatedStocks[index], ...quote };\n          } else {\n            updatedStocks.push({\n              ...quote,\n              name: getStockName(quote.symbol)\n            });\n          }\n        });\n        \n        return updatedStocks;\n      });\n      \n      setIsLoading(false);\n    }\n  });\n  \n  // Start real-time updates when names are ready\n  useEffect(() => {\n    if (isNamesReady && !realTimeStocks.isActive) {\n      realTimeStocks.start();\n    }\n  }, [isNamesReady, realTimeStocks]);\n  \n  return {\n    stocks,\n    isLoading: isLoading && !isNamesReady,\n    isRealTimeActive: realTimeStocks.isActive,\n    lastUpdate: realTimeStocks.lastUpdate,\n    updateCount: realTimeStocks.updateCount,\n    error: realTimeStocks.error,\n    forceUpdate: realTimeStocks.forceUpdate,\n    getStockName\n  };\n}\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;;;AAC9C;AACA;;;;AAqBO,SAAS;QAAkB,UAAA,iEAAoC,CAAC;;IACrE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG;IAE9B,QAAQ;IACR,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IAEjD,OAAO;IACP,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE1B,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,YAAY,OAAO,GAAG;YACtB,WAAW,OAAO,GAAG;QACvB;sCAAG;QAAC;QAAU;KAAQ;IAEtB,2BAA2B;IAC3B,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+DAAE,CAAC;YACxC,IAAI,CAAC,WAAW,OAAO,EAAE;YAEzB,cAAc,IAAI;YAClB;uEAAe,CAAA,OAAQ,OAAO;;YAC9B,SAAS;YAET,mCAAmC;YACnC,IAAI,YAAY,OAAO,EAAE;gBACvB,YAAY,OAAO,CAAC;YACtB;QACF;8DAAG,EAAE;IAEL,gBAAgB;IAChB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YAC/B,IAAI,CAAC,WAAW,OAAO,EAAE;YAEzB,SAAS;YAET,IAAI,WAAW,OAAO,EAAE;gBACtB,WAAW,OAAO,CAAC;YACrB;QACF;qDAAG,EAAE;IAEL,8CAA8C;IAC9C,MAAM,EAAE,aAAa,qBAAqB,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,oBAAiB,AAAD,EAAE;QAC/D,uBAAuB;QACvB,kBAAkB;QAClB,SAAS;IACX;IAEA,0BAA0B;IAC1B,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE;YACxB,QAAQ,GAAG,CAAC;YACZ,YAAY;YACZ,SAAS;QACX;+CAAG,EAAE;IAEL,yBAAyB;IACzB,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE;YACvB,QAAQ,GAAG,CAAC;YACZ,YAAY;QACd;8CAAG,EAAE;IAEL,yBAAyB;IACzB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YAC9B,IAAI;gBACF,MAAM;YACR,EAAE,OAAO,KAAK;gBACZ,YAAY;YACd;QACF;qDAAG;QAAC;QAAuB;KAAY;IAEvC,cAAc;IACd,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE;YAC7B,SAAS;QACX;oDAAG,EAAE;IAEL,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,WAAW,OAAO,GAAG;YAErB;+CAAO;oBACL,WAAW,OAAO,GAAG;gBACvB;;QACF;sCAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAlGgB;;QA8CiC,oIAAA,CAAA,oBAAiB;;;AAuD3D,SAAS,mBAAmB,QAAiC;;IAClE,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,sBAAsB;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,YAAY,OAAO,GAAG;QACxB;uCAAG;QAAC;KAAS;IAEb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM;qEAAuB,CAAC;oBAC5B,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,MAAM;oBAC/B,YAAY,OAAO,CAAC;gBACtB;;YAEA,OAAO,gBAAgB,CAAC,uBAAuB;YAE/C;gDAAO;oBACL,OAAO,mBAAmB,CAAC,uBAAuB;gBACpD;;QACF;uCAAG,EAAE;AACP;IApBgB;AAuBT,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,4BAA4B;IAC5B,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,oBAAiB,AAAD;IAEvD,+BAA+B;IAC/B,MAAM,iBAAiB,kBAAkB;QACvC,QAAQ;sEAAE,CAAC;gBACT,oCAAoC;gBACpC;8EAAU,CAAA;wBACR,MAAM,gBAAgB;+BAAI;yBAAW;wBAErC,OAAO,OAAO;sFAAC,CAAA;gCACb,MAAM,QAAQ,cAAc,SAAS;oGAAC,CAAA,QAAS,MAAM,MAAM,KAAK,MAAM,MAAM;;gCAC5E,IAAI,SAAS,GAAG;oCACd,aAAa,CAAC,MAAM,GAAG;wCAAE,GAAG,aAAa,CAAC,MAAM;wCAAE,GAAG,KAAK;oCAAC;gCAC7D,OAAO;oCACL,cAAc,IAAI,CAAC;wCACjB,GAAG,KAAK;wCACR,MAAM,aAAa,MAAM,MAAM;oCACjC;gCACF;4BACF;;wBAEA,OAAO;oBACT;;gBAEA,aAAa;YACf;;IACF;IAEA,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,gBAAgB,CAAC,eAAe,QAAQ,EAAE;gBAC5C,eAAe,KAAK;YACtB;QACF;yCAAG;QAAC;QAAc;KAAe;IAEjC,OAAO;QACL;QACA,WAAW,aAAa,CAAC;QACzB,kBAAkB,eAAe,QAAQ;QACzC,YAAY,eAAe,UAAU;QACrC,aAAa,eAAe,WAAW;QACvC,OAAO,eAAe,KAAK;QAC3B,aAAa,eAAe,WAAW;QACvC;IACF;AACF;IAlDgB;;QAKyB,oIAAA,CAAA,oBAAiB;QAGjC", "debugId": null}}, {"offset": {"line": 761, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/app/dashboard/stocks/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { \n  Search, \n  RefreshCw,\n  Filter,\n  AlertCircle,\n  Loader\n} from 'lucide-react';\nimport { formatCurrency, formatPercentage, getChangeColor } from '@/lib/utils';\nimport { NiftyStock } from '@/lib/nifty-stocks';\nimport { cacheService, CacheKeys } from '@/lib/cache-service';\nimport { StockListSkeleton, InlineLoading } from '@/components/ui/LoadingStates';\nimport { RealTimeIndicator } from '@/components/ui/RealTimeIndicator';\nimport { useBackgroundData } from '@/hooks/useBackgroundData';\nimport { useRealTimeStocks, useRealTimeUpdates } from '@/hooks/useRealTimeStocks';\n\nexport default function StockUniversalPage() {\n  // Background data hook for instant loading\n  const {\n    isNamesReady,\n    getStockName,\n    forceUpdate: forceBackgroundUpdate,\n    error: backgroundError\n  } = useBackgroundData();\n\n  // Search state\n  const [searchQuery, setSearchQuery] = useState('');\n  const [searchResults, setSearchResults] = useState<NiftyStock[]>([]);\n  const [isSearching, setIsSearching] = useState(false);\n\n  // Nifty 200 stocks state - now loads instantly with cached names\n  const [niftyStocks, setNiftyStocks] = useState<NiftyStock[]>([]);\n  const [loadingPriceData, setLoadingPriceData] = useState(false);\n  const [niftyError, setNiftyError] = useState<string | null>(null);\n\n  // Filter state\n  const [showFilters, setShowFilters] = useState(false);\n  const [priceFilter, setPriceFilter] = useState({ min: 0, max: 10000 });\n  const [showOnlyHoldings, setShowOnlyHoldings] = useState(false);\n  const [showAbove2000, setShowAbove2000] = useState(false);\n\n  // Load price data only (names are handled by background service)\n  const loadPriceData = async (forceRefresh = false) => {\n    setLoadingPriceData(true);\n    setNiftyError(null);\n\n    try {\n      if (forceRefresh) {\n        // Force background update for both names and prices\n        await forceBackgroundUpdate();\n      }\n\n      const batchSize = 25;\n      const totalBatches = Math.ceil(200 / batchSize);\n      const allStocks: NiftyStock[] = [];\n\n      console.log(`🚀 Loading price data for ${totalBatches} batches${forceRefresh ? ' (force refresh)' : ''}...`);\n\n      for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {\n        try {\n          let batchData;\n          if (!forceRefresh) {\n            const cacheKey = CacheKeys.niftyStocks(batchIndex);\n            const cached = cacheService.get<any>(cacheKey);\n            if (cached) {\n              batchData = cached;\n            }\n          }\n\n          if (!batchData) {\n            try {\n              // Pass forceRefresh parameter to API to control name caching\n              const url = `/api/stocks/nifty200?batchIndex=${batchIndex}&batchSize=${batchSize}${forceRefresh ? '&forceRefresh=true' : ''}`;\n              console.log(`🔄 Fetching batch ${batchIndex + 1}/${totalBatches}: ${url}`);\n\n              const response = await fetch(url, {\n                method: 'GET',\n                headers: {\n                  'Content-Type': 'application/json',\n                },\n                // Add timeout to prevent hanging\n                signal: AbortSignal.timeout(30000) // 30 second timeout\n              });\n\n              if (!response.ok) {\n                throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n              }\n\n              const data = await response.json();\n\n              if (data.success) {\n                batchData = data.data;\n\n                // Cache the batch data (but names are cached separately in the service)\n                if (!forceRefresh) {\n                  const cacheKey = CacheKeys.niftyStocks(batchIndex);\n                  cacheService.set(cacheKey, batchData);\n                }\n\n                console.log(`✅ Successfully fetched batch ${batchIndex + 1}: ${batchData.stocks?.length || 0} stocks`);\n              } else {\n                console.error(`❌ API returned error for batch ${batchIndex}:`, data.error);\n                continue;\n              }\n            } catch (fetchError) {\n              console.error(`❌ Network error fetching batch ${batchIndex}:`, fetchError);\n              // Continue with next batch instead of failing completely\n              continue;\n            }\n          }\n\n          if (batchData && batchData.stocks) {\n            // Use cached names from background service for instant display\n            const stocksWithCachedNames = batchData.stocks.map((stock: NiftyStock) => ({\n              ...stock,\n              name: getStockName(stock.symbol) || stock.name\n            }));\n\n            allStocks.push(...stocksWithCachedNames);\n            setNiftyStocks([...allStocks]);\n            console.log(`✅ Loaded batch ${batchIndex + 1}/${totalBatches}: ${stocksWithCachedNames.length} stocks (Total: ${allStocks.length})`);\n          }\n\n          if (batchIndex < totalBatches - 1) {\n            await new Promise(resolve => setTimeout(resolve, 200));\n          }\n\n        } catch (batchError) {\n          console.error(`Error loading batch ${batchIndex}:`, batchError);\n        }\n      }\n\n      setNiftyStocks(allStocks);\n      console.log(`🎉 Loaded all price data: ${allStocks.length} total stocks`);\n\n    } catch (error) {\n      console.error('Error loading price data:', error);\n      setNiftyError('Failed to load stock price data');\n    } finally {\n      setLoadingPriceData(false);\n    }\n  };\n\n  // Search within Nifty 200 stocks\n  const handleSearch = async (query: string) => {\n    if (!query.trim()) {\n      setSearchResults([]);\n      return;\n    }\n\n    setIsSearching(true);\n    try {\n      const response = await fetch('/api/stocks/nifty200', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ searchQuery: query }),\n      });\n      \n      const data = await response.json();\n      if (data.success) {\n        setSearchResults(data.data.stocks);\n      } else {\n        setSearchResults([]);\n      }\n    } catch (error) {\n      console.error('Search error:', error);\n      setSearchResults([]);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n\n  // Filter stocks based on current filters\n  const getFilteredStocks = (stocks: NiftyStock[]) => {\n    return stocks.filter(stock => {\n      // General filters\n      if (stock.price < priceFilter.min || stock.price > priceFilter.max) return false;\n      if (showOnlyHoldings && !stock.inHoldings) return false;\n      if (showAbove2000 && stock.price <= 2000) return false;\n      return true;\n    });\n  };\n\n  // Load initial data when names are ready\n  useEffect(() => {\n    if (isNamesReady) {\n      loadPriceData(false);\n    }\n  }, [isNamesReady]);\n\n  // Handle search with debouncing\n  useEffect(() => {\n    const debounceTimer = setTimeout(() => {\n      handleSearch(searchQuery);\n    }, 300);\n    return () => clearTimeout(debounceTimer);\n  }, [searchQuery]);\n\n  // Real-time stock updates\n  const realTimeStocks = useRealTimeStocks({\n    onUpdate: (quotes) => {\n      console.log(`⚡ Received real-time update: ${quotes.length} quotes`);\n\n      // Update existing stocks with new price data\n      setNiftyStocks(currentStocks => {\n        const updatedStocks = currentStocks.map(stock => {\n          const updatedQuote = quotes.find(quote => quote.symbol === stock.symbol);\n          if (updatedQuote) {\n            return {\n              ...stock,\n              price: updatedQuote.price || stock.price,\n              change: updatedQuote.change || stock.change,\n              changePercent: updatedQuote.changePercent || stock.changePercent,\n              volume: updatedQuote.volume || stock.volume,\n              high52Week: updatedQuote.high52Week || stock.high52Week,\n              low52Week: updatedQuote.low52Week || stock.low52Week,\n              high52WeekDate: updatedQuote.high52WeekDate || stock.high52WeekDate,\n              low52WeekDate: updatedQuote.low52WeekDate || stock.low52WeekDate,\n              isBOHEligible: updatedQuote.isBOHEligible || stock.isBOHEligible\n            };\n          }\n          return stock;\n        });\n\n        return updatedStocks;\n      });\n    },\n    onError: (error) => {\n      console.error('❌ Real-time update error:', error);\n      setNiftyError('Real-time updates temporarily unavailable');\n    }\n  });\n\n  // Start real-time updates when names are ready\n  useEffect(() => {\n    if (isNamesReady && !realTimeStocks.isActive) {\n      console.log('🚀 Starting real-time updates for Stock Universal page');\n      realTimeStocks.start();\n    }\n  }, [isNamesReady, realTimeStocks]);\n\n  // Stock row component for All Stocks tab\n  const StockRow = ({ stock }: { stock: NiftyStock }) => (\n    <div className=\"flex items-center justify-between p-4 hover:bg-gray-50 border-b border-gray-100\">\n      <div className=\"flex-1\">\n        <div className=\"flex items-center space-x-3\">\n          <div>\n            <div className=\"flex items-center space-x-2\">\n              <h4 className=\"font-medium text-gray-900\">{stock.symbol}</h4>\n              {stock.inHoldings && (\n                <span className=\"px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full\">\n                  In Holdings\n                </span>\n              )}\n              {stock.price >= 2000 && !stock.inHoldings && (\n                <span className=\"px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full\">\n                  Above ₹2000\n                </span>\n              )}\n            </div>\n            <p className=\"text-sm text-gray-600 truncate max-w-xs\">{stock.name}</p>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"flex items-center space-x-6\">\n        <div className=\"text-right\">\n          <div className=\"font-semibold text-gray-900\">\n            {formatCurrency(stock.price)}\n          </div>\n          <div className={`text-sm ${getChangeColor(stock.change)}`}>\n            {stock.change >= 0 ? '+' : ''}{formatCurrency(stock.change)} ({formatPercentage(stock.changePercent)})\n          </div>\n        </div>\n\n        <div className=\"text-right text-sm text-gray-600\">\n          <div>Vol: {stock.volume?.toLocaleString() || 'N/A'}</div>\n          <div className=\"text-xs\">\n            {stock.marketCap ? `₹${(stock.marketCap / 10000000).toFixed(0)}Cr` : 'N/A'}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Page Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Stock Universal - Nifty 200</h1>\n          <p className=\"text-gray-600 mt-1\">\n            All Nifty 200 stocks with real-time prices • {getFilteredStocks(niftyStocks).length} eligible stocks\n            {loadingPriceData && niftyStocks.length > 0 && (\n              <span className=\"text-blue-600\"> (Loading {niftyStocks.length}/200...)</span>\n            )}\n          </p>\n          <div className=\"mt-2\">\n            <RealTimeIndicator\n              isActive={realTimeStocks.isActive}\n              lastUpdate={realTimeStocks.lastUpdate}\n              updateCount={realTimeStocks.updateCount}\n              error={realTimeStocks.error}\n            />\n          </div>\n        </div>\n        <div className=\"flex space-x-3\">\n          <button \n            onClick={() => setShowFilters(!showFilters)}\n            className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2\"\n          >\n            <Filter className=\"h-4 w-4\" />\n            <span>Filters</span>\n          </button>\n          <button\n            onClick={() => loadPriceData(true)}\n            disabled={loadingPriceData}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center space-x-2\"\n          >\n            <RefreshCw className={`h-4 w-4 ${loadingPriceData ? 'animate-spin' : ''}`} />\n            <span>Refresh All</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Search */}\n      <div className=\"relative\">\n        <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n        <input\n          type=\"text\"\n          placeholder=\"Search stocks by symbol or name...\"\n          value={searchQuery}\n          onChange={(e) => setSearchQuery(e.target.value)}\n          className=\"w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n        />\n        {isSearching && (\n          <div className=\"absolute right-3 top-1/2 transform -translate-y-1/2\">\n            <RefreshCw className=\"h-5 w-5 text-gray-400 animate-spin\" />\n          </div>\n        )}\n      </div>\n\n      {/* Filters Panel */}\n      {showFilters && (\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Filter Options</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">Price Range</label>\n              <div className=\"flex space-x-2\">\n                <input\n                  type=\"number\"\n                  placeholder=\"Min\"\n                  value={priceFilter.min}\n                  onChange={(e) => setPriceFilter(prev => ({ ...prev, min: Number(e.target.value) }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                />\n                <input\n                  type=\"number\"\n                  placeholder=\"Max\"\n                  value={priceFilter.max}\n                  onChange={(e) => setPriceFilter(prev => ({ ...prev, max: Number(e.target.value) }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                />\n              </div>\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">Stock Filters</label>\n              <div className=\"space-y-2\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={showOnlyHoldings}\n                    onChange={(e) => setShowOnlyHoldings(e.target.checked)}\n                    className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-600\">Show only stocks in holdings</span>\n                </label>\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={showAbove2000}\n                    onChange={(e) => setShowAbove2000(e.target.checked)}\n                    className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-600\">Show stocks above ₹2000</span>\n                </label>\n              </div>\n            </div>\n            \n            <div className=\"flex items-end\">\n              <button\n                onClick={() => {\n                  setPriceFilter({ min: 0, max: 10000 });\n                  setShowOnlyHoldings(false);\n                  setShowAbove2000(false);\n                }}\n                className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n              >\n                Reset Filters\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Error Display */}\n      {niftyError && (\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <AlertCircle className=\"h-5 w-5 text-red-600 mr-2\" />\n            <span className=\"text-red-800\">{niftyError}</span>\n          </div>\n        </div>\n      )}\n\n      {/* Search Results */}\n      {searchQuery && searchResults.length > 0 && (\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 mb-6\">\n          <div className=\"p-4 border-b border-gray-200\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">\n              Search Results for &quot;{searchQuery}&quot; ({searchResults.length})\n            </h3>\n          </div>\n          <div>\n            {searchResults.map((stock, index) => (\n              <StockRow key={`search-${stock.symbol}-${index}`} stock={stock} />\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Main Stock List */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n        {/* Header */}\n        <div className=\"p-4 border-b border-gray-200\">\n          <h3 className=\"text-lg font-semibold text-gray-900\">\n            All Nifty 200 Stocks ({getFilteredStocks(niftyStocks).length}/{niftyStocks.length})\n          </h3>\n          <p className=\"text-sm text-gray-600 mt-1\">\n            Real-time prices from Yahoo Finance API\n          </p>\n        </div>\n\n        <div>\n          {!isNamesReady ? (\n            <div className=\"p-8 text-center text-gray-500\">\n              <Loader className=\"h-8 w-8 mx-auto mb-4 animate-spin text-blue-600\" />\n              <p>Initializing stock data...</p>\n              <p className=\"text-sm mt-1\">Loading stock names for the first time</p>\n            </div>\n          ) : loadingPriceData && niftyStocks.length === 0 ? (\n            <StockListSkeleton count={10} />\n          ) : (\n            <>\n              {getFilteredStocks(niftyStocks).map((stock, index) => (\n                <StockRow key={`nifty-${stock.symbol}-${index}`} stock={stock} />\n              ))}\n\n              {loadingPriceData && niftyStocks.length > 0 && (\n                <div className=\"p-4 border-t border-gray-100 bg-blue-50\">\n                  <div className=\"flex items-center justify-center space-x-2\">\n                    <Loader className=\"h-4 w-4 animate-spin text-blue-600\" />\n                    <span className=\"text-blue-700 text-sm\">\n                      Updating price data... ({niftyStocks.length}/200)\n                    </span>\n                  </div>\n                </div>\n              )}\n\n              {niftyStocks.length === 0 && !loadingPriceData && isNamesReady && (\n                <div className=\"p-8 text-center text-gray-500\">\n                  <AlertCircle className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n                  <p>No stocks loaded.</p>\n                  <p className=\"text-sm mt-1\">Try refreshing the data.</p>\n                </div>\n              )}\n            </>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAEA;AACA;AACA;AACA;AACA;;;AAhBA;;;;;;;;;AAkBe,SAAS;;IACtB,2CAA2C;IAC3C,MAAM,EACJ,YAAY,EACZ,YAAY,EACZ,aAAa,qBAAqB,EAClC,OAAO,eAAe,EACvB,GAAG,CAAA,GAAA,oIAAA,CAAA,oBAAiB,AAAD;IAEpB,eAAe;IACf,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,iEAAiE;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE5D,eAAe;IACf,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,KAAK;QAAG,KAAK;IAAM;IACpE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,iEAAiE;IACjE,MAAM,gBAAgB;YAAO,gFAAe;QAC1C,oBAAoB;QACpB,cAAc;QAEd,IAAI;YACF,IAAI,cAAc;gBAChB,oDAAoD;gBACpD,MAAM;YACR;YAEA,MAAM,YAAY;YAClB,MAAM,eAAe,KAAK,IAAI,CAAC,MAAM;YACrC,MAAM,YAA0B,EAAE;YAElC,QAAQ,GAAG,CAAC,AAAC,6BAAmD,OAAvB,cAAa,YAAiD,OAAvC,eAAe,qBAAqB,IAAG;YAEvG,IAAK,IAAI,aAAa,GAAG,aAAa,cAAc,aAAc;gBAChE,IAAI;oBACF,IAAI;oBACJ,IAAI,CAAC,cAAc;wBACjB,MAAM,WAAW,iIAAA,CAAA,YAAS,CAAC,WAAW,CAAC;wBACvC,MAAM,SAAS,iIAAA,CAAA,eAAY,CAAC,GAAG,CAAM;wBACrC,IAAI,QAAQ;4BACV,YAAY;wBACd;oBACF;oBAEA,IAAI,CAAC,WAAW;wBACd,IAAI;4BACF,6DAA6D;4BAC7D,MAAM,MAAM,AAAC,mCAA0D,OAAxB,YAAW,eAAyB,OAAZ,WAAqD,OAAzC,eAAe,uBAAuB;4BACzH,QAAQ,GAAG,CAAC,AAAC,qBAAsC,OAAlB,aAAa,GAAE,KAAoB,OAAjB,cAAa,MAAQ,OAAJ;4BAEpE,MAAM,WAAW,MAAM,MAAM,KAAK;gCAChC,QAAQ;gCACR,SAAS;oCACP,gBAAgB;gCAClB;gCACA,iCAAiC;gCACjC,QAAQ,YAAY,OAAO,CAAC,OAAO,oBAAoB;4BACzD;4BAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gCAChB,MAAM,IAAI,MAAM,AAAC,QAA2B,OAApB,SAAS,MAAM,EAAC,MAAwB,OAApB,SAAS,UAAU;4BACjE;4BAEA,MAAM,OAAO,MAAM,SAAS,IAAI;4BAEhC,IAAI,KAAK,OAAO,EAAE;oCAS+C;gCAR/D,YAAY,KAAK,IAAI;gCAErB,wEAAwE;gCACxE,IAAI,CAAC,cAAc;oCACjB,MAAM,WAAW,iIAAA,CAAA,YAAS,CAAC,WAAW,CAAC;oCACvC,iIAAA,CAAA,eAAY,CAAC,GAAG,CAAC,UAAU;gCAC7B;gCAEA,QAAQ,GAAG,CAAC,AAAC,gCAAkD,OAAnB,aAAa,GAAE,MAAkC,OAA9B,EAAA,oBAAA,UAAU,MAAM,cAAhB,wCAAA,kBAAkB,MAAM,KAAI,GAAE;4BAC/F,OAAO;gCACL,QAAQ,KAAK,CAAC,AAAC,kCAA4C,OAAX,YAAW,MAAI,KAAK,KAAK;gCACzE;4BACF;wBACF,EAAE,OAAO,YAAY;4BACnB,QAAQ,KAAK,CAAC,AAAC,kCAA4C,OAAX,YAAW,MAAI;4BAE/D;wBACF;oBACF;oBAEA,IAAI,aAAa,UAAU,MAAM,EAAE;wBACjC,+DAA+D;wBAC/D,MAAM,wBAAwB,UAAU,MAAM,CAAC,GAAG,CAAC,CAAC,QAAsB,CAAC;gCACzE,GAAG,KAAK;gCACR,MAAM,aAAa,MAAM,MAAM,KAAK,MAAM,IAAI;4BAChD,CAAC;wBAED,UAAU,IAAI,IAAI;wBAClB,eAAe;+BAAI;yBAAU;wBAC7B,QAAQ,GAAG,CAAC,AAAC,kBAAmC,OAAlB,aAAa,GAAE,KAAoB,OAAjB,cAAa,MAAmD,OAA/C,sBAAsB,MAAM,EAAC,oBAAmC,OAAjB,UAAU,MAAM,EAAC;oBACnI;oBAEA,IAAI,aAAa,eAAe,GAAG;wBACjC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACnD;gBAEF,EAAE,OAAO,YAAY;oBACnB,QAAQ,KAAK,CAAC,AAAC,uBAAiC,OAAX,YAAW,MAAI;gBACtD;YACF;YAEA,eAAe;YACf,QAAQ,GAAG,CAAC,AAAC,6BAA6C,OAAjB,UAAU,MAAM,EAAC;QAE5D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,cAAc;QAChB,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,iCAAiC;IACjC,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,iBAAiB,EAAE;YACnB;QACF;QAEA,eAAe;QACf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,aAAa;gBAAM;YAC5C;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,iBAAiB,KAAK,IAAI,CAAC,MAAM;YACnC,OAAO;gBACL,iBAAiB,EAAE;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,iBAAiB,EAAE;QACrB,SAAU;YACR,eAAe;QACjB;IACF;IAEA,yCAAyC;IACzC,MAAM,oBAAoB,CAAC;QACzB,OAAO,OAAO,MAAM,CAAC,CAAA;YACnB,kBAAkB;YAClB,IAAI,MAAM,KAAK,GAAG,YAAY,GAAG,IAAI,MAAM,KAAK,GAAG,YAAY,GAAG,EAAE,OAAO;YAC3E,IAAI,oBAAoB,CAAC,MAAM,UAAU,EAAE,OAAO;YAClD,IAAI,iBAAiB,MAAM,KAAK,IAAI,MAAM,OAAO;YACjD,OAAO;QACT;IACF;IAEA,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,cAAc;gBAChB,cAAc;YAChB;QACF;uCAAG;QAAC;KAAa;IAEjB,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM,gBAAgB;8DAAW;oBAC/B,aAAa;gBACf;6DAAG;YACH;gDAAO,IAAM,aAAa;;QAC5B;uCAAG;QAAC;KAAY;IAEhB,0BAA0B;IAC1B,MAAM,iBAAiB,CAAA,GAAA,oIAAA,CAAA,oBAAiB,AAAD,EAAE;QACvC,QAAQ;oEAAE,CAAC;gBACT,QAAQ,GAAG,CAAC,AAAC,gCAA6C,OAAd,OAAO,MAAM,EAAC;gBAE1D,6CAA6C;gBAC7C;4EAAe,CAAA;wBACb,MAAM,gBAAgB,cAAc,GAAG;kGAAC,CAAA;gCACtC,MAAM,eAAe,OAAO,IAAI;uHAAC,CAAA,QAAS,MAAM,MAAM,KAAK,MAAM,MAAM;;gCACvE,IAAI,cAAc;oCAChB,OAAO;wCACL,GAAG,KAAK;wCACR,OAAO,aAAa,KAAK,IAAI,MAAM,KAAK;wCACxC,QAAQ,aAAa,MAAM,IAAI,MAAM,MAAM;wCAC3C,eAAe,aAAa,aAAa,IAAI,MAAM,aAAa;wCAChE,QAAQ,aAAa,MAAM,IAAI,MAAM,MAAM;wCAC3C,YAAY,aAAa,UAAU,IAAI,MAAM,UAAU;wCACvD,WAAW,aAAa,SAAS,IAAI,MAAM,SAAS;wCACpD,gBAAgB,aAAa,cAAc,IAAI,MAAM,cAAc;wCACnE,eAAe,aAAa,aAAa,IAAI,MAAM,aAAa;wCAChE,eAAe,aAAa,aAAa,IAAI,MAAM,aAAa;oCAClE;gCACF;gCACA,OAAO;4BACT;;wBAEA,OAAO;oBACT;;YACF;;QACA,OAAO;oEAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,cAAc;YAChB;;IACF;IAEA,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,gBAAgB,CAAC,eAAe,QAAQ,EAAE;gBAC5C,QAAQ,GAAG,CAAC;gBACZ,eAAe,KAAK;YACtB;QACF;uCAAG;QAAC;QAAc;KAAe;IAEjC,yCAAyC;IACzC,MAAM,WAAW;YAAC,EAAE,KAAK,EAAyB;YAkC/B;6BAjCjB,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;;8CACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6B,MAAM,MAAM;;;;;;wCACtD,MAAM,UAAU,kBACf,6LAAC;4CAAK,WAAU;sDAA6D;;;;;;wCAI9E,MAAM,KAAK,IAAI,QAAQ,CAAC,MAAM,UAAU,kBACvC,6LAAC;4CAAK,WAAU;sDAAyD;;;;;;;;;;;;8CAK7E,6LAAC;oCAAE,WAAU;8CAA2C,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAKxE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,KAAK;;;;;;8CAE7B,6LAAC;oCAAI,WAAW,AAAC,WAAuC,OAA7B,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,MAAM;;wCACnD,MAAM,MAAM,IAAI,IAAI,MAAM;wCAAI,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,MAAM;wCAAE;wCAAG,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,aAAa;wCAAE;;;;;;;;;;;;;sCAIzG,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;wCAAI;wCAAM,EAAA,gBAAA,MAAM,MAAM,cAAZ,oCAAA,cAAc,cAAc,OAAM;;;;;;;8CAC7C,6LAAC;oCAAI,WAAU;8CACZ,MAAM,SAAS,GAAG,AAAC,IAA2C,OAAxC,CAAC,MAAM,SAAS,GAAG,QAAQ,EAAE,OAAO,CAAC,IAAG,QAAM;;;;;;;;;;;;;;;;;;;;;;;;;IAS/E,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;;oCAAqB;oCACc,kBAAkB,aAAa,MAAM;oCAAC;oCACnF,oBAAoB,YAAY,MAAM,GAAG,mBACxC,6LAAC;wCAAK,WAAU;;4CAAgB;4CAAW,YAAY,MAAM;4CAAC;;;;;;;;;;;;;0CAGlE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gJAAA,CAAA,oBAAiB;oCAChB,UAAU,eAAe,QAAQ;oCACjC,YAAY,eAAe,UAAU;oCACrC,aAAa,eAAe,WAAW;oCACvC,OAAO,eAAe,KAAK;;;;;;;;;;;;;;;;;kCAIjC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,eAAe,CAAC;gCAC/B,WAAU;;kDAEV,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC;gCACC,SAAS,IAAM,cAAc;gCAC7B,UAAU;gCACV,WAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAW,AAAC,WAAiD,OAAvC,mBAAmB,iBAAiB;;;;;;kDACrE,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,6LAAC;wBACC,MAAK;wBACL,aAAY;wBACZ,OAAO;wBACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wBAC9C,WAAU;;;;;;oBAEX,6BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;;;;;;;YAM1B,6BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO,YAAY,GAAG;gDACtB,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,KAAK,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAE,CAAC;gDACjF,WAAU;;;;;;0DAEZ,6LAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO,YAAY,GAAG;gDACtB,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,KAAK,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAE,CAAC;gDACjF,WAAU;;;;;;;;;;;;;;;;;;0CAKhB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDACC,MAAK;wDACL,SAAS;wDACT,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,OAAO;wDACrD,WAAU;;;;;;kEAEZ,6LAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;0DAE/C,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDACC,MAAK;wDACL,SAAS;wDACT,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,OAAO;wDAClD,WAAU;;;;;;kEAEZ,6LAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;;;;;;;;;;;;;0CAKnD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;wCACP,eAAe;4CAAE,KAAK;4CAAG,KAAK;wCAAM;wCACpC,oBAAoB;wCACpB,iBAAiB;oCACnB;oCACA,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;YASR,4BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6LAAC;4BAAK,WAAU;sCAAgB;;;;;;;;;;;;;;;;;YAMrC,eAAe,cAAc,MAAM,GAAG,mBACrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;;gCAAsC;gCACxB;gCAAY;gCAAS,cAAc,MAAM;gCAAC;;;;;;;;;;;;kCAGxE,6LAAC;kCACE,cAAc,GAAG,CAAC,CAAC,OAAO,sBACzB,6LAAC;gCAAiD,OAAO;+BAA1C,AAAC,UAAyB,OAAhB,MAAM,MAAM,EAAC,KAAS,OAAN;;;;;;;;;;;;;;;;0BAOjD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAsC;oCAC3B,kBAAkB,aAAa,MAAM;oCAAC;oCAAE,YAAY,MAAM;oCAAC;;;;;;;0CAEpF,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAK5C,6LAAC;kCACE,CAAC,6BACA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;8CAAE;;;;;;8CACH,6LAAC;oCAAE,WAAU;8CAAe;;;;;;;;;;;mCAE5B,oBAAoB,YAAY,MAAM,KAAK,kBAC7C,6LAAC,4IAAA,CAAA,oBAAiB;4BAAC,OAAO;;;;;iDAE1B;;gCACG,kBAAkB,aAAa,GAAG,CAAC,CAAC,OAAO,sBAC1C,6LAAC;wCAAgD,OAAO;uCAAzC,AAAC,SAAwB,OAAhB,MAAM,MAAM,EAAC,KAAS,OAAN;;;;;gCAGzC,oBAAoB,YAAY,MAAM,GAAG,mBACxC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDAAK,WAAU;;oDAAwB;oDACb,YAAY,MAAM;oDAAC;;;;;;;;;;;;;;;;;;gCAMnD,YAAY,MAAM,KAAK,KAAK,CAAC,oBAAoB,8BAChD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,6LAAC;sDAAE;;;;;;sDACH,6LAAC;4CAAE,WAAU;sDAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C;GAtdwB;;QAOlB,oIAAA,CAAA,oBAAiB;QAgLE,oIAAA,CAAA,oBAAiB;;;KAvLlB", "debugId": null}}]}