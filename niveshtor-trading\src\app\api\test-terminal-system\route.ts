import { NextRequest, NextResponse } from 'next/server';
import { staticDataCache } from '@/lib/static-data-cache';
import { dynamicDataUpdater } from '@/lib/dynamic-data-updater';
import { appDataPreloader } from '@/lib/app-data-preloader';

export async function GET(request: NextRequest) {
  try {
    console.log('🖥️ Testing terminal-style background data synchronization system...');

    const testResults = {
      timestamp: new Date().toISOString(),
      tests: [] as any[]
    };

    // Test 1: App Data Preloader Status
    console.log('🚀 Test 1: App Data Preloader Status...');
    const preloaderStatus = appDataPreloader.getStatus();
    const isReadyForInstantNav = appDataPreloader.isReadyForInstantNavigation();

    testResults.tests.push({
      name: 'App Data Preloader Status',
      status: preloaderStatus.isPreloaded && isReadyForInstantNav ? 'PASS' : 'FAIL',
      data: {
        isPreloaded: preloaderStatus.isPreloaded,
        isPreloading: preloaderStatus.isPreloading,
        readyForInstantNavigation: isReadyForInstantNav,
        preloadingResult: preloaderStatus.result ? {
          success: preloaderStatus.result.success,
          totalTime: preloaderStatus.result.totalTime,
          staticDataLoaded: preloaderStatus.result.staticDataLoaded,
          servicesStarted: preloaderStatus.result.servicesStarted
        } : null
      }
    });

    // Test 2: Static Data Cache Status
    console.log('📋 Test 2: Static Data Cache Status...');
    const staticCacheStatus = staticDataCache.getStaticCacheStatus();
    const staticCacheReady = staticDataCache.isStaticCacheReady();

    // Test static data availability
    const stockUniverse = staticDataCache.getStockUniverse();
    const bohEligible = staticDataCache.getBOHEligibleStocks();
    const holdingsStatic = staticDataCache.getHoldingsStatic();
    const gttOrdersStatic = staticDataCache.getGTTOrdersStatic();

    testResults.tests.push({
      name: 'Static Data Cache Status',
      status: staticCacheReady && stockUniverse.length > 0 ? 'PASS' : 'FAIL',
      data: {
        isInitialized: staticCacheStatus.isInitialized,
        isLoading: staticCacheStatus.isLoading,
        stockCount: staticCacheStatus.stockCount,
        bohEligibleCount: staticCacheStatus.bohEligibleCount,
        loadedAt: staticCacheStatus.loadedAt,
        sessionId: staticCacheStatus.sessionId,
        actualData: {
          stockUniverseCount: stockUniverse.length,
          bohEligibleCount: bohEligible.length,
          holdingsStaticCount: holdingsStatic.length,
          gttOrdersStaticCount: gttOrdersStatic.length
        },
        sampleStockData: stockUniverse.slice(0, 3).map(stock => ({
          symbol: stock.symbol,
          name: stock.name,
          isBOHEligible: stock.isBOHEligible,
          isInHoldings: stock.isInHoldings
        }))
      }
    });

    // Test 3: Dynamic Data Updater Status
    console.log('⚡ Test 3: Dynamic Data Updater Status...');
    const dynamicStatus = dynamicDataUpdater.getStatus();
    const stockPrices = staticDataCache.getAllStockPrices();

    testResults.tests.push({
      name: 'Dynamic Data Updater Status',
      status: dynamicStatus.isRunning ? 'PASS' : 'FAIL',
      data: {
        isRunning: dynamicStatus.isRunning,
        isMarketOpen: dynamicStatus.isMarketOpen,
        activeIntervals: dynamicStatus.activeIntervals,
        lastUpdateTimes: dynamicStatus.lastUpdateTimes,
        stockPricesCount: stockPrices.size,
        samplePrices: Array.from(stockPrices.entries()).slice(0, 3).map(([symbol, data]) => ({
          symbol,
          currentPrice: data.currentPrice,
          dayChange: data.dayChange,
          lastUpdated: data.lastUpdated
        }))
      }
    });

    // Test 4: Session Persistence
    console.log('💾 Test 4: Session Persistence...');
    let sessionPersistenceWorking = false;
    try {
      const sessionData = sessionStorage.getItem('niveshtor_static_cache');
      sessionPersistenceWorking = sessionData !== null;
    } catch (error) {
      console.warn('Session storage not available:', error);
    }

    testResults.tests.push({
      name: 'Session Persistence',
      status: sessionPersistenceWorking ? 'PASS' : 'FAIL',
      data: {
        sessionStorageAvailable: typeof sessionStorage !== 'undefined',
        staticCacheInSession: sessionPersistenceWorking,
        sessionId: staticCacheStatus.sessionId,
        cacheAge: staticCacheStatus.loadedAt ? 
          Math.round((new Date().getTime() - new Date(staticCacheStatus.loadedAt).getTime()) / 1000) : 0
      }
    });

    // Test 5: Data Separation (Static vs Dynamic)
    console.log('🔄 Test 5: Data Separation (Static vs Dynamic)...');
    
    // Check that static data doesn't change
    const staticDataConsistent = stockUniverse.every(stock => 
      stock.symbol && stock.name && typeof stock.isBOHEligible === 'boolean'
    );

    // Check that dynamic data is updating
    const dynamicDataUpdating = stockPrices.size > 0 && 
      Array.from(stockPrices.values()).some(price => 
        price.lastUpdated && new Date(price.lastUpdated).getTime() > Date.now() - 5 * 60 * 1000
      );

    testResults.tests.push({
      name: 'Data Separation (Static vs Dynamic)',
      status: staticDataConsistent && dynamicDataUpdating ? 'PASS' : 'FAIL',
      data: {
        staticDataConsistent,
        dynamicDataUpdating,
        staticDataFields: ['symbol', 'name', 'exchange', 'isBOHEligible', 'isInHoldings'],
        dynamicDataFields: ['currentPrice', 'dayChange', 'dayChangePercent', 'lastUpdated'],
        staticDataSample: stockUniverse[0] ? {
          symbol: stockUniverse[0].symbol,
          name: stockUniverse[0].name,
          isBOHEligible: stockUniverse[0].isBOHEligible
        } : null,
        dynamicDataSample: stockPrices.size > 0 ? 
          Array.from(stockPrices.entries())[0] : null
      }
    });

    // Test 6: Instant Navigation Readiness
    console.log('🚀 Test 6: Instant Navigation Readiness...');
    
    // Simulate navigation response times
    const navigationTests = [];
    const testEndpoints = [
      { name: 'Stock Universe', hasStaticData: stockUniverse.length > 0 },
      { name: 'BOH Eligible', hasStaticData: bohEligible.length > 0 },
      { name: 'Holdings', hasStaticData: holdingsStatic.length >= 0 },
      { name: 'GTT Orders', hasStaticData: gttOrdersStatic.length >= 0 }
    ];

    for (const endpoint of testEndpoints) {
      const startTime = performance.now();
      
      // Simulate instant data access
      const staticDataAvailable = endpoint.hasStaticData;
      const dynamicDataAvailable = stockPrices.size > 0;
      
      const endTime = performance.now();
      const responseTime = Math.round(endTime - startTime);

      navigationTests.push({
        page: endpoint.name,
        responseTime,
        staticDataReady: staticDataAvailable,
        dynamicDataReady: dynamicDataAvailable,
        instantNavigation: responseTime < 200 && staticDataAvailable
      });
    }

    const instantNavigationReady = navigationTests.every(test => test.instantNavigation);
    const averageResponseTime = navigationTests.reduce((sum, test) => sum + test.responseTime, 0) / navigationTests.length;

    testResults.tests.push({
      name: 'Instant Navigation Readiness',
      status: instantNavigationReady && averageResponseTime < 200 ? 'PASS' : 'FAIL',
      data: {
        instantNavigationReady,
        averageResponseTime: Math.round(averageResponseTime),
        target: '<200ms',
        navigationTests
      }
    });

    // Test 7: Terminal-Style Behavior Validation
    console.log('🖥️ Test 7: Terminal-Style Behavior Validation...');
    
    const terminalBehaviorChecks = {
      staticDataLoadedOnce: staticCacheStatus.isInitialized && !staticCacheStatus.isLoading,
      staticDataPersistent: sessionPersistenceWorking,
      dynamicDataContinuousUpdate: dynamicStatus.isRunning,
      noLoadingDelaysForStatic: instantNavigationReady,
      backgroundUpdatesOnly: dynamicStatus.activeIntervals.length > 0
    };

    const terminalBehaviorWorking = Object.values(terminalBehaviorChecks).every(check => check);

    testResults.tests.push({
      name: 'Terminal-Style Behavior Validation',
      status: terminalBehaviorWorking ? 'PASS' : 'FAIL',
      data: {
        terminalBehaviorWorking,
        checks: terminalBehaviorChecks,
        expectedBehavior: {
          firstVisit: 'Brief initialization (3-5s) to load all static data',
          subsequentNavigation: 'Immediate page loads with static data visible',
          continuousSession: 'Stock names never reload, only prices update',
          returnVisits: 'No initialization if same session',
          freshSession: 'Single initialization, then instant behavior'
        }
      }
    });

    // Calculate overall results
    const passedTests = testResults.tests.filter(t => t.status === 'PASS').length;
    const totalTests = testResults.tests.length;
    const overallStatus = passedTests === totalTests ? 'TERMINAL_READY' : 
                         passedTests >= totalTests * 0.85 ? 'MOSTLY_TERMINAL' : 'NEEDS_IMPROVEMENT';

    const summary = {
      overallStatus,
      passedTests,
      totalTests,
      successRate: `${Math.round((passedTests / totalTests) * 100)}%`,
      
      // Key terminal behavior metrics
      staticDataPreloaded: staticCacheReady && stockUniverse.length > 0,
      dynamicUpdatesRunning: dynamicStatus.isRunning,
      instantNavigationReady,
      sessionPersistenceWorking,
      averageNavigationTime: Math.round(averageResponseTime),
      
      // Data metrics
      totalStaticDataPoints: stockUniverse.length + bohEligible.length + holdingsStatic.length + gttOrdersStatic.length,
      dynamicDataPoints: stockPrices.size,
      
      // System readiness
      terminalStyleReady: overallStatus === 'TERMINAL_READY',
      systemMessage: overallStatus === 'TERMINAL_READY' 
        ? 'Terminal-style background data synchronization is fully operational'
        : overallStatus === 'MOSTLY_TERMINAL'
          ? 'Terminal-style system is mostly working with minor issues'
          : 'Terminal-style system needs attention before optimal performance',
      
      // Performance characteristics
      initializationTime: preloaderStatus.result?.totalTime || 0,
      staticCacheAge: staticCacheStatus.loadedAt ? 
        Math.round((new Date().getTime() - new Date(staticCacheStatus.loadedAt).getTime()) / 1000) : 0,
      backgroundUpdateIntervals: dynamicStatus.activeIntervals
    };

    console.log(`🖥️ Terminal-style system test: ${summary.systemMessage}`);

    return NextResponse.json({
      success: true,
      message: 'Terminal-style background data synchronization test completed',
      summary,
      testResults
    });

  } catch (error) {
    console.error('❌ Terminal-style system test failed:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Test failed',
        message: 'Terminal-style system test failed'
      },
      { status: 500 }
    );
  }
}
