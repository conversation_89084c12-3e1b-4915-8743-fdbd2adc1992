import { NextRequest, NextResponse } from 'next/server';
import { yahooFinanceService } from '@/lib/yahoo-finance';
import { NIFTY_200_SYMBOLS, getYahooSymbol, addBOHEligibility } from '@/lib/nifty-stocks';
import { holdingsService } from '@/lib/holdings-service';

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Debug: Checking BOH eligibility data...');

    // Test with first 20 stocks to see what data we get
    const testSymbols = NIFTY_200_SYMBOLS.slice(0, 20);
    const yahooSymbols = testSymbols.map(getYahooSymbol);
    
    console.log('🔍 Testing symbols:', testSymbols);

    // Fetch quotes with cached names
    const quotes = await yahooFinanceService.getMultipleQuotesWithCachedNames(yahooSymbols);
    
    console.log(`📊 Got ${quotes.length} quotes`);

    // Process each stock and check BOH eligibility
    const results = testSymbols.map(nseSymbol => {
      const yahooSymbol = getYahooSymbol(nseSymbol);
      const quote = quotes.find(q => q.symbol === yahooSymbol);
      
      if (!quote) {
        return {
          symbol: nseSymbol,
          yahooSymbol,
          status: 'NO_QUOTE',
          price: 0,
          high52Week: null,
          low52Week: null,
          high52WeekDate: null,
          low52WeekDate: null,
          hasDates: false,
          isBOHEligible: false,
          isEligible: false
        };
      }

      const price = quote.price || 0;
      const inHoldings = holdingsService.isStockInHoldings(nseSymbol);
      const isEligible = holdingsService.isStockEligibleForTrading(nseSymbol, price);

      const stock = {
        symbol: nseSymbol,
        name: quote.name,
        price,
        change: quote.change || 0,
        changePercent: quote.changePercent || 0,
        volume: quote.volume || 0,
        marketCap: quote.marketCap,
        high52Week: quote.high52Week,
        low52Week: quote.low52Week,
        high52WeekDate: quote.high52WeekDate,
        low52WeekDate: quote.low52WeekDate,
        isEligible,
        inHoldings
      };

      const stockWithBOH = addBOHEligibility(stock);

      return {
        symbol: nseSymbol,
        yahooSymbol,
        status: 'SUCCESS',
        price: stock.price,
        high52Week: stock.high52Week,
        low52Week: stock.low52Week,
        high52WeekDate: stock.high52WeekDate,
        low52WeekDate: stock.low52WeekDate,
        hasDates: !!(stock.high52WeekDate && stock.low52WeekDate),
        isBOHEligible: stockWithBOH.isBOHEligible,
        isEligible: stock.isEligible,
        inHoldings: stock.inHoldings
      };
    });

    const summary = {
      total: results.length,
      withQuotes: results.filter(r => r.status === 'SUCCESS').length,
      withDates: results.filter(r => r.hasDates).length,
      bohEligible: results.filter(r => r.isBOHEligible).length,
      eligible: results.filter(r => r.isEligible).length
    };

    console.log('📈 Summary:', summary);

    return NextResponse.json({
      success: true,
      message: `Debugged ${results.length} stocks`,
      data: results,
      summary
    });

  } catch (error) {
    console.error('❌ Debug BOH error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Debug failed' 
      },
      { status: 500 }
    );
  }
}
