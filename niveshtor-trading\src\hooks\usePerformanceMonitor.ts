// Performance monitoring hook for tracking navigation and API performance

import { useEffect, useRef, useState } from 'react';
import { usePathname } from 'next/navigation';

interface PerformanceMetrics {
  navigationTime: number;
  apiCallTimes: { [key: string]: number };
  renderTime: number;
  memoryUsage?: number;
}

export function usePerformanceMonitor() {
  const pathname = usePathname();
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    navigationTime: 0,
    apiCallTimes: {},
    renderTime: 0
  });
  
  const navigationStartTime = useRef<number>(0);
  const renderStartTime = useRef<number>(0);
  const apiCallTimes = useRef<{ [key: string]: number }>({});

  // Track navigation performance
  useEffect(() => {
    navigationStartTime.current = performance.now();
    
    const handleLoad = () => {
      const navigationTime = performance.now() - navigationStartTime.current;
      setMetrics(prev => ({ ...prev, navigationTime }));
    };

    // Use requestIdleCallback for better performance
    if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
      window.requestIdleCallback(handleLoad);
    } else {
      setTimeout(handleLoad, 0);
    }
  }, [pathname]);

  // Track render performance
  useEffect(() => {
    renderStartTime.current = performance.now();
    
    return () => {
      const renderTime = performance.now() - renderStartTime.current;
      setMetrics(prev => ({ ...prev, renderTime }));
    };
  });

  // Track memory usage (if available)
  useEffect(() => {
    const updateMemoryUsage = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        setMetrics(prev => ({
          ...prev,
          memoryUsage: memory.usedJSHeapSize / 1024 / 1024 // Convert to MB
        }));
      }
    };

    const interval = setInterval(updateMemoryUsage, 5000); // Update every 5 seconds
    updateMemoryUsage(); // Initial update

    return () => clearInterval(interval);
  }, []);

  // Function to track API call performance
  const trackApiCall = (apiName: string, startTime: number) => {
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    apiCallTimes.current[apiName] = duration;
    setMetrics(prev => ({
      ...prev,
      apiCallTimes: { ...prev.apiCallTimes, [apiName]: duration }
    }));
  };

  // Function to start tracking an API call
  const startApiCall = (apiName: string) => {
    const startTime = performance.now();
    return () => trackApiCall(apiName, startTime);
  };

  // Get performance insights
  const getPerformanceInsights = () => {
    const insights: string[] = [];
    
    if (metrics.navigationTime > 1000) {
      insights.push('Navigation is slow (>1s)');
    }
    
    if (metrics.renderTime > 100) {
      insights.push('Render time is high (>100ms)');
    }
    
    Object.entries(metrics.apiCallTimes).forEach(([api, time]) => {
      if (time > 2000) {
        insights.push(`${api} API is slow (>${time.toFixed(0)}ms)`);
      }
    });
    
    if (metrics.memoryUsage && metrics.memoryUsage > 50) {
      insights.push(`High memory usage (${metrics.memoryUsage.toFixed(1)}MB)`);
    }
    
    return insights;
  };

  // Log performance metrics to console (development only)
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.group('🚀 Performance Metrics');
      console.log('Navigation Time:', `${metrics.navigationTime.toFixed(2)}ms`);
      console.log('Render Time:', `${metrics.renderTime.toFixed(2)}ms`);
      console.log('API Call Times:', metrics.apiCallTimes);
      if (metrics.memoryUsage) {
        console.log('Memory Usage:', `${metrics.memoryUsage.toFixed(1)}MB`);
      }
      
      const insights = getPerformanceInsights();
      if (insights.length > 0) {
        console.warn('Performance Insights:', insights);
      }
      console.groupEnd();
    }
  }, [metrics]);

  return {
    metrics,
    startApiCall,
    getPerformanceInsights
  };
}

// Higher-order component for performance monitoring
export function withPerformanceMonitoring<T extends Record<string, any>>(
  Component: React.ComponentType<T>
) {
  return function PerformanceMonitoredComponent(props: T) {
    const { metrics } = usePerformanceMonitor();
    
    return (
      <>
        <Component {...props} />
        {process.env.NODE_ENV === 'development' && (
          <div className="fixed bottom-4 right-4 bg-black bg-opacity-75 text-white text-xs p-2 rounded z-50">
            <div>Nav: {metrics.navigationTime.toFixed(0)}ms</div>
            <div>Render: {metrics.renderTime.toFixed(0)}ms</div>
            {metrics.memoryUsage && (
              <div>Memory: {metrics.memoryUsage.toFixed(1)}MB</div>
            )}
          </div>
        )}
      </>
    );
  };
}

// Custom hook for API performance tracking
export function useApiPerformance() {
  const apiTimes = useRef<{ [key: string]: number }>({});
  
  const trackApiCall = async <T,>(
    apiName: string,
    apiCall: () => Promise<T>
  ): Promise<T> => {
    const startTime = performance.now();
    
    try {
      const result = await apiCall();
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      apiTimes.current[apiName] = duration;
      
      // Log slow API calls
      if (duration > 2000) {
        console.warn(`🐌 Slow API call: ${apiName} took ${duration.toFixed(0)}ms`);
      }
      
      return result;
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      console.error(`❌ API call failed: ${apiName} (${duration.toFixed(0)}ms)`, error);
      throw error;
    }
  };
  
  const getApiMetrics = () => apiTimes.current;
  
  return { trackApiCall, getApiMetrics };
}
