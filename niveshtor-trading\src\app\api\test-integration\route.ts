import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    console.log('🔗 Testing complete integration workflow...');

    const requestUrl = new URL(request.url);
    const baseUrl = `${requestUrl.protocol}//${requestUrl.host}`;

    const results = {
      timestamp: new Date().toISOString(),
      tests: [] as any[]
    };

    // Test 1: Weekly High Signal Detection
    console.log('📊 Test 1: Weekly High Signal Detection');
    try {
      const signalResponse = await fetch(`${baseUrl}/api/auto-gtt?action=signals`);
      const signalData = await signalResponse.json();
      
      results.tests.push({
        name: 'Weekly High Signal Detection',
        status: signalData.success ? 'PASS' : 'FAIL',
        data: {
          signalsFound: signalData.data?.length || 0,
          strongSignals: signalData.data?.filter((s: any) => s.signalStrength === 'STRONG').length || 0,
          moderateSignals: signalData.data?.filter((s: any) => s.signalStrength === 'MODERATE').length || 0
        },
        error: signalData.success ? null : signalData.error
      });
    } catch (error) {
      results.tests.push({
        name: 'Weekly High Signal Detection',
        status: 'ERROR',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test 2: Auto GTT Service Status
    console.log('🤖 Test 2: Auto GTT Service Status');
    try {
      const statusResponse = await fetch(`${baseUrl}/api/auto-gtt?action=status`);
      const statusData = await statusResponse.json();
      
      results.tests.push({
        name: 'Auto GTT Service Status',
        status: statusData.success ? 'PASS' : 'FAIL',
        data: {
          serviceInitialized: statusData.data?.service?.isInitialized || false,
          detectorRunning: statusData.data?.detector?.isRunning || false,
          marketOpen: statusData.data?.detector?.isMarketOpen || false,
          autoOrdersCount: statusData.data?.service?.autoCreatedOrders || 0
        },
        error: statusData.success ? null : statusData.error
      });
    } catch (error) {
      results.tests.push({
        name: 'Auto GTT Service Status',
        status: 'ERROR',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test 3: Manual GTT Order Creation
    console.log('📝 Test 3: Manual GTT Order Creation');
    try {
      const gttResponse = await fetch(`${baseUrl}/api/gtt/create-signal-orders`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      const gttData = await gttResponse.json();
      
      results.tests.push({
        name: 'Manual GTT Order Creation',
        status: gttData.success ? 'PASS' : 'FAIL',
        data: {
          totalBOHStocks: gttData.data?.totalBOHStocks || 0,
          validForGTT: gttData.data?.validForGTT || 0,
          ordersCreated: gttData.data?.orders?.length || 0,
          totalValue: gttData.data?.stats?.totalValue || 0
        },
        error: gttData.success ? null : gttData.error
      });
    } catch (error) {
      results.tests.push({
        name: 'Manual GTT Order Creation',
        status: 'ERROR',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test 4: Auto GTT Orders Retrieval
    console.log('📋 Test 4: Auto GTT Orders Retrieval');
    try {
      const ordersResponse = await fetch(`${baseUrl}/api/auto-gtt?action=orders`);
      const ordersData = await ordersResponse.json();
      
      results.tests.push({
        name: 'Auto GTT Orders Retrieval',
        status: ordersData.success ? 'PASS' : 'FAIL',
        data: {
          totalOrders: ordersData.data?.length || 0,
          pendingOrders: ordersData.data?.filter((o: any) => o.status === 'PENDING').length || 0,
          autoCreatedOrders: ordersData.data?.filter((o: any) => o.autoCreated).length || 0,
          signalOrders: ordersData.data?.filter((o: any) => o.source === 'SIGNAL').length || 0
        },
        error: ordersData.success ? null : ordersData.error
      });
    } catch (error) {
      results.tests.push({
        name: 'Auto GTT Orders Retrieval',
        status: 'ERROR',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test 5: System Health Check
    console.log('🏥 Test 5: System Health Check');
    try {
      const healthResponse = await fetch(`${baseUrl}/api/test-auto-gtt-system`);
      const healthData = await healthResponse.json();
      
      results.tests.push({
        name: 'System Health Check',
        status: healthData.success && healthData.results?.overall?.status === 'SUCCESS' ? 'PASS' : 'FAIL',
        data: {
          overallStatus: healthData.results?.overall?.status || 'UNKNOWN',
          readyForProduction: healthData.results?.overall?.readyForProduction || false,
          healthScore: healthData.results?.step5_health_check?.score || '0/0',
          signalsDetected: healthData.results?.step2_signal_detection?.signalsFound || 0
        },
        error: healthData.success ? null : healthData.error
      });
    } catch (error) {
      results.tests.push({
        name: 'System Health Check',
        status: 'ERROR',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Calculate overall results
    const passedTests = results.tests.filter(t => t.status === 'PASS').length;
    const totalTests = results.tests.length;
    const overallStatus = passedTests === totalTests ? 'ALL_PASS' : 
                         passedTests >= totalTests * 0.8 ? 'MOSTLY_PASS' : 'FAIL';

    const summary = {
      overallStatus,
      passedTests,
      totalTests,
      successRate: `${Math.round((passedTests / totalTests) * 100)}%`,
      readyForProduction: overallStatus === 'ALL_PASS' || overallStatus === 'MOSTLY_PASS',
      recommendations: [] as string[]
    };

    // Add recommendations based on failed tests
    results.tests.forEach(test => {
      if (test.status !== 'PASS') {
        switch (test.name) {
          case 'Weekly High Signal Detection':
            summary.recommendations.push('Check Yahoo Finance API connectivity and BOH eligibility logic');
            break;
          case 'Auto GTT Service Status':
            summary.recommendations.push('Restart the automatic GTT service and check configuration');
            break;
          case 'Manual GTT Order Creation':
            summary.recommendations.push('Verify GTT order creation API and stock data availability');
            break;
          case 'Auto GTT Orders Retrieval':
            summary.recommendations.push('Check order storage and retrieval mechanisms');
            break;
          case 'System Health Check':
            summary.recommendations.push('Run detailed system diagnostics and check all components');
            break;
        }
      }
    });

    console.log(`🎯 Integration Test Complete: ${summary.successRate} success rate`);

    return NextResponse.json({
      success: true,
      message: 'Integration test completed',
      summary,
      results
    });

  } catch (error) {
    console.error('❌ Integration test failed:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Integration test failed',
        message: 'Integration test failed'
      },
      { status: 500 }
    );
  }
}
