# Niveshtor - Stock Trading & Backtesting Platform

A professional, modern web application for stock trading backtesting and automated trading with focus on the "New Darvas Box" trading strategy. Built with Next.js, TypeScript, and integrated with Yahoo Finance API and SmartAPI for real-time trading.

## 🚀 Features

### Dashboard Pages
- **Capital Management**: Portfolio overview, risk management, and position sizing
- **Stock Universal**: Stock search, watchlist management, and real-time quotes
- **BOH Filter**: Breakout High filtering and screening system
- **Weekly High Signal**: Weekly high breakout signal generation and tracking
- **GTT Order**: Good Till Triggered order management system
- **Current Holding**: Active positions tracking with P&L analysis
- **Back Testing**: Comprehensive strategy backtesting with performance metrics
- **Connect Broker**: SmartAPI broker integration and authentication

### Technical Features
- Real-time stock data via Yahoo Finance API
- SmartAPI integration for live trading
- SQLite database with Prisma ORM
- Professional UI with Tailwind CSS
- Responsive design for all devices
- TypeScript for type safety
- Modern React patterns with hooks

## 🛠️ Technology Stack

- **Frontend**: Next.js 15, React 18, TypeScript
- **Styling**: Tailwind CSS, Lucide React Icons
- **Database**: SQLite with Prisma ORM
- **APIs**: Yahoo Finance, SmartAPI (Angel Broking)
- **Backend**: FastAPI (Python) for broker integration
- **Deployment**: Vercel-ready configuration

## 📋 Prerequisites

- Node.js 18+ and npm
- Python 3.8+ (for SmartAPI backend)
- Git

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone <repository-url>
cd niveshtor-trading
```

### 2. Install Dependencies
```bash
# Install Node.js dependencies
npm install

# Install Python dependencies for SmartAPI backend
cd python-backend
pip install -r requirements.txt
cd ..
```

### 3. Environment Setup
Create a `.env` file in the root directory:
```env
# Database
DATABASE_URL="file:./dev.db"

# SmartAPI Configuration (Optional - for live trading)
SMARTAPI_API_KEY="your_smartapi_key"
SMARTAPI_CLIENT_ID="your_client_id"
SMARTAPI_PASSWORD="your_password"
SMARTAPI_TOTP_SECRET="your_totp_secret"

# Application Settings
NODE_ENV="development"
NEXTAUTH_SECRET="your-secret-key-here"
NEXTAUTH_URL="http://localhost:3000"
```

### 4. Database Setup
```bash
# Generate Prisma client
npx prisma generate

# Create and migrate database
npx prisma db push

# (Optional) View database in Prisma Studio
npx prisma studio
```

### 5. Start the Application
```bash
# Terminal 1: Start Next.js frontend
npm run dev

# Terminal 2: Start Python backend (optional, for broker integration)
cd python-backend
python main.py
```

The application will be available at:
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000 (if running Python backend)

## 📁 Project Structure

```
niveshtor-trading/
├── src/
│   ├── app/                    # Next.js app directory
│   │   ├── dashboard/          # Dashboard pages
│   │   │   ├── capital/        # Capital Management
│   │   │   ├── stocks/         # Stock Universal
│   │   │   ├── boh-filter/     # BOH Filter
│   │   │   ├── weekly-high/    # Weekly High Signal
│   │   │   ├── gtt-orders/     # GTT Orders
│   │   │   ├── holdings/       # Current Holdings
│   │   │   ├── backtesting/    # Back Testing
│   │   │   └── broker/         # Connect Broker
│   │   └── api/                # API routes
│   ├── components/             # React components
│   │   └── layout/             # Layout components
│   └── lib/                    # Utility functions
├── python-backend/             # FastAPI backend for SmartAPI
├── prisma/                     # Database schema and migrations
├── public/                     # Static assets
└── docs/                       # Documentation
```

## 🔧 Configuration

### SmartAPI Setup (Optional)
To enable live trading features:

1. **Create SmartAPI Account**
   - Sign up at [Angel Broking](https://smartapi.angelbroking.com/)
   - Generate API credentials from the developer console

2. **Configure TOTP**
   - Enable 2FA in your Angel Broking mobile app
   - Note down the TOTP secret key

3. **Update Environment Variables**
   ```env
   SMARTAPI_API_KEY="your_api_key"
   SMARTAPI_CLIENT_ID="your_client_id"
   SMARTAPI_PASSWORD="your_trading_password"
   SMARTAPI_TOTP_SECRET="your_totp_secret"
   ```

### Yahoo Finance API
The application uses Yahoo Finance's free API for stock data. No additional configuration required.

## 📊 Trading Strategies

### Darvas Box Strategy
The primary focus strategy with the following components:
- Box formation identification
- Volume confirmation
- Breakout detection
- Risk management rules

### Weekly High Breakout
- Identifies stocks breaking weekly highs
- Volume-based confirmation
- Configurable lookback periods

### BOH (Breakout High) Filter
- Systematic screening for breakout candidates
- Multiple filter criteria
- Real-time scanning capabilities

## 🧪 Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## 📈 Usage Guide

### 1. Capital Management
- Set up your portfolio parameters
- Configure risk management rules
- Monitor portfolio performance

### 2. Stock Research
- Search for stocks using the Stock Universal page
- Create and manage watchlists
- Monitor real-time price movements

### 3. Strategy Implementation
- Use BOH Filter to screen stocks
- Set up Weekly High signals
- Configure GTT orders for automation

### 4. Backtesting
- Test strategies on historical data
- Analyze performance metrics
- Optimize strategy parameters

### 5. Live Trading (Optional)
- Connect your broker account
- Execute trades through the platform
- Monitor positions in real-time

## 🚀 Deployment

### Vercel Deployment (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Configure environment variables in Vercel dashboard
4. Deploy automatically

### Manual Deployment
```bash
# Build the application
npm run build

# Start production server
npm start
```

### Database Migration for Production
```bash
# For production deployment
npx prisma migrate deploy
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚠️ Disclaimer

This software is for educational and research purposes only. Trading in financial markets involves substantial risk of loss. The authors and contributors are not responsible for any financial losses incurred through the use of this software.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Check the documentation in the `/docs` folder
- Review the code comments for implementation details

## 🔄 Updates and Roadmap

### Current Version: 1.0.0
- ✅ Complete dashboard implementation
- ✅ Yahoo Finance integration
- ✅ SmartAPI broker connection
- ✅ Basic backtesting framework

### Planned Features
- [ ] Advanced charting with TradingView
- [ ] Machine learning signal enhancement
- [ ] Multi-broker support
- [ ] Mobile app development
- [ ] Advanced risk management tools
- [ ] Social trading features

---

**Built with ❤️ for the trading community**
