// Current Nifty 200 stock symbols - updated list as of 2025
const RAW_NIFTY_200_SYMBOLS = [
  'NYKAA', 'MRF', 'MANKIND', 'CHOL<PERSON><PERSON>', 'CONCOR', 'ICICIPRUL<PERSON>', 'PREMIERENE', '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>IRPO<PERSON>',
  'VBL', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'BH<PERSON>', 'B<PERSON>CO<PERSON>', 'INDHOTEL', 'COALINDI<PERSON>', 'HYUNDAI',
  'GODREJ<PERSON>', 'HINDUNILVR', 'ADANIENS<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'SH<PERSON><PERSON><PERSON>', 'VMM', 'CUMMINSIND',
  'LOD<PERSON>', 'ABB', 'COCHINSHIP', 'BRITANNIA', 'ULTRACEMCO', 'AUBANK', 'KALYANKJIL',
  'BDL', 'DIVI<PERSON><PERSON>', 'INDIGO', 'POWERGRID', 'OIL', 'HEROMOTOCO', '<PERSON><PERSON><PERSON>', '<PERSON>',
  '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'B<PERSON>CHLT<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'TORNTPHARM', 'TATATECH', 'MAHABANK',
  'M&M', 'ASIANPAINT', 'UNITDSPR', 'PIIND', 'ITC', 'ASHOKLEY', 'NESTLEIND',
  'HDFCAMC', 'ADANIGREEN', 'MARICO', 'APOLLOTYRE', 'LTF', 'HDFCBANK', 'TVSMOTOR',
  'ADANIPOWER', 'MARUTI', 'MOTHERSON', 'BAJAJHFL', 'NTPCGREEN', 'JIOFIN', 'BAJAJFINSV',
  'JSWENERGY', 'TORNTPOWER', 'NTPC', 'FEDERALBNK', 'ALKEM', 'NHPC', 'BAJAJ-AUTO',
  'EICHERMOT', 'M&MFIN', 'ETERNAL', 'MPHASIS', 'HUDCO', 'PETRONET', 'SUPREMEIND',
  'HAL', 'CIPLA', 'IRCTC', 'KOTAKBANK', 'POLICYBZR', 'INDIANB', 'CANBK', 'AXISBANK',
  'ONGC', 'LICI', 'SWIGGY', 'TATAMOTORS', 'IDEA', 'SOLARINDS', 'LICHSGFIN',
  'MAZDOCK', 'TATAPOWER', 'IREDA', 'SRF', 'BAJAJHLDNG', 'SBIN', 'BHARTIHEXA',
  'ZYDUSLIFE', 'VOLTAS', 'AMBUJACEM', 'MUTHOOTFIN', 'TITAN', 'ADANIPORTS', 'SBILIFE',
  'ATGL', 'ADANIENT', 'YESBANK', 'INFY', 'TATACONSUM', 'EXIDEIND', 'AUROPHARMA',
  'PAYTM', 'PFC', 'TATAELXSI', 'TATACOMM', 'SUNPHARMA', 'INDUSTOWER', 'JSWSTEEL',
  'ESCORTS', 'IRFC', 'BHARTIARTL', 'LUPIN', 'RVNL', 'POLYCAB', 'CGPOWER',
  'GLENMARK', 'HAVELLS', 'PIDILITIND', 'TCS', 'NMDC', 'LTIM', 'TRENT', 'SUZLON',
  'DMART', 'JUBLFOOD', 'SAIL', 'COLPAL', 'LT', 'MFSL', 'SONACOMS', 'PRESTIGE',
  'IDFCFIRSTB', 'ICICIBANK', 'SJVN', 'BEL', 'OFSS', 'WIPRO', 'ICICIGI', 'ABCAPITAL',
  'COFORGE', 'JINDALSTEL', 'GRASIM', 'BANKINDIA', 'PAGEIND', 'ABFRL', 'TIINDIA',
  'INDUSINDBK', 'PNB', 'RECLTD', 'KPITTECH', 'HDFCLIFE', 'RELIANCE', 'PERSISTENT',
  'DRREDDY', 'UPL', 'OLAELEC', 'TECHM', 'OBEROIRLTY', 'APOLLOHOSP', 'BHARATFORG',
  'NAUKRI', 'HINDPETRO', 'DLF', 'TATASTEEL', 'BPCL', 'HINDALCO', 'IRB', 'APLAPOLLO',
  'NATIONALUM', 'HCLTECH', 'SIEMENS', 'IOC', 'GODREJPROP', 'IGL', 'HINDZINC',
  'PHOENIXLTD', 'VEDL', 'UNIONBANK', 'MAXHEALTH', 'GAIL'
];

// Remove duplicates and create final list
export const NIFTY_200_SYMBOLS = [...new Set(RAW_NIFTY_200_SYMBOLS)];

// Validation: Log any duplicates found and final count
const duplicates = RAW_NIFTY_200_SYMBOLS.filter((symbol, index) =>
  RAW_NIFTY_200_SYMBOLS.indexOf(symbol) !== index
);

if (duplicates.length > 0) {
  console.warn('Duplicate symbols found and removed:', duplicates);
}

console.log(`Nifty 200 symbols loaded: ${NIFTY_200_SYMBOLS.length} unique symbols`);

export interface NiftyStock {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  marketCap?: number;
  high52Week?: number;
  low52Week?: number;
  high52WeekDate?: string;
  low52WeekDate?: string;
  isEligible: boolean; // CMP < 2000 or in holdings
  inHoldings: boolean;
  isBOHEligible?: boolean;
}

// Function to get Yahoo Finance symbol format for Indian stocks
export function getYahooSymbol(nseSymbol: string): string {
  // Handle special cases for Yahoo Finance symbol mapping
  const symbolMappings: { [key: string]: string } = {
    'M&M': 'MM.NS',
    'M&MFIN': 'MMFIN.NS',
    'BAJAJ-AUTO': 'BAJAJ_AUTO.NS',
    'L&T': 'LT.NS',
    'LTF': 'LTFH.NS', // L&T Finance Holdings
    'BOSCHLTD': 'BOSCHLTD.NS', // Ensure proper mapping for Bosch
    'BSOFT': 'BSOFT.NS' // Birlasoft
  };

  // Handle merged/renamed stocks - map to their current equivalent
  const renamedMappings: { [key: string]: string } = {
    'CADILAHC': 'ZYDUSLIFE.NS', // Cadila Healthcare renamed to Zydus Lifesciences
  };

  // Handle merged stocks (for backward compatibility)
  const delistedMappings: { [key: string]: string } = {
    'HDFC': 'HDFCBANK.NS', // HDFC merged with HDFC Bank in 2023
    'MINDTREE': 'LTIM.NS', // Mindtree merged with LTI to form LTIM (LTIMindtree)
    'PVR': 'PVRINOX.NS', // PVR merged with INOX to form PVRINOX
    ...renamedMappings
  };

  // Stocks that are completely delisted/suspended - these will be skipped
  const delistedStocks = new Set<string>([
    // Add any stocks that are completely delisted and should be skipped
  ]);

  // Stocks that might have issues - keep for now but monitor
  const problematicStocks = new Set([
    'WAAREEENER', // New listing, might not be available yet
    'PREMIERENE', // Check if available
    'GMRAIRPORT', // Check if available
    'ADANIENSOL', // Check if available
    'PATANJALI', // Check if available
    'VMM', // Check if available
    'KALYANKJIL', // Check if available
    'NTPCGREEN', // Check if available
    'JIOFIN', // New listing
    'BHARTIHEXA', // Check if available
    'ATGL', // Check if available
    'IREDA', // New listing
    'SWIGGY', // New listing
    'SOLARINDS', // Check if available
    'OLAELEC', // New listing
    'PHOENIXLTD', // Check if available
    'MAXHEALTH' // Check if available
  ]);

  // Check if stock is delisted/suspended - return null to skip
  if (delistedStocks.has(nseSymbol)) {
    console.log(`🚫 Skipping delisted/suspended stock: ${nseSymbol}`);
    return null as any; // This will be handled in the API to skip the stock
  }

  // Check for renamed/merged stocks first
  if (renamedMappings[nseSymbol]) {
    console.log(`📝 Mapping renamed stock ${nseSymbol} to ${renamedMappings[nseSymbol]}`);
    return renamedMappings[nseSymbol];
  }

  if (delistedMappings[nseSymbol]) {
    console.log(`📝 Mapping merged stock ${nseSymbol} to ${delistedMappings[nseSymbol]}`);
    return delistedMappings[nseSymbol];
  }

  if (symbolMappings[nseSymbol]) {
    return symbolMappings[nseSymbol];
  }

  // Log if this is a potentially problematic stock
  if (problematicStocks.has(nseSymbol)) {
    console.log(`⚠️ Fetching potentially new/problematic stock: ${nseSymbol}`);
  }

  return `${nseSymbol}.NS`;
}

// Function to get display name from NSE symbol
export function getDisplaySymbol(nseSymbol: string): string {
  return nseSymbol;
}

// Function to calculate BOH (Boom-Bust-Recovery) eligibility
export function calculateBOHEligibility(stock: NiftyStock): boolean {
  // BOH Eligible if 52-week low occurred AFTER 52-week high
  // This indicates: High (boom) → Low (bust) → Recovery pattern

  if (!stock.high52WeekDate || !stock.low52WeekDate) {
    return false; // Cannot determine without dates
  }

  const highDate = new Date(stock.high52WeekDate);
  const lowDate = new Date(stock.low52WeekDate);

  // Return true if low date is after high date (boom → bust → recovery pattern)
  return lowDate > highDate;
}

// Function to add BOH eligibility to stock data
export function addBOHEligibility(stock: NiftyStock): NiftyStock {
  return {
    ...stock,
    isBOHEligible: calculateBOHEligibility(stock)
  };
}
