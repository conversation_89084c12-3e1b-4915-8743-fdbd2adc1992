const CHUNK_PUBLIC_PATH = "server/app/api/test-boh/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/src_lib_yahoo-finance_ts_39b50dc8._.js");
runtime.loadChunk("server/chunks/node_modules_41866a20._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__47b13f16._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/test-boh/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/test-boh/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/test-boh/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
