import { NextRequest, NextResponse } from 'next/server';
import { automaticGTTService } from '@/lib/automatic-gtt-service';
import { weeklyHighSignalDetector } from '@/lib/weekly-high-signal-detector';

// POST - Initialize the automatic GTT service
export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Initializing Automatic GTT Service...');

    // Start the automatic GTT service
    await automaticGTTService.start();

    // Get initial status
    const detectorStatus = weeklyHighSignalDetector.getStatus();
    const serviceStats = automaticGTTService.getStatistics();

    console.log('✅ Automatic GTT Service initialized successfully');

    return NextResponse.json({
      success: true,
      message: 'Automatic GTT Service initialized successfully',
      data: {
        detector: detectorStatus,
        service: serviceStats,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ Failed to initialize Automatic GTT Service:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Initialization failed',
        message: 'Failed to initialize Automatic GTT Service'
      },
      { status: 500 }
    );
  }
}

// GET - Check initialization status
export async function GET(request: NextRequest) {
  try {
    const detectorStatus = weeklyHighSignalDetector.getStatus();
    const serviceStats = automaticGTTService.getStatistics();

    return NextResponse.json({
      success: true,
      data: {
        detector: detectorStatus,
        service: serviceStats,
        isInitialized: serviceStats.isInitialized,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ Error checking initialization status:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Status check failed' 
      },
      { status: 500 }
    );
  }
}
