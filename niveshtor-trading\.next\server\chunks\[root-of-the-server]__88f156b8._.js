module.exports = {

"[project]/.next-internal/server/app/api/fund-allocation/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/api/fund-allocation/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GET": ()=>GET,
    "POST": ()=>POST,
    "PUT": ()=>PUT
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
;
// In-memory storage for fund allocations (replace with database in production)
let fundAllocations = {};
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const userId = searchParams.get('userId') || 'default-user';
        const strategy = searchParams.get('strategy') || 'DARVAS_BOX';
        const key = `${userId}-${strategy}`;
        // Check if allocation exists in memory
        let fundAllocation = fundAllocations[key];
        if (!fundAllocation) {
            // Create default allocation if none exists
            fundAllocation = {
                id: `allocation-${key}-${Date.now()}`,
                userId,
                strategyName: strategy,
                totalAllocatedAmount: 50000,
                maxPerStock: 10000,
                maxPerTrade: 2000,
                isActive: true,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                stockAllocations: [
                    {
                        id: 'stock-reliance-1',
                        symbol: 'RELIANCE',
                        allocatedAmount: 8000,
                        usedAmount: 4000,
                        tradesCount: 2,
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    },
                    {
                        id: 'stock-tcs-1',
                        symbol: 'TCS',
                        allocatedAmount: 6000,
                        usedAmount: 2000,
                        tradesCount: 1,
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    }
                ]
            };
            // Store in memory
            fundAllocations[key] = fundAllocation;
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: fundAllocation
        });
    } catch (error) {
        console.error('Error fetching fund allocation:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to fetch fund allocation'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const body = await request.json();
        const { userId = 'default-user', strategyName = 'DARVAS_BOX', totalAllocatedAmount, maxPerStock, maxPerTrade } = body;
        const key = `${userId}-${strategyName}`;
        // Get existing allocation or create new one
        let fundAllocation = fundAllocations[key];
        if (!fundAllocation) {
            fundAllocation = {
                id: `allocation-${key}-${Date.now()}`,
                userId,
                strategyName,
                totalAllocatedAmount: 50000,
                maxPerStock: 10000,
                maxPerTrade: 2000,
                isActive: true,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                stockAllocations: []
            };
        }
        // Update allocation with new values
        fundAllocation.totalAllocatedAmount = parseFloat(totalAllocatedAmount) || fundAllocation.totalAllocatedAmount;
        fundAllocation.maxPerStock = parseFloat(maxPerStock) || fundAllocation.maxPerStock;
        fundAllocation.maxPerTrade = parseFloat(maxPerTrade) || fundAllocation.maxPerTrade;
        fundAllocation.updatedAt = new Date().toISOString();
        // Store updated allocation
        fundAllocations[key] = fundAllocation;
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            message: 'Fund allocation updated successfully',
            data: fundAllocation
        });
    } catch (error) {
        console.error('Error updating fund allocation:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to update fund allocation'
        }, {
            status: 500
        });
    }
}
async function PUT(request) {
    try {
        const body = await request.json();
        const { userId = 'default-user', strategyName = 'DARVAS_BOX', symbol, allocatedAmount } = body;
        const key = `${userId}-${strategyName}`;
        // Get fund allocation
        let fundAllocation = fundAllocations[key];
        if (!fundAllocation) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Fund allocation not found'
            }, {
                status: 404
            });
        }
        // Find existing stock allocation or create new one
        let stockAllocation = fundAllocation.stockAllocations.find((stock)=>stock.symbol === symbol);
        if (stockAllocation) {
            // Update existing allocation
            stockAllocation.allocatedAmount = parseFloat(allocatedAmount);
            stockAllocation.updatedAt = new Date().toISOString();
        } else {
            // Create new stock allocation
            stockAllocation = {
                id: `stock-${symbol}-${Date.now()}`,
                symbol,
                allocatedAmount: parseFloat(allocatedAmount),
                usedAmount: 0,
                tradesCount: 0,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            fundAllocation.stockAllocations.push(stockAllocation);
        }
        // Update fund allocation timestamp
        fundAllocation.updatedAt = new Date().toISOString();
        // Store updated allocation
        fundAllocations[key] = fundAllocation;
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            message: 'Stock allocation updated successfully',
            data: stockAllocation
        });
    } catch (error) {
        console.error('Error updating stock allocation:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to update stock allocation'
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__88f156b8._.js.map