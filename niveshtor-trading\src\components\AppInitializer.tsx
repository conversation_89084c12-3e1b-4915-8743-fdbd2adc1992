'use client';

import { useEffect, useState } from 'react';
import { Loader } from 'lucide-react';

export function AppInitializer({ children }: { children: React.ReactNode }) {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);

  const initializeApp = async () => {
    try {
      console.log('🚀 Simple initialization...');
      setIsInitializing(true);

      // Simple delay to simulate initialization
      await new Promise(resolve => setTimeout(resolve, 1000));

      setIsInitialized(true);
      setIsInitializing(false);
      console.log('✅ Simple initialization completed');

    } catch (error) {
      console.error('❌ Initialization failed:', error);
      setIsInitializing(false);
      setIsInitialized(false);
    }
  };

  useEffect(() => {
    // Check for bypass parameter
    const urlParams = new URLSearchParams(window.location.search);
    const bypassInit = urlParams.get('bypass') === 'true';

    if (bypassInit) {
      console.log('🔄 Bypassing initialization...');
      setIsInitialized(true);
      setIsInitializing(false);
      return;
    }

    // Start simple initialization
    initializeApp();
  }, []);

  // Show initialization screen while loading
  if (isInitializing) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="mb-6">
              <Loader className="h-12 w-12 animate-spin text-blue-600 mx-auto" />
            </div>

            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Initializing Niveshtor Trading
            </h2>
            <p className="text-gray-600 mb-6">
              Setting up the application...
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Show main app if initialized
  if (isInitialized) {
    return <>{children}</>;
  }

  // Fallback loading state
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <Loader className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
        <p className="text-gray-600">Loading application...</p>
      </div>
    </div>
  );
}

// Hook to check if app is initialized
export function useAppInitialization() {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkInitialization = async () => {
      try {
        const response = await fetch('/api/initialize-app');
        const result = await response.json();
        
        setIsInitialized(result.success && result.isFullyInitialized);
      } catch (error) {
        console.error('Error checking initialization:', error);
        setIsInitialized(false);
      } finally {
        setIsChecking(false);
      }
    };

    checkInitialization();
  }, []);

  return { isInitialized, isChecking };
}
