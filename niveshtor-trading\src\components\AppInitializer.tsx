'use client';

import { useEffect, useState } from 'react';
import { Loader, CheckCircle, AlertCircle, RefreshCw, Database, Zap, BarChart3 } from 'lucide-react';
import { appDataPreloader, PreloadingProgress, PreloadingResult } from '@/lib/app-data-preloader';

export function AppInitializer({ children }: { children: React.ReactNode }) {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState<PreloadingProgress>({
    stage: 'Starting',
    progress: 0,
    message: 'Initializing application...',
    isComplete: false
  });
  const [result, setResult] = useState<PreloadingResult | null>(null);

  const initializeApp = async () => {
    try {
      console.log('🚀 Starting comprehensive app data preloading...');
      setIsInitializing(true);
      setError(null);

      // Add progress listener
      const progressListener = (progressData: PreloadingProgress) => {
        setProgress(progressData);
        console.log(`📊 ${progressData.stage}: ${progressData.progress}% - ${progressData.message}`);
      };

      appDataPreloader.addProgressListener(progressListener);

      try {
        // Start comprehensive preloading
        const preloadResult = await appDataPreloader.preloadAllData();

        setResult(preloadResult);
        setIsInitialized(true);
        setIsInitializing(false);

        console.log('🎉 App initialization completed successfully');
        console.log(`📊 Loaded: ${preloadResult.staticDataLoaded.stockCount} stocks, ${preloadResult.staticDataLoaded.bohEligibleCount} BOH eligible`);

      } finally {
        appDataPreloader.removeProgressListener(progressListener);
      }

    } catch (error) {
      console.error('❌ App initialization failed:', error);
      setError(error instanceof Error ? error.message : 'Unknown error');
      setIsInitializing(false);
      setIsInitialized(false);
    }
  };

  useEffect(() => {
    // Check for bypass parameter
    const urlParams = new URLSearchParams(window.location.search);
    const bypassInit = urlParams.get('bypass') === 'true';

    if (bypassInit) {
      console.log('🔄 Bypassing initialization...');
      setIsInitialized(true);
      setIsInitializing(false);
      setError(null);
      return;
    }

    // Check if already preloaded
    const preloaderStatus = appDataPreloader.getStatus();
    if (preloaderStatus.isPreloaded) {
      console.log('✅ App already preloaded');
      setIsInitialized(true);
      setIsInitializing(false);
      setResult(preloaderStatus.result);
      return;
    }

    // Start initialization
    initializeApp();
  }, []);

  // Show initialization screen while loading
  if (isInitializing) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="bg-white rounded-xl shadow-xl p-8 max-w-lg w-full mx-4">
          <div className="text-center">
            <div className="mb-6">
              {progress.stage === 'Static Data Cache' ? (
                <Database className="h-12 w-12 text-blue-600 mx-auto animate-pulse" />
              ) : progress.stage === 'Background Services' ? (
                <Zap className="h-12 w-12 text-green-600 mx-auto animate-pulse" />
              ) : progress.stage === 'Initial Dynamic Data' ? (
                <BarChart3 className="h-12 w-12 text-purple-600 mx-auto animate-pulse" />
              ) : (
                <Loader className="h-12 w-12 animate-spin text-blue-600 mx-auto" />
              )}
            </div>

            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Initializing Niveshtor Trading
            </h2>
            <p className="text-gray-600 mb-4">
              Terminal-style data preloading for instant navigation
            </p>

            {/* Progress Bar */}
            <div className="mb-6">
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>{progress.stage}</span>
                <span>{progress.progress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300 ease-out"
                  style={{ width: `${progress.progress}%` }}
                ></div>
              </div>
              <p className="text-sm text-gray-500 mt-2">{progress.message}</p>
            </div>

            {/* Stage Details */}
            {result && (
              <div className="text-left space-y-2">
                {result.stages.map((stage, index) => (
                  <div key={index} className="flex items-center space-x-2 text-sm">
                    {stage.success ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <AlertCircle className="h-4 w-4 text-red-500" />
                    )}
                    <span className={stage.success ? 'text-green-700' : 'text-red-700'}>
                      {stage.name} ({stage.duration}ms)
                    </span>
                  </div>
                ))}
              </div>
            )}

            <div className="mt-6 text-xs text-gray-400">
              Loading static data once for instant navigation...
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show error screen if initialization failed
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="flex justify-center mb-4">
              <AlertCircle className="h-12 w-12 text-red-600" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Initialization Failed
            </h2>
            <p className="text-gray-600 mb-4">
              {error}
            </p>
            <button
              onClick={initializeApp}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry Initialization
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Show main app if initialized
  if (isInitialized || appDataPreloader.isReadyForInstantNavigation()) {
    return <>{children}</>;
  }

  // Fallback loading state
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <Loader className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
        <p className="text-gray-600">Loading application...</p>
      </div>
    </div>
  );
}

// Hook to check if app is initialized
export function useAppInitialization() {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkInitialization = async () => {
      try {
        const response = await fetch('/api/initialize-app');
        const result = await response.json();
        
        setIsInitialized(result.success && result.isFullyInitialized);
      } catch (error) {
        console.error('Error checking initialization:', error);
        setIsInitialized(false);
      } finally {
        setIsChecking(false);
      }
    };

    checkInitialization();
  }, []);

  return { isInitialized, isChecking };
}
