'use client';

import { useEffect, useState } from 'react';
import { Loader, CheckCircle, AlertCircle, RefreshCw } from 'lucide-react';

interface InitializationStatus {
  isInitialized: boolean;
  isInitializing: boolean;
  error: string | null;
  services: {
    name: string;
    status: 'SUCCESS' | 'ERROR' | 'PARTIAL';
    error?: string;
  }[];
}

export function AppInitializer({ children }: { children: React.ReactNode }) {
  const [initStatus, setInitStatus] = useState<InitializationStatus>({
    isInitialized: false,
    isInitializing: true,
    error: null,
    services: []
  });

  const initializeApp = async () => {
    try {
      console.log('🚀 Starting application initialization...');
      setInitStatus(prev => ({ ...prev, isInitializing: true, error: null }));

      const response = await fetch('/api/initialize-app', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      const result = await response.json();

      if (result.success) {
        setInitStatus({
          isInitialized: true,
          isInitializing: false,
          error: null,
          services: result.results.services
        });
        console.log('✅ Application initialization completed successfully');
      } else {
        throw new Error(result.error || 'Initialization failed');
      }
    } catch (error) {
      console.error('❌ Application initialization failed:', error);
      setInitStatus({
        isInitialized: false,
        isInitializing: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        services: []
      });
    }
  };

  useEffect(() => {
    // Check if already initialized
    const checkStatus = async () => {
      try {
        const response = await fetch('/api/initialize-app');
        const result = await response.json();

        if (result.success && result.isFullyInitialized) {
          setInitStatus({
            isInitialized: true,
            isInitializing: false,
            error: null,
            services: []
          });
          console.log('✅ Application already initialized');
        } else {
          // Initialize if not already done
          await initializeApp();
        }
      } catch (error) {
        console.log('🔄 Status check failed, proceeding with initialization...');
        await initializeApp();
      }
    };

    checkStatus();
  }, []);

  // Show initialization screen while loading
  if (initStatus.isInitializing) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="flex justify-center mb-4">
              <Loader className="h-12 w-12 text-blue-600 animate-spin" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Initializing Niveshtor Trading
            </h2>
            <p className="text-gray-600 mb-6">
              Setting up background services and loading market data...
            </p>
            
            {initStatus.services.length > 0 && (
              <div className="space-y-2 text-left">
                {initStatus.services.map((service, index) => (
                  <div key={index} className="flex items-center space-x-2 text-sm">
                    {service.status === 'SUCCESS' ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : service.status === 'ERROR' ? (
                      <AlertCircle className="h-4 w-4 text-red-500" />
                    ) : (
                      <Loader className="h-4 w-4 text-blue-500 animate-spin" />
                    )}
                    <span className={`${
                      service.status === 'SUCCESS' ? 'text-green-700' :
                      service.status === 'ERROR' ? 'text-red-700' :
                      'text-gray-700'
                    }`}>
                      {service.name}
                    </span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Show error screen if initialization failed
  if (initStatus.error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="flex justify-center mb-4">
              <AlertCircle className="h-12 w-12 text-red-600" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Initialization Failed
            </h2>
            <p className="text-gray-600 mb-4">
              {initStatus.error}
            </p>
            <button
              onClick={initializeApp}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry Initialization
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Show main app if initialized
  return <>{children}</>;
}

// Hook to check if app is initialized
export function useAppInitialization() {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkInitialization = async () => {
      try {
        const response = await fetch('/api/initialize-app');
        const result = await response.json();
        
        setIsInitialized(result.success && result.isFullyInitialized);
      } catch (error) {
        console.error('Error checking initialization:', error);
        setIsInitialized(false);
      } finally {
        setIsChecking(false);
      }
    };

    checkInitialization();
  }, []);

  return { isInitialized, isChecking };
}
