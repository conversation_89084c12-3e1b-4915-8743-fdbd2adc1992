'use client';

import { useState } from 'react';
import {
  Clock,
  Plus,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  AlertCircle,
  TrendingUp,
  TrendingDown
} from 'lucide-react';
import { formatCurrency, formatDateTime } from '@/lib/utils';

interface GTTOrder {
  id: string;
  symbol: string;
  name: string;
  orderType: 'BUY' | 'SELL';
  triggerPrice: number;
  quantity: number;
  price?: number;
  stopLoss?: number;
  target?: number;
  status: 'PENDING' | 'TRIGGERED' | 'CANCELLED' | 'EXPIRED';
  validTill: Date;
  createdAt: Date;
}

export default function GTTOrdersPage() {
  const [orders, setOrders] = useState<GTTOrder[]>([
    {
      id: '1',
      symbol: 'RELIANCE',
      name: 'Reliance Industries Ltd',
      orderType: 'BUY',
      triggerPrice: 2400,
      quantity: 10,
      price: 2405,
      stopLoss: 2300,
      target: 2600,
      status: 'PENDING',
      validTill: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
    },
    {
      id: '2',
      symbol: 'TCS',
      name: 'Tata Consultancy Services',
      orderType: 'SELL',
      triggerPrice: 3200,
      quantity: 5,
      price: 3195,
      stopLoss: 3300,
      target: 3000,
      status: 'TRIGGERED',
      validTill: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
      createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
    },
    {
      id: '3',
      symbol: 'INFY',
      name: 'Infosys Limited',
      orderType: 'BUY',
      triggerPrice: 1450,
      quantity: 15,
      price: 1455,
      status: 'EXPIRED',
      validTill: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000)
    }
  ]);

  const [showCreateModal, setShowCreateModal] = useState(false);
  const [filterStatus, setFilterStatus] = useState<'ALL' | 'PENDING' | 'TRIGGERED' | 'CANCELLED' | 'EXPIRED'>('ALL');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'text-yellow-600 bg-yellow-100';
      case 'TRIGGERED': return 'text-green-600 bg-green-100';
      case 'CANCELLED': return 'text-red-600 bg-red-100';
      case 'EXPIRED': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PENDING': return <Clock className="h-4 w-4" />;
      case 'TRIGGERED': return <CheckCircle className="h-4 w-4" />;
      case 'CANCELLED': return <XCircle className="h-4 w-4" />;
      case 'EXPIRED': return <AlertCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const filteredOrders = filterStatus === 'ALL'
    ? orders
    : orders.filter(order => order.status === filterStatus);

  const OrderRow = ({ order }: { order: GTTOrder }) => (
    <div className="flex items-center justify-between p-4 hover:bg-gray-50 border-b border-gray-100">
      <div className="flex-1">
        <div className="flex items-center space-x-3">
          <div className={`p-2 rounded-full ${order.orderType === 'BUY' ? 'bg-green-100' : 'bg-red-100'}`}>
            {order.orderType === 'BUY' ? (
              <TrendingUp className="h-4 w-4 text-green-600" />
            ) : (
              <TrendingDown className="h-4 w-4 text-red-600" />
            )}
          </div>
          <div>
            <h4 className="font-medium text-gray-900">{order.symbol}</h4>
            <p className="text-sm text-gray-600 truncate max-w-xs">{order.name}</p>
          </div>
          <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 ${getStatusColor(order.status)}`}>
            {getStatusIcon(order.status)}
            <span>{order.status}</span>
          </span>
        </div>
      </div>

      <div className="flex items-center space-x-6">
        <div className="text-right">
          <p className="font-medium text-gray-900">Trigger: {formatCurrency(order.triggerPrice)}</p>
          <p className="text-sm text-gray-600">Qty: {order.quantity}</p>
        </div>

        <div className="text-right">
          {order.price && (
            <p className="text-sm text-gray-600">Price: {formatCurrency(order.price)}</p>
          )}
          {order.stopLoss && (
            <p className="text-sm text-red-600">SL: {formatCurrency(order.stopLoss)}</p>
          )}
          {order.target && (
            <p className="text-sm text-green-600">Target: {formatCurrency(order.target)}</p>
          )}
        </div>

        <div className="text-right text-sm text-gray-600">
          <p>Valid till:</p>
          <p>{formatDateTime(order.validTill)}</p>
        </div>

        <div className="flex items-center space-x-2">
          <button className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
            <Edit className="h-4 w-4" />
          </button>
          <button className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors">
            <Trash2 className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );

  const CreateOrderModal = () => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Create GTT Order</h3>
          <button
            onClick={() => setShowCreateModal(false)}
            className="text-gray-400 hover:text-gray-600"
          >
            <XCircle className="h-6 w-6" />
          </button>
        </div>

        <form className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Stock Symbol</label>
            <input
              type="text"
              placeholder="e.g., RELIANCE"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Order Type</label>
              <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="BUY">BUY</option>
                <option value="SELL">SELL</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Quantity</label>
              <input
                type="number"
                placeholder="10"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Trigger Price</label>
            <input
              type="number"
              step="0.01"
              placeholder="2400.00"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Limit Price (Optional)</label>
            <input
              type="number"
              step="0.01"
              placeholder="2405.00"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Stop Loss (Optional)</label>
              <input
                type="number"
                step="0.01"
                placeholder="2300.00"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Target (Optional)</label>
              <input
                type="number"
                step="0.01"
                placeholder="2600.00"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Valid Till</label>
            <input
              type="datetime-local"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={() => setShowCreateModal(false)}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Create Order
            </button>
          </div>
        </form>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">GTT Orders</h1>
          <p className="text-gray-600 mt-1">Good Till Triggered order management</p>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <Plus className="h-4 w-4" />
          <span>Create GTT Order</span>
        </button>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending Orders</p>
              <p className="text-2xl font-bold text-yellow-600 mt-1">
                {orders.filter(o => o.status === 'PENDING').length}
              </p>
            </div>
            <Clock className="h-8 w-8 text-yellow-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Triggered Today</p>
              <p className="text-2xl font-bold text-green-600 mt-1">
                {orders.filter(o => o.status === 'TRIGGERED').length}
              </p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Expired</p>
              <p className="text-2xl font-bold text-gray-600 mt-1">
                {orders.filter(o => o.status === 'EXPIRED').length}
              </p>
            </div>
            <AlertCircle className="h-8 w-8 text-gray-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Success Rate</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">85%</p>
            </div>
            <TrendingUp className="h-8 w-8 text-gray-600" />
          </div>
        </div>
      </div>

      {/* Orders List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {/* Filter Tabs */}
        <div className="border-b border-gray-200">
          <div className="flex space-x-8 px-6">
            {(['ALL', 'PENDING', 'TRIGGERED', 'CANCELLED', 'EXPIRED'] as const).map((status) => (
              <button
                key={status}
                onClick={() => setFilterStatus(status)}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  filterStatus === status
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {status} ({status === 'ALL' ? orders.length : orders.filter(o => o.status === status).length})
              </button>
            ))}
          </div>
        </div>

        {/* Orders */}
        <div>
          {filteredOrders.length > 0 ? (
            filteredOrders.map((order) => (
              <OrderRow key={order.id} order={order} />
            ))
          ) : (
            <div className="p-8 text-center text-gray-500">
              <Clock className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No GTT orders found for the selected filter.</p>
              <p className="text-sm mt-1">Create your first GTT order to get started.</p>
            </div>
          )}
        </div>
      </div>

      {/* Create Order Modal */}
      {showCreateModal && <CreateOrderModal />}
    </div>
  );
}
