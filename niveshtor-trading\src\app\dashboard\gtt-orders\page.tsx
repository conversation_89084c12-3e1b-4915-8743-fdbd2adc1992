'use client';

import { useState, useEffect } from 'react';
import {
  Clock,
  TrendingUp,
  TrendingDown,
  CheckCircle,
  XCircle,
  AlertCircle,
  Wifi,
  WifiOff,
  RefreshCw,
  Calendar,
  Target,
  Activity,
  Plus,
  Settings,
  Play,
  Pause,
  BellRing
} from 'lucide-react';
import { formatCurrency, formatDateTime } from '@/lib/utils';
import { useGTTOrders } from '@/hooks/useCentralData';

interface GTTOrder {
  id: string;
  gttId?: string; // Angel One GTT ID
  symbol: string;
  name: string;
  orderType: 'BUY' | 'SELL';
  triggerPrice: number;
  quantity: number;
  status: 'PENDING' | 'TRIGGERED' | 'CANCELLED' | 'EXPIRED';
  createdAt: Date;
  source: 'SIGNAL' | 'HOLDING' | 'SALE'; // Which tab this order belongs to
}

type TabType = 'SIGNAL' | 'HOLDING' | 'SALE';

export default function GTTOrdersPage() {
  const [activeTab, setActiveTab] = useState<TabType>('SIGNAL');

  // Central Data Manager for real-time GTT orders
  const {
    orders: centralGTTOrders,
    lastUpdated: gttLastUpdated,
    isLoading: gttIsLoading,
    error: gttError,
    refresh: refreshGTTOrders
  } = useGTTOrders();

  const [orders, setOrders] = useState<GTTOrder[]>([
    // Sample GTT Buy on Signal orders
    {
      id: '1',
      symbol: 'RELIANCE',
      name: 'Reliance Industries Ltd',
      orderType: 'BUY',
      triggerPrice: 2400.05,
      quantity: 8,
      status: 'PENDING',
      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      source: 'SIGNAL'
    },
    {
      id: '2',
      symbol: 'TCS',
      name: 'Tata Consultancy Services',
      orderType: 'BUY',
      triggerPrice: 3200.05,
      quantity: 6,
      status: 'TRIGGERED',
      createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      source: 'SIGNAL'
    },
    // Sample GTT Buy on Holding orders
    {
      id: '3',
      symbol: 'INFY',
      name: 'Infosys Limited',
      orderType: 'BUY',
      triggerPrice: 1350.00,
      quantity: 14,
      status: 'PENDING',
      createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      source: 'HOLDING'
    },
    // Sample GTT Sale orders
    {
      id: '4',
      symbol: 'WIPRO',
      name: 'Wipro Limited',
      orderType: 'SELL',
      triggerPrice: 450.00,
      quantity: 44,
      status: 'PENDING',
      createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      source: 'SALE'
    }
  ]);

  const [isAngelOneConnected, setIsAngelOneConnected] = useState(false);
  const [lastSync, setLastSync] = useState<Date | null>(null);
  const [isCreatingOrders, setIsCreatingOrders] = useState(false);
  const [createOrdersError, setCreateOrdersError] = useState<string | null>(null);

  // Automatic GTT Service state
  const [autoGTTEnabled, setAutoGTTEnabled] = useState(false);
  const [autoGTTStatus, setAutoGTTStatus] = useState<any>(null);
  const [showAutoGTTSettings, setShowAutoGTTSettings] = useState(false);
  const [autoGTTNotifications, setAutoGTTNotifications] = useState<string[]>([]);

  // Filter orders by active tab
  const filteredOrders = orders.filter(order => order.source === activeTab);

  // Interface for Weekly High Signal data
  interface WeeklyHighStock {
    symbol: string;
    name: string;
    currentPrice: number;
    lastWeekHighest: number;
    suggestedBuyPrice: number;
    percentDifference: number;
    suggestedGTTQuantity: number;
    isBOHEligible: boolean;
    inHoldings: boolean;
  }

  // Function to fetch BOH eligible stocks and create GTT orders using the API
  const fetchAndCreateGTTOrders = async () => {
    try {
      console.log('🔍 Fetching BOH eligible stocks and creating GTT orders via API...');

      // Use the dedicated API endpoint for creating signal orders
      const response = await fetch('/api/gtt/create-signal-orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log(`📡 API Response status: ${response.status}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ API request failed: ${response.status} - ${errorText}`);
        throw new Error(`API request failed: ${response.status}`);
      }

      const data = await response.json();
      console.log('📊 API Response data:', data);

      if (!data.success) {
        console.error('❌ API returned error:', data.error);
        throw new Error(data.error || 'Failed to fetch GTT order data');
      }

      console.log(`✅ API returned ${data.data.orders.length} GTT orders to create`);
      console.log(`📊 BOH Stats: Total BOH stocks: ${data.data.totalBOHStocks}, Valid for GTT: ${data.data.validForGTT}`);

      if (data.data.stats.avgTriggerPrice > 0) {
        console.log(`💰 Price Stats: Avg Trigger: ₹${data.data.stats.avgTriggerPrice.toFixed(2)}, Total Value: ₹${data.data.stats.totalValue.toFixed(2)}`);
      }

      return data.data.orders;
    } catch (error) {
      console.error('❌ Error fetching GTT orders from API:', error);
      throw error;
    }
  };

  // Function to create GTT orders for all BOH eligible stocks
  const createAllSignalOrders = async () => {
    setIsCreatingOrders(true);
    setCreateOrdersError(null);

    try {
      console.log('🚀 Starting to create GTT orders for all BOH eligible stocks...');

      // Fetch GTT order data from API
      const gttOrderRequests = await fetchAndCreateGTTOrders();

      console.log(`🔍 Received ${gttOrderRequests.length} GTT order requests from API`);

      if (gttOrderRequests.length === 0) {
        const errorMsg = 'No BOH eligible stocks found for GTT orders. This could be due to:\n' +
                        '• No stocks marked as BOH eligible\n' +
                        '• All BOH stocks filtered out due to price/quantity constraints\n' +
                        '• API data fetching issues';
        console.warn('⚠️ ' + errorMsg);
        setCreateOrdersError(errorMsg);
        return;
      }

      // Filter out stocks that already have pending signal orders
      const existingSignalSymbols = orders
        .filter(order => order.source === 'SIGNAL' && order.status === 'PENDING')
        .map(order => order.symbol);

      console.log(`🔍 Existing signal orders: ${existingSignalSymbols.length} symbols:`, existingSignalSymbols);

      const newOrderRequests = gttOrderRequests.filter((orderReq: any) =>
        !existingSignalSymbols.includes(orderReq.symbol)
      );

      console.log(`📊 Creating orders for ${newOrderRequests.length} new stocks (${existingSignalSymbols.length} already have orders)`);

      if (newOrderRequests.length === 0) {
        const errorMsg = `All ${gttOrderRequests.length} BOH eligible stocks already have pending GTT orders`;
        console.warn('⚠️ ' + errorMsg);
        setCreateOrdersError(errorMsg);
        return;
      }

      // Create GTT orders for each stock
      const newOrders: GTTOrder[] = [];
      let successCount = 0;
      let errorCount = 0;

      for (const orderReq of newOrderRequests) {
        try {
          const newOrder: GTTOrder = {
            id: `signal_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
            symbol: orderReq.symbol,
            name: orderReq.name,
            orderType: orderReq.orderType,
            triggerPrice: orderReq.triggerPrice,
            quantity: orderReq.quantity,
            status: 'PENDING',
            createdAt: new Date(),
            source: orderReq.source
          };

          newOrders.push(newOrder);
          successCount++;

          console.log(`✅ Created GTT order for ${orderReq.symbol}: Trigger=₹${orderReq.triggerPrice.toFixed(2)}, Qty=${orderReq.quantity}`);
        } catch (error) {
          console.error(`❌ Failed to create order for ${orderReq.symbol}:`, error);
          errorCount++;
        }
      }

      // Add new orders to the existing orders
      setOrders(prevOrders => [...prevOrders, ...newOrders]);
      setLastSync(new Date());

      console.log(`🎉 Successfully created ${successCount} GTT orders (${errorCount} errors)`);

      // Show success message
      if (successCount > 0) {
        const totalValue = newOrders.reduce((sum, order) => sum + (order.triggerPrice * order.quantity), 0);
        const message = `🎉 Successfully created ${successCount} GTT orders for BOH eligible stocks!\n\n` +
                       `📊 Summary:\n` +
                       `• Total Orders: ${successCount}\n` +
                       `• Total Investment: ₹${totalValue.toLocaleString()}\n` +
                       `• Average Trigger Price: ₹${(totalValue / newOrders.reduce((sum, order) => sum + order.quantity, 0)).toFixed(2)}\n\n` +
                       `✅ All orders are now visible in the GTT Buy on Signal tab.`;

        alert(message);
        console.log('🎉 GTT Orders Created Successfully:', {
          successCount,
          totalValue,
          orders: newOrders.map(o => ({ symbol: o.symbol, trigger: o.triggerPrice, qty: o.quantity }))
        });
      }

      if (errorCount > 0) {
        const errorMsg = `Created ${successCount} orders successfully, but ${errorCount} failed`;
        console.warn('⚠️ Some GTT orders failed:', errorMsg);
        setCreateOrdersError(errorMsg);
      }

    } catch (error) {
      console.error('❌ Error creating signal orders:', error);
      setCreateOrdersError(error instanceof Error ? error.message : 'Failed to create orders');
    } finally {
      setIsCreatingOrders(false);
    }
  };

  // Function to clear all pending signal orders (for weekly cleanup)
  const clearPendingSignalOrders = () => {
    setOrders(prevOrders =>
      prevOrders.filter(order =>
        !(order.source === 'SIGNAL' && order.status === 'PENDING')
      )
    );
    console.log('🧹 Cleared all pending signal orders');
  };

  // Function to get signal orders statistics
  const getSignalOrdersStats = () => {
    const signalOrders = orders.filter(order => order.source === 'SIGNAL');
    return {
      total: signalOrders.length,
      pending: signalOrders.filter(order => order.status === 'PENDING').length,
      triggered: signalOrders.filter(order => order.status === 'TRIGGERED').length,
      cancelled: signalOrders.filter(order => order.status === 'CANCELLED').length,
      expired: signalOrders.filter(order => order.status === 'EXPIRED').length
    };
  };

  // Automatic GTT Service functions
  const initializeAutoGTTService = async () => {
    try {
      const response = await fetch('/api/auto-gtt/initialize', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      const data = await response.json();

      if (data.success) {
        console.log('✅ Auto GTT Service initialized');
        setAutoGTTEnabled(data.data.service.isEnabled);
        addAutoGTTNotification('🚀 Automatic GTT order detection started');
      } else {
        console.error('❌ Failed to initialize Auto GTT Service:', data.error);
      }
    } catch (error) {
      console.error('❌ Error initializing auto GTT service:', error);
    }
  };

  const fetchAutoGTTStatus = async () => {
    try {
      const response = await fetch('/api/auto-gtt?action=status');
      const data = await response.json();

      if (data.success) {
        setAutoGTTStatus(data.data);
        setAutoGTTEnabled(data.data.service.isEnabled);
      }
    } catch (error) {
      console.error('❌ Error fetching auto GTT status:', error);
    }
  };

  const toggleAutoGTTService = async () => {
    try {
      const action = autoGTTEnabled ? 'stop' : 'start';
      const response = await fetch('/api/auto-gtt', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action })
      });

      const data = await response.json();

      if (data.success) {
        setAutoGTTEnabled(!autoGTTEnabled);
        await fetchAutoGTTStatus();

        const message = autoGTTEnabled ?
          '⏹️ Automatic GTT order creation stopped' :
          '🚀 Automatic GTT order creation started';

        addAutoGTTNotification(message);
      }
    } catch (error) {
      console.error('❌ Error toggling auto GTT service:', error);
    }
  };

  const addAutoGTTNotification = (message: string) => {
    setAutoGTTNotifications(prev => [message, ...prev.slice(0, 4)]); // Keep last 5 notifications

    // Auto-remove notification after 5 seconds
    setTimeout(() => {
      setAutoGTTNotifications(prev => prev.filter(n => n !== message));
    }, 5000);
  };

  // Use Central Data Manager for real-time GTT orders
  useEffect(() => {
    if (centralGTTOrders.length > 0) {
      // Convert central GTT orders to display format
      const displayOrders: GTTOrder[] = centralGTTOrders.map((centralOrder: any) => ({
        id: centralOrder.id,
        symbol: centralOrder.symbol,
        name: centralOrder.name,
        orderType: centralOrder.orderType,
        triggerPrice: centralOrder.triggerPrice,
        quantity: centralOrder.quantity,
        status: centralOrder.status,
        createdAt: new Date(centralOrder.createdAt),
        source: centralOrder.source,
        autoCreated: centralOrder.autoCreated,
        signalStrength: centralOrder.signalStrength
      }));

      setOrders(displayOrders);
      console.log(`📊 Updated orders from Central Data Manager: ${displayOrders.length} orders`);
    }
  }, [centralGTTOrders]);

  // Legacy load auto GTT orders function (now uses Central Data Manager)
  const loadAutoGTTOrders = async () => {
    console.log('🔄 Refreshing GTT orders via Central Data Manager...');
    await refreshGTTOrders();
  };

  // Initialize data on component mount
  useEffect(() => {
    const initializeData = async () => {
      console.log('🚀 Initializing GTT Orders page...');

      // Initialize with empty orders
      setOrders([]);
      setLastSync(new Date());

      // Initialize automatic GTT service first
      await initializeAutoGTTService();

      // Then load orders and status
      await Promise.all([
        loadAutoGTTOrders(),
        fetchAutoGTTStatus()
      ]);

      console.log('✅ GTT Orders page initialization complete');
    };

    initializeData();

    // Set up periodic status updates
    const statusInterval = setInterval(fetchAutoGTTStatus, 30000); // Every 30 seconds

    // Set up periodic order refresh
    const orderInterval = setInterval(loadAutoGTTOrders, 60000); // Every 60 seconds

    return () => {
      clearInterval(statusInterval);
      clearInterval(orderInterval);
    };
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'text-yellow-600 bg-yellow-100';
      case 'TRIGGERED': return 'text-green-600 bg-green-100';
      case 'CANCELLED': return 'text-red-600 bg-red-100';
      case 'EXPIRED': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PENDING': return <Clock className="h-4 w-4" />;
      case 'TRIGGERED': return <CheckCircle className="h-4 w-4" />;
      case 'CANCELLED': return <XCircle className="h-4 w-4" />;
      case 'EXPIRED': return <AlertCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getTabInfo = (tab: TabType) => {
    switch (tab) {
      case 'SIGNAL':
        return {
          title: 'GTT Buy on Signal',
          description: 'Automated buy orders based on Weekly High Signal data',
          icon: <Activity className="h-5 w-5" />,
          automation: 'Auto-created continuously during market hours (9:15 AM - 3:30 PM)'
        };
      case 'HOLDING':
        return {
          title: 'GTT Buy on Holding',
          description: 'Additional buy orders at lower support levels for existing holdings',
          icon: <TrendingDown className="h-5 w-5" />,
          automation: 'Auto-created when Ignorable Lower Price is calculated'
        };
      case 'SALE':
        return {
          title: 'GTT Sale',
          description: 'Sell orders triggered when target prices are reached',
          icon: <Target className="h-5 w-5" />,
          automation: 'Auto-created when Target Price is set for holdings'
        };
    }
  };

  const OrdersTable = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Stock
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Order Type
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Trigger Price
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Quantity
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Created Date
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredOrders.length > 0 ? (
              filteredOrders.map((order) => (
                <tr key={order.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{order.symbol}</div>
                      <div className="text-sm text-gray-500 truncate max-w-xs">{order.name}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className={`p-1 rounded-full mr-2 ${order.orderType === 'BUY' ? 'bg-green-100' : 'bg-red-100'}`}>
                        {order.orderType === 'BUY' ? (
                          <TrendingUp className="h-3 w-3 text-green-600" />
                        ) : (
                          <TrendingDown className="h-3 w-3 text-red-600" />
                        )}
                      </div>
                      <span className={`text-sm font-medium ${order.orderType === 'BUY' ? 'text-green-600' : 'text-red-600'}`}>
                        {order.orderType}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatCurrency(order.triggerPrice)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {order.quantity}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                      {getStatusIcon(order.status)}
                      <span className="ml-1">{order.status}</span>
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDateTime(order.createdAt)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button className="text-red-600 hover:text-red-900 mr-3">
                      Cancel
                    </button>
                    <button className="text-blue-600 hover:text-blue-900">
                      View
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={7} className="px-6 py-12 text-center">
                  <div className="text-gray-500">
                    <Clock className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>No GTT orders found for {getTabInfo(activeTab).title}.</p>
                    <p className="text-sm mt-1">{getTabInfo(activeTab).automation}</p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">GTT Orders</h1>
          <p className="text-gray-600 mt-1">Automated Good Till Triggered order management</p>
        </div>
        <div className="flex items-center space-x-4">
          {/* Angel One Connection Status */}
          <div className="flex items-center space-x-2">
            {isAngelOneConnected ? (
              <div className="flex items-center space-x-2 text-green-600">
                <Wifi className="h-4 w-4" />
                <span className="text-sm font-medium">Angel One Connected</span>
              </div>
            ) : (
              <div className="flex items-center space-x-2 text-red-600">
                <WifiOff className="h-4 w-4" />
                <span className="text-sm font-medium">Angel One Disconnected</span>
              </div>
            )}
          </div>
          <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2">
            <RefreshCw className="h-4 w-4" />
            <span>Sync Orders</span>
          </button>
        </div>
      </div>

      {/* Automation Status */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Calendar className="h-5 w-5 text-blue-600" />
            <div>
              <p className="text-sm font-medium text-blue-900">Continuous Automation Active</p>
              <p className="text-xs text-blue-700">
                Orders are automatically created when new Weekly High Signals are detected during market hours
                {lastSync && ` • Last sync: ${formatDateTime(lastSync)}`}
              </p>
            </div>
          </div>

          {/* Real-time Auto GTT Controls */}
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${autoGTTEnabled ? 'bg-green-500' : 'bg-gray-400'}`}></div>
              <span className="text-xs text-gray-600">
                Real-time Auto GTT {autoGTTEnabled ? 'ON' : 'OFF'}
              </span>
            </div>

            <button
              onClick={toggleAutoGTTService}
              className={`px-3 py-1 rounded-md text-xs font-medium flex items-center space-x-1 transition-colors ${
                autoGTTEnabled
                  ? 'bg-red-100 text-red-700 hover:bg-red-200'
                  : 'bg-green-100 text-green-700 hover:bg-green-200'
              }`}
            >
              {autoGTTEnabled ? <Pause className="h-3 w-3" /> : <Play className="h-3 w-3" />}
              <span>{autoGTTEnabled ? 'Stop' : 'Start'}</span>
            </button>

            <button
              onClick={() => setShowAutoGTTSettings(!showAutoGTTSettings)}
              className="px-3 py-1 bg-gray-100 text-gray-700 rounded-md text-xs font-medium hover:bg-gray-200 transition-colors flex items-center space-x-1"
            >
              <Settings className="h-3 w-3" />
              <span>Settings</span>
            </button>
          </div>
        </div>

        {/* Auto GTT Status Details */}
        {autoGTTStatus && (
          <div className="mt-3 pt-3 border-t border-blue-200">
            <div className="grid grid-cols-4 gap-4 text-xs">
              <div>
                <span className="text-blue-600 font-medium">Market Status:</span>
                <span className={`ml-1 ${autoGTTStatus.detector.isMarketOpen ? 'text-green-600' : 'text-gray-600'}`}>
                  {autoGTTStatus.detector.isMarketOpen ? 'Open' : 'Closed'}
                </span>
              </div>
              <div>
                <span className="text-blue-600 font-medium">Signals:</span>
                <span className="ml-1 text-gray-700">{autoGTTStatus.detector.lastSignalCount || 0}</span>
              </div>
              <div>
                <span className="text-blue-600 font-medium">Auto Orders:</span>
                <span className="ml-1 text-gray-700">{autoGTTStatus.service.autoCreatedOrders || 0}</span>
              </div>
              <div>
                <span className="text-blue-600 font-medium">Today:</span>
                <span className="ml-1 text-gray-700">
                  {autoGTTStatus.service.todayOrders || 0}/{autoGTTStatus.service.dailyLimit || 20}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Auto GTT Notifications */}
      {autoGTTNotifications.length > 0 && (
        <div className="space-y-2">
          {autoGTTNotifications.map((notification, index) => (
            <div key={index} className="bg-green-50 border border-green-200 rounded-lg p-3 flex items-center space-x-2">
              <BellRing className="h-4 w-4 text-green-600" />
              <span className="text-green-800 text-sm">{notification}</span>
            </div>
          ))}
        </div>
      )}

      {/* Auto GTT Settings Modal */}
      {showAutoGTTSettings && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Auto GTT Settings</h3>
              <button
                onClick={() => setShowAutoGTTSettings(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <XCircle className="h-5 w-5" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Polling Interval (minutes)
                </label>
                <input
                  type="number"
                  min="1"
                  max="60"
                  defaultValue="5"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Minimum Signal Strength
                </label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                  <option value="MODERATE">Moderate</option>
                  <option value="STRONG">Strong Only</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Max Orders Per Day
                </label>
                <input
                  type="number"
                  min="1"
                  max="50"
                  defaultValue="20"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                />
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="marketHoursOnly"
                  defaultChecked
                  className="rounded border-gray-300"
                />
                <label htmlFor="marketHoursOnly" className="text-sm text-gray-700">
                  Only during market hours (9:15 AM - 3:30 PM)
                </label>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="volumeConfirmation"
                  defaultChecked
                  className="rounded border-gray-300"
                />
                <label htmlFor="volumeConfirmation" className="text-sm text-gray-700">
                  Require volume confirmation (20% above average)
                </label>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowAutoGTTSettings(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  // TODO: Save settings
                  setShowAutoGTTSettings(false);
                  addAutoGTTNotification('⚙️ Auto GTT settings updated');
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Save Settings
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {(['SIGNAL', 'HOLDING', 'SALE'] as const).map((tab) => {
              const tabInfo = getTabInfo(tab);
              const tabOrders = orders.filter(o => o.source === tab);
              return (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors flex items-center space-x-2 ${
                    activeTab === tab
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tabInfo.icon}
                  <span>{tabInfo.title}</span>
                  <span className="bg-gray-100 text-gray-600 px-2 py-0.5 rounded-full text-xs">
                    {tabOrders.length}
                  </span>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {getTabInfo(activeTab).title}
                </h3>
                <p className="text-gray-600 text-sm mb-1">
                  {getTabInfo(activeTab).description}
                </p>
                <p className="text-blue-600 text-xs font-medium">
                  {getTabInfo(activeTab).automation}
                </p>
              </div>

              {/* Automatic Order Status - Only show for SIGNAL tab */}
              {activeTab === 'SIGNAL' && (
                <div className="flex flex-col items-end space-y-2">
                  <div className="flex items-center space-x-2 px-3 py-2 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span className="text-green-700 text-sm font-medium">Automatic Creation Active</span>
                    </div>
                  </div>

                  <button
                    onClick={createAllSignalOrders}
                    disabled={isCreatingOrders}
                    className={`px-3 py-1.5 rounded-lg text-xs transition-colors flex items-center space-x-2 ${
                      isCreatingOrders
                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                    title="Manual creation for testing purposes only - orders are created automatically"
                  >
                    {isCreatingOrders ? (
                      <>
                        <RefreshCw className="h-3 w-3 animate-spin" />
                        <span>Creating...</span>
                      </>
                    ) : (
                      <>
                        <Plus className="h-3 w-3" />
                        <span>Manual Create (Test)</span>
                      </>
                    )}
                  </button>

                  {createOrdersError && (
                    <p className="text-red-600 text-xs max-w-xs text-right">
                      {createOrdersError}
                    </p>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Signal Orders Info - Only show for SIGNAL tab */}
          {activeTab === 'SIGNAL' && (
            <div className="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-blue-900 mb-1">
                    Weekly High Signal Orders
                  </h4>
                  <p className="text-xs text-blue-700">
                    Orders are automatically created for BOH eligible stocks from the Weekly High Signal page.
                    Trigger Price = Last Week's High + ₹0.05, Quantity = ₹2,000 ÷ Trigger Price
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-blue-900">
                    {getSignalOrdersStats().pending} Pending
                  </div>
                  <div className="text-xs text-blue-700">
                    {getSignalOrdersStats().triggered} Triggered
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Orders Table */}
          <OrdersTable />
        </div>
      </div>

      {/* Summary Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Orders</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{orders.length}</p>
            </div>
            <Activity className="h-8 w-8 text-gray-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending Orders</p>
              <p className="text-2xl font-bold text-yellow-600 mt-1">
                {orders.filter(o => o.status === 'PENDING').length}
              </p>
            </div>
            <Clock className="h-8 w-8 text-yellow-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Triggered Today</p>
              <p className="text-2xl font-bold text-green-600 mt-1">
                {orders.filter(o => o.status === 'TRIGGERED').length}
              </p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Success Rate</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">
                {orders.length > 0 ? Math.round((orders.filter(o => o.status === 'TRIGGERED').length / orders.length) * 100) : 0}%
              </p>
            </div>
            <TrendingUp className="h-8 w-8 text-gray-600" />
          </div>
        </div>
      </div>
    </div>
  );
}
