'use client';

import { useState, useEffect } from 'react';
import {
  Clock,
  TrendingUp,
  TrendingDown,
  CheckCircle,
  XCircle,
  AlertCircle,
  Wifi,
  WifiOff,
  RefreshCw,
  Calendar,
  Target,
  Activity
} from 'lucide-react';
import { formatCurrency, formatDateTime } from '@/lib/utils';

interface GTTOrder {
  id: string;
  gttId?: string; // Angel One GTT ID
  symbol: string;
  name: string;
  orderType: 'BUY' | 'SELL';
  triggerPrice: number;
  quantity: number;
  status: 'PENDING' | 'TRIGGERED' | 'CANCELLED' | 'EXPIRED';
  createdAt: Date;
  source: 'SIGNAL' | 'HOLDING' | 'SALE'; // Which tab this order belongs to
}

type TabType = 'SIGNAL' | 'HOLDING' | 'SALE';

export default function GTTOrdersPage() {
  const [activeTab, setActiveTab] = useState<TabType>('SIGNAL');
  const [orders, setOrders] = useState<GTTOrder[]>([
    // Sample GTT Buy on Signal orders
    {
      id: '1',
      symbol: 'RELIANCE',
      name: 'Reliance Industries Ltd',
      orderType: 'BUY',
      triggerPrice: 2400.05,
      quantity: 8,
      status: 'PENDING',
      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      source: 'SIGNAL'
    },
    {
      id: '2',
      symbol: 'TCS',
      name: 'Tata Consultancy Services',
      orderType: 'BUY',
      triggerPrice: 3200.05,
      quantity: 6,
      status: 'TRIGGERED',
      createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      source: 'SIGNAL'
    },
    // Sample GTT Buy on Holding orders
    {
      id: '3',
      symbol: 'INFY',
      name: 'Infosys Limited',
      orderType: 'BUY',
      triggerPrice: 1350.00,
      quantity: 14,
      status: 'PENDING',
      createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      source: 'HOLDING'
    },
    // Sample GTT Sale orders
    {
      id: '4',
      symbol: 'WIPRO',
      name: 'Wipro Limited',
      orderType: 'SELL',
      triggerPrice: 450.00,
      quantity: 44,
      status: 'PENDING',
      createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      source: 'SALE'
    }
  ]);

  const [isAngelOneConnected, setIsAngelOneConnected] = useState(false);
  const [lastSync, setLastSync] = useState<Date | null>(null);

  // Filter orders by active tab
  const filteredOrders = orders.filter(order => order.source === activeTab);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'text-yellow-600 bg-yellow-100';
      case 'TRIGGERED': return 'text-green-600 bg-green-100';
      case 'CANCELLED': return 'text-red-600 bg-red-100';
      case 'EXPIRED': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PENDING': return <Clock className="h-4 w-4" />;
      case 'TRIGGERED': return <CheckCircle className="h-4 w-4" />;
      case 'CANCELLED': return <XCircle className="h-4 w-4" />;
      case 'EXPIRED': return <AlertCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getTabInfo = (tab: TabType) => {
    switch (tab) {
      case 'SIGNAL':
        return {
          title: 'GTT Buy on Signal',
          description: 'Automated buy orders based on Weekly High Signal data',
          icon: <Activity className="h-5 w-5" />,
          automation: 'Auto-created every Friday 8:00 PM'
        };
      case 'HOLDING':
        return {
          title: 'GTT Buy on Holding',
          description: 'Additional buy orders at lower support levels for existing holdings',
          icon: <TrendingDown className="h-5 w-5" />,
          automation: 'Auto-created when Ignorable Lower Price is calculated'
        };
      case 'SALE':
        return {
          title: 'GTT Sale',
          description: 'Sell orders triggered when target prices are reached',
          icon: <Target className="h-5 w-5" />,
          automation: 'Auto-created when Target Price is set for holdings'
        };
    }
  };

  const OrdersTable = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Stock
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Order Type
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Trigger Price
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Quantity
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Created Date
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredOrders.length > 0 ? (
              filteredOrders.map((order) => (
                <tr key={order.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{order.symbol}</div>
                      <div className="text-sm text-gray-500 truncate max-w-xs">{order.name}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className={`p-1 rounded-full mr-2 ${order.orderType === 'BUY' ? 'bg-green-100' : 'bg-red-100'}`}>
                        {order.orderType === 'BUY' ? (
                          <TrendingUp className="h-3 w-3 text-green-600" />
                        ) : (
                          <TrendingDown className="h-3 w-3 text-red-600" />
                        )}
                      </div>
                      <span className={`text-sm font-medium ${order.orderType === 'BUY' ? 'text-green-600' : 'text-red-600'}`}>
                        {order.orderType}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatCurrency(order.triggerPrice)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {order.quantity}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                      {getStatusIcon(order.status)}
                      <span className="ml-1">{order.status}</span>
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDateTime(order.createdAt)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button className="text-red-600 hover:text-red-900 mr-3">
                      Cancel
                    </button>
                    <button className="text-blue-600 hover:text-blue-900">
                      View
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={7} className="px-6 py-12 text-center">
                  <div className="text-gray-500">
                    <Clock className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>No GTT orders found for {getTabInfo(activeTab).title}.</p>
                    <p className="text-sm mt-1">{getTabInfo(activeTab).automation}</p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">GTT Orders</h1>
          <p className="text-gray-600 mt-1">Automated Good Till Triggered order management</p>
        </div>
        <div className="flex items-center space-x-4">
          {/* Angel One Connection Status */}
          <div className="flex items-center space-x-2">
            {isAngelOneConnected ? (
              <div className="flex items-center space-x-2 text-green-600">
                <Wifi className="h-4 w-4" />
                <span className="text-sm font-medium">Angel One Connected</span>
              </div>
            ) : (
              <div className="flex items-center space-x-2 text-red-600">
                <WifiOff className="h-4 w-4" />
                <span className="text-sm font-medium">Angel One Disconnected</span>
              </div>
            )}
          </div>
          <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2">
            <RefreshCw className="h-4 w-4" />
            <span>Sync Orders</span>
          </button>
        </div>
      </div>

      {/* Automation Status */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center space-x-2">
          <Calendar className="h-5 w-5 text-blue-600" />
          <div>
            <p className="text-sm font-medium text-blue-900">Next Automation: Friday 8:00 PM</p>
            <p className="text-xs text-blue-700">
              Weekly High Signal orders will be automatically created/updated
              {lastSync && ` • Last sync: ${formatDateTime(lastSync)}`}
            </p>
          </div>
        </div>
      </div>

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">GTT Orders</h1>
          <p className="text-gray-600 mt-1">Good Till Triggered order management</p>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <Plus className="h-4 w-4" />
          <span>Create GTT Order</span>
        </button>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending Orders</p>
              <p className="text-2xl font-bold text-yellow-600 mt-1">
                {orders.filter(o => o.status === 'PENDING').length}
              </p>
            </div>
            <Clock className="h-8 w-8 text-yellow-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Triggered Today</p>
              <p className="text-2xl font-bold text-green-600 mt-1">
                {orders.filter(o => o.status === 'TRIGGERED').length}
              </p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Expired</p>
              <p className="text-2xl font-bold text-gray-600 mt-1">
                {orders.filter(o => o.status === 'EXPIRED').length}
              </p>
            </div>
            <AlertCircle className="h-8 w-8 text-gray-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Success Rate</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">85%</p>
            </div>
            <TrendingUp className="h-8 w-8 text-gray-600" />
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {(['SIGNAL', 'HOLDING', 'SALE'] as const).map((tab) => {
              const tabInfo = getTabInfo(tab);
              const tabOrders = orders.filter(o => o.source === tab);
              return (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors flex items-center space-x-2 ${
                    activeTab === tab
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tabInfo.icon}
                  <span>{tabInfo.title}</span>
                  <span className="bg-gray-100 text-gray-600 px-2 py-0.5 rounded-full text-xs">
                    {tabOrders.length}
                  </span>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {getTabInfo(activeTab).title}
            </h3>
            <p className="text-gray-600 text-sm mb-1">
              {getTabInfo(activeTab).description}
            </p>
            <p className="text-blue-600 text-xs font-medium">
              {getTabInfo(activeTab).automation}
            </p>
          </div>

          {/* Orders Table */}
          <OrdersTable />
        </div>
      </div>

      {/* Summary Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Orders</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{orders.length}</p>
            </div>
            <Activity className="h-8 w-8 text-gray-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending Orders</p>
              <p className="text-2xl font-bold text-yellow-600 mt-1">
                {orders.filter(o => o.status === 'PENDING').length}
              </p>
            </div>
            <Clock className="h-8 w-8 text-yellow-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Triggered Today</p>
              <p className="text-2xl font-bold text-green-600 mt-1">
                {orders.filter(o => o.status === 'TRIGGERED').length}
              </p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Success Rate</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">
                {orders.length > 0 ? Math.round((orders.filter(o => o.status === 'TRIGGERED').length / orders.length) * 100) : 0}%
              </p>
            </div>
            <TrendingUp className="h-8 w-8 text-gray-600" />
          </div>
        </div>
      </div>
    </div>
  );
}
