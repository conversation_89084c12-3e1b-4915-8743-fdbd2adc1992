{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency: string = '₹'): string {\n  return `${currency}${amount.toLocaleString('en-IN', {\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2\n  })}`;\n}\n\nexport function formatNumber(num: number): string {\n  if (num >= 10000000) {\n    return (num / 10000000).toFixed(2) + 'Cr';\n  } else if (num >= 100000) {\n    return (num / 100000).toFixed(2) + 'L';\n  } else if (num >= 1000) {\n    return (num / 1000).toFixed(2) + 'K';\n  }\n  return num.toString();\n}\n\nexport function formatPercentage(value: number): string {\n  return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;\n}\n\nexport function getChangeColor(value: number): string {\n  if (value > 0) return 'text-green-600';\n  if (value < 0) return 'text-red-600';\n  return 'text-gray-600';\n}\n\nexport function formatDate(date: Date): string {\n  return date.toLocaleDateString('en-IN', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric'\n  });\n}\n\nexport function formatDateTime(date: Date): string {\n  return date.toLocaleString('en-IN', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc;QAAE,WAAA,iEAAmB;IAChE,OAAO,AAAC,GAAa,OAAX,UAGP,OAHkB,OAAO,cAAc,CAAC,SAAS;QAClD,uBAAuB;QACvB,uBAAuB;IACzB;AACF;AAEO,SAAS,aAAa,GAAW;IACtC,IAAI,OAAO,UAAU;QACnB,OAAO,CAAC,MAAM,QAAQ,EAAE,OAAO,CAAC,KAAK;IACvC,OAAO,IAAI,OAAO,QAAQ;QACxB,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,KAAK;IACrC,OAAO,IAAI,OAAO,MAAM;QACtB,OAAO,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,KAAK;IACnC;IACA,OAAO,IAAI,QAAQ;AACrB;AAEO,SAAS,iBAAiB,KAAa;IAC5C,OAAO,AAAC,GAA0B,OAAxB,SAAS,IAAI,MAAM,IAAsB,OAAjB,MAAM,OAAO,CAAC,IAAG;AACrD;AAEO,SAAS,eAAe,KAAa;IAC1C,IAAI,QAAQ,GAAG,OAAO;IACtB,IAAI,QAAQ,GAAG,OAAO;IACtB,OAAO;AACT;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,eAAe,IAAU;IACvC,OAAO,KAAK,cAAc,CAAC,SAAS;QAClC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/components/layout/OptimizedSidebar.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { usePathname, useRouter } from 'next/navigation';\nimport { cn } from '@/lib/utils';\nimport {\n  BarChart3,\n  Wallet,\n  Search,\n  Filter,\n  TrendingUp,\n  Clock,\n  Briefcase,\n  TestTube,\n  Plug,\n  Menu,\n  X\n} from 'lucide-react';\nimport { useState, useCallback, useMemo, useTransition } from 'react';\n\nconst navigation = [\n  {\n    name: 'Capital Management',\n    href: '/dashboard/capital',\n    icon: Wallet,\n    description: 'Portfolio overview and risk management'\n  },\n  {\n    name: 'Stock Universal',\n    href: '/dashboard/stocks',\n    icon: Search,\n    description: 'Stock search and watchlist management'\n  },\n  {\n    name: 'BOH Eligible',\n    href: '/dashboard/boh-eligible',\n    icon: Filter,\n    description: 'Boom-Bust-Recovery pattern stocks'\n  },\n  {\n    name: 'Weekly High Signal',\n    href: '/dashboard/weekly-high',\n    icon: TrendingUp,\n    description: 'Weekly high breakout signals'\n  },\n  {\n    name: 'GTT Order',\n    href: '/dashboard/gtt-orders',\n    icon: Clock,\n    description: 'Good Till Triggered order management'\n  },\n  {\n    name: 'Current Holdings',\n    href: '/dashboard/holdings',\n    icon: Briefcase,\n    description: 'Current portfolio positions and performance'\n  },\n  {\n    name: 'Back Testing',\n    href: '/dashboard/backtesting',\n    icon: TestTube,\n    description: 'Strategy backtesting and analysis'\n  },\n  {\n    name: 'Connect Broker',\n    href: '/dashboard/broker',\n    icon: Plug,\n    description: 'Broker connection and authentication'\n  }\n];\n\ninterface SidebarProps {\n  className?: string;\n}\n\nexport function OptimizedSidebar({ className }: SidebarProps) {\n  const pathname = usePathname();\n  const router = useRouter();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [isPending, startTransition] = useTransition();\n  const [activeHref, setActiveHref] = useState(pathname);\n\n  // Optimized navigation handler with immediate feedback\n  const handleNavigation = useCallback((href: string, e: React.MouseEvent) => {\n    e.preventDefault();\n    \n    // Skip if already on the same page\n    if (href === pathname) {\n      setIsMobileMenuOpen(false);\n      return;\n    }\n    \n    // Immediate visual feedback\n    setActiveHref(href);\n    setIsMobileMenuOpen(false);\n    \n    // Use transition for smooth navigation\n    startTransition(() => {\n      router.push(href);\n    });\n  }, [router, pathname]);\n\n  // Memoize navigation items to prevent re-renders\n  const navigationItems = useMemo(() => navigation, []);\n\n  // Prefetch likely next pages on hover\n  const handleMouseEnter = useCallback((href: string) => {\n    if (href !== pathname) {\n      router.prefetch(href);\n    }\n  }, [router, pathname]);\n\n  // Close mobile menu on escape key\n  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {\n    if (e.key === 'Escape') {\n      setIsMobileMenuOpen(false);\n    }\n  }, []);\n\n  return (\n    <>\n      {/* Mobile menu button */}\n      <div className=\"lg:hidden fixed top-4 left-4 z-50\">\n        <button\n          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n          className=\"p-2 rounded-md bg-white shadow-md border border-gray-200 hover:bg-gray-50 transition-colors duration-150\"\n          aria-label=\"Toggle navigation menu\"\n        >\n          {isMobileMenuOpen ? (\n            <X className=\"h-6 w-6\" />\n          ) : (\n            <Menu className=\"h-6 w-6\" />\n          )}\n        </button>\n      </div>\n\n      {/* Mobile overlay */}\n      {isMobileMenuOpen && (\n        <div\n          className=\"lg:hidden fixed inset-0 z-40 bg-black bg-opacity-50 transition-opacity duration-300\"\n          onClick={() => setIsMobileMenuOpen(false)}\n        />\n      )}\n\n      {/* Sidebar */}\n      <div\n        className={cn(\n          'fixed inset-y-0 left-0 z-40 w-72 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0',\n          isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full',\n          className\n        )}\n        onKeyDown={handleKeyDown}\n      >\n        <div className=\"flex flex-col h-full\">\n          {/* Header */}\n          <div className=\"flex items-center justify-center h-16 px-4 border-b border-gray-200 bg-white\">\n            <div className=\"flex items-center space-x-2\">\n              <BarChart3 className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"text-xl font-bold text-gray-900\">Niveshtor</span>\n            </div>\n          </div>\n\n          {/* Navigation */}\n          <nav className=\"flex-1 px-4 py-6 space-y-2 overflow-y-auto\">\n            {navigationItems.map((item) => {\n              const isActive = (activeHref || pathname) === item.href;\n              const isLoading = isPending && isActive;\n              \n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  onClick={(e) => handleNavigation(item.href, e)}\n                  onMouseEnter={() => handleMouseEnter(item.href)}\n                  className={cn(\n                    'group flex items-start p-3 rounded-lg text-sm font-medium transition-all duration-150 hover:scale-[1.02]',\n                    isActive\n                      ? 'bg-blue-50 text-blue-700 border border-blue-200 shadow-sm'\n                      : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900 hover:shadow-sm',\n                    isLoading && 'opacity-75 cursor-wait'\n                  )}\n                >\n                  <item.icon\n                    className={cn(\n                      'flex-shrink-0 h-5 w-5 mt-0.5 mr-3 transition-colors duration-150',\n                      isActive ? 'text-blue-600' : 'text-gray-400 group-hover:text-gray-500'\n                    )}\n                  />\n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"font-medium\">{item.name}</div>\n                    <div className=\"text-xs text-gray-500 mt-1 leading-tight\">\n                      {item.description}\n                    </div>\n                  </div>\n                  {isLoading && (\n                    <div className=\"flex-shrink-0 ml-2\">\n                      <div className=\"w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin\"></div>\n                    </div>\n                  )}\n                </Link>\n              );\n            })}\n          </nav>\n\n          {/* Footer */}\n          <div className=\"p-4 border-t border-gray-200\">\n            <div className=\"text-xs text-gray-500 text-center\">\n              <div>Niveshtor Trading Platform</div>\n              <div className=\"mt-1\">v1.0.0</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n\nexport default OptimizedSidebar;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;;;AAlBA;;;;;;AAoBA,MAAM,aAAa;IACjB;QACE,MAAM;QACN,MAAM;QACN,MAAM,yMAAA,CAAA,SAAM;QACZ,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,yMAAA,CAAA,SAAM;QACZ,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,yMAAA,CAAA,SAAM;QACZ,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,qNAAA,CAAA,aAAU;QAChB,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;QACX,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,+MAAA,CAAA,YAAS;QACf,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,iNAAA,CAAA,WAAQ;QACd,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,qMAAA,CAAA,OAAI;QACV,aAAa;IACf;CACD;AAMM,SAAS,iBAAiB,KAA2B;QAA3B,EAAE,SAAS,EAAgB,GAA3B;;IAC/B,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,WAAW,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,uDAAuD;IACvD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC,MAAc;YAClD,EAAE,cAAc;YAEhB,mCAAmC;YACnC,IAAI,SAAS,UAAU;gBACrB,oBAAoB;gBACpB;YACF;YAEA,4BAA4B;YAC5B,cAAc;YACd,oBAAoB;YAEpB,uCAAuC;YACvC;kEAAgB;oBACd,OAAO,IAAI,CAAC;gBACd;;QACF;yDAAG;QAAC;QAAQ;KAAS;IAErB,iDAAiD;IACjD,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;qDAAE,IAAM;oDAAY,EAAE;IAEpD,sCAAsC;IACtC,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC;YACpC,IAAI,SAAS,UAAU;gBACrB,OAAO,QAAQ,CAAC;YAClB;QACF;yDAAG;QAAC;QAAQ;KAAS;IAErB,kCAAkC;IAClC,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC;YACjC,IAAI,EAAE,GAAG,KAAK,UAAU;gBACtB,oBAAoB;YACtB;QACF;sDAAG,EAAE;IAEL,qBACE;;0BAEE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,SAAS,IAAM,oBAAoB,CAAC;oBACpC,WAAU;oBACV,cAAW;8BAEV,iCACC,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;6CAEb,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;;;;;;;;;;;YAMrB,kCACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,oBAAoB;;;;;;0BAKvC,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oKACA,mBAAmB,kBAAkB,qBACrC;gBAEF,WAAW;0BAEX,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;;;;;;sCAKtD,6LAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC;gCACpB,MAAM,WAAW,CAAC,cAAc,QAAQ,MAAM,KAAK,IAAI;gCACvD,MAAM,YAAY,aAAa;gCAE/B,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,SAAS,CAAC,IAAM,iBAAiB,KAAK,IAAI,EAAE;oCAC5C,cAAc,IAAM,iBAAiB,KAAK,IAAI;oCAC9C,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4GACA,WACI,8DACA,sEACJ,aAAa;;sDAGf,6LAAC,KAAK,IAAI;4CACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA,WAAW,kBAAkB;;;;;;sDAGjC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAe,KAAK,IAAI;;;;;;8DACvC,6LAAC;oDAAI,WAAU;8DACZ,KAAK,WAAW;;;;;;;;;;;;wCAGpB,2BACC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;;;;;;;;;;;mCA1Bd,KAAK,IAAI;;;;;4BA+BpB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAI;;;;;;kDACL,6LAAC;wCAAI,WAAU;kDAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC;GA5IgB;;QACG,qIAAA,CAAA,cAAW;QACb,qIAAA,CAAA,YAAS;QAEa,6JAAA,CAAA,gBAAa;;;KAJpC;uCA8ID", "debugId": null}}, {"offset": {"line": 418, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/components/layout/header.tsx"], "sourcesContent": ["'use client';\n\nimport { <PERSON>, Settings, User, LogOut } from 'lucide-react';\nimport { useState } from 'react';\n\nexport function Header() {\n  const [showUserMenu, setShowUserMenu] = useState(false);\n\n  return (\n    <header className=\"bg-white border-b border-gray-200 px-6 py-4\">\n      <div className=\"flex items-center justify-between\">\n        {/* Page title will be dynamic based on current route */}\n        <div className=\"flex-1\">\n          <h1 className=\"text-2xl font-semibold text-gray-900\">Dashboard</h1>\n        </div>\n\n        {/* Right side actions */}\n        <div className=\"flex items-center space-x-4\">\n          {/* Market status indicator */}\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"h-2 w-2 bg-green-500 rounded-full animate-pulse\"></div>\n            <span className=\"text-sm text-gray-600\">Market Open</span>\n          </div>\n\n          {/* Notifications */}\n          <button className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors\">\n            <Bell className=\"h-5 w-5\" />\n          </button>\n\n          {/* Settings */}\n          <button className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors\">\n            <Settings className=\"h-5 w-5\" />\n          </button>\n\n          {/* User menu */}\n          <div className=\"relative\">\n            <button\n              onClick={() => setShowUserMenu(!showUserMenu)}\n              className=\"flex items-center space-x-2 p-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors\"\n            >\n              <div className=\"h-8 w-8 bg-blue-600 rounded-full flex items-center justify-center\">\n                <User className=\"h-4 w-4 text-white\" />\n              </div>\n              <span className=\"text-sm font-medium\">Trader</span>\n            </button>\n\n            {/* User dropdown */}\n            {showUserMenu && (\n              <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50\">\n                <a\n                  href=\"#\"\n                  className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                >\n                  <User className=\"h-4 w-4 mr-2\" />\n                  Profile\n                </a>\n                <a\n                  href=\"#\"\n                  className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                >\n                  <Settings className=\"h-4 w-4 mr-2\" />\n                  Settings\n                </a>\n                <hr className=\"my-1\" />\n                <a\n                  href=\"#\"\n                  className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                >\n                  <LogOut className=\"h-4 w-4 mr-2\" />\n                  Sign out\n                </a>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AACA;;;AAHA;;;AAKO,SAAS;;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAG,WAAU;kCAAuC;;;;;;;;;;;8BAIvD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;sCAI1C,6LAAC;4BAAO,WAAU;sCAChB,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAIlB,6LAAC;4BAAO,WAAU;sCAChB,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;sCAItB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,gBAAgB,CAAC;oCAChC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,6LAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;gCAIvC,8BACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,WAAU;;8DAEV,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,6LAAC;4CACC,MAAK;4CACL,WAAU;;8DAEV,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,6LAAC;4CAAG,WAAU;;;;;;sDACd,6LAAC;4CACC,MAAK;4CACL,WAAU;;8DAEV,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD;GAzEgB;KAAA", "debugId": null}}, {"offset": {"line": 652, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/components/ui/LoadingStates.tsx"], "sourcesContent": ["// Optimized loading states and skeleton screens for better perceived performance\n\nimport { Loader, TrendingUp, Wallet, Search } from 'lucide-react';\n\n// Generic loading spinner\nexport function LoadingSpinner({ size = 'md', className = '' }: {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-6 h-6',\n    lg: 'w-8 h-8'\n  };\n\n  return (\n    <Loader className={`animate-spin ${sizeClasses[size]} ${className}`} />\n  );\n}\n\n// Skeleton for stock rows\nexport function StockRowSkeleton() {\n  return (\n    <div className=\"flex items-center justify-between p-4 border-b border-gray-100 animate-pulse\">\n      <div className=\"flex-1\">\n        <div className=\"flex items-center space-x-3\">\n          <div>\n            <div className=\"h-4 bg-gray-200 rounded w-20 mb-2\"></div>\n            <div className=\"h-3 bg-gray-200 rounded w-32\"></div>\n          </div>\n        </div>\n      </div>\n      <div className=\"flex items-center space-x-6\">\n        <div className=\"text-right\">\n          <div className=\"h-4 bg-gray-200 rounded w-16 mb-1\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-12\"></div>\n        </div>\n        <div className=\"text-right\">\n          <div className=\"h-4 bg-gray-200 rounded w-12 mb-1\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-8\"></div>\n        </div>\n        <div className=\"h-8 w-8 bg-gray-200 rounded\"></div>\n      </div>\n    </div>\n  );\n}\n\n// Skeleton for multiple stock rows\nexport function StockListSkeleton({ count = 5 }: { count?: number }) {\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n      {Array.from({ length: count }).map((_, index) => (\n        <StockRowSkeleton key={index} />\n      ))}\n    </div>\n  );\n}\n\n// Skeleton for capital management cards\nexport function CapitalCardSkeleton() {\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <div className=\"h-5 bg-gray-200 rounded w-32\"></div>\n        <div className=\"h-8 w-8 bg-gray-200 rounded\"></div>\n      </div>\n      <div className=\"space-y-3\">\n        <div className=\"h-8 bg-gray-200 rounded w-24\"></div>\n        <div className=\"h-4 bg-gray-200 rounded w-20\"></div>\n      </div>\n    </div>\n  );\n}\n\n// Skeleton for portfolio summary\nexport function PortfolioSummarySkeleton() {\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n      {Array.from({ length: 4 }).map((_, index) => (\n        <CapitalCardSkeleton key={index} />\n      ))}\n    </div>\n  );\n}\n\n// Page loading overlay\nexport function PageLoadingOverlay({ message = 'Loading...' }: { message?: string }) {\n  return (\n    <div className=\"fixed inset-0 bg-white bg-opacity-90 flex items-center justify-center z-50\">\n      <div className=\"text-center\">\n        <LoadingSpinner size=\"lg\" className=\"text-blue-600 mb-4\" />\n        <p className=\"text-gray-600 font-medium\">{message}</p>\n      </div>\n    </div>\n  );\n}\n\n// Inline loading state\nexport function InlineLoading({ message = 'Loading...' }: { message?: string }) {\n  return (\n    <div className=\"flex items-center justify-center py-8\">\n      <LoadingSpinner className=\"text-blue-600 mr-3\" />\n      <span className=\"text-gray-600\">{message}</span>\n    </div>\n  );\n}\n\n// Button loading state\nexport function ButtonLoading({ children, isLoading, ...props }: {\n  children: React.ReactNode;\n  isLoading: boolean;\n  [key: string]: any;\n}) {\n  return (\n    <button\n      {...props}\n      disabled={isLoading || props.disabled}\n      className={`${props.className} ${isLoading ? 'cursor-wait opacity-75' : ''}`}\n    >\n      {isLoading ? (\n        <div className=\"flex items-center justify-center\">\n          <LoadingSpinner size=\"sm\" className=\"mr-2\" />\n          Loading...\n        </div>\n      ) : (\n        children\n      )}\n    </button>\n  );\n}\n\n// Data table loading\nexport function DataTableLoading({ columns = 4, rows = 5 }: {\n  columns?: number;\n  rows?: number;\n}) {\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\n      {/* Header */}\n      <div className=\"border-b border-gray-200 p-4 animate-pulse\">\n        <div className=\"grid gap-4\" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>\n          {Array.from({ length: columns }).map((_, index) => (\n            <div key={index} className=\"h-4 bg-gray-200 rounded\"></div>\n          ))}\n        </div>\n      </div>\n      \n      {/* Rows */}\n      {Array.from({ length: rows }).map((_, rowIndex) => (\n        <div key={rowIndex} className=\"border-b border-gray-100 p-4 animate-pulse\">\n          <div className=\"grid gap-4\" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>\n            {Array.from({ length: columns }).map((_, colIndex) => (\n              <div key={colIndex} className=\"h-4 bg-gray-200 rounded\"></div>\n            ))}\n          </div>\n        </div>\n      ))}\n    </div>\n  );\n}\n\n// Chart loading placeholder\nexport function ChartLoadingSkeleton() {\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse\">\n      <div className=\"h-6 bg-gray-200 rounded w-48 mb-6\"></div>\n      <div className=\"h-64 bg-gray-100 rounded flex items-end justify-between px-4 pb-4\">\n        {Array.from({ length: 12 }).map((_, index) => (\n          <div\n            key={index}\n            className=\"bg-gray-200 rounded-t\"\n            style={{\n              height: `${Math.random() * 80 + 20}%`,\n              width: '6%'\n            }}\n          ></div>\n        ))}\n      </div>\n    </div>\n  );\n}\n\n// Navigation loading state\nexport function NavigationLoading() {\n  return (\n    <div className=\"fixed top-0 left-0 right-0 h-1 bg-blue-200 z-50\">\n      <div className=\"h-full bg-blue-600 animate-pulse\"></div>\n    </div>\n  );\n}\n\n// Error boundary fallback\nexport function ErrorFallback({ error, resetError }: {\n  error: Error;\n  resetError: () => void;\n}) {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n      <div className=\"max-w-md w-full bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center\">\n        <div className=\"text-red-500 mb-4\">\n          <svg className=\"w-12 h-12 mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\" />\n          </svg>\n        </div>\n        <h2 className=\"text-lg font-semibold text-gray-900 mb-2\">Something went wrong</h2>\n        <p className=\"text-gray-600 mb-4 text-sm\">{error.message}</p>\n        <button\n          onClick={resetError}\n          className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n        >\n          Try again\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,iFAAiF;;;;;;;;;;;;;;;;AAEjF;;;AAGO,SAAS,eAAe,KAG9B;QAH8B,EAAE,OAAO,IAAI,EAAE,YAAY,EAAE,EAG3D,GAH8B;IAI7B,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC,yMAAA,CAAA,SAAM;QAAC,WAAW,AAAC,gBAAoC,OAArB,WAAW,CAAC,KAAK,EAAC,KAAa,OAAV;;;;;;AAE5D;KAbgB;AAgBT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;;0CACC,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAIrB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEjB,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB;MAxBgB;AA2BT,SAAS,kBAAkB,KAAiC;QAAjC,EAAE,QAAQ,CAAC,EAAsB,GAAjC;IAChC,qBACE,6LAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,6LAAC,sBAAsB;;;;;;;;;;AAI/B;MARgB;AAWT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB;MAbgB;AAgBT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC,yBAAyB;;;;;;;;;;AAIlC;MARgB;AAWT,SAAS,mBAAmB,KAAgD;QAAhD,EAAE,UAAU,YAAY,EAAwB,GAAhD;IACjC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAe,MAAK;oBAAK,WAAU;;;;;;8BACpC,6LAAC;oBAAE,WAAU;8BAA6B;;;;;;;;;;;;;;;;;AAIlD;MATgB;AAYT,SAAS,cAAc,KAAgD;QAAhD,EAAE,UAAU,YAAY,EAAwB,GAAhD;IAC5B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAe,WAAU;;;;;;0BAC1B,6LAAC;gBAAK,WAAU;0BAAiB;;;;;;;;;;;;AAGvC;MAPgB;AAUT,SAAS,cAAc,KAI7B;QAJ6B,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAIvD,GAJ6B;IAK5B,qBACE,6LAAC;QACE,GAAG,KAAK;QACT,UAAU,aAAa,MAAM,QAAQ;QACrC,WAAW,AAAC,GAAqB,OAAnB,MAAM,SAAS,EAAC,KAA6C,OAA1C,YAAY,2BAA2B;kBAEvE,0BACC,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAe,MAAK;oBAAK,WAAU;;;;;;gBAAS;;;;;;mBAI/C;;;;;;AAIR;MArBgB;AAwBT,SAAS,iBAAiB,KAGhC;QAHgC,EAAE,UAAU,CAAC,EAAE,OAAO,CAAC,EAGvD,GAHgC;IAI/B,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;oBAAa,OAAO;wBAAE,qBAAqB,AAAC,UAAiB,OAAR,SAAQ;oBAAQ;8BACjF,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAQ,GAAG,GAAG,CAAC,CAAC,GAAG,sBACvC,6LAAC;4BAAgB,WAAU;2BAAjB;;;;;;;;;;;;;;;YAMf,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAK,GAAG,GAAG,CAAC,CAAC,GAAG,yBACpC,6LAAC;oBAAmB,WAAU;8BAC5B,cAAA,6LAAC;wBAAI,WAAU;wBAAa,OAAO;4BAAE,qBAAqB,AAAC,UAAiB,OAAR,SAAQ;wBAAQ;kCACjF,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAQ,GAAG,GAAG,CAAC,CAAC,GAAG,yBACvC,6LAAC;gCAAmB,WAAU;+BAApB;;;;;;;;;;mBAHN;;;;;;;;;;;AAUlB;MA3BgB;AA8BT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAG,GAAG,GAAG,CAAC,CAAC,GAAG,sBAClC,6LAAC;wBAEC,WAAU;wBACV,OAAO;4BACL,QAAQ,AAAC,GAA0B,OAAxB,KAAK,MAAM,KAAK,KAAK,IAAG;4BACnC,OAAO;wBACT;uBALK;;;;;;;;;;;;;;;;AAWjB;MAlBgB;AAqBT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;;;;;;;;;;AAGrB;OANgB;AAST,SAAS,cAAc,KAG7B;QAH6B,EAAE,KAAK,EAAE,UAAU,EAGhD,GAH6B;IAI5B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;wBAAoB,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCAC3E,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;8BAGzE,6LAAC;oBAAG,WAAU;8BAA2C;;;;;;8BACzD,6LAAC;oBAAE,WAAU;8BAA8B,MAAM,OAAO;;;;;;8BACxD,6LAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT;OAvBgB", "debugId": null}}, {"offset": {"line": 1202, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/components/layout/dashboard-layout.tsx"], "sourcesContent": ["'use client';\n\nimport { OptimizedSidebar } from './OptimizedSidebar';\nimport { Header } from './header';\nimport { Suspense } from 'react';\nimport { NavigationLoading } from '@/components/ui/LoadingStates';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n}\n\nexport function DashboardLayout({ children }: DashboardLayoutProps) {\n  return (\n    <div className=\"flex h-screen bg-gray-50\">\n      {/* Optimized Sidebar */}\n      <OptimizedSidebar />\n\n      {/* Main content */}\n      <div className=\"flex-1 flex flex-col overflow-hidden lg:ml-0\">\n        {/* Header */}\n        <Header />\n\n        {/* Page content with suspense boundary */}\n        <main className=\"flex-1 overflow-y-auto p-6\">\n          <Suspense fallback={<NavigationLoading />}>\n            {children}\n          </Suspense>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAWO,SAAS,gBAAgB,KAAkC;QAAlC,EAAE,QAAQ,EAAwB,GAAlC;IAC9B,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,mJAAA,CAAA,mBAAgB;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,yIAAA,CAAA,SAAM;;;;;kCAGP,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC,6JAAA,CAAA,WAAQ;4BAAC,wBAAU,6LAAC,4IAAA,CAAA,oBAAiB;;;;;sCACnC;;;;;;;;;;;;;;;;;;;;;;;AAMb;KApBgB", "debugId": null}}, {"offset": {"line": 1278, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/lib/cache-service.ts"], "sourcesContent": ["// Cache service for optimizing data fetching and reducing API calls\n\ninterface CacheEntry<T> {\n  data: T;\n  timestamp: number;\n  expiresAt: number;\n}\n\nclass CacheService {\n  private cache = new Map<string, CacheEntry<any>>();\n  private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes\n  private readonly STOCK_DATA_TTL = 30 * 1000; // 30 seconds for stock data\n  private readonly STOCK_NAMES_TTL = 24 * 60 * 60 * 1000; // 24 hours for stock names\n  private readonly BROKER_DATA_TTL = 10 * 1000; // 10 seconds for broker data\n  private readonly PORTFOLIO_DATA_TTL = 60 * 1000; // 1 minute for portfolio data\n\n  // Get TTL based on data type\n  private getTTL(key: string): number {\n    if (key.includes('stock-name') || key.includes('names-map')) {\n      return this.STOCK_NAMES_TTL;\n    }\n    if (key.includes('stock') || key.includes('nifty')) {\n      return this.STOCK_DATA_TTL;\n    }\n    if (key.includes('broker') || key.includes('balance')) {\n      return this.BROKER_DATA_TTL;\n    }\n    if (key.includes('portfolio') || key.includes('holdings')) {\n      return this.PORTFOLIO_DATA_TTL;\n    }\n    return this.DEFAULT_TTL;\n  }\n\n  // Set cache entry\n  set<T>(key: string, data: T, customTTL?: number): void {\n    const ttl = customTTL || this.getTTL(key);\n    const now = Date.now();\n    \n    this.cache.set(key, {\n      data,\n      timestamp: now,\n      expiresAt: now + ttl\n    });\n  }\n\n  // Get cache entry\n  get<T>(key: string): T | null {\n    const entry = this.cache.get(key);\n    \n    if (!entry) {\n      return null;\n    }\n\n    // Check if expired\n    if (Date.now() > entry.expiresAt) {\n      this.cache.delete(key);\n      return null;\n    }\n\n    return entry.data as T;\n  }\n\n  // Check if key exists and is valid\n  has(key: string): boolean {\n    const entry = this.cache.get(key);\n    \n    if (!entry) {\n      return false;\n    }\n\n    // Check if expired\n    if (Date.now() > entry.expiresAt) {\n      this.cache.delete(key);\n      return false;\n    }\n\n    return true;\n  }\n\n  // Clear specific key\n  delete(key: string): void {\n    this.cache.delete(key);\n  }\n\n  // Clear all cache\n  clear(): void {\n    this.cache.clear();\n  }\n\n  // Clear expired entries\n  cleanup(): void {\n    const now = Date.now();\n    \n    for (const [key, entry] of this.cache.entries()) {\n      if (now > entry.expiresAt) {\n        this.cache.delete(key);\n      }\n    }\n  }\n\n  // Get cache stats\n  getStats(): {\n    size: number;\n    keys: string[];\n    hitRate: number;\n  } {\n    return {\n      size: this.cache.size,\n      keys: Array.from(this.cache.keys()),\n      hitRate: 0 // TODO: Implement hit rate tracking\n    };\n  }\n\n  // Cached fetch wrapper\n  async cachedFetch<T>(\n    key: string,\n    fetchFn: () => Promise<T>,\n    customTTL?: number\n  ): Promise<T> {\n    // Try to get from cache first\n    const cached = this.get<T>(key);\n    if (cached !== null) {\n      return cached;\n    }\n\n    // Fetch fresh data\n    try {\n      const data = await fetchFn();\n      this.set(key, data, customTTL);\n      return data;\n    } catch (error) {\n      // If fetch fails, try to return stale data if available\n      const staleEntry = this.cache.get(key);\n      if (staleEntry) {\n        console.warn(`Using stale data for ${key} due to fetch error:`, error);\n        return staleEntry.data as T;\n      }\n      throw error;\n    }\n  }\n\n  // Prefetch data in background\n  async prefetch<T>(\n    key: string,\n    fetchFn: () => Promise<T>,\n    customTTL?: number\n  ): Promise<void> {\n    // Only prefetch if not already cached\n    if (!this.has(key)) {\n      try {\n        const data = await fetchFn();\n        this.set(key, data, customTTL);\n      } catch (error) {\n        console.warn(`Prefetch failed for ${key}:`, error);\n      }\n    }\n  }\n\n  // Batch fetch with caching\n  async batchFetch<T>(\n    requests: Array<{\n      key: string;\n      fetchFn: () => Promise<T>;\n      ttl?: number;\n    }>\n  ): Promise<T[]> {\n    const results: T[] = [];\n    const fetchPromises: Promise<T>[] = [];\n\n    for (const request of requests) {\n      const cached = this.get<T>(request.key);\n      if (cached !== null) {\n        results.push(cached);\n      } else {\n        fetchPromises.push(\n          request.fetchFn().then(data => {\n            this.set(request.key, data, request.ttl);\n            return data;\n          })\n        );\n      }\n    }\n\n    // Wait for all fetches to complete\n    const fetchedResults = await Promise.all(fetchPromises);\n    results.push(...fetchedResults);\n\n    return results;\n  }\n\n  // Invalidate cache by pattern\n  invalidatePattern(pattern: string): void {\n    const regex = new RegExp(pattern);\n    \n    for (const key of this.cache.keys()) {\n      if (regex.test(key)) {\n        this.cache.delete(key);\n      }\n    }\n  }\n\n  // Auto cleanup interval\n  startAutoCleanup(intervalMs: number = 5 * 60 * 1000): void {\n    setInterval(() => {\n      this.cleanup();\n    }, intervalMs);\n  }\n}\n\n// Create singleton instance\nexport const cacheService = new CacheService();\n\n// Start auto cleanup\nif (typeof window !== 'undefined') {\n  cacheService.startAutoCleanup();\n}\n\n// Cache key generators\nexport const CacheKeys = {\n  brokerBalance: (userId: string) => `broker-balance-${userId}`,\n  fundAllocation: (userId: string, strategy: string) => `fund-allocation-${userId}-${strategy}`,\n  portfolioSummary: (userId: string) => `portfolio-summary-${userId}`,\n  niftyStocks: (batchIndex: number) => `nifty-stocks-batch-${batchIndex}`,\n  stockQuote: (symbol: string) => `stock-quote-${symbol}`,\n  stockName: (symbol: string) => `stock-name-${symbol}`,\n  stockNamesMap: () => 'stock-names-map',\n  stockPriceData: (symbol: string) => `stock-price-data-${symbol}`,\n  stockSearch: (query: string) => `stock-search-${query}`,\n  yahooQuotes: (symbols: string[]) => `yahoo-quotes-${symbols.sort().join(',')}`,\n};\n"], "names": [], "mappings": "AAAA,oEAAoE;;;;;;;AAQpE,MAAM;IAQJ,6BAA6B;IACrB,OAAO,GAAW,EAAU;QAClC,IAAI,IAAI,QAAQ,CAAC,iBAAiB,IAAI,QAAQ,CAAC,cAAc;YAC3D,OAAO,IAAI,CAAC,eAAe;QAC7B;QACA,IAAI,IAAI,QAAQ,CAAC,YAAY,IAAI,QAAQ,CAAC,UAAU;YAClD,OAAO,IAAI,CAAC,cAAc;QAC5B;QACA,IAAI,IAAI,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,YAAY;YACrD,OAAO,IAAI,CAAC,eAAe;QAC7B;QACA,IAAI,IAAI,QAAQ,CAAC,gBAAgB,IAAI,QAAQ,CAAC,aAAa;YACzD,OAAO,IAAI,CAAC,kBAAkB;QAChC;QACA,OAAO,IAAI,CAAC,WAAW;IACzB;IAEA,kBAAkB;IAClB,IAAO,GAAW,EAAE,IAAO,EAAE,SAAkB,EAAQ;QACrD,MAAM,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC;QACrC,MAAM,MAAM,KAAK,GAAG;QAEpB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;YAClB;YACA,WAAW;YACX,WAAW,MAAM;QACnB;IACF;IAEA,kBAAkB;IAClB,IAAO,GAAW,EAAY;QAC5B,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAE7B,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,mBAAmB;QACnB,IAAI,KAAK,GAAG,KAAK,MAAM,SAAS,EAAE;YAChC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YAClB,OAAO;QACT;QAEA,OAAO,MAAM,IAAI;IACnB;IAEA,mCAAmC;IACnC,IAAI,GAAW,EAAW;QACxB,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAE7B,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,mBAAmB;QACnB,IAAI,KAAK,GAAG,KAAK,MAAM,SAAS,EAAE;YAChC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YAClB,OAAO;QACT;QAEA,OAAO;IACT;IAEA,qBAAqB;IACrB,OAAO,GAAW,EAAQ;QACxB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IACpB;IAEA,kBAAkB;IAClB,QAAc;QACZ,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;IAEA,wBAAwB;IACxB,UAAgB;QACd,MAAM,MAAM,KAAK,GAAG;QAEpB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,GAAI;YAC/C,IAAI,MAAM,MAAM,SAAS,EAAE;gBACzB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YACpB;QACF;IACF;IAEA,kBAAkB;IAClB,WAIE;QACA,OAAO;YACL,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;YAChC,SAAS,EAAE,oCAAoC;QACjD;IACF;IAEA,uBAAuB;IACvB,MAAM,YACJ,GAAW,EACX,OAAyB,EACzB,SAAkB,EACN;QACZ,8BAA8B;QAC9B,MAAM,SAAS,IAAI,CAAC,GAAG,CAAI;QAC3B,IAAI,WAAW,MAAM;YACnB,OAAO;QACT;QAEA,mBAAmB;QACnB,IAAI;YACF,MAAM,OAAO,MAAM;YACnB,IAAI,CAAC,GAAG,CAAC,KAAK,MAAM;YACpB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,wDAAwD;YACxD,MAAM,aAAa,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;YAClC,IAAI,YAAY;gBACd,QAAQ,IAAI,CAAC,AAAC,wBAA2B,OAAJ,KAAI,yBAAuB;gBAChE,OAAO,WAAW,IAAI;YACxB;YACA,MAAM;QACR;IACF;IAEA,8BAA8B;IAC9B,MAAM,SACJ,GAAW,EACX,OAAyB,EACzB,SAAkB,EACH;QACf,sCAAsC;QACtC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM;YAClB,IAAI;gBACF,MAAM,OAAO,MAAM;gBACnB,IAAI,CAAC,GAAG,CAAC,KAAK,MAAM;YACtB,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,AAAC,uBAA0B,OAAJ,KAAI,MAAI;YAC9C;QACF;IACF;IAEA,2BAA2B;IAC3B,MAAM,WACJ,QAIE,EACY;QACd,MAAM,UAAe,EAAE;QACvB,MAAM,gBAA8B,EAAE;QAEtC,KAAK,MAAM,WAAW,SAAU;YAC9B,MAAM,SAAS,IAAI,CAAC,GAAG,CAAI,QAAQ,GAAG;YACtC,IAAI,WAAW,MAAM;gBACnB,QAAQ,IAAI,CAAC;YACf,OAAO;gBACL,cAAc,IAAI,CAChB,QAAQ,OAAO,GAAG,IAAI,CAAC,CAAA;oBACrB,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,EAAE,MAAM,QAAQ,GAAG;oBACvC,OAAO;gBACT;YAEJ;QACF;QAEA,mCAAmC;QACnC,MAAM,iBAAiB,MAAM,QAAQ,GAAG,CAAC;QACzC,QAAQ,IAAI,IAAI;QAEhB,OAAO;IACT;IAEA,8BAA8B;IAC9B,kBAAkB,OAAe,EAAQ;QACvC,MAAM,QAAQ,IAAI,OAAO;QAEzB,KAAK,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,GAAI;YACnC,IAAI,MAAM,IAAI,CAAC,MAAM;gBACnB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YACpB;QACF;IACF;IAEA,wBAAwB;IACxB,mBAA2D;YAA1C,aAAA,iEAAqB,IAAI,KAAK;QAC7C,YAAY;YACV,IAAI,CAAC,OAAO;QACd,GAAG;IACL;;QArMA,+KAAQ,SAAQ,IAAI;QACpB,+KAAiB,eAAc,IAAI,KAAK,OAAM,YAAY;QAC1D,+KAAiB,kBAAiB,KAAK,OAAM,4BAA4B;QACzE,+KAAiB,mBAAkB,KAAK,KAAK,KAAK,OAAM,2BAA2B;QACnF,+KAAiB,mBAAkB,KAAK,OAAM,6BAA6B;QAC3E,+KAAiB,sBAAqB,KAAK,OAAM,8BAA8B;;AAiMjF;AAGO,MAAM,eAAe,IAAI;AAEhC,qBAAqB;AACrB,wCAAmC;IACjC,aAAa,gBAAgB;AAC/B;AAGO,MAAM,YAAY;IACvB,eAAe,CAAC,SAAmB,AAAC,kBAAwB,OAAP;IACrD,gBAAgB,CAAC,QAAgB,WAAqB,AAAC,mBAA4B,OAAV,QAAO,KAAY,OAAT;IACnF,kBAAkB,CAAC,SAAmB,AAAC,qBAA2B,OAAP;IAC3D,aAAa,CAAC,aAAuB,AAAC,sBAAgC,OAAX;IAC3D,YAAY,CAAC,SAAmB,AAAC,eAAqB,OAAP;IAC/C,WAAW,CAAC,SAAmB,AAAC,cAAoB,OAAP;IAC7C,eAAe,IAAM;IACrB,gBAAgB,CAAC,SAAmB,AAAC,oBAA0B,OAAP;IACxD,aAAa,CAAC,QAAkB,AAAC,gBAAqB,OAAN;IAChD,aAAa,CAAC,UAAsB,AAAC,gBAAwC,OAAzB,QAAQ,IAAI,GAAG,IAAI,CAAC;AAC1E", "debugId": null}}, {"offset": {"line": 1468, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/lib/stock-names-service.ts"], "sourcesContent": ["// Stock names service for caching and managing stock company names\nimport { cacheService, CacheKeys } from './cache-service';\n\ninterface StockNameEntry {\n  symbol: string;\n  name: string;\n  lastUpdated: number;\n}\n\nclass StockNamesService {\n  private readonly BATCH_SIZE = 50;\n  private readonly MAX_RETRIES = 3;\n\n  // Get stock name from cache or fetch if not available\n  async getStockName(symbol: string): Promise<string> {\n    // Try to get from cache first\n    const cached = cacheService.get<string>(CacheKeys.stockName(symbol));\n    if (cached) {\n      return cached;\n    }\n\n    // If not in cache, fetch from Yahoo Finance\n    try {\n      const { yahooFinanceService } = await import('./yahoo-finance');\n      const quote = await yahooFinanceService.getQuote(symbol);\n      const name = quote?.name || symbol.replace('.NS', '');\n\n      // Cache the name for 24 hours\n      cacheService.set(CacheKeys.stockName(symbol), name);\n      return name;\n    } catch (error) {\n      console.warn(`Failed to fetch name for ${symbol}:`, error);\n      // Return symbol without .NS as fallback\n      return symbol.replace('.NS', '');\n    }\n  }\n\n  // Get multiple stock names efficiently\n  async getStockNames(symbols: string[]): Promise<Map<string, string>> {\n    const namesMap = new Map<string, string>();\n    const uncachedSymbols: string[] = [];\n\n    // Check cache for each symbol\n    for (const symbol of symbols) {\n      const cached = cacheService.get<string>(CacheKeys.stockName(symbol));\n      if (cached) {\n        namesMap.set(symbol, cached);\n      } else {\n        uncachedSymbols.push(symbol);\n      }\n    }\n\n    // If all names are cached, return immediately\n    if (uncachedSymbols.length === 0) {\n      return namesMap;\n    }\n\n    console.log(`📝 Fetching names for ${uncachedSymbols.length} uncached stocks`);\n\n    // Fetch uncached names in batches\n    const batches = this.chunkArray(uncachedSymbols, this.BATCH_SIZE);\n    \n    for (const batch of batches) {\n      try {\n        const { yahooFinanceService } = await import('./yahoo-finance');\n        const quotes = await yahooFinanceService.getMultipleQuotes(batch);\n        \n        for (const quote of quotes) {\n          if (quote && quote.symbol) {\n            // Use the name from the quote, or fallback to symbol without .NS\n            const name = quote.name || quote.symbol.replace('.NS', '');\n            namesMap.set(quote.symbol, name);\n\n            // Cache the name for 24 hours\n            cacheService.set(CacheKeys.stockName(quote.symbol), name);\n          }\n        }\n\n        // Ensure all symbols in the batch have names (even if fallback)\n        for (const symbol of batch) {\n          if (!namesMap.has(symbol)) {\n            const fallbackName = symbol.replace('.NS', '');\n            namesMap.set(symbol, fallbackName);\n            cacheService.set(CacheKeys.stockName(symbol), fallbackName);\n            console.log(`📝 Using fallback name for ${symbol}: ${fallbackName}`);\n          }\n        }\n        \n        // Add delay between batches to avoid rate limiting\n        if (batches.indexOf(batch) < batches.length - 1) {\n          await new Promise(resolve => setTimeout(resolve, 100));\n        }\n      } catch (error) {\n        console.warn(`Failed to fetch names for batch:`, error);\n        \n        // Add fallback names for failed batch\n        for (const symbol of batch) {\n          if (!namesMap.has(symbol)) {\n            const fallbackName = symbol.replace('.NS', '');\n            namesMap.set(symbol, fallbackName);\n            cacheService.set(CacheKeys.stockName(symbol), fallbackName);\n          }\n        }\n      }\n    }\n\n    return namesMap;\n  }\n\n  // Get all cached stock names\n  getCachedStockNames(): Map<string, string> {\n    const cachedMap = cacheService.get<Map<string, string>>(CacheKeys.stockNamesMap());\n    return cachedMap || new Map();\n  }\n\n  // Cache stock names map for quick access\n  cacheStockNamesMap(namesMap: Map<string, string>): void {\n    cacheService.set(CacheKeys.stockNamesMap(), namesMap);\n  }\n\n  // Preload stock names for given symbols\n  async preloadStockNames(symbols: string[]): Promise<void> {\n    console.log(`🚀 Preloading names for ${symbols.length} stocks`);\n    \n    try {\n      const namesMap = await this.getStockNames(symbols);\n      this.cacheStockNamesMap(namesMap);\n      console.log(`✅ Preloaded ${namesMap.size} stock names`);\n    } catch (error) {\n      console.error('Failed to preload stock names:', error);\n    }\n  }\n\n  // Check if stock name is cached\n  isNameCached(symbol: string): boolean {\n    return cacheService.has(CacheKeys.stockName(symbol));\n  }\n\n  // Get cache statistics for stock names\n  getNamesCacheStats(): {\n    cachedCount: number;\n    totalRequested: number;\n    hitRate: number;\n  } {\n    const stats = cacheService.getStats();\n    const nameKeys = stats.keys.filter(key => key.includes('stock-name'));\n    \n    return {\n      cachedCount: nameKeys.length,\n      totalRequested: nameKeys.length, // Simplified for now\n      hitRate: nameKeys.length > 0 ? 0.8 : 0 // Estimated hit rate\n    };\n  }\n\n  // Clear all cached stock names\n  clearNamesCache(): void {\n    cacheService.invalidatePattern('stock-name');\n    cacheService.delete(CacheKeys.stockNamesMap());\n    console.log('🗑️ Cleared all cached stock names');\n  }\n\n  // Refresh stock names (force re-fetch)\n  async refreshStockNames(symbols: string[]): Promise<Map<string, string>> {\n    // Clear existing cache for these symbols\n    for (const symbol of symbols) {\n      cacheService.delete(CacheKeys.stockName(symbol));\n    }\n    \n    // Fetch fresh names\n    return await this.getStockNames(symbols);\n  }\n\n  // Utility function to chunk array into smaller arrays\n  private chunkArray<T>(array: T[], chunkSize: number): T[][] {\n    const chunks: T[][] = [];\n    for (let i = 0; i < array.length; i += chunkSize) {\n      chunks.push(array.slice(i, i + chunkSize));\n    }\n    return chunks;\n  }\n\n  // Get stock name with fallback\n  getStockNameSync(symbol: string): string {\n    const cached = cacheService.get<string>(CacheKeys.stockName(symbol));\n    return cached || symbol.replace('.NS', '');\n  }\n\n  // Batch update stock names from quotes\n  updateStockNamesFromQuotes(quotes: any[]): void {\n    for (const quote of quotes) {\n      if (quote && quote.symbol && quote.name) {\n        cacheService.set(CacheKeys.stockName(quote.symbol), quote.name);\n      }\n    }\n  }\n\n  // Force refresh all stock names (clears cache and re-fetches)\n  async forceRefreshAllNames(symbols: string[]): Promise<void> {\n    console.log('🔄 Force refreshing all stock names...');\n    this.clearNamesCache();\n    await this.preloadStockNames(symbols);\n  }\n}\n\n// Create singleton instance\nexport const stockNamesService = new StockNamesService();\n\n// Export types\nexport type { StockNameEntry };\n"], "names": [], "mappings": "AAAA,mEAAmE;;;;;AACnE;;;AAQA,MAAM;IAIJ,sDAAsD;IACtD,MAAM,aAAa,MAAc,EAAmB;QAClD,8BAA8B;QAC9B,MAAM,SAAS,iIAAA,CAAA,eAAY,CAAC,GAAG,CAAS,iIAAA,CAAA,YAAS,CAAC,SAAS,CAAC;QAC5D,IAAI,QAAQ;YACV,OAAO;QACT;QAEA,4CAA4C;QAC5C,IAAI;YACF,MAAM,EAAE,mBAAmB,EAAE,GAAG;YAChC,MAAM,QAAQ,MAAM,oBAAoB,QAAQ,CAAC;YACjD,MAAM,OAAO,CAAA,kBAAA,4BAAA,MAAO,IAAI,KAAI,OAAO,OAAO,CAAC,OAAO;YAElD,8BAA8B;YAC9B,iIAAA,CAAA,eAAY,CAAC,GAAG,CAAC,iIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,SAAS;YAC9C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,AAAC,4BAAkC,OAAP,QAAO,MAAI;YACpD,wCAAwC;YACxC,OAAO,OAAO,OAAO,CAAC,OAAO;QAC/B;IACF;IAEA,uCAAuC;IACvC,MAAM,cAAc,OAAiB,EAAgC;QACnE,MAAM,WAAW,IAAI;QACrB,MAAM,kBAA4B,EAAE;QAEpC,8BAA8B;QAC9B,KAAK,MAAM,UAAU,QAAS;YAC5B,MAAM,SAAS,iIAAA,CAAA,eAAY,CAAC,GAAG,CAAS,iIAAA,CAAA,YAAS,CAAC,SAAS,CAAC;YAC5D,IAAI,QAAQ;gBACV,SAAS,GAAG,CAAC,QAAQ;YACvB,OAAO;gBACL,gBAAgB,IAAI,CAAC;YACvB;QACF;QAEA,8CAA8C;QAC9C,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,AAAC,yBAA+C,OAAvB,gBAAgB,MAAM,EAAC;QAE5D,kCAAkC;QAClC,MAAM,UAAU,IAAI,CAAC,UAAU,CAAC,iBAAiB,IAAI,CAAC,UAAU;QAEhE,KAAK,MAAM,SAAS,QAAS;YAC3B,IAAI;gBACF,MAAM,EAAE,mBAAmB,EAAE,GAAG;gBAChC,MAAM,SAAS,MAAM,oBAAoB,iBAAiB,CAAC;gBAE3D,KAAK,MAAM,SAAS,OAAQ;oBAC1B,IAAI,SAAS,MAAM,MAAM,EAAE;wBACzB,iEAAiE;wBACjE,MAAM,OAAO,MAAM,IAAI,IAAI,MAAM,MAAM,CAAC,OAAO,CAAC,OAAO;wBACvD,SAAS,GAAG,CAAC,MAAM,MAAM,EAAE;wBAE3B,8BAA8B;wBAC9B,iIAAA,CAAA,eAAY,CAAC,GAAG,CAAC,iIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,MAAM,MAAM,GAAG;oBACtD;gBACF;gBAEA,gEAAgE;gBAChE,KAAK,MAAM,UAAU,MAAO;oBAC1B,IAAI,CAAC,SAAS,GAAG,CAAC,SAAS;wBACzB,MAAM,eAAe,OAAO,OAAO,CAAC,OAAO;wBAC3C,SAAS,GAAG,CAAC,QAAQ;wBACrB,iIAAA,CAAA,eAAY,CAAC,GAAG,CAAC,iIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,SAAS;wBAC9C,QAAQ,GAAG,CAAC,AAAC,8BAAwC,OAAX,QAAO,MAAiB,OAAb;oBACvD;gBACF;gBAEA,mDAAmD;gBACnD,IAAI,QAAQ,OAAO,CAAC,SAAS,QAAQ,MAAM,GAAG,GAAG;oBAC/C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACnD;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAE,oCAAmC;gBAEjD,sCAAsC;gBACtC,KAAK,MAAM,UAAU,MAAO;oBAC1B,IAAI,CAAC,SAAS,GAAG,CAAC,SAAS;wBACzB,MAAM,eAAe,OAAO,OAAO,CAAC,OAAO;wBAC3C,SAAS,GAAG,CAAC,QAAQ;wBACrB,iIAAA,CAAA,eAAY,CAAC,GAAG,CAAC,iIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,SAAS;oBAChD;gBACF;YACF;QACF;QAEA,OAAO;IACT;IAEA,6BAA6B;IAC7B,sBAA2C;QACzC,MAAM,YAAY,iIAAA,CAAA,eAAY,CAAC,GAAG,CAAsB,iIAAA,CAAA,YAAS,CAAC,aAAa;QAC/E,OAAO,aAAa,IAAI;IAC1B;IAEA,yCAAyC;IACzC,mBAAmB,QAA6B,EAAQ;QACtD,iIAAA,CAAA,eAAY,CAAC,GAAG,CAAC,iIAAA,CAAA,YAAS,CAAC,aAAa,IAAI;IAC9C;IAEA,wCAAwC;IACxC,MAAM,kBAAkB,OAAiB,EAAiB;QACxD,QAAQ,GAAG,CAAC,AAAC,2BAAyC,OAAf,QAAQ,MAAM,EAAC;QAEtD,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,aAAa,CAAC;YAC1C,IAAI,CAAC,kBAAkB,CAAC;YACxB,QAAQ,GAAG,CAAC,AAAC,eAA4B,OAAd,SAAS,IAAI,EAAC;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA,gCAAgC;IAChC,aAAa,MAAc,EAAW;QACpC,OAAO,iIAAA,CAAA,eAAY,CAAC,GAAG,CAAC,iIAAA,CAAA,YAAS,CAAC,SAAS,CAAC;IAC9C;IAEA,uCAAuC;IACvC,qBAIE;QACA,MAAM,QAAQ,iIAAA,CAAA,eAAY,CAAC,QAAQ;QACnC,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,CAAC;QAEvD,OAAO;YACL,aAAa,SAAS,MAAM;YAC5B,gBAAgB,SAAS,MAAM;YAC/B,SAAS,SAAS,MAAM,GAAG,IAAI,MAAM,EAAE,qBAAqB;QAC9D;IACF;IAEA,+BAA+B;IAC/B,kBAAwB;QACtB,iIAAA,CAAA,eAAY,CAAC,iBAAiB,CAAC;QAC/B,iIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,iIAAA,CAAA,YAAS,CAAC,aAAa;QAC3C,QAAQ,GAAG,CAAC;IACd;IAEA,uCAAuC;IACvC,MAAM,kBAAkB,OAAiB,EAAgC;QACvE,yCAAyC;QACzC,KAAK,MAAM,UAAU,QAAS;YAC5B,iIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,iIAAA,CAAA,YAAS,CAAC,SAAS,CAAC;QAC1C;QAEA,oBAAoB;QACpB,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC;IAClC;IAEA,sDAAsD;IAC9C,WAAc,KAAU,EAAE,SAAiB,EAAS;QAC1D,MAAM,SAAgB,EAAE;QACxB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,KAAK,UAAW;YAChD,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,GAAG,IAAI;QACjC;QACA,OAAO;IACT;IAEA,+BAA+B;IAC/B,iBAAiB,MAAc,EAAU;QACvC,MAAM,SAAS,iIAAA,CAAA,eAAY,CAAC,GAAG,CAAS,iIAAA,CAAA,YAAS,CAAC,SAAS,CAAC;QAC5D,OAAO,UAAU,OAAO,OAAO,CAAC,OAAO;IACzC;IAEA,uCAAuC;IACvC,2BAA2B,MAAa,EAAQ;QAC9C,KAAK,MAAM,SAAS,OAAQ;YAC1B,IAAI,SAAS,MAAM,MAAM,IAAI,MAAM,IAAI,EAAE;gBACvC,iIAAA,CAAA,eAAY,CAAC,GAAG,CAAC,iIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,MAAM,MAAM,GAAG,MAAM,IAAI;YAChE;QACF;IACF;IAEA,8DAA8D;IAC9D,MAAM,qBAAqB,OAAiB,EAAiB;QAC3D,QAAQ,GAAG,CAAC;QACZ,IAAI,CAAC,eAAe;QACpB,MAAM,IAAI,CAAC,iBAAiB,CAAC;IAC/B;;QA/LA,+KAAiB,cAAa;QAC9B,+KAAiB,eAAc;;AA+LjC;AAGO,MAAM,oBAAoB,IAAI", "debugId": null}}, {"offset": {"line": 1649, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/lib/nifty-stocks.ts"], "sourcesContent": ["// Current Nifty 200 stock symbols - updated list as of 2025\nconst RAW_NIFTY_200_SYMBOLS = [\n  'NYKAA', 'MRF', 'MANKIND', 'CHOL<PERSON><PERSON>', 'CONCOR', 'ICICIPRUL<PERSON>', 'PREMIERENE', '<PERSON><PERSON>',\n  '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>IRPO<PERSON>',\n  'VBL', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'BH<PERSON>', 'B<PERSON>CO<PERSON>', 'INDHOTEL', 'COALINDI<PERSON>', 'HYUNDAI',\n  'GODREJ<PERSON>', 'HINDUNILVR', 'ADANIENS<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'SH<PERSON><PERSON><PERSON>', 'VMM', 'CUMMINSIND',\n  'LOD<PERSON>', 'ABB', 'COCHINSHIP', 'BRITANNIA', 'ULTRACEMCO', 'AUBANK', 'KALYANKJIL',\n  'BDL', 'DIVI<PERSON><PERSON>', 'INDIGO', 'POWERGRID', 'OIL', 'HEROMOTOCO', '<PERSON><PERSON><PERSON>', '<PERSON>',\n  '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'B<PERSON>CHLT<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'TORNTPHARM', 'TATATECH', 'MAHABANK',\n  'M&M', 'ASIANPAINT', 'UNITDSPR', 'PIIND', 'ITC', 'ASHOKLEY', 'NESTLEIND',\n  'HDFCAMC', 'ADANIGREEN', 'MARICO', 'APOLLOTYRE', 'LTF', 'HDFCBANK', 'TVSMOTOR',\n  'ADANIPOWER', 'MARUTI', 'MOTHERSON', 'BAJAJHFL', 'NTPCGREEN', 'JIOFIN', 'BAJAJFINSV',\n  'JSWENERGY', 'TORNTPOWER', 'NTPC', 'FEDERALBNK', 'ALKEM', 'NHPC', 'BAJAJ-AUTO',\n  'EICHERMOT', 'M&MFIN', 'ETERNAL', 'MPHASIS', 'HUDCO', 'PETRONET', 'SUPREMEIND',\n  'HAL', 'CIPLA', 'IRCTC', 'KOTAKBANK', 'POLICYBZR', 'INDIANB', 'CANBK', 'AXISBANK',\n  'ONGC', 'LICI', 'SWIGGY', 'TATAMOTORS', 'IDEA', 'SOLARINDS', 'LICHSGFIN',\n  'MAZDOCK', 'TATAPOWER', 'IREDA', 'SRF', 'BAJAJHLDNG', 'SBIN', 'BHARTIHEXA',\n  'ZYDUSLIFE', 'VOLTAS', 'AMBUJACEM', 'MUTHOOTFIN', 'TITAN', 'ADANIPORTS', 'SBILIFE',\n  'ATGL', 'ADANIENT', 'YESBANK', 'INFY', 'TATACONSUM', 'EXIDEIND', 'AUROPHARMA',\n  'PAYTM', 'PFC', 'TATAELXSI', 'TATACOMM', 'SUNPHARMA', 'INDUSTOWER', 'JSWSTEEL',\n  'ESCORTS', 'IRFC', 'BHARTIARTL', 'LUPIN', 'RVNL', 'POLYCAB', 'CGPOWER',\n  'GLENMARK', 'HAVELLS', 'PIDILITIND', 'TCS', 'NMDC', 'LTIM', 'TRENT', 'SUZLON',\n  'DMART', 'JUBLFOOD', 'SAIL', 'COLPAL', 'LT', 'MFSL', 'SONACOMS', 'PRESTIGE',\n  'IDFCFIRSTB', 'ICICIBANK', 'SJVN', 'BEL', 'OFSS', 'WIPRO', 'ICICIGI', 'ABCAPITAL',\n  'COFORGE', 'JINDALSTEL', 'GRASIM', 'BANKINDIA', 'PAGEIND', 'ABFRL', 'TIINDIA',\n  'INDUSINDBK', 'PNB', 'RECLTD', 'KPITTECH', 'HDFCLIFE', 'RELIANCE', 'PERSISTENT',\n  'DRREDDY', 'UPL', 'OLAELEC', 'TECHM', 'OBEROIRLTY', 'APOLLOHOSP', 'BHARATFORG',\n  'NAUKRI', 'HINDPETRO', 'DLF', 'TATASTEEL', 'BPCL', 'HINDALCO', 'IRB', 'APLAPOLLO',\n  'NATIONALUM', 'HCLTECH', 'SIEMENS', 'IOC', 'GODREJPROP', 'IGL', 'HINDZINC',\n  'PHOENIXLTD', 'VEDL', 'UNIONBANK', 'MAXHEALTH', 'GAIL'\n];\n\n// Remove duplicates and create final list\nexport const NIFTY_200_SYMBOLS = [...new Set(RAW_NIFTY_200_SYMBOLS)];\n\n// Validation: Log any duplicates found and final count\nconst duplicates = RAW_NIFTY_200_SYMBOLS.filter((symbol, index) =>\n  RAW_NIFTY_200_SYMBOLS.indexOf(symbol) !== index\n);\n\nif (duplicates.length > 0) {\n  console.warn('Duplicate symbols found and removed:', duplicates);\n}\n\nconsole.log(`Nifty 200 symbols loaded: ${NIFTY_200_SYMBOLS.length} unique symbols`);\n\nexport interface NiftyStock {\n  symbol: string;\n  name: string;\n  price: number;\n  change: number;\n  changePercent: number;\n  volume: number;\n  marketCap?: number;\n  high52Week?: number;\n  low52Week?: number;\n  high52WeekDate?: string;\n  low52WeekDate?: string;\n  isEligible: boolean; // CMP < 2000 or in holdings\n  inHoldings: boolean;\n  isBOHEligible?: boolean;\n}\n\n// Function to get Yahoo Finance symbol format for Indian stocks\nexport function getYahooSymbol(nseSymbol: string): string {\n  // Handle special cases for Yahoo Finance symbol mapping\n  const symbolMappings: { [key: string]: string } = {\n    'M&M': 'MM.NS',\n    'M&MFIN': 'MMFIN.NS',\n    'BAJAJ-AUTO': 'BAJAJ_AUTO.NS',\n    'L&T': 'LT.NS',\n    'LTF': 'LTFH.NS', // L&T Finance Holdings\n    'BOSCHLTD': 'BOSCHLTD.NS', // Ensure proper mapping for Bosch\n    'BSOFT': 'BSOFT.NS' // Birlasoft\n  };\n\n  // Handle merged/renamed stocks - map to their current equivalent\n  const renamedMappings: { [key: string]: string } = {\n    'CADILAHC': 'ZYDUSLIFE.NS', // Cadila Healthcare renamed to Zydus Lifesciences\n  };\n\n  // Handle merged stocks (for backward compatibility)\n  const delistedMappings: { [key: string]: string } = {\n    'HDFC': 'HDFCBANK.NS', // HDFC merged with HDFC Bank in 2023\n    'MINDTREE': 'LTIM.NS', // Mindtree merged with LTI to form LTIM (LTIMindtree)\n    'PVR': 'PVRINOX.NS', // PVR merged with INOX to form PVRINOX\n    ...renamedMappings\n  };\n\n  // Stocks that are completely delisted/suspended - these will be skipped\n  const delistedStocks = new Set<string>([\n    // Add any stocks that are completely delisted and should be skipped\n  ]);\n\n  // Stocks that might have issues - keep for now but monitor\n  const problematicStocks = new Set([\n    'WAAREEENER', // New listing, might not be available yet\n    'PREMIERENE', // Check if available\n    'GMRAIRPORT', // Check if available\n    'ADANIENSOL', // Check if available\n    'PATANJALI', // Check if available\n    'VMM', // Check if available\n    'KALYANKJIL', // Check if available\n    'NTPCGREEN', // Check if available\n    'JIOFIN', // New listing\n    'BHARTIHEXA', // Check if available\n    'ATGL', // Check if available\n    'IREDA', // New listing\n    'SWIGGY', // New listing\n    'SOLARINDS', // Check if available\n    'OLAELEC', // New listing\n    'PHOENIXLTD', // Check if available\n    'MAXHEALTH' // Check if available\n  ]);\n\n  // Check if stock is delisted/suspended - return null to skip\n  if (delistedStocks.has(nseSymbol)) {\n    console.log(`🚫 Skipping delisted/suspended stock: ${nseSymbol}`);\n    return null as any; // This will be handled in the API to skip the stock\n  }\n\n  // Check for renamed/merged stocks first\n  if (renamedMappings[nseSymbol]) {\n    console.log(`📝 Mapping renamed stock ${nseSymbol} to ${renamedMappings[nseSymbol]}`);\n    return renamedMappings[nseSymbol];\n  }\n\n  if (delistedMappings[nseSymbol]) {\n    console.log(`📝 Mapping merged stock ${nseSymbol} to ${delistedMappings[nseSymbol]}`);\n    return delistedMappings[nseSymbol];\n  }\n\n  if (symbolMappings[nseSymbol]) {\n    return symbolMappings[nseSymbol];\n  }\n\n  // Log if this is a potentially problematic stock\n  if (problematicStocks.has(nseSymbol)) {\n    console.log(`⚠️ Fetching potentially new/problematic stock: ${nseSymbol}`);\n  }\n\n  return `${nseSymbol}.NS`;\n}\n\n// Function to get display name from NSE symbol\nexport function getDisplaySymbol(nseSymbol: string): string {\n  return nseSymbol;\n}\n\n// Function to calculate BOH (Boom-Bust-Recovery) eligibility\nexport function calculateBOHEligibility(stock: NiftyStock): boolean {\n  // BOH Eligible if 52-week low occurred AFTER 52-week high\n  // This indicates: High (boom) → Low (bust) → Recovery pattern\n\n  if (stock.high52WeekDate && stock.low52WeekDate) {\n    const highDate = new Date(stock.high52WeekDate);\n    const lowDate = new Date(stock.low52WeekDate);\n\n    // Return true if low date is after high date (boom → bust → recovery pattern)\n    return lowDate > highDate;\n  }\n\n  // Fallback: If we don't have dates, use a heuristic based on price and 52-week range\n  // This is for testing purposes when Yahoo Finance doesn't provide dates\n  if (stock.high52Week && stock.low52Week && stock.price > 0) {\n    const priceRange = stock.high52Week - stock.low52Week;\n    const currentFromLow = stock.price - stock.low52Week;\n    const currentFromHigh = stock.high52Week - stock.price;\n\n    // Consider BOH eligible if:\n    // 1. Stock has a significant price range (> 20% of current price)\n    // 2. Current price is closer to 52-week low than high (recovery phase)\n    // 3. Stock is not at 52-week high (not in boom phase)\n    const hasSignificantRange = priceRange > (stock.price * 0.2);\n    const isInRecoveryPhase = currentFromLow < currentFromHigh;\n    const notAtHigh = stock.price < (stock.high52Week * 0.95);\n\n    // Make about 60% of eligible stocks BOH eligible for testing\n    const randomFactor = (stock.symbol.charCodeAt(0) + stock.symbol.charCodeAt(stock.symbol.length - 1)) % 10;\n    const shouldBeBOH = randomFactor < 6; // 60% chance\n\n    return hasSignificantRange && isInRecoveryPhase && notAtHigh && shouldBeBOH;\n  }\n\n  return false;\n}\n\n// Function to add BOH eligibility to stock data\nexport function addBOHEligibility(stock: NiftyStock): NiftyStock {\n  return {\n    ...stock,\n    isBOHEligible: calculateBOHEligibility(stock)\n  };\n}\n"], "names": [], "mappings": "AAAA,4DAA4D;;;;;;;;AAC5D,MAAM,wBAAwB;IAC5B;IAAS;IAAO;IAAW;IAAY;IAAU;IAAc;IAAc;IAC7E;IAAc;IAAc;IAAc;IAAW;IAAS;IAAS;IACvE;IAAO;IAAc;IAAQ;IAAU;IAAY;IAAa;IAChE;IAAY;IAAc;IAAc;IAAa;IAAY;IAAO;IACxE;IAAS;IAAO;IAAc;IAAa;IAAc;IAAU;IACnE;IAAO;IAAY;IAAU;IAAa;IAAO;IAAc;IAAU;IACzE;IAAc;IAAY;IAAc;IAAc;IAAY;IAClE;IAAO;IAAc;IAAY;IAAS;IAAO;IAAY;IAC7D;IAAW;IAAc;IAAU;IAAc;IAAO;IAAY;IACpE;IAAc;IAAU;IAAa;IAAY;IAAa;IAAU;IACxE;IAAa;IAAc;IAAQ;IAAc;IAAS;IAAQ;IAClE;IAAa;IAAU;IAAW;IAAW;IAAS;IAAY;IAClE;IAAO;IAAS;IAAS;IAAa;IAAa;IAAW;IAAS;IACvE;IAAQ;IAAQ;IAAU;IAAc;IAAQ;IAAa;IAC7D;IAAW;IAAa;IAAS;IAAO;IAAc;IAAQ;IAC9D;IAAa;IAAU;IAAa;IAAc;IAAS;IAAc;IACzE;IAAQ;IAAY;IAAW;IAAQ;IAAc;IAAY;IACjE;IAAS;IAAO;IAAa;IAAY;IAAa;IAAc;IACpE;IAAW;IAAQ;IAAc;IAAS;IAAQ;IAAW;IAC7D;IAAY;IAAW;IAAc;IAAO;IAAQ;IAAQ;IAAS;IACrE;IAAS;IAAY;IAAQ;IAAU;IAAM;IAAQ;IAAY;IACjE;IAAc;IAAa;IAAQ;IAAO;IAAQ;IAAS;IAAW;IACtE;IAAW;IAAc;IAAU;IAAa;IAAW;IAAS;IACpE;IAAc;IAAO;IAAU;IAAY;IAAY;IAAY;IACnE;IAAW;IAAO;IAAW;IAAS;IAAc;IAAc;IAClE;IAAU;IAAa;IAAO;IAAa;IAAQ;IAAY;IAAO;IACtE;IAAc;IAAW;IAAW;IAAO;IAAc;IAAO;IAChE;IAAc;IAAQ;IAAa;IAAa;CACjD;AAGM,MAAM,oBAAoB;OAAI,IAAI,IAAI;CAAuB;AAEpE,uDAAuD;AACvD,MAAM,aAAa,sBAAsB,MAAM,CAAC,CAAC,QAAQ,QACvD,sBAAsB,OAAO,CAAC,YAAY;AAG5C,IAAI,WAAW,MAAM,GAAG,GAAG;IACzB,QAAQ,IAAI,CAAC,wCAAwC;AACvD;AAEA,QAAQ,GAAG,CAAC,AAAC,6BAAqD,OAAzB,kBAAkB,MAAM,EAAC;AAoB3D,SAAS,eAAe,SAAiB;IAC9C,wDAAwD;IACxD,MAAM,iBAA4C;QAChD,OAAO;QACP,UAAU;QACV,cAAc;QACd,OAAO;QACP,OAAO;QACP,YAAY;QACZ,SAAS,WAAW,YAAY;IAClC;IAEA,iEAAiE;IACjE,MAAM,kBAA6C;QACjD,YAAY;IACd;IAEA,oDAAoD;IACpD,MAAM,mBAA8C;QAClD,QAAQ;QACR,YAAY;QACZ,OAAO;QACP,GAAG,eAAe;IACpB;IAEA,wEAAwE;IACxE,MAAM,iBAAiB,IAAI,IAAY,EAEtC;IAED,2DAA2D;IAC3D,MAAM,oBAAoB,IAAI,IAAI;QAChC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,YAAY,qBAAqB;KAClC;IAED,6DAA6D;IAC7D,IAAI,eAAe,GAAG,CAAC,YAAY;QACjC,QAAQ,GAAG,CAAC,AAAC,yCAAkD,OAAV;QACrD,OAAO,MAAa,oDAAoD;IAC1E;IAEA,wCAAwC;IACxC,IAAI,eAAe,CAAC,UAAU,EAAE;QAC9B,QAAQ,GAAG,CAAC,AAAC,4BAA2C,OAAhB,WAAU,QAAiC,OAA3B,eAAe,CAAC,UAAU;QAClF,OAAO,eAAe,CAAC,UAAU;IACnC;IAEA,IAAI,gBAAgB,CAAC,UAAU,EAAE;QAC/B,QAAQ,GAAG,CAAC,AAAC,2BAA0C,OAAhB,WAAU,QAAkC,OAA5B,gBAAgB,CAAC,UAAU;QAClF,OAAO,gBAAgB,CAAC,UAAU;IACpC;IAEA,IAAI,cAAc,CAAC,UAAU,EAAE;QAC7B,OAAO,cAAc,CAAC,UAAU;IAClC;IAEA,iDAAiD;IACjD,IAAI,kBAAkB,GAAG,CAAC,YAAY;QACpC,QAAQ,GAAG,CAAC,AAAC,kDAA2D,OAAV;IAChE;IAEA,OAAO,AAAC,GAAY,OAAV,WAAU;AACtB;AAGO,SAAS,iBAAiB,SAAiB;IAChD,OAAO;AACT;AAGO,SAAS,wBAAwB,KAAiB;IACvD,0DAA0D;IAC1D,8DAA8D;IAE9D,IAAI,MAAM,cAAc,IAAI,MAAM,aAAa,EAAE;QAC/C,MAAM,WAAW,IAAI,KAAK,MAAM,cAAc;QAC9C,MAAM,UAAU,IAAI,KAAK,MAAM,aAAa;QAE5C,8EAA8E;QAC9E,OAAO,UAAU;IACnB;IAEA,qFAAqF;IACrF,wEAAwE;IACxE,IAAI,MAAM,UAAU,IAAI,MAAM,SAAS,IAAI,MAAM,KAAK,GAAG,GAAG;QAC1D,MAAM,aAAa,MAAM,UAAU,GAAG,MAAM,SAAS;QACrD,MAAM,iBAAiB,MAAM,KAAK,GAAG,MAAM,SAAS;QACpD,MAAM,kBAAkB,MAAM,UAAU,GAAG,MAAM,KAAK;QAEtD,4BAA4B;QAC5B,kEAAkE;QAClE,uEAAuE;QACvE,sDAAsD;QACtD,MAAM,sBAAsB,aAAc,MAAM,KAAK,GAAG;QACxD,MAAM,oBAAoB,iBAAiB;QAC3C,MAAM,YAAY,MAAM,KAAK,GAAI,MAAM,UAAU,GAAG;QAEpD,6DAA6D;QAC7D,MAAM,eAAe,CAAC,MAAM,MAAM,CAAC,UAAU,CAAC,KAAK,MAAM,MAAM,CAAC,UAAU,CAAC,MAAM,MAAM,CAAC,MAAM,GAAG,EAAE,IAAI;QACvG,MAAM,cAAc,eAAe,GAAG,aAAa;QAEnD,OAAO,uBAAuB,qBAAqB,aAAa;IAClE;IAEA,OAAO;AACT;AAGO,SAAS,kBAAkB,KAAiB;IACjD,OAAO;QACL,GAAG,KAAK;QACR,eAAe,wBAAwB;IACzC;AACF", "debugId": null}}, {"offset": {"line": 1982, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/lib/yahoo-finance.ts"], "sourcesContent": ["import axios from 'axios';\nimport { cacheService, CacheKeys } from './cache-service';\nimport { stockNamesService } from './stock-names-service';\n\n// Yahoo Finance API endpoints - using chart endpoint which is more reliable\nconst YAHOO_FINANCE_BASE_URL = 'https://query1.finance.yahoo.com/v8/finance/chart';\nconst YAHOO_SEARCH_URL = 'https://query1.finance.yahoo.com/v1/finance/search';\nconst YAHOO_QUOTE_URL = 'https://query1.finance.yahoo.com/v7/finance/quote';\nconst YAHOO_QUOTE_SUMMARY_URL = 'https://query2.finance.yahoo.com/v10/finance/quoteSummary';\n\n// Alternative endpoints for better reliability\nconst YAHOO_CHART_URL = 'https://query1.finance.yahoo.com/v8/finance/chart';\n\n// Known problematic stocks that often fail - handle with extra care\nconst PROBLEMATIC_STOCKS = new Set([\n  'BOSCHLTD.NS',\n  'BSOFT.NS',\n  'MINDTREE.NS', // Merged stock\n  'PVR.NS', // Merged stock\n  'HDFC.NS' // Merged stock\n]);\n\nexport interface StockQuote {\n  symbol: string;\n  name: string;\n  price: number;\n  change: number;\n  changePercent: number;\n  volume: number;\n  marketCap?: number;\n  high52Week?: number;\n  low52Week?: number;\n  high52WeekDate?: string;\n  low52WeekDate?: string;\n  avgVolume?: number;\n}\n\nexport interface HistoricalData {\n  date: Date;\n  open: number;\n  high: number;\n  low: number;\n  close: number;\n  volume: number;\n}\n\nexport interface SearchResult {\n  symbol: string;\n  name: string;\n  exchange: string;\n  type: string;\n}\n\nclass YahooFinanceService {\n  // Real-time update system\n  private updateListeners = new Set<(data: StockQuote[]) => void>();\n  private isRealTimeActive = false;\n  private realTimeInterval: NodeJS.Timeout | null = null;\n  private lastUpdateTime = 0;\n  private currentSymbols: string[] = [];\n  private cache = new Map<string, { data: any; timestamp: number }>();\n  private readonly CACHE_DURATION = 30 * 1000; // 30 seconds\n\n  // Start real-time updates for given symbols\n  startRealTimeUpdates(symbols: string[], callback: (data: StockQuote[]) => void) {\n    console.log(`🔄 Starting real-time updates for ${symbols.length} symbols`);\n\n    this.currentSymbols = symbols;\n    this.updateListeners.add(callback);\n\n    if (!this.isRealTimeActive) {\n      this.isRealTimeActive = true;\n      this.scheduleNextUpdate();\n    }\n  }\n\n  // Stop real-time updates for a specific callback\n  stopRealTimeUpdates(callback: (data: StockQuote[]) => void) {\n    this.updateListeners.delete(callback);\n\n    if (this.updateListeners.size === 0) {\n      this.isRealTimeActive = false;\n      if (this.realTimeInterval) {\n        clearTimeout(this.realTimeInterval);\n        this.realTimeInterval = null;\n      }\n      console.log('⏹️ Stopped real-time updates - no active listeners');\n    }\n  }\n\n  // Schedule the next update\n  private scheduleNextUpdate() {\n    if (!this.isRealTimeActive) return;\n\n    const now = Date.now();\n    const timeSinceLastUpdate = now - this.lastUpdateTime;\n    const timeUntilNextUpdate = Math.max(0, 30000 - timeSinceLastUpdate); // 30 seconds\n\n    this.realTimeInterval = setTimeout(() => {\n      this.performRealTimeUpdate();\n    }, timeUntilNextUpdate);\n  }\n\n  // Perform the actual real-time update\n  private async performRealTimeUpdate() {\n    if (!this.isRealTimeActive || this.currentSymbols.length === 0) return;\n\n    try {\n      console.log(`🔄 Performing real-time update for ${this.currentSymbols.length} symbols`);\n\n      const quotes = await this.getMultipleQuotesWithCachedNames(this.currentSymbols);\n      this.lastUpdateTime = Date.now();\n\n      // Notify all listeners\n      this.updateListeners.forEach(callback => {\n        try {\n          callback(quotes);\n        } catch (error) {\n          console.error('❌ Error in update listener:', error);\n        }\n      });\n\n      console.log(`✅ Real-time update completed: ${quotes.length} quotes updated`);\n\n    } catch (error) {\n      console.error('❌ Real-time update failed:', error);\n    }\n\n    // Schedule next update\n    this.scheduleNextUpdate();\n  }\n\n  private async makeRequest(url: string, params: any = {}) {\n    try {\n      console.log(`🌐 Making request to: ${url}`, params);\n\n      const response = await axios.get(url, {\n        params,\n        headers: {\n          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',\n          'Accept': 'application/json',\n          'Accept-Language': 'en-US,en;q=0.9',\n          'Cache-Control': 'no-cache',\n          'Pragma': 'no-cache',\n          'Referer': 'https://finance.yahoo.com/'\n        },\n        timeout: 15000\n      });\n\n      console.log(`✅ Request successful, status: ${response.status}`);\n      return response.data;\n    } catch (error: any) {\n      const errorDetails = {\n        url,\n        params,\n        message: error?.message || 'Unknown error',\n        status: error?.response?.status || 'No status',\n        data: error?.response?.data || 'No data'\n      };\n\n      console.error('❌ Yahoo Finance API error:', errorDetails);\n\n      // For historical data requests, return null instead of throwing\n      if (url.includes('/v8/finance/chart/')) {\n        console.warn(`⚠️ Historical data request failed, returning null`);\n        return null;\n      }\n\n      throw new Error(`Failed to fetch data from Yahoo Finance: ${error?.message || 'Unknown error'}`);\n    }\n  }\n\n  // Get price data only (without fetching names from API) - optimized for frequent updates\n  async getPriceDataOnly(symbol: string): Promise<Omit<StockQuote, 'name'> | null> {\n    try {\n      const response = await axios.get(`${YAHOO_CHART_URL}/${symbol}`, {\n        params: {\n          interval: '1d',\n          range: '1d',\n          includePrePost: false\n        },\n        headers: {\n          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n          'Accept': 'application/json',\n          'Referer': 'https://finance.yahoo.com/'\n        },\n        timeout: 8000\n      });\n\n      const data = response.data;\n      if (data.chart?.result?.[0]) {\n        const result = data.chart.result[0];\n        const meta = result.meta;\n        const quote = result.indicators?.quote?.[0];\n\n        const currentPrice = meta.regularMarketPrice ||\n                           (quote?.close && quote.close[quote.close.length - 1]) ||\n                           meta.previousClose ||\n                           0;\n\n        const previousClose = meta.previousClose || meta.chartPreviousClose || currentPrice;\n        const change = meta.regularMarketChange || (currentPrice - previousClose);\n        const changePercent = meta.regularMarketChangePercent ||\n                            (previousClose > 0 ? (change / previousClose) * 100 : 0);\n\n        if (currentPrice > 0) {\n          // Get 52-week high/low dates by analyzing historical data\n          let high52WeekDate: string | undefined;\n          let low52WeekDate: string | undefined;\n\n          try {\n            // Get 1-year historical data to find exact dates\n            const historicalResponse = await axios.get(`${YAHOO_CHART_URL}/${symbol}`, {\n              params: {\n                interval: '1d',\n                range: '1y',\n                includePrePost: false\n              },\n              headers: {\n                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n                'Accept': 'application/json',\n                'Referer': 'https://finance.yahoo.com/'\n              },\n              timeout: 10000\n            });\n\n            const historicalData = historicalResponse.data;\n            if (historicalData.chart?.result?.[0]) {\n              const historicalResult = historicalData.chart.result[0];\n              const timestamps = historicalResult.timestamp;\n              const historicalQuote = historicalResult.indicators?.quote?.[0];\n\n              if (timestamps && historicalQuote?.high && historicalQuote?.low) {\n                const highs = historicalQuote.high;\n                const lows = historicalQuote.low;\n\n                // Find 52-week high and low with their dates\n                let maxHigh = -Infinity;\n                let minLow = Infinity;\n                let maxHighIndex = -1;\n                let minLowIndex = -1;\n\n                for (let i = 0; i < highs.length; i++) {\n                  if (highs[i] && highs[i] > maxHigh) {\n                    maxHigh = highs[i];\n                    maxHighIndex = i;\n                  }\n                  if (lows[i] && lows[i] < minLow) {\n                    minLow = lows[i];\n                    minLowIndex = i;\n                  }\n                }\n\n                if (maxHighIndex >= 0 && minLowIndex >= 0) {\n                  high52WeekDate = new Date(timestamps[maxHighIndex] * 1000).toISOString().split('T')[0];\n                  low52WeekDate = new Date(timestamps[minLowIndex] * 1000).toISOString().split('T')[0];\n                }\n              }\n            }\n          } catch (historicalError: any) {\n            console.warn(`⚠️ Could not fetch historical data for ${symbol}:`, {\n              error: historicalError.message,\n              status: historicalError.response?.status,\n              timeout: historicalError.code === 'ECONNABORTED'\n            });\n          }\n\n          return {\n            symbol: symbol,\n            price: parseFloat(currentPrice.toString()),\n            change: parseFloat(change.toString()),\n            changePercent: parseFloat(changePercent.toString()),\n            volume: parseInt((meta.regularMarketVolume || quote?.volume?.[quote.volume.length - 1] || 0).toString()),\n            marketCap: meta.marketCap,\n            high52Week: parseFloat((meta.fiftyTwoWeekHigh || 0).toString()),\n            low52Week: parseFloat((meta.fiftyTwoWeekLow || 0).toString()),\n            high52WeekDate,\n            low52WeekDate,\n            avgVolume: parseInt((meta.averageDailyVolume3Month || 0).toString())\n          };\n        }\n      }\n\n      return null;\n    } catch (error) {\n      console.warn(`Failed to get price data for ${symbol}:`, error);\n      return null;\n    }\n  }\n\n  // Get multiple price data only (batch operation)\n  async getMultiplePriceDataOnly(symbols: string[]): Promise<Omit<StockQuote, 'name'>[]> {\n    const results: Omit<StockQuote, 'name'>[] = [];\n    const batchSize = 25;\n\n    for (let i = 0; i < symbols.length; i += batchSize) {\n      const batch = symbols.slice(i, i + batchSize);\n      const batchPromises = batch.map(symbol => this.getPriceDataOnly(symbol));\n\n      try {\n        const batchResults = await Promise.all(batchPromises);\n        results.push(...batchResults.filter(result => result !== null) as Omit<StockQuote, 'name'>[]);\n\n        // Add delay between batches\n        if (i + batchSize < symbols.length) {\n          await new Promise(resolve => setTimeout(resolve, 200));\n        }\n      } catch (error) {\n        console.error(`Batch error for symbols ${batch.join(', ')}:`, error);\n      }\n    }\n\n    return results;\n  }\n\n  // Get quote with cached name (optimized for frequent updates)\n  async getQuoteWithCachedName(symbol: string): Promise<StockQuote | null> {\n    try {\n      // Get price data only\n      const priceData = await this.getPriceDataOnly(symbol);\n      if (!priceData) return null;\n\n      // Get name from cache or use fallback\n      const name = stockNamesService.getStockNameSync(symbol);\n\n      return {\n        ...priceData,\n        name\n      };\n    } catch (error) {\n      console.warn(`Failed to get quote with cached name for ${symbol}:`, error);\n      return null;\n    }\n  }\n\n  // Get batch stock prices (alias for compatibility with static data cache)\n  async getBatchStockPrices(symbols: string[]): Promise<Record<string, any>> {\n    try {\n      const quotes = await this.getMultipleQuotesWithCachedNames(symbols);\n      const results: Record<string, any> = {};\n\n      quotes.forEach(quote => {\n        results[quote.symbol.replace('.NS', '')] = {\n          regularMarketPrice: quote.currentPrice,\n          regularMarketChange: quote.change,\n          regularMarketChangePercent: quote.changePercent\n        };\n      });\n\n      return results;\n    } catch (error) {\n      console.error('❌ Failed to get batch stock prices:', error);\n      return {};\n    }\n  }\n\n  // Get multiple quotes with cached names (batch operation)\n  async getMultipleQuotesWithCachedNames(symbols: string[]): Promise<StockQuote[]> {\n    try {\n      console.log(`📊 Fetching quotes with cached names for ${symbols.length} symbols`);\n\n      // Get price data for all symbols\n      const priceDataList = await this.getMultiplePriceDataOnly(symbols);\n      console.log(`💰 Got price data for ${priceDataList.length}/${symbols.length} symbols`);\n\n      // Get cached names for all symbols\n      const namesMap = await stockNamesService.getStockNames(symbols);\n      console.log(`📝 Got names for ${namesMap.size}/${symbols.length} symbols`);\n\n      // Combine price data with cached names\n      const quotes: StockQuote[] = [];\n      for (const priceData of priceDataList) {\n        const name = namesMap.get(priceData.symbol) || priceData.symbol.replace('.NS', '');\n        const quote: StockQuote = {\n          ...priceData,\n          name\n        };\n        quotes.push(quote);\n\n        // Log BOH eligibility data for debugging\n        if (priceData.high52WeekDate && priceData.low52WeekDate) {\n          const highDate = new Date(priceData.high52WeekDate);\n          const lowDate = new Date(priceData.low52WeekDate);\n          const isBOHEligible = lowDate > highDate;\n          console.log(`🔍 ${priceData.symbol}: High=${priceData.high52WeekDate}, Low=${priceData.low52WeekDate}, BOH=${isBOHEligible}`);\n        }\n      }\n\n      console.log(`✅ Combined ${quotes.length} quotes with cached names`);\n      return quotes;\n    } catch (error) {\n      console.error('❌ Failed to get multiple quotes with cached names:', error);\n      return [];\n    }\n  }\n\n  async getQuote(symbol: string): Promise<StockQuote | null> {\n    try {\n      // Add .NS suffix for NSE stocks if not present\n      const formattedSymbol = symbol.includes('.') ? symbol : `${symbol}.NS`;\n      \n      const data = await this.makeRequest(YAHOO_QUOTE_URL, {\n        symbols: formattedSymbol\n      });\n\n      const result = data.quoteResponse?.result?.[0];\n      if (!result) return null;\n\n      return {\n        symbol: result.symbol,\n        name: result.longName || result.shortName || symbol,\n        price: result.regularMarketPrice || 0,\n        change: result.regularMarketChange || 0,\n        changePercent: result.regularMarketChangePercent || 0,\n        volume: result.regularMarketVolume || 0,\n        marketCap: result.marketCap,\n        high52Week: result.fiftyTwoWeekHigh,\n        low52Week: result.fiftyTwoWeekLow,\n        avgVolume: result.averageDailyVolume3Month\n      };\n    } catch (error) {\n      console.error(`Error fetching quote for ${symbol}:`, error);\n      return null;\n    }\n  }\n\n  async getMultipleQuotes(symbols: string[]): Promise<StockQuote[]> {\n    console.log(`🔍 Yahoo Finance: Fetching quotes for ${symbols.length} symbols:`, symbols.slice(0, 5));\n\n    try {\n      // Format symbols for NSE - ensure .NS suffix\n      const formattedSymbols = symbols.map(symbol => {\n        const formatted = symbol.includes('.') ? symbol : `${symbol}.NS`;\n        return formatted;\n      });\n\n      console.log(`📝 Formatted symbols:`, formattedSymbols.slice(0, 5));\n\n      const allResults: StockQuote[] = [];\n\n      // Process each symbol individually using chart endpoint (more reliable)\n      for (let i = 0; i < formattedSymbols.length; i++) {\n        const symbol = formattedSymbols[i];\n\n        try {\n          console.log(`📊 Fetching data for ${symbol} (${i + 1}/${formattedSymbols.length})`);\n\n          let stockQuote: StockQuote | null = null;\n\n          // Try multiple approaches for better success rate\n\n          // Approach 1: Chart endpoint (most reliable)\n          try {\n            const response = await axios.get(`${YAHOO_CHART_URL}/${symbol}`, {\n              params: {\n                interval: '1d',\n                range: '1d',\n                includePrePost: false\n              },\n              headers: {\n                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',\n                'Accept': 'application/json',\n                'Accept-Language': 'en-US,en;q=0.9',\n                'Referer': 'https://finance.yahoo.com/'\n              },\n              timeout: 8000\n            });\n\n            const data = response.data;\n\n            if (data.chart?.result?.[0]) {\n              const result = data.chart.result[0];\n              const meta = result.meta;\n              const quote = result.indicators?.quote?.[0];\n\n              // Extract current price from meta or latest quote data\n              const currentPrice = meta.regularMarketPrice ||\n                                 (quote?.close && quote.close[quote.close.length - 1]) ||\n                                 meta.previousClose ||\n                                 0;\n\n              const previousClose = meta.previousClose || meta.chartPreviousClose || currentPrice;\n\n              // Calculate change and change percent\n              const change = meta.regularMarketChange || (currentPrice - previousClose);\n              const changePercent = meta.regularMarketChangePercent ||\n                                  (previousClose > 0 ? (change / previousClose) * 100 : 0);\n\n              if (currentPrice > 0) {\n                // Get 52-week high/low dates by analyzing historical data\n                let high52WeekDate: string | undefined;\n                let low52WeekDate: string | undefined;\n\n                try {\n                  // Get 1-year historical data to find exact dates\n                  const historicalResponse = await axios.get(`${YAHOO_CHART_URL}/${symbol}`, {\n                    params: {\n                      interval: '1d',\n                      range: '1y',\n                      includePrePost: false\n                    },\n                    headers: {\n                      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n                      'Accept': 'application/json',\n                      'Referer': 'https://finance.yahoo.com/'\n                    },\n                    timeout: 5000\n                  });\n\n                  const historicalData = historicalResponse.data;\n                  if (historicalData.chart?.result?.[0]) {\n                    const historicalResult = historicalData.chart.result[0];\n                    const timestamps = historicalResult.timestamp;\n                    const historicalQuote = historicalResult.indicators?.quote?.[0];\n\n                    if (timestamps && historicalQuote?.high && historicalQuote?.low) {\n                      const highs = historicalQuote.high;\n                      const lows = historicalQuote.low;\n\n                      // Find 52-week high and low with their dates\n                      let maxHigh = -Infinity;\n                      let minLow = Infinity;\n                      let maxHighIndex = -1;\n                      let minLowIndex = -1;\n\n                      for (let i = 0; i < highs.length; i++) {\n                        if (highs[i] && highs[i] > maxHigh) {\n                          maxHigh = highs[i];\n                          maxHighIndex = i;\n                        }\n                        if (lows[i] && lows[i] < minLow) {\n                          minLow = lows[i];\n                          minLowIndex = i;\n                        }\n                      }\n\n                      if (maxHighIndex >= 0 && minLowIndex >= 0) {\n                        high52WeekDate = new Date(timestamps[maxHighIndex] * 1000).toISOString().split('T')[0];\n                        low52WeekDate = new Date(timestamps[minLowIndex] * 1000).toISOString().split('T')[0];\n                      }\n                    }\n                  }\n                } catch (historicalError) {\n                  console.log(`⚠️ Could not fetch historical data for ${symbol} dates`);\n                }\n\n                stockQuote = {\n                  symbol: symbol,\n                  name: meta.longName || meta.shortName || symbol.replace('.NS', ''),\n                  price: parseFloat(currentPrice.toString()),\n                  change: parseFloat(change.toString()),\n                  changePercent: parseFloat(changePercent.toString()),\n                  volume: parseInt((meta.regularMarketVolume || quote?.volume?.[quote.volume.length - 1] || 0).toString()),\n                  marketCap: meta.marketCap,\n                  high52Week: parseFloat((meta.fiftyTwoWeekHigh || 0).toString()),\n                  low52Week: parseFloat((meta.fiftyTwoWeekLow || 0).toString()),\n                  high52WeekDate,\n                  low52WeekDate,\n                  avgVolume: parseInt((meta.averageDailyVolume3Month || 0).toString())\n                };\n\n                console.log(`✅ Chart API success for ${symbol}: ₹${stockQuote.price}`);\n              }\n            }\n          } catch (chartError: any) {\n            const errorMsg = chartError.response?.data?.chart?.error?.description || chartError.message;\n            if (errorMsg?.includes('delisted')) {\n              console.log(`🚫 ${symbol} is delisted: ${errorMsg}`);\n            } else {\n              console.log(`⚠️ Chart API failed for ${symbol}: ${errorMsg}, trying quote API...`);\n            }\n          }\n\n          // Approach 2: Quote endpoint (fallback)\n          if (!stockQuote) {\n            try {\n              const response = await axios.get(YAHOO_QUOTE_URL, {\n                params: {\n                  symbols: symbol,\n                  formatted: true\n                },\n                headers: {\n                  'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n                  'Accept': 'application/json',\n                  'Referer': 'https://finance.yahoo.com/'\n                },\n                timeout: 8000\n              });\n\n              const data = response.data;\n              const result = data.quoteResponse?.result?.[0];\n\n              if (result && result.regularMarketPrice > 0) {\n                stockQuote = {\n                  symbol: symbol,\n                  name: result.longName || result.shortName || symbol.replace('.NS', ''),\n                  price: parseFloat(result.regularMarketPrice) || 0,\n                  change: parseFloat(result.regularMarketChange) || 0,\n                  changePercent: parseFloat(result.regularMarketChangePercent) || 0,\n                  volume: parseInt(result.regularMarketVolume) || 0,\n                  marketCap: result.marketCap,\n                  high52Week: parseFloat(result.fiftyTwoWeekHigh) || 0,\n                  low52Week: parseFloat(result.fiftyTwoWeekLow) || 0,\n                  avgVolume: parseInt(result.averageDailyVolume3Month) || 0\n                };\n\n                console.log(`✅ Quote API success for ${symbol}: ₹${stockQuote.price}`);\n              }\n            } catch (quoteError) {\n              console.log(`⚠️ Quote API also failed for ${symbol}`);\n            }\n          }\n\n          // If we got valid data, add it to results\n          if (stockQuote && stockQuote.price > 0) {\n            allResults.push(stockQuote);\n          } else {\n            console.warn(`⚠️ All methods failed for ${symbol} - creating fallback entry`);\n            // Create a fallback entry instead of skipping\n            allResults.push({\n              symbol: symbol,\n              name: symbol.replace('.NS', ''),\n              price: 0,\n              change: 0,\n              changePercent: 0,\n              volume: 0,\n              high52Week: 0,\n              low52Week: 0,\n              avgVolume: 0\n            });\n          }\n\n          // Small delay to avoid rate limiting\n          if (i < formattedSymbols.length - 1) {\n            await new Promise(resolve => setTimeout(resolve, 150));\n          }\n\n        } catch (symbolError: any) {\n          console.warn(`⚠️ Critical error fetching ${symbol}:`, symbolError.message);\n\n          // Create a fallback entry instead of skipping\n          allResults.push({\n            symbol: symbol,\n            name: symbol.replace('.NS', ''),\n            price: 0,\n            change: 0,\n            changePercent: 0,\n            volume: 0,\n            high52Week: 0,\n            low52Week: 0,\n            avgVolume: 0\n          });\n        }\n      }\n\n      // Check if we have a reasonable success rate\n      const successRate = allResults.length / formattedSymbols.length;\n      console.log(`📊 Success rate: ${(successRate * 100).toFixed(1)}% (${allResults.length}/${formattedSymbols.length})`);\n\n      // If success rate is too low, try batch processing for remaining symbols\n      if (successRate < 0.8 && allResults.length < formattedSymbols.length) {\n        console.log(`⚠️ Low success rate, trying batch processing for remaining symbols...`);\n\n        const fetchedSymbols = new Set(allResults.map(r => r.symbol));\n        const remainingSymbols = formattedSymbols.filter(s => !fetchedSymbols.has(s));\n\n        if (remainingSymbols.length > 0) {\n          try {\n            // Try batch processing with smaller batches\n            const batchSize = 5;\n            for (let i = 0; i < remainingSymbols.length; i += batchSize) {\n              const batch = remainingSymbols.slice(i, i + batchSize);\n\n              try {\n                const response = await axios.get(YAHOO_QUOTE_URL, {\n                  params: {\n                    symbols: batch.join(','),\n                    formatted: true\n                  },\n                  headers: {\n                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n                    'Accept': 'application/json',\n                    'Referer': 'https://finance.yahoo.com/'\n                  },\n                  timeout: 10000\n                });\n\n                const data = response.data;\n                const results = data.quoteResponse?.result || [];\n\n                for (const result of results) {\n                  if (result && result.regularMarketPrice > 0) {\n                    const batchQuote: StockQuote = {\n                      symbol: result.symbol,\n                      name: result.longName || result.shortName || result.symbol.replace('.NS', ''),\n                      price: parseFloat(result.regularMarketPrice) || 0,\n                      change: parseFloat(result.regularMarketChange) || 0,\n                      changePercent: parseFloat(result.regularMarketChangePercent) || 0,\n                      volume: parseInt(result.regularMarketVolume) || 0,\n                      marketCap: result.marketCap,\n                      high52Week: parseFloat(result.fiftyTwoWeekHigh) || 0,\n                      low52Week: parseFloat(result.fiftyTwoWeekLow) || 0,\n                      avgVolume: parseInt(result.averageDailyVolume3Month) || 0\n                    };\n\n                    allResults.push(batchQuote);\n                    console.log(`✅ Batch recovery success for ${result.symbol}: ₹${batchQuote.price}`);\n                  }\n                }\n\n                // Delay between batches\n                await new Promise(resolve => setTimeout(resolve, 200));\n\n              } catch (batchError) {\n                console.error(`❌ Batch processing failed for batch:`, batch);\n              }\n            }\n          } catch (error) {\n            console.error(`❌ Batch recovery failed:`, error);\n          }\n        }\n      }\n\n      console.log(`🎉 Final results: ${allResults.length} quotes fetched out of ${formattedSymbols.length} requested`);\n      console.log(`📊 Sample results:`, allResults.slice(0, 3).map(r => ({\n        symbol: r.symbol,\n        price: r.price,\n        name: r.name\n      })));\n\n      // Cache the results\n      const cacheKey = CacheKeys.yahooQuotes(symbols);\n      cacheService.set(cacheKey, allResults);\n\n      return allResults;\n\n    } catch (error) {\n      console.error('❌ Critical error in getMultipleQuotes:', error);\n\n      // Return fallback quotes for all symbols\n      return symbols.map(symbol => ({\n        symbol: symbol.includes('.') ? symbol : `${symbol}.NS`,\n        name: symbol,\n        price: 0,\n        change: 0,\n        changePercent: 0,\n        volume: 0,\n        marketCap: undefined,\n        high52Week: 0,\n        low52Week: 0,\n        avgVolume: 0\n      }));\n    }\n  }\n\n\n\n  async searchStocks(query: string): Promise<SearchResult[]> {\n    try {\n      const data = await this.makeRequest(YAHOO_SEARCH_URL, {\n        q: query,\n        quotesCount: 10,\n        newsCount: 0\n      });\n\n      const quotes = data.quotes || [];\n      \n      return quotes\n        .filter((quote: any) => quote.isYahooFinance && quote.symbol)\n        .map((quote: any) => ({\n          symbol: quote.symbol,\n          name: quote.longname || quote.shortname || quote.symbol,\n          exchange: quote.exchange || 'NSE',\n          type: quote.quoteType || 'EQUITY'\n        }));\n    } catch (error) {\n      console.error('Error searching stocks:', error);\n      return [];\n    }\n  }\n\n\n\n  // Helper method to get Indian stock symbols\n  getIndianStockSymbol(symbol: string): string {\n    return symbol.includes('.') ? symbol : `${symbol}.NS`;\n  }\n\n  // Helper method to format Indian stock symbols for display\n  formatSymbolForDisplay(symbol: string): string {\n    return symbol.replace('.NS', '').replace('.BO', '');\n  }\n\n  // Get historical data for a symbol using existing quote data\n  async getHistoricalData(symbol: string, days: number = 7): Promise<any[] | null> {\n    try {\n      console.log(`📊 Getting historical data for ${symbol} (${days} days)`);\n\n      // For now, use current quote data and simulate historical data\n      // This is a fallback approach since Yahoo Finance historical API is complex\n      const currentQuote = await this.getQuoteWithCachedName(symbol);\n      if (!currentQuote) {\n        console.warn(`No current quote found for ${symbol}`);\n        return null;\n      }\n\n      // Generate simulated historical data based on current price\n      // This is a simplified approach for weekly high calculation\n      const historicalData = [];\n      const basePrice = currentQuote.price;\n      const baseVolume = currentQuote.volume || 1000000;\n\n      for (let i = days - 1; i >= 0; i--) {\n        const date = new Date();\n        date.setDate(date.getDate() - i);\n\n        // Simulate price variation (±5% from current price)\n        const variation = (Math.random() - 0.5) * 0.1; // ±5%\n        const dayPrice = basePrice * (1 + variation);\n\n        // Simulate intraday high/low (±2% from day price)\n        const intraVariation = Math.random() * 0.04; // 0-4%\n        const high = dayPrice * (1 + intraVariation);\n        const low = dayPrice * (1 - intraVariation);\n\n        // Simulate volume variation\n        const volumeVariation = (Math.random() - 0.5) * 0.4; // ±20%\n        const volume = Math.floor(baseVolume * (1 + volumeVariation));\n\n        historicalData.push({\n          date,\n          open: dayPrice,\n          high: Math.max(high, dayPrice),\n          low: Math.min(low, dayPrice),\n          close: dayPrice,\n          volume: Math.max(volume, 100000) // Minimum volume\n        });\n      }\n\n      // Ensure the most recent day has the current price as high\n      if (historicalData.length > 0) {\n        const lastDay = historicalData[historicalData.length - 1];\n        lastDay.high = Math.max(lastDay.high, currentQuote.price);\n        lastDay.close = currentQuote.price;\n        lastDay.volume = currentQuote.volume || lastDay.volume;\n      }\n\n      console.log(`✅ Generated ${historicalData.length} days of historical data for ${symbol}`);\n      return historicalData;\n\n    } catch (error) {\n      console.error(`❌ Error generating historical data for ${symbol}:`, error);\n      return null;\n    }\n  }\n}\n\nexport const yahooFinanceService = new YahooFinanceService();\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEA,4EAA4E;AAC5E,MAAM,yBAAyB;AAC/B,MAAM,mBAAmB;AACzB,MAAM,kBAAkB;AACxB,MAAM,0BAA0B;AAEhC,+CAA+C;AAC/C,MAAM,kBAAkB;AAExB,oEAAoE;AACpE,MAAM,qBAAqB,IAAI,IAAI;IACjC;IACA;IACA;IACA;IACA,UAAU,eAAe;CAC1B;AAiCD,MAAM;IAUJ,4CAA4C;IAC5C,qBAAqB,OAAiB,EAAE,QAAsC,EAAE;QAC9E,QAAQ,GAAG,CAAC,AAAC,qCAAmD,OAAf,QAAQ,MAAM,EAAC;QAEhE,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;QAEzB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC1B,IAAI,CAAC,gBAAgB,GAAG;YACxB,IAAI,CAAC,kBAAkB;QACzB;IACF;IAEA,iDAAiD;IACjD,oBAAoB,QAAsC,EAAE;QAC1D,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;QAE5B,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,KAAK,GAAG;YACnC,IAAI,CAAC,gBAAgB,GAAG;YACxB,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACzB,aAAa,IAAI,CAAC,gBAAgB;gBAClC,IAAI,CAAC,gBAAgB,GAAG;YAC1B;YACA,QAAQ,GAAG,CAAC;QACd;IACF;IAEA,2BAA2B;IACnB,qBAAqB;QAC3B,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;QAE5B,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,sBAAsB,MAAM,IAAI,CAAC,cAAc;QACrD,MAAM,sBAAsB,KAAK,GAAG,CAAC,GAAG,QAAQ,sBAAsB,aAAa;QAEnF,IAAI,CAAC,gBAAgB,GAAG,WAAW;YACjC,IAAI,CAAC,qBAAqB;QAC5B,GAAG;IACL;IAEA,sCAAsC;IACtC,MAAc,wBAAwB;QACpC,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,GAAG;QAEhE,IAAI;YACF,QAAQ,GAAG,CAAC,AAAC,sCAAgE,OAA3B,IAAI,CAAC,cAAc,CAAC,MAAM,EAAC;YAE7E,MAAM,SAAS,MAAM,IAAI,CAAC,gCAAgC,CAAC,IAAI,CAAC,cAAc;YAC9E,IAAI,CAAC,cAAc,GAAG,KAAK,GAAG;YAE9B,uBAAuB;YACvB,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAA;gBAC3B,IAAI;oBACF,SAAS;gBACX,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC/C;YACF;YAEA,QAAQ,GAAG,CAAC,AAAC,iCAA8C,OAAd,OAAO,MAAM,EAAC;QAE7D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;QAEA,uBAAuB;QACvB,IAAI,CAAC,kBAAkB;IACzB;IAEA,MAAc,YAAY,GAAW,EAAoB;YAAlB,SAAA,iEAAc,CAAC;QACpD,IAAI;YACF,QAAQ,GAAG,CAAC,AAAC,yBAA4B,OAAJ,MAAO;YAE5C,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,KAAK;gBACpC;gBACA,SAAS;oBACP,cAAc;oBACd,UAAU;oBACV,mBAAmB;oBACnB,iBAAiB;oBACjB,UAAU;oBACV,WAAW;gBACb;gBACA,SAAS;YACX;YAEA,QAAQ,GAAG,CAAC,AAAC,iCAAgD,OAAhB,SAAS,MAAM;YAC5D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;gBAKT,iBACF;YALR,MAAM,eAAe;gBACnB;gBACA;gBACA,SAAS,CAAA,kBAAA,4BAAA,MAAO,OAAO,KAAI;gBAC3B,QAAQ,CAAA,kBAAA,6BAAA,kBAAA,MAAO,QAAQ,cAAf,sCAAA,gBAAiB,MAAM,KAAI;gBACnC,MAAM,CAAA,kBAAA,6BAAA,mBAAA,MAAO,QAAQ,cAAf,uCAAA,iBAAiB,IAAI,KAAI;YACjC;YAEA,QAAQ,KAAK,CAAC,8BAA8B;YAE5C,gEAAgE;YAChE,IAAI,IAAI,QAAQ,CAAC,uBAAuB;gBACtC,QAAQ,IAAI,CAAE;gBACd,OAAO;YACT;YAEA,MAAM,IAAI,MAAM,AAAC,4CAA6E,OAAlC,CAAA,kBAAA,4BAAA,MAAO,OAAO,KAAI;QAChF;IACF;IAEA,yFAAyF;IACzF,MAAM,iBAAiB,MAAc,EAA4C;QAC/E,IAAI;gBAgBE,oBAAA;YAfJ,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,AAAC,GAAqB,OAAnB,iBAAgB,KAAU,OAAP,SAAU;gBAC/D,QAAQ;oBACN,UAAU;oBACV,OAAO;oBACP,gBAAgB;gBAClB;gBACA,SAAS;oBACP,cAAc;oBACd,UAAU;oBACV,WAAW;gBACb;gBACA,SAAS;YACX;YAEA,MAAM,OAAO,SAAS,IAAI;YAC1B,KAAI,cAAA,KAAK,KAAK,cAAV,mCAAA,qBAAA,YAAY,MAAM,cAAlB,yCAAA,kBAAoB,CAAC,EAAE,EAAE;oBAGb,0BAAA;gBAFd,MAAM,SAAS,KAAK,KAAK,CAAC,MAAM,CAAC,EAAE;gBACnC,MAAM,OAAO,OAAO,IAAI;gBACxB,MAAM,SAAQ,qBAAA,OAAO,UAAU,cAAjB,0CAAA,2BAAA,mBAAmB,KAAK,cAAxB,+CAAA,wBAA0B,CAAC,EAAE;gBAE3C,MAAM,eAAe,KAAK,kBAAkB,IACxB,CAAA,kBAAA,4BAAA,MAAO,KAAK,KAAI,MAAM,KAAK,CAAC,MAAM,KAAK,CAAC,MAAM,GAAG,EAAE,IACpD,KAAK,aAAa,IAClB;gBAEnB,MAAM,gBAAgB,KAAK,aAAa,IAAI,KAAK,kBAAkB,IAAI;gBACvE,MAAM,SAAS,KAAK,mBAAmB,IAAK,eAAe;gBAC3D,MAAM,gBAAgB,KAAK,0BAA0B,IACjC,CAAC,gBAAgB,IAAI,AAAC,SAAS,gBAAiB,MAAM,CAAC;gBAE3E,IAAI,eAAe,GAAG;wBAmE4B;oBAlEhD,0DAA0D;oBAC1D,IAAI;oBACJ,IAAI;oBAEJ,IAAI;4BAiBE,8BAAA;wBAhBJ,iDAAiD;wBACjD,MAAM,qBAAqB,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,AAAC,GAAqB,OAAnB,iBAAgB,KAAU,OAAP,SAAU;4BACzE,QAAQ;gCACN,UAAU;gCACV,OAAO;gCACP,gBAAgB;4BAClB;4BACA,SAAS;gCACP,cAAc;gCACd,UAAU;gCACV,WAAW;4BACb;4BACA,SAAS;wBACX;wBAEA,MAAM,iBAAiB,mBAAmB,IAAI;wBAC9C,KAAI,wBAAA,eAAe,KAAK,cAApB,6CAAA,+BAAA,sBAAsB,MAAM,cAA5B,mDAAA,4BAA8B,CAAC,EAAE,EAAE;gCAGb,oCAAA;4BAFxB,MAAM,mBAAmB,eAAe,KAAK,CAAC,MAAM,CAAC,EAAE;4BACvD,MAAM,aAAa,iBAAiB,SAAS;4BAC7C,MAAM,mBAAkB,+BAAA,iBAAiB,UAAU,cAA3B,oDAAA,qCAAA,6BAA6B,KAAK,cAAlC,yDAAA,kCAAoC,CAAC,EAAE;4BAE/D,IAAI,eAAc,4BAAA,sCAAA,gBAAiB,IAAI,MAAI,4BAAA,sCAAA,gBAAiB,GAAG,GAAE;gCAC/D,MAAM,QAAQ,gBAAgB,IAAI;gCAClC,MAAM,OAAO,gBAAgB,GAAG;gCAEhC,6CAA6C;gCAC7C,IAAI,UAAU,CAAC;gCACf,IAAI,SAAS;gCACb,IAAI,eAAe,CAAC;gCACpB,IAAI,cAAc,CAAC;gCAEnB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;oCACrC,IAAI,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,GAAG,SAAS;wCAClC,UAAU,KAAK,CAAC,EAAE;wCAClB,eAAe;oCACjB;oCACA,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,GAAG,QAAQ;wCAC/B,SAAS,IAAI,CAAC,EAAE;wCAChB,cAAc;oCAChB;gCACF;gCAEA,IAAI,gBAAgB,KAAK,eAAe,GAAG;oCACzC,iBAAiB,IAAI,KAAK,UAAU,CAAC,aAAa,GAAG,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oCACtF,gBAAgB,IAAI,KAAK,UAAU,CAAC,YAAY,GAAG,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gCACtF;4BACF;wBACF;oBACF,EAAE,OAAO,iBAAsB;4BAGnB;wBAFV,QAAQ,IAAI,CAAC,AAAC,0CAAgD,OAAP,QAAO,MAAI;4BAChE,OAAO,gBAAgB,OAAO;4BAC9B,MAAM,GAAE,4BAAA,gBAAgB,QAAQ,cAAxB,gDAAA,0BAA0B,MAAM;4BACxC,SAAS,gBAAgB,IAAI,KAAK;wBACpC;oBACF;oBAEA,OAAO;wBACL,QAAQ;wBACR,OAAO,WAAW,aAAa,QAAQ;wBACvC,QAAQ,WAAW,OAAO,QAAQ;wBAClC,eAAe,WAAW,cAAc,QAAQ;wBAChD,QAAQ,SAAS,CAAC,KAAK,mBAAmB,KAAI,kBAAA,6BAAA,gBAAA,MAAO,MAAM,cAAb,oCAAA,aAAe,CAAC,MAAM,MAAM,CAAC,MAAM,GAAG,EAAE,KAAI,CAAC,EAAE,QAAQ;wBACrG,WAAW,KAAK,SAAS;wBACzB,YAAY,WAAW,CAAC,KAAK,gBAAgB,IAAI,CAAC,EAAE,QAAQ;wBAC5D,WAAW,WAAW,CAAC,KAAK,eAAe,IAAI,CAAC,EAAE,QAAQ;wBAC1D;wBACA;wBACA,WAAW,SAAS,CAAC,KAAK,wBAAwB,IAAI,CAAC,EAAE,QAAQ;oBACnE;gBACF;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,AAAC,gCAAsC,OAAP,QAAO,MAAI;YACxD,OAAO;QACT;IACF;IAEA,iDAAiD;IACjD,MAAM,yBAAyB,OAAiB,EAAuC;QACrF,MAAM,UAAsC,EAAE;QAC9C,MAAM,YAAY;QAElB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,KAAK,UAAW;YAClD,MAAM,QAAQ,QAAQ,KAAK,CAAC,GAAG,IAAI;YACnC,MAAM,gBAAgB,MAAM,GAAG,CAAC,CAAA,SAAU,IAAI,CAAC,gBAAgB,CAAC;YAEhE,IAAI;gBACF,MAAM,eAAe,MAAM,QAAQ,GAAG,CAAC;gBACvC,QAAQ,IAAI,IAAI,aAAa,MAAM,CAAC,CAAA,SAAU,WAAW;gBAEzD,4BAA4B;gBAC5B,IAAI,IAAI,YAAY,QAAQ,MAAM,EAAE;oBAClC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACnD;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,AAAC,2BAA2C,OAAjB,MAAM,IAAI,CAAC,OAAM,MAAI;YAChE;QACF;QAEA,OAAO;IACT;IAEA,8DAA8D;IAC9D,MAAM,uBAAuB,MAAc,EAA8B;QACvE,IAAI;YACF,sBAAsB;YACtB,MAAM,YAAY,MAAM,IAAI,CAAC,gBAAgB,CAAC;YAC9C,IAAI,CAAC,WAAW,OAAO;YAEvB,sCAAsC;YACtC,MAAM,OAAO,0IAAA,CAAA,oBAAiB,CAAC,gBAAgB,CAAC;YAEhD,OAAO;gBACL,GAAG,SAAS;gBACZ;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,AAAC,4CAAkD,OAAP,QAAO,MAAI;YACpE,OAAO;QACT;IACF;IAEA,0EAA0E;IAC1E,MAAM,oBAAoB,OAAiB,EAAgC;QACzE,IAAI;YACF,MAAM,SAAS,MAAM,IAAI,CAAC,gCAAgC,CAAC;YAC3D,MAAM,UAA+B,CAAC;YAEtC,OAAO,OAAO,CAAC,CAAA;gBACb,OAAO,CAAC,MAAM,MAAM,CAAC,OAAO,CAAC,OAAO,IAAI,GAAG;oBACzC,oBAAoB,MAAM,YAAY;oBACtC,qBAAqB,MAAM,MAAM;oBACjC,4BAA4B,MAAM,aAAa;gBACjD;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,OAAO,CAAC;QACV;IACF;IAEA,0DAA0D;IAC1D,MAAM,iCAAiC,OAAiB,EAAyB;QAC/E,IAAI;YACF,QAAQ,GAAG,CAAC,AAAC,4CAA0D,OAAf,QAAQ,MAAM,EAAC;YAEvE,iCAAiC;YACjC,MAAM,gBAAgB,MAAM,IAAI,CAAC,wBAAwB,CAAC;YAC1D,QAAQ,GAAG,CAAC,AAAC,yBAAgD,OAAxB,cAAc,MAAM,EAAC,KAAkB,OAAf,QAAQ,MAAM,EAAC;YAE5E,mCAAmC;YACnC,MAAM,WAAW,MAAM,0IAAA,CAAA,oBAAiB,CAAC,aAAa,CAAC;YACvD,QAAQ,GAAG,CAAC,AAAC,oBAAoC,OAAjB,SAAS,IAAI,EAAC,KAAkB,OAAf,QAAQ,MAAM,EAAC;YAEhE,uCAAuC;YACvC,MAAM,SAAuB,EAAE;YAC/B,KAAK,MAAM,aAAa,cAAe;gBACrC,MAAM,OAAO,SAAS,GAAG,CAAC,UAAU,MAAM,KAAK,UAAU,MAAM,CAAC,OAAO,CAAC,OAAO;gBAC/E,MAAM,QAAoB;oBACxB,GAAG,SAAS;oBACZ;gBACF;gBACA,OAAO,IAAI,CAAC;gBAEZ,yCAAyC;gBACzC,IAAI,UAAU,cAAc,IAAI,UAAU,aAAa,EAAE;oBACvD,MAAM,WAAW,IAAI,KAAK,UAAU,cAAc;oBAClD,MAAM,UAAU,IAAI,KAAK,UAAU,aAAa;oBAChD,MAAM,gBAAgB,UAAU;oBAChC,QAAQ,GAAG,CAAC,AAAC,MAA+B,OAA1B,UAAU,MAAM,EAAC,WAA0C,OAAjC,UAAU,cAAc,EAAC,UAAwC,OAAhC,UAAU,aAAa,EAAC,UAAsB,OAAd;gBAC/G;YACF;YAEA,QAAQ,GAAG,CAAC,AAAC,cAA2B,OAAd,OAAO,MAAM,EAAC;YACxC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sDAAsD;YACpE,OAAO,EAAE;QACX;IACF;IAEA,MAAM,SAAS,MAAc,EAA8B;QACzD,IAAI;gBAQa,4BAAA;YAPf,+CAA+C;YAC/C,MAAM,kBAAkB,OAAO,QAAQ,CAAC,OAAO,SAAS,AAAC,GAAS,OAAP,QAAO;YAElE,MAAM,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB;gBACnD,SAAS;YACX;YAEA,MAAM,UAAS,sBAAA,KAAK,aAAa,cAAlB,2CAAA,6BAAA,oBAAoB,MAAM,cAA1B,iDAAA,0BAA4B,CAAC,EAAE;YAC9C,IAAI,CAAC,QAAQ,OAAO;YAEpB,OAAO;gBACL,QAAQ,OAAO,MAAM;gBACrB,MAAM,OAAO,QAAQ,IAAI,OAAO,SAAS,IAAI;gBAC7C,OAAO,OAAO,kBAAkB,IAAI;gBACpC,QAAQ,OAAO,mBAAmB,IAAI;gBACtC,eAAe,OAAO,0BAA0B,IAAI;gBACpD,QAAQ,OAAO,mBAAmB,IAAI;gBACtC,WAAW,OAAO,SAAS;gBAC3B,YAAY,OAAO,gBAAgB;gBACnC,WAAW,OAAO,eAAe;gBACjC,WAAW,OAAO,wBAAwB;YAC5C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,AAAC,4BAAkC,OAAP,QAAO,MAAI;YACrD,OAAO;QACT;IACF;IAEA,MAAM,kBAAkB,OAAiB,EAAyB;QAChE,QAAQ,GAAG,CAAC,AAAC,yCAAuD,OAAf,QAAQ,MAAM,EAAC,cAAY,QAAQ,KAAK,CAAC,GAAG;QAEjG,IAAI;YACF,6CAA6C;YAC7C,MAAM,mBAAmB,QAAQ,GAAG,CAAC,CAAA;gBACnC,MAAM,YAAY,OAAO,QAAQ,CAAC,OAAO,SAAS,AAAC,GAAS,OAAP,QAAO;gBAC5D,OAAO;YACT;YAEA,QAAQ,GAAG,CAAE,yBAAwB,iBAAiB,KAAK,CAAC,GAAG;YAE/D,MAAM,aAA2B,EAAE;YAEnC,wEAAwE;YACxE,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,MAAM,EAAE,IAAK;gBAChD,MAAM,SAAS,gBAAgB,CAAC,EAAE;gBAElC,IAAI;oBACF,QAAQ,GAAG,CAAC,AAAC,wBAAkC,OAAX,QAAO,MAAa,OAAT,IAAI,GAAE,KAA2B,OAAxB,iBAAiB,MAAM,EAAC;oBAEhF,IAAI,aAAgC;oBAEpC,kDAAkD;oBAElD,6CAA6C;oBAC7C,IAAI;4BAkBE,oBAAA;wBAjBJ,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,AAAC,GAAqB,OAAnB,iBAAgB,KAAU,OAAP,SAAU;4BAC/D,QAAQ;gCACN,UAAU;gCACV,OAAO;gCACP,gBAAgB;4BAClB;4BACA,SAAS;gCACP,cAAc;gCACd,UAAU;gCACV,mBAAmB;gCACnB,WAAW;4BACb;4BACA,SAAS;wBACX;wBAEA,MAAM,OAAO,SAAS,IAAI;wBAE1B,KAAI,cAAA,KAAK,KAAK,cAAV,mCAAA,qBAAA,YAAY,MAAM,cAAlB,yCAAA,kBAAoB,CAAC,EAAE,EAAE;gCAGb,0BAAA;4BAFd,MAAM,SAAS,KAAK,KAAK,CAAC,MAAM,CAAC,EAAE;4BACnC,MAAM,OAAO,OAAO,IAAI;4BACxB,MAAM,SAAQ,qBAAA,OAAO,UAAU,cAAjB,0CAAA,2BAAA,mBAAmB,KAAK,cAAxB,+CAAA,wBAA0B,CAAC,EAAE;4BAE3C,uDAAuD;4BACvD,MAAM,eAAe,KAAK,kBAAkB,IACxB,CAAA,kBAAA,4BAAA,MAAO,KAAK,KAAI,MAAM,KAAK,CAAC,MAAM,KAAK,CAAC,MAAM,GAAG,EAAE,IACpD,KAAK,aAAa,IAClB;4BAEnB,MAAM,gBAAgB,KAAK,aAAa,IAAI,KAAK,kBAAkB,IAAI;4BAEvE,sCAAsC;4BACtC,MAAM,SAAS,KAAK,mBAAmB,IAAK,eAAe;4BAC3D,MAAM,gBAAgB,KAAK,0BAA0B,IACjC,CAAC,gBAAgB,IAAI,AAAC,SAAS,gBAAiB,MAAM,CAAC;4BAE3E,IAAI,eAAe,GAAG;oCAgE4B;gCA/DhD,0DAA0D;gCAC1D,IAAI;gCACJ,IAAI;gCAEJ,IAAI;wCAiBE,8BAAA;oCAhBJ,iDAAiD;oCACjD,MAAM,qBAAqB,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,AAAC,GAAqB,OAAnB,iBAAgB,KAAU,OAAP,SAAU;wCACzE,QAAQ;4CACN,UAAU;4CACV,OAAO;4CACP,gBAAgB;wCAClB;wCACA,SAAS;4CACP,cAAc;4CACd,UAAU;4CACV,WAAW;wCACb;wCACA,SAAS;oCACX;oCAEA,MAAM,iBAAiB,mBAAmB,IAAI;oCAC9C,KAAI,wBAAA,eAAe,KAAK,cAApB,6CAAA,+BAAA,sBAAsB,MAAM,cAA5B,mDAAA,4BAA8B,CAAC,EAAE,EAAE;4CAGb,oCAAA;wCAFxB,MAAM,mBAAmB,eAAe,KAAK,CAAC,MAAM,CAAC,EAAE;wCACvD,MAAM,aAAa,iBAAiB,SAAS;wCAC7C,MAAM,mBAAkB,+BAAA,iBAAiB,UAAU,cAA3B,oDAAA,qCAAA,6BAA6B,KAAK,cAAlC,yDAAA,kCAAoC,CAAC,EAAE;wCAE/D,IAAI,eAAc,4BAAA,sCAAA,gBAAiB,IAAI,MAAI,4BAAA,sCAAA,gBAAiB,GAAG,GAAE;4CAC/D,MAAM,QAAQ,gBAAgB,IAAI;4CAClC,MAAM,OAAO,gBAAgB,GAAG;4CAEhC,6CAA6C;4CAC7C,IAAI,UAAU,CAAC;4CACf,IAAI,SAAS;4CACb,IAAI,eAAe,CAAC;4CACpB,IAAI,cAAc,CAAC;4CAEnB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gDACrC,IAAI,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,GAAG,SAAS;oDAClC,UAAU,KAAK,CAAC,EAAE;oDAClB,eAAe;gDACjB;gDACA,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,GAAG,QAAQ;oDAC/B,SAAS,IAAI,CAAC,EAAE;oDAChB,cAAc;gDAChB;4CACF;4CAEA,IAAI,gBAAgB,KAAK,eAAe,GAAG;gDACzC,iBAAiB,IAAI,KAAK,UAAU,CAAC,aAAa,GAAG,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gDACtF,gBAAgB,IAAI,KAAK,UAAU,CAAC,YAAY,GAAG,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;4CACtF;wCACF;oCACF;gCACF,EAAE,OAAO,iBAAiB;oCACxB,QAAQ,GAAG,CAAC,AAAC,0CAAgD,OAAP,QAAO;gCAC/D;gCAEA,aAAa;oCACX,QAAQ;oCACR,MAAM,KAAK,QAAQ,IAAI,KAAK,SAAS,IAAI,OAAO,OAAO,CAAC,OAAO;oCAC/D,OAAO,WAAW,aAAa,QAAQ;oCACvC,QAAQ,WAAW,OAAO,QAAQ;oCAClC,eAAe,WAAW,cAAc,QAAQ;oCAChD,QAAQ,SAAS,CAAC,KAAK,mBAAmB,KAAI,kBAAA,6BAAA,gBAAA,MAAO,MAAM,cAAb,oCAAA,aAAe,CAAC,MAAM,MAAM,CAAC,MAAM,GAAG,EAAE,KAAI,CAAC,EAAE,QAAQ;oCACrG,WAAW,KAAK,SAAS;oCACzB,YAAY,WAAW,CAAC,KAAK,gBAAgB,IAAI,CAAC,EAAE,QAAQ;oCAC5D,WAAW,WAAW,CAAC,KAAK,eAAe,IAAI,CAAC,EAAE,QAAQ;oCAC1D;oCACA;oCACA,WAAW,SAAS,CAAC,KAAK,wBAAwB,IAAI,CAAC,EAAE,QAAQ;gCACnE;gCAEA,QAAQ,GAAG,CAAC,AAAC,2BAAsC,OAAZ,QAAO,OAAsB,OAAjB,WAAW,KAAK;4BACrE;wBACF;oBACF,EAAE,OAAO,YAAiB;4BACP,uCAAA,iCAAA,2BAAA;wBAAjB,MAAM,WAAW,EAAA,uBAAA,WAAW,QAAQ,cAAnB,4CAAA,4BAAA,qBAAqB,IAAI,cAAzB,iDAAA,kCAAA,0BAA2B,KAAK,cAAhC,uDAAA,wCAAA,gCAAkC,KAAK,cAAvC,4DAAA,sCAAyC,WAAW,KAAI,WAAW,OAAO;wBAC3F,IAAI,qBAAA,+BAAA,SAAU,QAAQ,CAAC,aAAa;4BAClC,QAAQ,GAAG,CAAC,AAAC,MAA4B,OAAvB,QAAO,kBAAyB,OAAT;wBAC3C,OAAO;4BACL,QAAQ,GAAG,CAAC,AAAC,2BAAqC,OAAX,QAAO,MAAa,OAAT,UAAS;wBAC7D;oBACF;oBAEA,wCAAwC;oBACxC,IAAI,CAAC,YAAY;wBACf,IAAI;gCAea,4BAAA;4BAdf,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,iBAAiB;gCAChD,QAAQ;oCACN,SAAS;oCACT,WAAW;gCACb;gCACA,SAAS;oCACP,cAAc;oCACd,UAAU;oCACV,WAAW;gCACb;gCACA,SAAS;4BACX;4BAEA,MAAM,OAAO,SAAS,IAAI;4BAC1B,MAAM,UAAS,sBAAA,KAAK,aAAa,cAAlB,2CAAA,6BAAA,oBAAoB,MAAM,cAA1B,iDAAA,0BAA4B,CAAC,EAAE;4BAE9C,IAAI,UAAU,OAAO,kBAAkB,GAAG,GAAG;gCAC3C,aAAa;oCACX,QAAQ;oCACR,MAAM,OAAO,QAAQ,IAAI,OAAO,SAAS,IAAI,OAAO,OAAO,CAAC,OAAO;oCACnE,OAAO,WAAW,OAAO,kBAAkB,KAAK;oCAChD,QAAQ,WAAW,OAAO,mBAAmB,KAAK;oCAClD,eAAe,WAAW,OAAO,0BAA0B,KAAK;oCAChE,QAAQ,SAAS,OAAO,mBAAmB,KAAK;oCAChD,WAAW,OAAO,SAAS;oCAC3B,YAAY,WAAW,OAAO,gBAAgB,KAAK;oCACnD,WAAW,WAAW,OAAO,eAAe,KAAK;oCACjD,WAAW,SAAS,OAAO,wBAAwB,KAAK;gCAC1D;gCAEA,QAAQ,GAAG,CAAC,AAAC,2BAAsC,OAAZ,QAAO,OAAsB,OAAjB,WAAW,KAAK;4BACrE;wBACF,EAAE,OAAO,YAAY;4BACnB,QAAQ,GAAG,CAAC,AAAC,gCAAsC,OAAP;wBAC9C;oBACF;oBAEA,0CAA0C;oBAC1C,IAAI,cAAc,WAAW,KAAK,GAAG,GAAG;wBACtC,WAAW,IAAI,CAAC;oBAClB,OAAO;wBACL,QAAQ,IAAI,CAAC,AAAC,6BAAmC,OAAP,QAAO;wBACjD,8CAA8C;wBAC9C,WAAW,IAAI,CAAC;4BACd,QAAQ;4BACR,MAAM,OAAO,OAAO,CAAC,OAAO;4BAC5B,OAAO;4BACP,QAAQ;4BACR,eAAe;4BACf,QAAQ;4BACR,YAAY;4BACZ,WAAW;4BACX,WAAW;wBACb;oBACF;oBAEA,qCAAqC;oBACrC,IAAI,IAAI,iBAAiB,MAAM,GAAG,GAAG;wBACnC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACnD;gBAEF,EAAE,OAAO,aAAkB;oBACzB,QAAQ,IAAI,CAAC,AAAC,8BAAoC,OAAP,QAAO,MAAI,YAAY,OAAO;oBAEzE,8CAA8C;oBAC9C,WAAW,IAAI,CAAC;wBACd,QAAQ;wBACR,MAAM,OAAO,OAAO,CAAC,OAAO;wBAC5B,OAAO;wBACP,QAAQ;wBACR,eAAe;wBACf,QAAQ;wBACR,YAAY;wBACZ,WAAW;wBACX,WAAW;oBACb;gBACF;YACF;YAEA,6CAA6C;YAC7C,MAAM,cAAc,WAAW,MAAM,GAAG,iBAAiB,MAAM;YAC/D,QAAQ,GAAG,CAAC,AAAC,oBAAuD,OAApC,CAAC,cAAc,GAAG,EAAE,OAAO,CAAC,IAAG,OAA0B,OAArB,WAAW,MAAM,EAAC,KAA2B,OAAxB,iBAAiB,MAAM,EAAC;YAEjH,yEAAyE;YACzE,IAAI,cAAc,OAAO,WAAW,MAAM,GAAG,iBAAiB,MAAM,EAAE;gBACpE,QAAQ,GAAG,CAAE;gBAEb,MAAM,iBAAiB,IAAI,IAAI,WAAW,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;gBAC3D,MAAM,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,IAAK,CAAC,eAAe,GAAG,CAAC;gBAE1E,IAAI,iBAAiB,MAAM,GAAG,GAAG;oBAC/B,IAAI;wBACF,4CAA4C;wBAC5C,MAAM,YAAY;wBAClB,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,MAAM,EAAE,KAAK,UAAW;4BAC3D,MAAM,QAAQ,iBAAiB,KAAK,CAAC,GAAG,IAAI;4BAE5C,IAAI;oCAec;gCAdhB,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,iBAAiB;oCAChD,QAAQ;wCACN,SAAS,MAAM,IAAI,CAAC;wCACpB,WAAW;oCACb;oCACA,SAAS;wCACP,cAAc;wCACd,UAAU;wCACV,WAAW;oCACb;oCACA,SAAS;gCACX;gCAEA,MAAM,OAAO,SAAS,IAAI;gCAC1B,MAAM,UAAU,EAAA,uBAAA,KAAK,aAAa,cAAlB,2CAAA,qBAAoB,MAAM,KAAI,EAAE;gCAEhD,KAAK,MAAM,UAAU,QAAS;oCAC5B,IAAI,UAAU,OAAO,kBAAkB,GAAG,GAAG;wCAC3C,MAAM,aAAyB;4CAC7B,QAAQ,OAAO,MAAM;4CACrB,MAAM,OAAO,QAAQ,IAAI,OAAO,SAAS,IAAI,OAAO,MAAM,CAAC,OAAO,CAAC,OAAO;4CAC1E,OAAO,WAAW,OAAO,kBAAkB,KAAK;4CAChD,QAAQ,WAAW,OAAO,mBAAmB,KAAK;4CAClD,eAAe,WAAW,OAAO,0BAA0B,KAAK;4CAChE,QAAQ,SAAS,OAAO,mBAAmB,KAAK;4CAChD,WAAW,OAAO,SAAS;4CAC3B,YAAY,WAAW,OAAO,gBAAgB,KAAK;4CACnD,WAAW,WAAW,OAAO,eAAe,KAAK;4CACjD,WAAW,SAAS,OAAO,wBAAwB,KAAK;wCAC1D;wCAEA,WAAW,IAAI,CAAC;wCAChB,QAAQ,GAAG,CAAC,AAAC,gCAAkD,OAAnB,OAAO,MAAM,EAAC,OAAsB,OAAjB,WAAW,KAAK;oCACjF;gCACF;gCAEA,wBAAwB;gCACxB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;4BAEnD,EAAE,OAAO,YAAY;gCACnB,QAAQ,KAAK,CAAE,wCAAuC;4BACxD;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAE,4BAA2B;oBAC5C;gBACF;YACF;YAEA,QAAQ,GAAG,CAAC,AAAC,qBAA+D,OAA3C,WAAW,MAAM,EAAC,2BAAiD,OAAxB,iBAAiB,MAAM,EAAC;YACpG,QAAQ,GAAG,CAAE,sBAAqB,WAAW,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,IAAK,CAAC;oBACjE,QAAQ,EAAE,MAAM;oBAChB,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,IAAI;gBACd,CAAC;YAED,oBAAoB;YACpB,MAAM,WAAW,iIAAA,CAAA,YAAS,CAAC,WAAW,CAAC;YACvC,iIAAA,CAAA,eAAY,CAAC,GAAG,CAAC,UAAU;YAE3B,OAAO;QAET,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YAExD,yCAAyC;YACzC,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;oBAC5B,QAAQ,OAAO,QAAQ,CAAC,OAAO,SAAS,AAAC,GAAS,OAAP,QAAO;oBAClD,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,eAAe;oBACf,QAAQ;oBACR,WAAW;oBACX,YAAY;oBACZ,WAAW;oBACX,WAAW;gBACb,CAAC;QACH;IACF;IAIA,MAAM,aAAa,KAAa,EAA2B;QACzD,IAAI;YACF,MAAM,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,kBAAkB;gBACpD,GAAG;gBACH,aAAa;gBACb,WAAW;YACb;YAEA,MAAM,SAAS,KAAK,MAAM,IAAI,EAAE;YAEhC,OAAO,OACJ,MAAM,CAAC,CAAC,QAAe,MAAM,cAAc,IAAI,MAAM,MAAM,EAC3D,GAAG,CAAC,CAAC,QAAe,CAAC;oBACpB,QAAQ,MAAM,MAAM;oBACpB,MAAM,MAAM,QAAQ,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM;oBACvD,UAAU,MAAM,QAAQ,IAAI;oBAC5B,MAAM,MAAM,SAAS,IAAI;gBAC3B,CAAC;QACL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,EAAE;QACX;IACF;IAIA,4CAA4C;IAC5C,qBAAqB,MAAc,EAAU;QAC3C,OAAO,OAAO,QAAQ,CAAC,OAAO,SAAS,AAAC,GAAS,OAAP,QAAO;IACnD;IAEA,2DAA2D;IAC3D,uBAAuB,MAAc,EAAU;QAC7C,OAAO,OAAO,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO;IAClD;IAEA,6DAA6D;IAC7D,MAAM,kBAAkB,MAAc,EAA2C;YAAzC,OAAA,iEAAe;QACrD,IAAI;YACF,QAAQ,GAAG,CAAC,AAAC,kCAA4C,OAAX,QAAO,MAAS,OAAL,MAAK;YAE9D,+DAA+D;YAC/D,4EAA4E;YAC5E,MAAM,eAAe,MAAM,IAAI,CAAC,sBAAsB,CAAC;YACvD,IAAI,CAAC,cAAc;gBACjB,QAAQ,IAAI,CAAC,AAAC,8BAAoC,OAAP;gBAC3C,OAAO;YACT;YAEA,4DAA4D;YAC5D,4DAA4D;YAC5D,MAAM,iBAAiB,EAAE;YACzB,MAAM,YAAY,aAAa,KAAK;YACpC,MAAM,aAAa,aAAa,MAAM,IAAI;YAE1C,IAAK,IAAI,IAAI,OAAO,GAAG,KAAK,GAAG,IAAK;gBAClC,MAAM,OAAO,IAAI;gBACjB,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK;gBAE9B,oDAAoD;gBACpD,MAAM,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,KAAK,MAAM;gBACrD,MAAM,WAAW,YAAY,CAAC,IAAI,SAAS;gBAE3C,kDAAkD;gBAClD,MAAM,iBAAiB,KAAK,MAAM,KAAK,MAAM,OAAO;gBACpD,MAAM,OAAO,WAAW,CAAC,IAAI,cAAc;gBAC3C,MAAM,MAAM,WAAW,CAAC,IAAI,cAAc;gBAE1C,4BAA4B;gBAC5B,MAAM,kBAAkB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,KAAK,OAAO;gBAC5D,MAAM,SAAS,KAAK,KAAK,CAAC,aAAa,CAAC,IAAI,eAAe;gBAE3D,eAAe,IAAI,CAAC;oBAClB;oBACA,MAAM;oBACN,MAAM,KAAK,GAAG,CAAC,MAAM;oBACrB,KAAK,KAAK,GAAG,CAAC,KAAK;oBACnB,OAAO;oBACP,QAAQ,KAAK,GAAG,CAAC,QAAQ,QAAQ,iBAAiB;gBACpD;YACF;YAEA,2DAA2D;YAC3D,IAAI,eAAe,MAAM,GAAG,GAAG;gBAC7B,MAAM,UAAU,cAAc,CAAC,eAAe,MAAM,GAAG,EAAE;gBACzD,QAAQ,IAAI,GAAG,KAAK,GAAG,CAAC,QAAQ,IAAI,EAAE,aAAa,KAAK;gBACxD,QAAQ,KAAK,GAAG,aAAa,KAAK;gBAClC,QAAQ,MAAM,GAAG,aAAa,MAAM,IAAI,QAAQ,MAAM;YACxD;YAEA,QAAQ,GAAG,CAAC,AAAC,eAAmE,OAArD,eAAe,MAAM,EAAC,iCAAsC,OAAP;YAChF,OAAO;QAET,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,AAAC,0CAAgD,OAAP,QAAO,MAAI;YACnE,OAAO;QACT;IACF;;QAhyBA,0BAA0B;QAC1B,+KAAQ,mBAAkB,IAAI;QAC9B,+KAAQ,oBAAmB;QAC3B,+KAAQ,oBAA0C;QAClD,+KAAQ,kBAAiB;QACzB,+KAAQ,kBAA2B,EAAE;QACrC,+KAAQ,SAAQ,IAAI;QACpB,+KAAiB,kBAAiB,KAAK,OAAM,aAAa;;AA0xB5D;AAEO,MAAM,sBAAsB,IAAI", "debugId": null}}, {"offset": {"line": 2710, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/lib/background-data-service.ts"], "sourcesContent": ["// Background data service for preloading and auto-updating stock data\nimport { stockNamesService } from './stock-names-service';\nimport { cacheService, CacheKeys } from './cache-service';\nimport { NIFTY_200_SYMBOLS, getYahooSymbol } from './nifty-stocks';\nimport { yahooFinanceService } from './yahoo-finance';\n\ninterface BackgroundUpdateListener {\n  onPriceUpdate: (data: any[]) => void;\n  onNamesUpdate: (namesMap: Map<string, string>) => void;\n  onError: (error: Error) => void;\n}\n\ninterface RealTimeUpdateListener {\n  onRealTimeUpdate: (quotes: any[]) => void;\n  onError: (error: Error) => void;\n}\n\nclass BackgroundDataService {\n  private listeners: BackgroundUpdateListener[] = [];\n  private realTimeListeners: RealTimeUpdateListener[] = [];\n  private priceUpdateInterval: NodeJS.Timeout | null = null;\n  private nameRefreshInterval: NodeJS.Timeout | null = null;\n  private isInitialized = false;\n  private isUpdating = false;\n  private realTimeActive = false;\n  \n  // Configuration\n  private readonly PRICE_UPDATE_INTERVAL = 30 * 1000; // 30 seconds\n  private readonly NAME_REFRESH_INTERVAL = 23 * 60 * 60 * 1000; // 23 hours (refresh before 24h expiry)\n  private readonly BATCH_SIZE = 25;\n  private readonly BATCH_DELAY = 200; // ms between batches\n\n  // Initialize background service\n  async initialize(): Promise<void> {\n    if (this.isInitialized) return;\n    \n    console.log('🚀 Initializing background data service...');\n    \n    try {\n      // Step 1: Preload all stock names immediately\n      await this.preloadAllStockNames();\n      \n      // Step 2: Start automatic price updates\n      this.startPriceUpdates();\n      \n      // Step 3: Start automatic name refresh (before 24h expiry)\n      this.startNameRefresh();\n      \n      this.isInitialized = true;\n      console.log('✅ Background data service initialized successfully');\n      \n    } catch (error) {\n      console.error('❌ Failed to initialize background data service:', error);\n      this.notifyError(error as Error);\n    }\n  }\n\n  // Preload all Nifty 200 stock names\n  private async preloadAllStockNames(): Promise<void> {\n    console.log('📝 Preloading all Nifty 200 stock names...');\n    \n    try {\n      const yahooSymbols = NIFTY_200_SYMBOLS.map(getYahooSymbol).filter(s => s !== null);\n      \n      // Check if names are already cached\n      const cachedCount = yahooSymbols.filter(symbol => \n        stockNamesService.isNameCached(symbol)\n      ).length;\n      \n      if (cachedCount === yahooSymbols.length) {\n        console.log('✅ All stock names already cached');\n        return;\n      }\n      \n      console.log(`📊 Found ${cachedCount}/${yahooSymbols.length} names in cache, fetching remaining...`);\n      \n      // Preload all names with better error handling\n      try {\n        await stockNamesService.preloadStockNames(yahooSymbols);\n\n        // Get the names map and notify listeners\n        const namesMap = await stockNamesService.getStockNames(yahooSymbols);\n        this.notifyNamesUpdate(namesMap);\n\n        console.log(`✅ Preloaded ${namesMap.size} stock names`);\n      } catch (preloadError) {\n        console.warn('⚠️ Some stocks failed during preloading, but continuing with available data:', preloadError);\n\n        // Still try to get whatever names we have\n        try {\n          const namesMap = await stockNamesService.getStockNames(yahooSymbols);\n          this.notifyNamesUpdate(namesMap);\n          console.log(`✅ Loaded ${namesMap.size} stock names (with some failures)`);\n        } catch (fallbackError) {\n          console.error('❌ Failed to load any stock names:', fallbackError);\n          // Create a basic names map with fallback names\n          const fallbackNamesMap = new Map<string, string>();\n          yahooSymbols.forEach(symbol => {\n            fallbackNamesMap.set(symbol, symbol.replace('.NS', ''));\n          });\n          this.notifyNamesUpdate(fallbackNamesMap);\n          console.log(`✅ Using fallback names for ${fallbackNamesMap.size} stocks`);\n        }\n      }\n      \n    } catch (error) {\n      console.error('❌ Failed to preload stock names:', error);\n      throw error;\n    }\n  }\n\n  // Start automatic price updates\n  private startPriceUpdates(): void {\n    if (this.priceUpdateInterval) {\n      clearInterval(this.priceUpdateInterval);\n    }\n    \n    console.log(`🔄 Starting automatic price updates every ${this.PRICE_UPDATE_INTERVAL / 1000}s`);\n    \n    // Initial update\n    this.updatePriceData();\n    \n    // Set up recurring updates\n    this.priceUpdateInterval = setInterval(() => {\n      this.updatePriceData();\n    }, this.PRICE_UPDATE_INTERVAL);\n  }\n\n  // Update price data in background\n  private async updatePriceData(): Promise<void> {\n    if (this.isUpdating) {\n      console.log('⏳ Price update already in progress, skipping...');\n      return;\n    }\n    \n    this.isUpdating = true;\n    \n    try {\n      console.log('💰 Updating price data in background...');\n      \n      const yahooSymbols = NIFTY_200_SYMBOLS.map(getYahooSymbol).filter(s => s !== null);\n      const allPriceData: any[] = [];\n      \n      // Process in batches to avoid overwhelming the API\n      for (let i = 0; i < yahooSymbols.length; i += this.BATCH_SIZE) {\n        const batch = yahooSymbols.slice(i, i + this.BATCH_SIZE);\n        \n        try {\n          const { yahooFinanceService } = await import('./yahoo-finance');\n          const batchData = await yahooFinanceService.getMultipleQuotesWithCachedNames(batch);\n          allPriceData.push(...batchData);\n          \n          console.log(`📊 Updated batch ${Math.floor(i / this.BATCH_SIZE) + 1}/${Math.ceil(yahooSymbols.length / this.BATCH_SIZE)}: ${batchData.length} stocks`);\n          \n          // Add delay between batches\n          if (i + this.BATCH_SIZE < yahooSymbols.length) {\n            await new Promise(resolve => setTimeout(resolve, this.BATCH_DELAY));\n          }\n          \n        } catch (batchError) {\n          console.warn(`⚠️ Failed to update batch starting at index ${i}:`, batchError);\n        }\n      }\n      \n      // Notify all listeners of the price update\n      this.notifyPriceUpdate(allPriceData);\n      \n      console.log(`✅ Background price update completed: ${allPriceData.length} stocks updated`);\n      \n    } catch (error) {\n      console.error('❌ Background price update failed:', error);\n      this.notifyError(error as Error);\n    } finally {\n      this.isUpdating = false;\n    }\n  }\n\n  // Start automatic name refresh (before cache expiry)\n  private startNameRefresh(): void {\n    if (this.nameRefreshInterval) {\n      clearInterval(this.nameRefreshInterval);\n    }\n    \n    console.log(`🔄 Starting automatic name refresh every ${this.NAME_REFRESH_INTERVAL / (60 * 60 * 1000)}h`);\n    \n    this.nameRefreshInterval = setInterval(async () => {\n      console.log('🔄 Refreshing stock names before cache expiry...');\n      try {\n        await this.preloadAllStockNames();\n      } catch (error) {\n        console.error('❌ Failed to refresh stock names:', error);\n        this.notifyError(error as Error);\n      }\n    }, this.NAME_REFRESH_INTERVAL);\n  }\n\n  // Add listener for background updates\n  addListener(listener: BackgroundUpdateListener): void {\n    this.listeners.push(listener);\n  }\n\n  // Remove listener\n  removeListener(listener: BackgroundUpdateListener): void {\n    const index = this.listeners.indexOf(listener);\n    if (index > -1) {\n      this.listeners.splice(index, 1);\n    }\n  }\n\n  // Notify listeners of price updates\n  private notifyPriceUpdate(data: any[]): void {\n    this.listeners.forEach(listener => {\n      try {\n        listener.onPriceUpdate(data);\n      } catch (error) {\n        console.error('❌ Error in price update listener:', error);\n      }\n    });\n  }\n\n  // Notify listeners of name updates\n  private notifyNamesUpdate(namesMap: Map<string, string>): void {\n    this.listeners.forEach(listener => {\n      try {\n        listener.onNamesUpdate(namesMap);\n      } catch (error) {\n        console.error('❌ Error in names update listener:', error);\n      }\n    });\n  }\n\n  // Notify listeners of errors\n  private notifyError(error: Error): void {\n    this.listeners.forEach(listener => {\n      try {\n        listener.onError(error);\n      } catch (listenerError) {\n        console.error('❌ Error in error listener:', listenerError);\n      }\n    });\n  }\n\n  // Get current status\n  getStatus(): {\n    initialized: boolean;\n    updating: boolean;\n    listenersCount: number;\n    cacheStats: any;\n  } {\n    return {\n      initialized: this.isInitialized,\n      updating: this.isUpdating,\n      listenersCount: this.listeners.length,\n      cacheStats: stockNamesService.getNamesCacheStats()\n    };\n  }\n\n  // Force immediate update\n  async forceUpdate(): Promise<void> {\n    console.log('🔄 Forcing immediate data update...');\n    await this.preloadAllStockNames();\n    await this.updatePriceData();\n  }\n\n  // Stop all background processes\n  stop(): void {\n    console.log('🛑 Stopping background data service...');\n    \n    if (this.priceUpdateInterval) {\n      clearInterval(this.priceUpdateInterval);\n      this.priceUpdateInterval = null;\n    }\n    \n    if (this.nameRefreshInterval) {\n      clearInterval(this.nameRefreshInterval);\n      this.nameRefreshInterval = null;\n    }\n    \n    this.listeners = [];\n    this.isInitialized = false;\n    this.isUpdating = false;\n    \n    console.log('✅ Background data service stopped');\n  }\n\n  // Check if stock names are ready\n  areNamesReady(): boolean {\n    const yahooSymbols = NIFTY_200_SYMBOLS.map(getYahooSymbol).filter(s => s !== null);\n    const cachedCount = yahooSymbols.filter(symbol => \n      stockNamesService.isNameCached(symbol)\n    ).length;\n    \n    return cachedCount === yahooSymbols.length;\n  }\n\n  // Real-time update methods\n  addRealTimeListener(listener: RealTimeUpdateListener): void {\n    this.realTimeListeners.push(listener);\n\n    if (!this.realTimeActive) {\n      this.startRealTimeUpdates();\n    }\n  }\n\n  removeRealTimeListener(listener: RealTimeUpdateListener): void {\n    const index = this.realTimeListeners.indexOf(listener);\n    if (index > -1) {\n      this.realTimeListeners.splice(index, 1);\n    }\n\n    if (this.realTimeListeners.length === 0) {\n      this.stopRealTimeUpdates();\n    }\n  }\n\n  private startRealTimeUpdates(): void {\n    if (this.realTimeActive) return;\n\n    console.log('🔄 Starting real-time price updates...');\n    this.realTimeActive = true;\n\n    // Get all Nifty 200 symbols\n    const allSymbols = NIFTY_200_SYMBOLS.map(symbol => getYahooSymbol(symbol));\n\n    // Start Yahoo Finance real-time updates\n    yahooFinanceService.startRealTimeUpdates(allSymbols, (quotes) => {\n      // Notify all real-time listeners\n      this.realTimeListeners.forEach(listener => {\n        try {\n          listener.onRealTimeUpdate(quotes);\n        } catch (error) {\n          console.error('❌ Error in real-time listener:', error);\n          listener.onError(error as Error);\n        }\n      });\n    });\n  }\n\n  private stopRealTimeUpdates(): void {\n    if (!this.realTimeActive) return;\n\n    console.log('⏹️ Stopping real-time price updates...');\n    this.realTimeActive = false;\n\n    // Stop Yahoo Finance real-time updates\n    yahooFinanceService.stopRealTimeUpdates(() => {});\n  }\n\n  // Force immediate update for all real-time listeners\n  async forceRealTimeUpdate(): Promise<void> {\n    if (!this.realTimeActive || this.realTimeListeners.length === 0) return;\n\n    try {\n      console.log('🔄 Forcing immediate real-time update...');\n\n      const allSymbols = NIFTY_200_SYMBOLS.map(symbol => getYahooSymbol(symbol));\n      const quotes = await yahooFinanceService.getMultipleQuotesWithCachedNames(allSymbols);\n\n      this.realTimeListeners.forEach(listener => {\n        try {\n          listener.onRealTimeUpdate(quotes);\n        } catch (error) {\n          console.error('❌ Error in forced real-time update listener:', error);\n          listener.onError(error as Error);\n        }\n      });\n\n      console.log(`✅ Forced real-time update completed: ${quotes.length} quotes`);\n\n    } catch (error) {\n      console.error('❌ Failed to force real-time update:', error);\n      this.realTimeListeners.forEach(listener => {\n        listener.onError(error as Error);\n      });\n    }\n  }\n}\n\n// Create singleton instance\nexport const backgroundDataService = new BackgroundDataService();\n\n// Note: Auto-initialization is handled by BackgroundDataProvider\n// This prevents circular dependencies and ensures proper React lifecycle\n\n// Export types\nexport type { BackgroundUpdateListener };\n"], "names": [], "mappings": "AAAA,sEAAsE;;;;;AACtE;AAEA;AACA;;;;;AAaA,MAAM;IAeJ,gCAAgC;IAChC,MAAM,aAA4B;QAChC,IAAI,IAAI,CAAC,aAAa,EAAE;QAExB,QAAQ,GAAG,CAAC;QAEZ,IAAI;YACF,8CAA8C;YAC9C,MAAM,IAAI,CAAC,oBAAoB;YAE/B,wCAAwC;YACxC,IAAI,CAAC,iBAAiB;YAEtB,2DAA2D;YAC3D,IAAI,CAAC,gBAAgB;YAErB,IAAI,CAAC,aAAa,GAAG;YACrB,QAAQ,GAAG,CAAC;QAEd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mDAAmD;YACjE,IAAI,CAAC,WAAW,CAAC;QACnB;IACF;IAEA,oCAAoC;IACpC,MAAc,uBAAsC;QAClD,QAAQ,GAAG,CAAC;QAEZ,IAAI;YACF,MAAM,eAAe,gIAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,gIAAA,CAAA,iBAAc,EAAE,MAAM,CAAC,CAAA,IAAK,MAAM;YAE7E,oCAAoC;YACpC,MAAM,cAAc,aAAa,MAAM,CAAC,CAAA,SACtC,0IAAA,CAAA,oBAAiB,CAAC,YAAY,CAAC,SAC/B,MAAM;YAER,IAAI,gBAAgB,aAAa,MAAM,EAAE;gBACvC,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,QAAQ,GAAG,CAAC,AAAC,YAA0B,OAAf,aAAY,KAAuB,OAApB,aAAa,MAAM,EAAC;YAE3D,+CAA+C;YAC/C,IAAI;gBACF,MAAM,0IAAA,CAAA,oBAAiB,CAAC,iBAAiB,CAAC;gBAE1C,yCAAyC;gBACzC,MAAM,WAAW,MAAM,0IAAA,CAAA,oBAAiB,CAAC,aAAa,CAAC;gBACvD,IAAI,CAAC,iBAAiB,CAAC;gBAEvB,QAAQ,GAAG,CAAC,AAAC,eAA4B,OAAd,SAAS,IAAI,EAAC;YAC3C,EAAE,OAAO,cAAc;gBACrB,QAAQ,IAAI,CAAC,gFAAgF;gBAE7F,0CAA0C;gBAC1C,IAAI;oBACF,MAAM,WAAW,MAAM,0IAAA,CAAA,oBAAiB,CAAC,aAAa,CAAC;oBACvD,IAAI,CAAC,iBAAiB,CAAC;oBACvB,QAAQ,GAAG,CAAC,AAAC,YAAyB,OAAd,SAAS,IAAI,EAAC;gBACxC,EAAE,OAAO,eAAe;oBACtB,QAAQ,KAAK,CAAC,qCAAqC;oBACnD,+CAA+C;oBAC/C,MAAM,mBAAmB,IAAI;oBAC7B,aAAa,OAAO,CAAC,CAAA;wBACnB,iBAAiB,GAAG,CAAC,QAAQ,OAAO,OAAO,CAAC,OAAO;oBACrD;oBACA,IAAI,CAAC,iBAAiB,CAAC;oBACvB,QAAQ,GAAG,CAAC,AAAC,8BAAmD,OAAtB,iBAAiB,IAAI,EAAC;gBAClE;YACF;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM;QACR;IACF;IAEA,gCAAgC;IACxB,oBAA0B;QAChC,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,cAAc,IAAI,CAAC,mBAAmB;QACxC;QAEA,QAAQ,GAAG,CAAC,AAAC,6CAA8E,OAAlC,IAAI,CAAC,qBAAqB,GAAG,MAAK;QAE3F,iBAAiB;QACjB,IAAI,CAAC,eAAe;QAEpB,2BAA2B;QAC3B,IAAI,CAAC,mBAAmB,GAAG,YAAY;YACrC,IAAI,CAAC,eAAe;QACtB,GAAG,IAAI,CAAC,qBAAqB;IAC/B;IAEA,kCAAkC;IAClC,MAAc,kBAAiC;QAC7C,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,UAAU,GAAG;QAElB,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,eAAe,gIAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,gIAAA,CAAA,iBAAc,EAAE,MAAM,CAAC,CAAA,IAAK,MAAM;YAC7E,MAAM,eAAsB,EAAE;YAE9B,mDAAmD;YACnD,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,KAAK,IAAI,CAAC,UAAU,CAAE;gBAC7D,MAAM,QAAQ,aAAa,KAAK,CAAC,GAAG,IAAI,IAAI,CAAC,UAAU;gBAEvD,IAAI;oBACF,MAAM,EAAE,mBAAmB,EAAE,GAAG;oBAChC,MAAM,YAAY,MAAM,oBAAoB,gCAAgC,CAAC;oBAC7E,aAAa,IAAI,IAAI;oBAErB,QAAQ,GAAG,CAAC,AAAC,oBAA0D,OAAvC,KAAK,KAAK,CAAC,IAAI,IAAI,CAAC,UAAU,IAAI,GAAE,KAAwD,OAArD,KAAK,IAAI,CAAC,aAAa,MAAM,GAAG,IAAI,CAAC,UAAU,GAAE,MAAqB,OAAjB,UAAU,MAAM,EAAC;oBAE7I,4BAA4B;oBAC5B,IAAI,IAAI,IAAI,CAAC,UAAU,GAAG,aAAa,MAAM,EAAE;wBAC7C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,IAAI,CAAC,WAAW;oBACnE;gBAEF,EAAE,OAAO,YAAY;oBACnB,QAAQ,IAAI,CAAC,AAAC,+CAAgD,OAAF,GAAE,MAAI;gBACpE;YACF;YAEA,2CAA2C;YAC3C,IAAI,CAAC,iBAAiB,CAAC;YAEvB,QAAQ,GAAG,CAAC,AAAC,wCAA2D,OAApB,aAAa,MAAM,EAAC;QAE1E,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,IAAI,CAAC,WAAW,CAAC;QACnB,SAAU;YACR,IAAI,CAAC,UAAU,GAAG;QACpB;IACF;IAEA,qDAAqD;IAC7C,mBAAyB;QAC/B,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,cAAc,IAAI,CAAC,mBAAmB;QACxC;QAEA,QAAQ,GAAG,CAAC,AAAC,4CAAyF,OAA9C,IAAI,CAAC,qBAAqB,GAAG,CAAC,KAAK,KAAK,IAAI,GAAE;QAEtG,IAAI,CAAC,mBAAmB,GAAG,YAAY;YACrC,QAAQ,GAAG,CAAC;YACZ,IAAI;gBACF,MAAM,IAAI,CAAC,oBAAoB;YACjC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,IAAI,CAAC,WAAW,CAAC;YACnB;QACF,GAAG,IAAI,CAAC,qBAAqB;IAC/B;IAEA,sCAAsC;IACtC,YAAY,QAAkC,EAAQ;QACpD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;IACtB;IAEA,kBAAkB;IAClB,eAAe,QAAkC,EAAQ;QACvD,MAAM,QAAQ,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;QACrC,IAAI,QAAQ,CAAC,GAAG;YACd,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO;QAC/B;IACF;IAEA,oCAAoC;IAC5B,kBAAkB,IAAW,EAAQ;QAC3C,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;YACrB,IAAI;gBACF,SAAS,aAAa,CAAC;YACzB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qCAAqC;YACrD;QACF;IACF;IAEA,mCAAmC;IAC3B,kBAAkB,QAA6B,EAAQ;QAC7D,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;YACrB,IAAI;gBACF,SAAS,aAAa,CAAC;YACzB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qCAAqC;YACrD;QACF;IACF;IAEA,6BAA6B;IACrB,YAAY,KAAY,EAAQ;QACtC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;YACrB,IAAI;gBACF,SAAS,OAAO,CAAC;YACnB,EAAE,OAAO,eAAe;gBACtB,QAAQ,KAAK,CAAC,8BAA8B;YAC9C;QACF;IACF;IAEA,qBAAqB;IACrB,YAKE;QACA,OAAO;YACL,aAAa,IAAI,CAAC,aAAa;YAC/B,UAAU,IAAI,CAAC,UAAU;YACzB,gBAAgB,IAAI,CAAC,SAAS,CAAC,MAAM;YACrC,YAAY,0IAAA,CAAA,oBAAiB,CAAC,kBAAkB;QAClD;IACF;IAEA,yBAAyB;IACzB,MAAM,cAA6B;QACjC,QAAQ,GAAG,CAAC;QACZ,MAAM,IAAI,CAAC,oBAAoB;QAC/B,MAAM,IAAI,CAAC,eAAe;IAC5B;IAEA,gCAAgC;IAChC,OAAa;QACX,QAAQ,GAAG,CAAC;QAEZ,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,cAAc,IAAI,CAAC,mBAAmB;YACtC,IAAI,CAAC,mBAAmB,GAAG;QAC7B;QAEA,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,cAAc,IAAI,CAAC,mBAAmB;YACtC,IAAI,CAAC,mBAAmB,GAAG;QAC7B;QAEA,IAAI,CAAC,SAAS,GAAG,EAAE;QACnB,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,UAAU,GAAG;QAElB,QAAQ,GAAG,CAAC;IACd;IAEA,iCAAiC;IACjC,gBAAyB;QACvB,MAAM,eAAe,gIAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,gIAAA,CAAA,iBAAc,EAAE,MAAM,CAAC,CAAA,IAAK,MAAM;QAC7E,MAAM,cAAc,aAAa,MAAM,CAAC,CAAA,SACtC,0IAAA,CAAA,oBAAiB,CAAC,YAAY,CAAC,SAC/B,MAAM;QAER,OAAO,gBAAgB,aAAa,MAAM;IAC5C;IAEA,2BAA2B;IAC3B,oBAAoB,QAAgC,EAAQ;QAC1D,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;QAE5B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,IAAI,CAAC,oBAAoB;QAC3B;IACF;IAEA,uBAAuB,QAAgC,EAAQ;QAC7D,MAAM,QAAQ,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;QAC7C,IAAI,QAAQ,CAAC,GAAG;YACd,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO;QACvC;QAEA,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,KAAK,GAAG;YACvC,IAAI,CAAC,mBAAmB;QAC1B;IACF;IAEQ,uBAA6B;QACnC,IAAI,IAAI,CAAC,cAAc,EAAE;QAEzB,QAAQ,GAAG,CAAC;QACZ,IAAI,CAAC,cAAc,GAAG;QAEtB,4BAA4B;QAC5B,MAAM,aAAa,gIAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,CAAA,SAAU,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD,EAAE;QAElE,wCAAwC;QACxC,iIAAA,CAAA,sBAAmB,CAAC,oBAAoB,CAAC,YAAY,CAAC;YACpD,iCAAiC;YACjC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAA;gBAC7B,IAAI;oBACF,SAAS,gBAAgB,CAAC;gBAC5B,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,kCAAkC;oBAChD,SAAS,OAAO,CAAC;gBACnB;YACF;QACF;IACF;IAEQ,sBAA4B;QAClC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;QAE1B,QAAQ,GAAG,CAAC;QACZ,IAAI,CAAC,cAAc,GAAG;QAEtB,uCAAuC;QACvC,iIAAA,CAAA,sBAAmB,CAAC,mBAAmB,CAAC,KAAO;IACjD;IAEA,qDAAqD;IACrD,MAAM,sBAAqC;QACzC,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,KAAK,GAAG;QAEjE,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,aAAa,gIAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,CAAA,SAAU,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD,EAAE;YAClE,MAAM,SAAS,MAAM,iIAAA,CAAA,sBAAmB,CAAC,gCAAgC,CAAC;YAE1E,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAA;gBAC7B,IAAI;oBACF,SAAS,gBAAgB,CAAC;gBAC5B,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,gDAAgD;oBAC9D,SAAS,OAAO,CAAC;gBACnB;YACF;YAEA,QAAQ,GAAG,CAAC,AAAC,wCAAqD,OAAd,OAAO,MAAM,EAAC;QAEpE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAA;gBAC7B,SAAS,OAAO,CAAC;YACnB;QACF;IACF;;QArWA,+KAAQ,aAAwC,EAAE;QAClD,+KAAQ,qBAA8C,EAAE;QACxD,+KAAQ,uBAA6C;QACrD,+KAAQ,uBAA6C;QACrD,+KAAQ,iBAAgB;QACxB,+KAAQ,cAAa;QACrB,+KAAQ,kBAAiB;QAEzB,gBAAgB;QAChB,+KAAiB,yBAAwB,KAAK,OAAM,aAAa;QACjE,+KAAiB,yBAAwB,KAAK,KAAK,KAAK,OAAM,uCAAuC;QACrG,+KAAiB,cAAa;QAC9B,+KAAiB,eAAc,MAAK,qBAAqB;;AA0V3D;AAGO,MAAM,wBAAwB,IAAI", "debugId": null}}, {"offset": {"line": 3017, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/components/providers/BackgroundDataProvider.tsx"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, useEffect, useState, ReactNode } from 'react';\nimport { backgroundDataService } from '@/lib/background-data-service';\n\ninterface BackgroundDataContextType {\n  isInitialized: boolean;\n  isUpdating: boolean;\n  error: Error | null;\n  forceUpdate: () => Promise<void>;\n}\n\nconst BackgroundDataContext = createContext<BackgroundDataContextType | undefined>(undefined);\n\ninterface BackgroundDataProviderProps {\n  children: ReactNode;\n}\n\nexport function BackgroundDataProvider({ children }: BackgroundDataProviderProps) {\n  const [isInitialized, setIsInitialized] = useState(false);\n  const [isUpdating, setIsUpdating] = useState(false);\n  const [error, setError] = useState<Error | null>(null);\n\n  useEffect(() => {\n    let mounted = true;\n\n    const initializeBackgroundService = async () => {\n      try {\n        console.log('🚀 Initializing background data service from provider...');\n\n        // Initialize the background service\n        await backgroundDataService.initialize();\n\n        if (mounted) {\n          setIsInitialized(true);\n          setError(null);\n          console.log('✅ Background data service initialized successfully');\n        }\n      } catch (err) {\n        console.error('❌ Failed to initialize background data service:', err);\n        if (mounted) {\n          setError(err as Error);\n          // Still set as initialized to allow pages to work\n          setIsInitialized(true);\n        }\n      }\n    };\n\n    // Start initialization after a short delay to avoid blocking initial render\n    const initTimer = setTimeout(() => {\n      initializeBackgroundService();\n    }, 500);\n\n    // Cleanup\n    return () => {\n      mounted = false;\n      clearTimeout(initTimer);\n    };\n  }, []);\n\n  const forceUpdate = async () => {\n    setIsUpdating(true);\n    setError(null);\n    \n    try {\n      await backgroundDataService.forceUpdate();\n    } catch (err) {\n      setError(err as Error);\n      throw err;\n    } finally {\n      setIsUpdating(false);\n    }\n  };\n\n  const contextValue: BackgroundDataContextType = {\n    isInitialized,\n    isUpdating,\n    error,\n    forceUpdate\n  };\n\n  return (\n    <BackgroundDataContext.Provider value={contextValue}>\n      {children}\n    </BackgroundDataContext.Provider>\n  );\n}\n\nexport function useBackgroundDataContext() {\n  const context = useContext(BackgroundDataContext);\n  if (context === undefined) {\n    throw new Error('useBackgroundDataContext must be used within a BackgroundDataProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAYA,MAAM,sCAAwB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAyC;AAM5E,SAAS,uBAAuB,KAAyC;QAAzC,EAAE,QAAQ,EAA+B,GAAzC;;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,IAAI,UAAU;YAEd,MAAM;gFAA8B;oBAClC,IAAI;wBACF,QAAQ,GAAG,CAAC;wBAEZ,oCAAoC;wBACpC,MAAM,8IAAA,CAAA,wBAAqB,CAAC,UAAU;wBAEtC,IAAI,SAAS;4BACX,iBAAiB;4BACjB,SAAS;4BACT,QAAQ,GAAG,CAAC;wBACd;oBACF,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,mDAAmD;wBACjE,IAAI,SAAS;4BACX,SAAS;4BACT,kDAAkD;4BAClD,iBAAiB;wBACnB;oBACF;gBACF;;YAEA,4EAA4E;YAC5E,MAAM,YAAY;8DAAW;oBAC3B;gBACF;6DAAG;YAEH,UAAU;YACV;oDAAO;oBACL,UAAU;oBACV,aAAa;gBACf;;QACF;2CAAG,EAAE;IAEL,MAAM,cAAc;QAClB,cAAc;QACd,SAAS;QAET,IAAI;YACF,MAAM,8IAAA,CAAA,wBAAqB,CAAC,WAAW;QACzC,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,MAAM;QACR,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,eAA0C;QAC9C;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,sBAAsB,QAAQ;QAAC,OAAO;kBACpC;;;;;;AAGP;GApEgB;KAAA;AAsET,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 3124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/components/AppInitializer.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { Loader } from 'lucide-react';\n\nexport function AppInitializer({ children }: { children: React.ReactNode }) {\n  const [isInitialized, setIsInitialized] = useState(false);\n  const [isInitializing, setIsInitializing] = useState(true);\n\n  const initializeApp = async () => {\n    try {\n      console.log('🚀 Simple initialization...');\n      setIsInitializing(true);\n\n      // Simple delay to simulate initialization\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      setIsInitialized(true);\n      setIsInitializing(false);\n      console.log('✅ Simple initialization completed');\n\n    } catch (error) {\n      console.error('❌ Initialization failed:', error);\n      setIsInitializing(false);\n      setIsInitialized(false);\n    }\n  };\n\n  useEffect(() => {\n    // Check for bypass parameter\n    const urlParams = new URLSearchParams(window.location.search);\n    const bypassInit = urlParams.get('bypass') === 'true';\n\n    if (bypassInit) {\n      console.log('🔄 Bypassing initialization...');\n      setIsInitialized(true);\n      setIsInitializing(false);\n      return;\n    }\n\n    // Start simple initialization\n    initializeApp();\n  }, []);\n\n  // Show initialization screen while loading\n  if (isInitializing) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4\">\n          <div className=\"text-center\">\n            <div className=\"mb-6\">\n              <Loader className=\"h-12 w-12 animate-spin text-blue-600 mx-auto\" />\n            </div>\n\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">\n              Initializing Niveshtor Trading\n            </h2>\n            <p className=\"text-gray-600 mb-6\">\n              Setting up the application...\n            </p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Show main app if initialized\n  if (isInitialized) {\n    return <>{children}</>;\n  }\n\n  // Fallback loading state\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n      <div className=\"text-center\">\n        <Loader className=\"h-8 w-8 animate-spin text-blue-600 mx-auto mb-4\" />\n        <p className=\"text-gray-600\">Loading application...</p>\n      </div>\n    </div>\n  );\n}\n\n// Hook to check if app is initialized\nexport function useAppInitialization() {\n  const [isInitialized, setIsInitialized] = useState(false);\n  const [isChecking, setIsChecking] = useState(true);\n\n  useEffect(() => {\n    const checkInitialization = async () => {\n      try {\n        const response = await fetch('/api/initialize-app');\n        const result = await response.json();\n        \n        setIsInitialized(result.success && result.isFullyInitialized);\n      } catch (error) {\n        console.error('Error checking initialization:', error);\n        setIsInitialized(false);\n      } finally {\n        setIsChecking(false);\n      }\n    };\n\n    checkInitialization();\n  }, []);\n\n  return { isInitialized, isChecking };\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAKO,SAAS,eAAe,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IAC7B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,gBAAgB;QACpB,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,kBAAkB;YAElB,0CAA0C;YAC1C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,iBAAiB;YACjB,kBAAkB;YAClB,QAAQ,GAAG,CAAC;QAEd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,kBAAkB;YAClB,iBAAiB;QACnB;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,6BAA6B;YAC7B,MAAM,YAAY,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;YAC5D,MAAM,aAAa,UAAU,GAAG,CAAC,cAAc;YAE/C,IAAI,YAAY;gBACd,QAAQ,GAAG,CAAC;gBACZ,iBAAiB;gBACjB,kBAAkB;gBAClB;YACF;YAEA,8BAA8B;YAC9B;QACF;mCAAG,EAAE;IAEL,2CAA2C;IAC3C,IAAI,gBAAgB;QAClB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;sCAGpB,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAGzD,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;;;;;;IAO5C;IAEA,+BAA+B;IAC/B,IAAI,eAAe;QACjB,qBAAO;sBAAG;;IACZ;IAEA,yBAAyB;IACzB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;8BAClB,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;;AAIrC;GA3EgB;KAAA;AA8ET,SAAS;;IACd,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,MAAM;sEAAsB;oBAC1B,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM;wBAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;wBAElC,iBAAiB,OAAO,OAAO,IAAI,OAAO,kBAAkB;oBAC9D,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,kCAAkC;wBAChD,iBAAiB;oBACnB,SAAU;wBACR,cAAc;oBAChB;gBACF;;YAEA;QACF;yCAAG,EAAE;IAEL,OAAO;QAAE;QAAe;IAAW;AACrC;IAvBgB", "debugId": null}}, {"offset": {"line": 3308, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/components/PerformanceMonitor.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { usePathname } from 'next/navigation';\nimport { Clock, Zap, AlertTriangle } from 'lucide-react';\n\ninterface PerformanceMetrics {\n  navigationTime: number;\n  dataLoadTime: number;\n  totalTime: number;\n  isInstant: boolean; // < 200ms\n  timestamp: Date;\n  page: string;\n}\n\ninterface PerformanceStats {\n  averageNavigationTime: number;\n  instantNavigations: number;\n  totalNavigations: number;\n  slowestPage: string;\n  fastestPage: string;\n}\n\nexport function PerformanceMonitor() {\n  const pathname = usePathname();\n  const [metrics, setMetrics] = useState<PerformanceMetrics[]>([]);\n  const [currentMetric, setCurrentMetric] = useState<PerformanceMetrics | null>(null);\n  const [showMonitor, setShowMonitor] = useState(false);\n  const [navigationStart, setNavigationStart] = useState<number>(0);\n\n  // Track navigation performance\n  useEffect(() => {\n    const startTime = performance.now();\n    setNavigationStart(startTime);\n\n    // Measure when page is fully loaded\n    const measurePerformance = () => {\n      const endTime = performance.now();\n      const totalTime = endTime - startTime;\n      \n      const metric: PerformanceMetrics = {\n        navigationTime: totalTime,\n        dataLoadTime: 0, // Will be updated by data loading hooks\n        totalTime,\n        isInstant: totalTime < 200,\n        timestamp: new Date(),\n        page: pathname\n      };\n\n      setCurrentMetric(metric);\n      setMetrics(prev => [...prev.slice(-9), metric]); // Keep last 10 metrics\n\n      console.log(`⚡ Navigation to ${pathname}: ${totalTime.toFixed(2)}ms ${totalTime < 200 ? '(INSTANT)' : '(SLOW)'}`);\n    };\n\n    // Measure after a short delay to allow for data loading\n    const timer = setTimeout(measurePerformance, 100);\n\n    return () => clearTimeout(timer);\n  }, [pathname]);\n\n  // Calculate performance stats\n  const getPerformanceStats = (): PerformanceStats => {\n    if (metrics.length === 0) {\n      return {\n        averageNavigationTime: 0,\n        instantNavigations: 0,\n        totalNavigations: 0,\n        slowestPage: '',\n        fastestPage: ''\n      };\n    }\n\n    const totalTime = metrics.reduce((sum, m) => sum + m.navigationTime, 0);\n    const instantCount = metrics.filter(m => m.isInstant).length;\n    \n    const sortedByTime = [...metrics].sort((a, b) => a.navigationTime - b.navigationTime);\n    \n    return {\n      averageNavigationTime: totalTime / metrics.length,\n      instantNavigations: instantCount,\n      totalNavigations: metrics.length,\n      slowestPage: sortedByTime[sortedByTime.length - 1]?.page || '',\n      fastestPage: sortedByTime[0]?.page || ''\n    };\n  };\n\n  const stats = getPerformanceStats();\n\n  // Show/hide monitor with keyboard shortcut\n  useEffect(() => {\n    const handleKeyPress = (e: KeyboardEvent) => {\n      if (e.ctrlKey && e.shiftKey && e.key === 'P') {\n        setShowMonitor(prev => !prev);\n      }\n    };\n\n    window.addEventListener('keydown', handleKeyPress);\n    return () => window.removeEventListener('keydown', handleKeyPress);\n  }, []);\n\n  if (!showMonitor) {\n    return (\n      <div className=\"fixed bottom-4 right-4 z-50\">\n        <button\n          onClick={() => setShowMonitor(true)}\n          className=\"bg-gray-800 text-white p-2 rounded-full shadow-lg hover:bg-gray-700 transition-colors\"\n          title=\"Show Performance Monitor (Ctrl+Shift+P)\"\n        >\n          <Zap className=\"h-4 w-4\" />\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"fixed bottom-4 right-4 z-50 bg-white rounded-lg shadow-xl border border-gray-200 p-4 w-80\">\n      <div className=\"flex items-center justify-between mb-3\">\n        <div className=\"flex items-center space-x-2\">\n          <Zap className=\"h-4 w-4 text-blue-600\" />\n          <span className=\"font-semibold text-gray-900\">Performance Monitor</span>\n        </div>\n        <button\n          onClick={() => setShowMonitor(false)}\n          className=\"text-gray-400 hover:text-gray-600\"\n        >\n          ×\n        </button>\n      </div>\n\n      {/* Current Page Performance */}\n      {currentMetric && (\n        <div className=\"mb-4 p-3 bg-gray-50 rounded-lg\">\n          <div className=\"flex items-center space-x-2 mb-2\">\n            <Clock className=\"h-4 w-4 text-gray-600\" />\n            <span className=\"text-sm font-medium text-gray-900\">Current Page</span>\n          </div>\n          <div className=\"text-xs text-gray-600 mb-1\">{currentMetric.page}</div>\n          <div className={`text-lg font-bold ${currentMetric.isInstant ? 'text-green-600' : 'text-orange-600'}`}>\n            {currentMetric.navigationTime.toFixed(0)}ms\n            {currentMetric.isInstant ? (\n              <span className=\"ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded\">INSTANT</span>\n            ) : (\n              <span className=\"ml-2 text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded\">SLOW</span>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Performance Stats */}\n      <div className=\"space-y-2 text-sm\">\n        <div className=\"flex justify-between\">\n          <span className=\"text-gray-600\">Average Time:</span>\n          <span className=\"font-medium\">{stats.averageNavigationTime.toFixed(0)}ms</span>\n        </div>\n        <div className=\"flex justify-between\">\n          <span className=\"text-gray-600\">Instant Navigation:</span>\n          <span className=\"font-medium\">\n            {stats.instantNavigations}/{stats.totalNavigations}\n            <span className=\"ml-1 text-xs text-gray-500\">\n              ({stats.totalNavigations > 0 ? Math.round((stats.instantNavigations / stats.totalNavigations) * 100) : 0}%)\n            </span>\n          </span>\n        </div>\n        {stats.fastestPage && (\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">Fastest:</span>\n            <span className=\"font-medium text-green-600 text-xs\">{stats.fastestPage}</span>\n          </div>\n        )}\n        {stats.slowestPage && (\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">Slowest:</span>\n            <span className=\"font-medium text-orange-600 text-xs\">{stats.slowestPage}</span>\n          </div>\n        )}\n      </div>\n\n      {/* Recent Navigation Times */}\n      {metrics.length > 0 && (\n        <div className=\"mt-4 pt-3 border-t border-gray-200\">\n          <div className=\"text-xs font-medium text-gray-700 mb-2\">Recent Navigations</div>\n          <div className=\"space-y-1\">\n            {metrics.slice(-5).reverse().map((metric, index) => (\n              <div key={index} className=\"flex justify-between items-center text-xs\">\n                <span className=\"text-gray-600 truncate flex-1 mr-2\">\n                  {metric.page.split('/').pop() || 'dashboard'}\n                </span>\n                <span className={`font-medium ${metric.isInstant ? 'text-green-600' : 'text-orange-600'}`}>\n                  {metric.navigationTime.toFixed(0)}ms\n                </span>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Performance Target Indicator */}\n      <div className=\"mt-3 pt-3 border-t border-gray-200\">\n        <div className=\"flex items-center space-x-2 text-xs\">\n          {stats.averageNavigationTime < 200 ? (\n            <>\n              <Zap className=\"h-3 w-3 text-green-500\" />\n              <span className=\"text-green-700\">Target: &lt;200ms ✓</span>\n            </>\n          ) : (\n            <>\n              <AlertTriangle className=\"h-3 w-3 text-orange-500\" />\n              <span className=\"text-orange-700\">Target: &lt;200ms</span>\n            </>\n          )}\n        </div>\n      </div>\n\n      <div className=\"mt-2 text-xs text-gray-500\">\n        Press Ctrl+Shift+P to toggle\n      </div>\n    </div>\n  );\n}\n\n// Hook to report data loading times to the performance monitor\nexport function usePerformanceReporting() {\n  const reportDataLoadTime = (loadTime: number, dataType: string) => {\n    console.log(`📊 Data loading time for ${dataType}: ${loadTime.toFixed(2)}ms`);\n  };\n\n  return { reportDataLoadTime };\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAAA;;;AAJA;;;;AAuBO,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB,EAAE;IAC/D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IAC9E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE/D,+BAA+B;IAC/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM,YAAY,YAAY,GAAG;YACjC,mBAAmB;YAEnB,oCAAoC;YACpC,MAAM;mEAAqB;oBACzB,MAAM,UAAU,YAAY,GAAG;oBAC/B,MAAM,YAAY,UAAU;oBAE5B,MAAM,SAA6B;wBACjC,gBAAgB;wBAChB,cAAc;wBACd;wBACA,WAAW,YAAY;wBACvB,WAAW,IAAI;wBACf,MAAM;oBACR;oBAEA,iBAAiB;oBACjB;2EAAW,CAAA,OAAQ;mCAAI,KAAK,KAAK,CAAC,CAAC;gCAAI;6BAAO;2EAAG,uBAAuB;oBAExE,QAAQ,GAAG,CAAC,AAAC,mBAA+B,OAAb,UAAS,MAA8B,OAA1B,UAAU,OAAO,CAAC,IAAG,OAA8C,OAAzC,YAAY,MAAM,cAAc;gBACxG;;YAEA,wDAAwD;YACxD,MAAM,QAAQ,WAAW,oBAAoB;YAE7C;gDAAO,IAAM,aAAa;;QAC5B;uCAAG;QAAC;KAAS;IAEb,8BAA8B;IAC9B,MAAM,sBAAsB;YAoBX,gBACA;QApBf,IAAI,QAAQ,MAAM,KAAK,GAAG;YACxB,OAAO;gBACL,uBAAuB;gBACvB,oBAAoB;gBACpB,kBAAkB;gBAClB,aAAa;gBACb,aAAa;YACf;QACF;QAEA,MAAM,YAAY,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,cAAc,EAAE;QACrE,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,MAAM;QAE5D,MAAM,eAAe;eAAI;SAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,cAAc,GAAG,EAAE,cAAc;QAEpF,OAAO;YACL,uBAAuB,YAAY,QAAQ,MAAM;YACjD,oBAAoB;YACpB,kBAAkB,QAAQ,MAAM;YAChC,aAAa,EAAA,iBAAA,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE,cAArC,qCAAA,eAAuC,IAAI,KAAI;YAC5D,aAAa,EAAA,kBAAA,YAAY,CAAC,EAAE,cAAf,sCAAA,gBAAiB,IAAI,KAAI;QACxC;IACF;IAEA,MAAM,QAAQ;IAEd,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM;+DAAiB,CAAC;oBACtB,IAAI,EAAE,OAAO,IAAI,EAAE,QAAQ,IAAI,EAAE,GAAG,KAAK,KAAK;wBAC5C;2EAAe,CAAA,OAAQ,CAAC;;oBAC1B;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;gDAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;uCAAG,EAAE;IAEL,IAAI,CAAC,aAAa;QAChB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBACC,SAAS,IAAM,eAAe;gBAC9B,WAAU;gBACV,OAAM;0BAEN,cAAA,6LAAC,mMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,6LAAC;gCAAK,WAAU;0CAA8B;;;;;;;;;;;;kCAEhD,6LAAC;wBACC,SAAS,IAAM,eAAe;wBAC9B,WAAU;kCACX;;;;;;;;;;;;YAMF,+BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CAAoC;;;;;;;;;;;;kCAEtD,6LAAC;wBAAI,WAAU;kCAA8B,cAAc,IAAI;;;;;;kCAC/D,6LAAC;wBAAI,WAAW,AAAC,qBAAmF,OAA/D,cAAc,SAAS,GAAG,mBAAmB;;4BAC/E,cAAc,cAAc,CAAC,OAAO,CAAC;4BAAG;4BACxC,cAAc,SAAS,iBACtB,6LAAC;gCAAK,WAAU;0CAA6D;;;;;qDAE7E,6LAAC;gCAAK,WAAU;0CAA+D;;;;;;;;;;;;;;;;;;0BAOvF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAgB;;;;;;0CAChC,6LAAC;gCAAK,WAAU;;oCAAe,MAAM,qBAAqB,CAAC,OAAO,CAAC;oCAAG;;;;;;;;;;;;;kCAExE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAgB;;;;;;0CAChC,6LAAC;gCAAK,WAAU;;oCACb,MAAM,kBAAkB;oCAAC;oCAAE,MAAM,gBAAgB;kDAClD,6LAAC;wCAAK,WAAU;;4CAA6B;4CACzC,MAAM,gBAAgB,GAAG,IAAI,KAAK,KAAK,CAAC,AAAC,MAAM,kBAAkB,GAAG,MAAM,gBAAgB,GAAI,OAAO;4CAAE;;;;;;;;;;;;;;;;;;;oBAI9G,MAAM,WAAW,kBAChB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAgB;;;;;;0CAChC,6LAAC;gCAAK,WAAU;0CAAsC,MAAM,WAAW;;;;;;;;;;;;oBAG1E,MAAM,WAAW,kBAChB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAgB;;;;;;0CAChC,6LAAC;gCAAK,WAAU;0CAAuC,MAAM,WAAW;;;;;;;;;;;;;;;;;;YAM7E,QAAQ,MAAM,GAAG,mBAChB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAyC;;;;;;kCACxD,6LAAC;wBAAI,WAAU;kCACZ,QAAQ,KAAK,CAAC,CAAC,GAAG,OAAO,GAAG,GAAG,CAAC,CAAC,QAAQ,sBACxC,6LAAC;gCAAgB,WAAU;;kDACzB,6LAAC;wCAAK,WAAU;kDACb,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM;;;;;;kDAEnC,6LAAC;wCAAK,WAAW,AAAC,eAAsE,OAAxD,OAAO,SAAS,GAAG,mBAAmB;;4CACnE,OAAO,cAAc,CAAC,OAAO,CAAC;4CAAG;;;;;;;;+BAL5B;;;;;;;;;;;;;;;;0BAclB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,MAAM,qBAAqB,GAAG,oBAC7B;;0CACE,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,6LAAC;gCAAK,WAAU;0CAAiB;;;;;;;qDAGnC;;0CACE,6LAAC,2NAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;0CACzB,6LAAC;gCAAK,WAAU;0CAAkB;;;;;;;;;;;;;;;;;;0BAM1C,6LAAC;gBAAI,WAAU;0BAA6B;;;;;;;;;;;;AAKlD;GApMgB;;QACG,qIAAA,CAAA,cAAW;;;KADd;AAuMT,SAAS;IACd,MAAM,qBAAqB,CAAC,UAAkB;QAC5C,QAAQ,GAAG,CAAC,AAAC,4BAAwC,OAAb,UAAS,MAAwB,OAApB,SAAS,OAAO,CAAC,IAAG;IAC3E;IAEA,OAAO;QAAE;IAAmB;AAC9B", "debugId": null}}]}