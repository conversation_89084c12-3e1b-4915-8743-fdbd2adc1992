{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/app/dashboard/gtt-orders/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport {\n  Clock,\n  TrendingUp,\n  TrendingDown,\n  CheckCircle,\n  XCircle,\n  AlertCircle,\n  Wifi,\n  WifiOff,\n  RefreshCw,\n  Calendar,\n  Target,\n  Activity,\n  Plus\n} from 'lucide-react';\nimport { formatCurrency, formatDateTime } from '@/lib/utils';\n\ninterface GTTOrder {\n  id: string;\n  gttId?: string; // Angel One GTT ID\n  symbol: string;\n  name: string;\n  orderType: 'BUY' | 'SELL';\n  triggerPrice: number;\n  quantity: number;\n  status: 'PENDING' | 'TRIGGERED' | 'CANCELLED' | 'EXPIRED';\n  createdAt: Date;\n  source: 'SIGNAL' | 'HOLDING' | 'SALE'; // Which tab this order belongs to\n}\n\ntype TabType = 'SIGNAL' | 'HOLDING' | 'SALE';\n\nexport default function GTTOrdersPage() {\n  const [activeTab, setActiveTab] = useState<TabType>('SIGNAL');\n  const [orders, setOrders] = useState<GTTOrder[]>([\n    // Sample GTT Buy on Signal orders\n    {\n      id: '1',\n      symbol: 'RELIANCE',\n      name: 'Reliance Industries Ltd',\n      orderType: 'BUY',\n      triggerPrice: 2400.05,\n      quantity: 8,\n      status: 'PENDING',\n      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),\n      source: 'SIGNAL'\n    },\n    {\n      id: '2',\n      symbol: 'TCS',\n      name: 'Tata Consultancy Services',\n      orderType: 'BUY',\n      triggerPrice: 3200.05,\n      quantity: 6,\n      status: 'TRIGGERED',\n      createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),\n      source: 'SIGNAL'\n    },\n    // Sample GTT Buy on Holding orders\n    {\n      id: '3',\n      symbol: 'INFY',\n      name: 'Infosys Limited',\n      orderType: 'BUY',\n      triggerPrice: 1350.00,\n      quantity: 14,\n      status: 'PENDING',\n      createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),\n      source: 'HOLDING'\n    },\n    // Sample GTT Sale orders\n    {\n      id: '4',\n      symbol: 'WIPRO',\n      name: 'Wipro Limited',\n      orderType: 'SELL',\n      triggerPrice: 450.00,\n      quantity: 44,\n      status: 'PENDING',\n      createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),\n      source: 'SALE'\n    }\n  ]);\n\n  const [isAngelOneConnected, setIsAngelOneConnected] = useState(false);\n  const [lastSync, setLastSync] = useState<Date | null>(null);\n  const [isCreatingOrders, setIsCreatingOrders] = useState(false);\n  const [createOrdersError, setCreateOrdersError] = useState<string | null>(null);\n\n  // Filter orders by active tab\n  const filteredOrders = orders.filter(order => order.source === activeTab);\n\n  // Interface for Weekly High Signal data\n  interface WeeklyHighStock {\n    symbol: string;\n    name: string;\n    currentPrice: number;\n    lastWeekHighest: number;\n    suggestedBuyPrice: number;\n    percentDifference: number;\n    suggestedGTTQuantity: number;\n    isBOHEligible: boolean;\n    inHoldings: boolean;\n  }\n\n  // Function to fetch BOH eligible stocks and create GTT orders using the API\n  const fetchAndCreateGTTOrders = async () => {\n    try {\n      console.log('🔍 Fetching BOH eligible stocks and creating GTT orders via API...');\n\n      // Use the dedicated API endpoint for creating signal orders\n      const response = await fetch('/api/gtt/create-signal-orders', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      console.log(`📡 API Response status: ${response.status}`);\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error(`❌ API request failed: ${response.status} - ${errorText}`);\n        throw new Error(`API request failed: ${response.status}`);\n      }\n\n      const data = await response.json();\n      console.log('📊 API Response data:', data);\n\n      if (!data.success) {\n        console.error('❌ API returned error:', data.error);\n        throw new Error(data.error || 'Failed to fetch GTT order data');\n      }\n\n      console.log(`✅ API returned ${data.data.orders.length} GTT orders to create`);\n      console.log(`📊 BOH Stats: Total BOH stocks: ${data.data.totalBOHStocks}, Valid for GTT: ${data.data.validForGTT}`);\n\n      if (data.data.stats.avgTriggerPrice > 0) {\n        console.log(`💰 Price Stats: Avg Trigger: ₹${data.data.stats.avgTriggerPrice.toFixed(2)}, Total Value: ₹${data.data.stats.totalValue.toFixed(2)}`);\n      }\n\n      return data.data.orders;\n    } catch (error) {\n      console.error('❌ Error fetching GTT orders from API:', error);\n      throw error;\n    }\n  };\n\n  // Function to create GTT orders for all BOH eligible stocks\n  const createAllSignalOrders = async () => {\n    setIsCreatingOrders(true);\n    setCreateOrdersError(null);\n\n    try {\n      console.log('🚀 Starting to create GTT orders for all BOH eligible stocks...');\n\n      // Fetch GTT order data from API\n      const gttOrderRequests = await fetchAndCreateGTTOrders();\n\n      console.log(`🔍 Received ${gttOrderRequests.length} GTT order requests from API`);\n\n      if (gttOrderRequests.length === 0) {\n        const errorMsg = 'No BOH eligible stocks found for GTT orders. This could be due to:\\n' +\n                        '• No stocks marked as BOH eligible\\n' +\n                        '• All BOH stocks filtered out due to price/quantity constraints\\n' +\n                        '• API data fetching issues';\n        console.warn('⚠️ ' + errorMsg);\n        setCreateOrdersError(errorMsg);\n        return;\n      }\n\n      // Filter out stocks that already have pending signal orders\n      const existingSignalSymbols = orders\n        .filter(order => order.source === 'SIGNAL' && order.status === 'PENDING')\n        .map(order => order.symbol);\n\n      console.log(`🔍 Existing signal orders: ${existingSignalSymbols.length} symbols:`, existingSignalSymbols);\n\n      const newOrderRequests = gttOrderRequests.filter((orderReq: any) =>\n        !existingSignalSymbols.includes(orderReq.symbol)\n      );\n\n      console.log(`📊 Creating orders for ${newOrderRequests.length} new stocks (${existingSignalSymbols.length} already have orders)`);\n\n      if (newOrderRequests.length === 0) {\n        const errorMsg = `All ${gttOrderRequests.length} BOH eligible stocks already have pending GTT orders`;\n        console.warn('⚠️ ' + errorMsg);\n        setCreateOrdersError(errorMsg);\n        return;\n      }\n\n      // Create GTT orders for each stock\n      const newOrders: GTTOrder[] = [];\n      let successCount = 0;\n      let errorCount = 0;\n\n      for (const orderReq of newOrderRequests) {\n        try {\n          const newOrder: GTTOrder = {\n            id: `signal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            symbol: orderReq.symbol,\n            name: orderReq.name,\n            orderType: orderReq.orderType,\n            triggerPrice: orderReq.triggerPrice,\n            quantity: orderReq.quantity,\n            status: 'PENDING',\n            createdAt: new Date(),\n            source: orderReq.source\n          };\n\n          newOrders.push(newOrder);\n          successCount++;\n\n          console.log(`✅ Created GTT order for ${orderReq.symbol}: Trigger=₹${orderReq.triggerPrice.toFixed(2)}, Qty=${orderReq.quantity}`);\n        } catch (error) {\n          console.error(`❌ Failed to create order for ${orderReq.symbol}:`, error);\n          errorCount++;\n        }\n      }\n\n      // Add new orders to the existing orders\n      setOrders(prevOrders => [...prevOrders, ...newOrders]);\n      setLastSync(new Date());\n\n      console.log(`🎉 Successfully created ${successCount} GTT orders (${errorCount} errors)`);\n\n      // Show success message\n      if (successCount > 0) {\n        const totalValue = newOrders.reduce((sum, order) => sum + (order.triggerPrice * order.quantity), 0);\n        const message = `🎉 Successfully created ${successCount} GTT orders for BOH eligible stocks!\\n\\n` +\n                       `📊 Summary:\\n` +\n                       `• Total Orders: ${successCount}\\n` +\n                       `• Total Investment: ₹${totalValue.toLocaleString()}\\n` +\n                       `• Average Trigger Price: ₹${(totalValue / newOrders.reduce((sum, order) => sum + order.quantity, 0)).toFixed(2)}\\n\\n` +\n                       `✅ All orders are now visible in the GTT Buy on Signal tab.`;\n\n        alert(message);\n        console.log('🎉 GTT Orders Created Successfully:', {\n          successCount,\n          totalValue,\n          orders: newOrders.map(o => ({ symbol: o.symbol, trigger: o.triggerPrice, qty: o.quantity }))\n        });\n      }\n\n      if (errorCount > 0) {\n        const errorMsg = `Created ${successCount} orders successfully, but ${errorCount} failed`;\n        console.warn('⚠️ Some GTT orders failed:', errorMsg);\n        setCreateOrdersError(errorMsg);\n      }\n\n    } catch (error) {\n      console.error('❌ Error creating signal orders:', error);\n      setCreateOrdersError(error instanceof Error ? error.message : 'Failed to create orders');\n    } finally {\n      setIsCreatingOrders(false);\n    }\n  };\n\n  // Function to clear all pending signal orders (for weekly cleanup)\n  const clearPendingSignalOrders = () => {\n    setOrders(prevOrders =>\n      prevOrders.filter(order =>\n        !(order.source === 'SIGNAL' && order.status === 'PENDING')\n      )\n    );\n    console.log('🧹 Cleared all pending signal orders');\n  };\n\n  // Function to get signal orders statistics\n  const getSignalOrdersStats = () => {\n    const signalOrders = orders.filter(order => order.source === 'SIGNAL');\n    return {\n      total: signalOrders.length,\n      pending: signalOrders.filter(order => order.status === 'PENDING').length,\n      triggered: signalOrders.filter(order => order.status === 'TRIGGERED').length,\n      cancelled: signalOrders.filter(order => order.status === 'CANCELLED').length,\n      expired: signalOrders.filter(order => order.status === 'EXPIRED').length\n    };\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'PENDING': return 'text-yellow-600 bg-yellow-100';\n      case 'TRIGGERED': return 'text-green-600 bg-green-100';\n      case 'CANCELLED': return 'text-red-600 bg-red-100';\n      case 'EXPIRED': return 'text-gray-600 bg-gray-100';\n      default: return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'PENDING': return <Clock className=\"h-4 w-4\" />;\n      case 'TRIGGERED': return <CheckCircle className=\"h-4 w-4\" />;\n      case 'CANCELLED': return <XCircle className=\"h-4 w-4\" />;\n      case 'EXPIRED': return <AlertCircle className=\"h-4 w-4\" />;\n      default: return <Clock className=\"h-4 w-4\" />;\n    }\n  };\n\n  const getTabInfo = (tab: TabType) => {\n    switch (tab) {\n      case 'SIGNAL':\n        return {\n          title: 'GTT Buy on Signal',\n          description: 'Automated buy orders based on Weekly High Signal data',\n          icon: <Activity className=\"h-5 w-5\" />,\n          automation: 'Auto-created every Friday 8:00 PM'\n        };\n      case 'HOLDING':\n        return {\n          title: 'GTT Buy on Holding',\n          description: 'Additional buy orders at lower support levels for existing holdings',\n          icon: <TrendingDown className=\"h-5 w-5\" />,\n          automation: 'Auto-created when Ignorable Lower Price is calculated'\n        };\n      case 'SALE':\n        return {\n          title: 'GTT Sale',\n          description: 'Sell orders triggered when target prices are reached',\n          icon: <Target className=\"h-5 w-5\" />,\n          automation: 'Auto-created when Target Price is set for holdings'\n        };\n    }\n  };\n\n  const OrdersTable = () => (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n      <div className=\"overflow-x-auto\">\n        <table className=\"min-w-full divide-y divide-gray-200\">\n          <thead className=\"bg-gray-50\">\n            <tr>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Stock\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Order Type\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Trigger Price\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Quantity\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Status\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Created Date\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Actions\n              </th>\n            </tr>\n          </thead>\n          <tbody className=\"bg-white divide-y divide-gray-200\">\n            {filteredOrders.length > 0 ? (\n              filteredOrders.map((order) => (\n                <tr key={order.id} className=\"hover:bg-gray-50\">\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div>\n                      <div className=\"text-sm font-medium text-gray-900\">{order.symbol}</div>\n                      <div className=\"text-sm text-gray-500 truncate max-w-xs\">{order.name}</div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      <div className={`p-1 rounded-full mr-2 ${order.orderType === 'BUY' ? 'bg-green-100' : 'bg-red-100'}`}>\n                        {order.orderType === 'BUY' ? (\n                          <TrendingUp className=\"h-3 w-3 text-green-600\" />\n                        ) : (\n                          <TrendingDown className=\"h-3 w-3 text-red-600\" />\n                        )}\n                      </div>\n                      <span className={`text-sm font-medium ${order.orderType === 'BUY' ? 'text-green-600' : 'text-red-600'}`}>\n                        {order.orderType}\n                      </span>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {formatCurrency(order.triggerPrice)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {order.quantity}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>\n                      {getStatusIcon(order.status)}\n                      <span className=\"ml-1\">{order.status}</span>\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    {formatDateTime(order.createdAt)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <button className=\"text-red-600 hover:text-red-900 mr-3\">\n                      Cancel\n                    </button>\n                    <button className=\"text-blue-600 hover:text-blue-900\">\n                      View\n                    </button>\n                  </td>\n                </tr>\n              ))\n            ) : (\n              <tr>\n                <td colSpan={7} className=\"px-6 py-12 text-center\">\n                  <div className=\"text-gray-500\">\n                    <Clock className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n                    <p>No GTT orders found for {getTabInfo(activeTab).title}.</p>\n                    <p className=\"text-sm mt-1\">{getTabInfo(activeTab).automation}</p>\n                  </div>\n                </td>\n              </tr>\n            )}\n          </tbody>\n        </table>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Page Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">GTT Orders</h1>\n          <p className=\"text-gray-600 mt-1\">Automated Good Till Triggered order management</p>\n        </div>\n        <div className=\"flex items-center space-x-4\">\n          {/* Angel One Connection Status */}\n          <div className=\"flex items-center space-x-2\">\n            {isAngelOneConnected ? (\n              <div className=\"flex items-center space-x-2 text-green-600\">\n                <Wifi className=\"h-4 w-4\" />\n                <span className=\"text-sm font-medium\">Angel One Connected</span>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-2 text-red-600\">\n                <WifiOff className=\"h-4 w-4\" />\n                <span className=\"text-sm font-medium\">Angel One Disconnected</span>\n              </div>\n            )}\n          </div>\n          <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2\">\n            <RefreshCw className=\"h-4 w-4\" />\n            <span>Sync Orders</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Automation Status */}\n      <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n        <div className=\"flex items-center space-x-2\">\n          <Calendar className=\"h-5 w-5 text-blue-600\" />\n          <div>\n            <p className=\"text-sm font-medium text-blue-900\">Next Automation: Friday 8:00 PM</p>\n            <p className=\"text-xs text-blue-700\">\n              Weekly High Signal orders will be automatically created/updated\n              {lastSync && ` • Last sync: ${formatDateTime(lastSync)}`}\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Tab Navigation */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n        <div className=\"border-b border-gray-200\">\n          <nav className=\"flex space-x-8 px-6\">\n            {(['SIGNAL', 'HOLDING', 'SALE'] as const).map((tab) => {\n              const tabInfo = getTabInfo(tab);\n              const tabOrders = orders.filter(o => o.source === tab);\n              return (\n                <button\n                  key={tab}\n                  onClick={() => setActiveTab(tab)}\n                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors flex items-center space-x-2 ${\n                    activeTab === tab\n                      ? 'border-blue-500 text-blue-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  {tabInfo.icon}\n                  <span>{tabInfo.title}</span>\n                  <span className=\"bg-gray-100 text-gray-600 px-2 py-0.5 rounded-full text-xs\">\n                    {tabOrders.length}\n                  </span>\n                </button>\n              );\n            })}\n          </nav>\n        </div>\n\n        {/* Tab Content */}\n        <div className=\"p-6\">\n          <div className=\"mb-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                  {getTabInfo(activeTab).title}\n                </h3>\n                <p className=\"text-gray-600 text-sm mb-1\">\n                  {getTabInfo(activeTab).description}\n                </p>\n                <p className=\"text-blue-600 text-xs font-medium\">\n                  {getTabInfo(activeTab).automation}\n                </p>\n              </div>\n\n              {/* Create All Signal Orders Button - Only show for SIGNAL tab */}\n              {activeTab === 'SIGNAL' && (\n                <div className=\"flex flex-col items-end space-y-2\">\n                  <button\n                    onClick={createAllSignalOrders}\n                    disabled={isCreatingOrders}\n                    className={`px-4 py-2 rounded-lg font-medium text-sm transition-colors flex items-center space-x-2 ${\n                      isCreatingOrders\n                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                        : 'bg-green-600 text-white hover:bg-green-700'\n                    }`}\n                  >\n                    {isCreatingOrders ? (\n                      <>\n                        <RefreshCw className=\"h-4 w-4 animate-spin\" />\n                        <span>Creating Orders...</span>\n                      </>\n                    ) : (\n                      <>\n                        <Plus className=\"h-4 w-4\" />\n                        <span>Create All Signal Orders</span>\n                      </>\n                    )}\n                  </button>\n\n                  {createOrdersError && (\n                    <p className=\"text-red-600 text-xs max-w-xs text-right\">\n                      {createOrdersError}\n                    </p>\n                  )}\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Signal Orders Info - Only show for SIGNAL tab */}\n          {activeTab === 'SIGNAL' && (\n            <div className=\"mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h4 className=\"text-sm font-medium text-blue-900 mb-1\">\n                    Weekly High Signal Orders\n                  </h4>\n                  <p className=\"text-xs text-blue-700\">\n                    Orders are automatically created for BOH eligible stocks from the Weekly High Signal page.\n                    Trigger Price = Last Week's High + ₹0.05, Quantity = ₹2,000 ÷ Trigger Price\n                  </p>\n                </div>\n                <div className=\"text-right\">\n                  <div className=\"text-sm font-medium text-blue-900\">\n                    {getSignalOrdersStats().pending} Pending\n                  </div>\n                  <div className=\"text-xs text-blue-700\">\n                    {getSignalOrdersStats().triggered} Triggered\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Orders Table */}\n          <OrdersTable />\n        </div>\n      </div>\n\n      {/* Summary Statistics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Total Orders</p>\n              <p className=\"text-2xl font-bold text-gray-900 mt-1\">{orders.length}</p>\n            </div>\n            <Activity className=\"h-8 w-8 text-gray-600\" />\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Pending Orders</p>\n              <p className=\"text-2xl font-bold text-yellow-600 mt-1\">\n                {orders.filter(o => o.status === 'PENDING').length}\n              </p>\n            </div>\n            <Clock className=\"h-8 w-8 text-yellow-600\" />\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Triggered Today</p>\n              <p className=\"text-2xl font-bold text-green-600 mt-1\">\n                {orders.filter(o => o.status === 'TRIGGERED').length}\n              </p>\n            </div>\n            <CheckCircle className=\"h-8 w-8 text-green-600\" />\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Success Rate</p>\n              <p className=\"text-2xl font-bold text-gray-900 mt-1\">\n                {orders.length > 0 ? Math.round((orders.filter(o => o.status === 'TRIGGERED').length / orders.length) * 100) : 0}%\n              </p>\n            </div>\n            <TrendingUp className=\"h-8 w-8 text-gray-600\" />\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AAlBA;;;;;AAmCe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACpD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;QAC/C,kCAAkC;QAClC;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,WAAW;YACX,cAAc;YACd,UAAU;YACV,QAAQ;YACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;YACpD,QAAQ;QACV;QACA;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,WAAW;YACX,cAAc;YACd,UAAU;YACV,QAAQ;YACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;YACpD,QAAQ;QACV;QACA,mCAAmC;QACnC;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,WAAW;YACX,cAAc;YACd,UAAU;YACV,QAAQ;YACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;YACpD,QAAQ;QACV;QACA,yBAAyB;QACzB;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,WAAW;YACX,cAAc;YACd,UAAU;YACV,QAAQ;YACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;YACpD,QAAQ;QACV;KACD;IAED,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1E,8BAA8B;IAC9B,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;IAe/D,4EAA4E;IAC5E,MAAM,0BAA0B;QAC9B,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,4DAA4D;YAC5D,MAAM,WAAW,MAAM,MAAM,iCAAiC;gBAC5D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,SAAS,MAAM,EAAE;YAExD,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,WAAW;gBACvE,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,yBAAyB;YAErC,IAAI,CAAC,KAAK,OAAO,EAAE;gBACjB,QAAQ,KAAK,CAAC,yBAAyB,KAAK,KAAK;gBACjD,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,qBAAqB,CAAC;YAC5E,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,KAAK,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,KAAK,IAAI,CAAC,WAAW,EAAE;YAElH,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,GAAG;gBACvC,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,gBAAgB,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI;YACnJ;YAEA,OAAO,KAAK,IAAI,CAAC,MAAM;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM;QACR;IACF;IAEA,4DAA4D;IAC5D,MAAM,wBAAwB;QAC5B,oBAAoB;QACpB,qBAAqB;QAErB,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,gCAAgC;YAChC,MAAM,mBAAmB,MAAM;YAE/B,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,iBAAiB,MAAM,CAAC,4BAA4B,CAAC;YAEhF,IAAI,iBAAiB,MAAM,KAAK,GAAG;gBACjC,MAAM,WAAW,yEACD,yCACA,sEACA;gBAChB,QAAQ,IAAI,CAAC,QAAQ;gBACrB,qBAAqB;gBACrB;YACF;YAEA,4DAA4D;YAC5D,MAAM,wBAAwB,OAC3B,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK,YAAY,MAAM,MAAM,KAAK,WAC9D,GAAG,CAAC,CAAA,QAAS,MAAM,MAAM;YAE5B,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,sBAAsB,MAAM,CAAC,SAAS,CAAC,EAAE;YAEnF,MAAM,mBAAmB,iBAAiB,MAAM,CAAC,CAAC,WAChD,CAAC,sBAAsB,QAAQ,CAAC,SAAS,MAAM;YAGjD,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,iBAAiB,MAAM,CAAC,aAAa,EAAE,sBAAsB,MAAM,CAAC,qBAAqB,CAAC;YAEhI,IAAI,iBAAiB,MAAM,KAAK,GAAG;gBACjC,MAAM,WAAW,CAAC,IAAI,EAAE,iBAAiB,MAAM,CAAC,oDAAoD,CAAC;gBACrG,QAAQ,IAAI,CAAC,QAAQ;gBACrB,qBAAqB;gBACrB;YACF;YAEA,mCAAmC;YACnC,MAAM,YAAwB,EAAE;YAChC,IAAI,eAAe;YACnB,IAAI,aAAa;YAEjB,KAAK,MAAM,YAAY,iBAAkB;gBACvC,IAAI;oBACF,MAAM,WAAqB;wBACzB,IAAI,CAAC,OAAO,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;wBACrE,QAAQ,SAAS,MAAM;wBACvB,MAAM,SAAS,IAAI;wBACnB,WAAW,SAAS,SAAS;wBAC7B,cAAc,SAAS,YAAY;wBACnC,UAAU,SAAS,QAAQ;wBAC3B,QAAQ;wBACR,WAAW,IAAI;wBACf,QAAQ,SAAS,MAAM;oBACzB;oBAEA,UAAU,IAAI,CAAC;oBACf;oBAEA,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,SAAS,YAAY,CAAC,OAAO,CAAC,GAAG,MAAM,EAAE,SAAS,QAAQ,EAAE;gBAClI,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,SAAS,MAAM,CAAC,CAAC,CAAC,EAAE;oBAClE;gBACF;YACF;YAEA,wCAAwC;YACxC,UAAU,CAAA,aAAc;uBAAI;uBAAe;iBAAU;YACrD,YAAY,IAAI;YAEhB,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,aAAa,aAAa,EAAE,WAAW,QAAQ,CAAC;YAEvF,uBAAuB;YACvB,IAAI,eAAe,GAAG;gBACpB,MAAM,aAAa,UAAU,MAAM,CAAC,CAAC,KAAK,QAAU,MAAO,MAAM,YAAY,GAAG,MAAM,QAAQ,EAAG;gBACjG,MAAM,UAAU,CAAC,wBAAwB,EAAE,aAAa,wCAAwC,CAAC,GAClF,CAAC,aAAa,CAAC,GACf,CAAC,gBAAgB,EAAE,aAAa,EAAE,CAAC,GACnC,CAAC,qBAAqB,EAAE,WAAW,cAAc,GAAG,EAAE,CAAC,GACvD,CAAC,0BAA0B,EAAE,CAAC,aAAa,UAAU,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,QAAQ,EAAE,EAAE,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC,GACtH,CAAC,0DAA0D,CAAC;gBAE3E,MAAM;gBACN,QAAQ,GAAG,CAAC,uCAAuC;oBACjD;oBACA;oBACA,QAAQ,UAAU,GAAG,CAAC,CAAA,IAAK,CAAC;4BAAE,QAAQ,EAAE,MAAM;4BAAE,SAAS,EAAE,YAAY;4BAAE,KAAK,EAAE,QAAQ;wBAAC,CAAC;gBAC5F;YACF;YAEA,IAAI,aAAa,GAAG;gBAClB,MAAM,WAAW,CAAC,QAAQ,EAAE,aAAa,0BAA0B,EAAE,WAAW,OAAO,CAAC;gBACxF,QAAQ,IAAI,CAAC,8BAA8B;gBAC3C,qBAAqB;YACvB;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,qBAAqB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAChE,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,mEAAmE;IACnE,MAAM,2BAA2B;QAC/B,UAAU,CAAA,aACR,WAAW,MAAM,CAAC,CAAA,QAChB,CAAC,CAAC,MAAM,MAAM,KAAK,YAAY,MAAM,MAAM,KAAK,SAAS;QAG7D,QAAQ,GAAG,CAAC;IACd;IAEA,2CAA2C;IAC3C,MAAM,uBAAuB;QAC3B,MAAM,eAAe,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;QAC7D,OAAO;YACL,OAAO,aAAa,MAAM;YAC1B,SAAS,aAAa,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK,WAAW,MAAM;YACxE,WAAW,aAAa,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK,aAAa,MAAM;YAC5E,WAAW,aAAa,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK,aAAa,MAAM;YAC5E,SAAS,aAAa,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK,WAAW,MAAM;QAC1E;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAW,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YACxC,KAAK;gBAAa,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChD,KAAK;gBAAa,qBAAO,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5C,KAAK;gBAAW,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAC9C;gBAAS,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QACnC;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBAC1B,YAAY;gBACd;YACF,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,oBAAM,8OAAC,sNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;oBAC9B,YAAY;gBACd;YACF,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,oBAAM,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;oBACxB,YAAY;gBACd;QACJ;IACF;IAEA,MAAM,cAAc,kBAClB,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAM,WAAU;;sCACf,8OAAC;4BAAM,WAAU;sCACf,cAAA,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;;;;;;;;;;;;sCAKnG,8OAAC;4BAAM,WAAU;sCACd,eAAe,MAAM,GAAG,IACvB,eAAe,GAAG,CAAC,CAAC,sBAClB,8OAAC;oCAAkB,WAAU;;sDAC3B,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAqC,MAAM,MAAM;;;;;;kEAChE,8OAAC;wDAAI,WAAU;kEAA2C,MAAM,IAAI;;;;;;;;;;;;;;;;;sDAGxE,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,sBAAsB,EAAE,MAAM,SAAS,KAAK,QAAQ,iBAAiB,cAAc;kEACjG,MAAM,SAAS,KAAK,sBACnB,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;iFAEtB,8OAAC,sNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;kEAG5B,8OAAC;wDAAK,WAAW,CAAC,oBAAoB,EAAE,MAAM,SAAS,KAAK,QAAQ,mBAAmB,gBAAgB;kEACpG,MAAM,SAAS;;;;;;;;;;;;;;;;;sDAItB,8OAAC;4CAAG,WAAU;sDACX,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,YAAY;;;;;;sDAEpC,8OAAC;4CAAG,WAAU;sDACX,MAAM,QAAQ;;;;;;sDAEjB,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAK,WAAW,CAAC,wEAAwE,EAAE,eAAe,MAAM,MAAM,GAAG;;oDACvH,cAAc,MAAM,MAAM;kEAC3B,8OAAC;wDAAK,WAAU;kEAAQ,MAAM,MAAM;;;;;;;;;;;;;;;;;sDAGxC,8OAAC;4CAAG,WAAU;sDACX,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,SAAS;;;;;;sDAEjC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAO,WAAU;8DAAuC;;;;;;8DAGzD,8OAAC;oDAAO,WAAU;8DAAoC;;;;;;;;;;;;;mCAxCjD,MAAM,EAAE;;;;0DA+CnB,8OAAC;0CACC,cAAA,8OAAC;oCAAG,SAAS;oCAAG,WAAU;8CACxB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;;oDAAE;oDAAyB,WAAW,WAAW,KAAK;oDAAC;;;;;;;0DACxD,8OAAC;gDAAE,WAAU;0DAAgB,WAAW,WAAW,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAW/E,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAEpC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACZ,oCACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;yDAGxC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;;;;;;0CAI5C,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAAoC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;;wCAAwB;wCAElC,YAAY,CAAC,cAAc,EAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW;;;;;;;;;;;;;;;;;;;;;;;;0BAOhE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,AAAC;gCAAC;gCAAU;gCAAW;6BAAO,CAAW,GAAG,CAAC,CAAC;gCAC7C,MAAM,UAAU,WAAW;gCAC3B,MAAM,YAAY,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;gCAClD,qBACE,8OAAC;oCAEC,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAC,uFAAuF,EACjG,cAAc,MACV,kCACA,8EACJ;;wCAED,QAAQ,IAAI;sDACb,8OAAC;sDAAM,QAAQ,KAAK;;;;;;sDACpB,8OAAC;4CAAK,WAAU;sDACb,UAAU,MAAM;;;;;;;mCAXd;;;;;4BAeX;;;;;;;;;;;kCAKJ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DACX,WAAW,WAAW,KAAK;;;;;;8DAE9B,8OAAC;oDAAE,WAAU;8DACV,WAAW,WAAW,WAAW;;;;;;8DAEpC,8OAAC;oDAAE,WAAU;8DACV,WAAW,WAAW,UAAU;;;;;;;;;;;;wCAKpC,cAAc,0BACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS;oDACT,UAAU;oDACV,WAAW,CAAC,uFAAuF,EACjG,mBACI,iDACA,8CACJ;8DAED,iCACC;;0EACE,8OAAC,gNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;0EACrB,8OAAC;0EAAK;;;;;;;qFAGR;;0EACE,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;0EAAK;;;;;;;;;;;;;gDAKX,mCACC,8OAAC;oDAAE,WAAU;8DACV;;;;;;;;;;;;;;;;;;;;;;;4BASZ,cAAc,0BACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAyC;;;;;;8DAGvD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAKvC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;wDACZ,uBAAuB,OAAO;wDAAC;;;;;;;8DAElC,8OAAC;oDAAI,WAAU;;wDACZ,uBAAuB,SAAS;wDAAC;;;;;;;;;;;;;;;;;;;;;;;;0CAQ5C,8OAAC;;;;;;;;;;;;;;;;;0BAKL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAyC,OAAO,MAAM;;;;;;;;;;;;8CAErE,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIxB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDACV,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;;;;;;;;;;;;8CAGtD,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIrB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDACV,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;;;;;;;;;;;;8CAGxD,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAI3B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;;gDACV,OAAO,MAAM,GAAG,IAAI,KAAK,KAAK,CAAC,AAAC,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM,GAAG,OAAO,MAAM,GAAI,OAAO;gDAAE;;;;;;;;;;;;;8CAGrH,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC", "debugId": null}}, {"offset": {"line": 1263, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/trending-down.js", "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/node_modules/lucide-react/src/icons/trending-down.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 17h6v-6', key: 't6n2it' }],\n  ['path', { d: 'm22 17-8.5-8.5-5 5L2 7', key: 'x473p' }],\n];\n\n/**\n * @component @name TrendingDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMTdoNnYtNiIgLz4KICA8cGF0aCBkPSJtMjIgMTctOC41LTguNS01IDVMMiA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/trending-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrendingDown = createLucideIcon('trending-down', __iconNode);\n\nexport default TrendingDown;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC5C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAS,CAAA;KAAA;CACxD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 1305, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/circle-check-big.js", "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/node_modules/lucide-react/src/icons/circle-check-big.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M21.801 10A10 10 0 1 1 17 3.335', key: 'yps3ct' }],\n  ['path', { d: 'm9 11 3 3L22 4', key: '1pflzl' }],\n];\n\n/**\n * @component @name CircleCheckBig\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEuODAxIDEwQTEwIDEwIDAgMSAxIDE3IDMuMzM1IiAvPgogIDxwYXRoIGQ9Im05IDExIDMgM0wyMiA0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/circle-check-big\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleCheckBig = createLucideIcon('circle-check-big', __iconNode);\n\nexport default CircleCheckBig;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAmC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAChE;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACjD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,cAAA,CAAiB,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAoB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 1347, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/circle-x.js", "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/node_modules/lucide-react/src/icons/circle-x.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['path', { d: 'm15 9-6 6', key: '1uzhvr' }],\n  ['path', { d: 'm9 9 6 6', key: 'z0biqf' }],\n];\n\n/**\n * @component @name CircleX\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJtMTUgOS02IDYiIC8+CiAgPHBhdGggZD0ibTkgOSA2IDYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-x\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleX = createLucideIcon('circle-x', __iconNode);\n\nexport default CircleX;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAU,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,AAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 1398, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/circle-alert.js", "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/node_modules/lucide-react/src/icons/circle-alert.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['line', { x1: '12', x2: '12', y1: '8', y2: '12', key: '1pkeuh' }],\n  ['line', { x1: '12', x2: '12.01', y1: '16', y2: '16', key: '4dfq90' }],\n];\n\n/**\n * @component @name CircleAlert\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjgiIHkyPSIxMiIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMi4wMSIgeTE9IjE2IiB5Mj0iMTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-alert\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleAlert = createLucideIcon('circle-alert', __iconNode);\n\nexport default CircleAlert;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjE;QAAC,MAAA,CAAQ;QAAA,CAAA;YAAE,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,IAAM,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACvE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 1455, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/wifi.js", "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/node_modules/lucide-react/src/icons/wifi.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 20h.01', key: 'zekei9' }],\n  ['path', { d: 'M2 8.82a15 15 0 0 1 20 0', key: 'dnpr2z' }],\n  ['path', { d: 'M5 12.859a10 10 0 0 1 14 0', key: '1x1e6c' }],\n  ['path', { d: 'M8.5 16.429a5 5 0 0 1 7 0', key: '1bycff' }],\n];\n\n/**\n * @component @name Wifi\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMjBoLjAxIiAvPgogIDxwYXRoIGQ9Ik0yIDguODJhMTUgMTUgMCAwIDEgMjAgMCIgLz4KICA8cGF0aCBkPSJNNSAxMi44NTlhMTAgMTAgMCAwIDEgMTQgMCIgLz4KICA8cGF0aCBkPSJNOC41IDE2LjQyOWE1IDUgMCAwIDEgNyAwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/wifi\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Wifi = createLucideIcon('wifi', __iconNode);\n\nexport default Wifi;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA4B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3D;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC5D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 1511, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/wifi-off.js", "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/node_modules/lucide-react/src/icons/wifi-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 20h.01', key: 'zekei9' }],\n  ['path', { d: 'M8.5 16.429a5 5 0 0 1 7 0', key: '1bycff' }],\n  ['path', { d: 'M5 12.859a10 10 0 0 1 5.17-2.69', key: '1dl1wf' }],\n  ['path', { d: 'M19 12.859a10 10 0 0 0-2.007-1.523', key: '4k23kn' }],\n  ['path', { d: 'M2 8.82a15 15 0 0 1 4.177-2.643', key: '1grhjp' }],\n  ['path', { d: 'M22 8.82a15 15 0 0 0-11.288-3.764', key: 'z3jwby' }],\n  ['path', { d: 'm2 2 20 20', key: '1ooewy' }],\n];\n\n/**\n * @component @name WifiOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMjBoLjAxIiAvPgogIDxwYXRoIGQ9Ik04LjUgMTYuNDI5YTUgNSAwIDAgMSA3IDAiIC8+CiAgPHBhdGggZD0iTTUgMTIuODU5YTEwIDEwIDAgMCAxIDUuMTctMi42OSIgLz4KICA8cGF0aCBkPSJNMTkgMTIuODU5YTEwIDEwIDAgMCAwLTIuMDA3LTEuNTIzIiAvPgogIDxwYXRoIGQ9Ik0yIDguODJhMTUgMTUgMCAwIDEgNC4xNzctMi42NDMiIC8+CiAgPHBhdGggZD0iTTIyIDguODJhMTUgMTUgMCAwIDAtMTEuMjg4LTMuNzY0IiAvPgogIDxwYXRoIGQ9Im0yIDIgMjAgMjAiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/wifi-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst WifiOff = createLucideIcon('wifi-off', __iconNode);\n\nexport default WifiOff;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1D;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAmC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAChE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAsC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACnE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAmC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAChE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAqC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClE;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC7C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAU,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 1588, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/refresh-cw.js", "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/node_modules/lucide-react/src/icons/refresh-cw.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8', key: 'v9h5vc' }],\n  ['path', { d: 'M21 3v5h-5', key: '1q7to0' }],\n  ['path', { d: 'M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16', key: '3uifl3' }],\n  ['path', { d: 'M8 16H3v5', key: '1cv678' }],\n];\n\n/**\n * @component @name RefreshCw\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAxMmE5IDkgMCAwIDEgOS05IDkuNzUgOS43NSAwIDAgMSA2Ljc0IDIuNzRMMjEgOCIgLz4KICA8cGF0aCBkPSJNMjEgM3Y1aC01IiAvPgogIDxwYXRoIGQ9Ik0yMSAxMmE5IDkgMCAwIDEtOSA5IDkuNzUgOS43NSAwIDAgMS02Ljc0LTIuNzRMMyAxNiIgLz4KICA8cGF0aCBkPSJNOCAxNkgzdjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/refresh-cw\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RefreshCw = createLucideIcon('refresh-cw', __iconNode);\n\nexport default RefreshCw;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAsD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACnF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAuD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACpF;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC5C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 1644, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/calendar.js", "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/node_modules/lucide-react/src/icons/calendar.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M8 2v4', key: '1cmpym' }],\n  ['path', { d: 'M16 2v4', key: '4m81vk' }],\n  ['rect', { width: '18', height: '18', x: '3', y: '4', rx: '2', key: '1hopcy' }],\n  ['path', { d: 'M3 10h18', key: '8toen8' }],\n];\n\n/**\n * @component @name Calendar\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOCAydjQiIC8+CiAgPHBhdGggZD0iTTE2IDJ2NCIgLz4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0zIDEwaDE4IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/calendar\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Calendar = createLucideIcon('calendar', __iconNode);\n\nexport default Calendar;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACvC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;YAAM,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9E;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 1704, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/target.js", "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/node_modules/lucide-react/src/icons/target.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['circle', { cx: '12', cy: '12', r: '6', key: '1vlfrh' }],\n  ['circle', { cx: '12', cy: '12', r: '2', key: '1c9p78' }],\n];\n\n/**\n * @component @name Target\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSI2IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/target\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Target = createLucideIcon('target', __iconNode);\n\nexport default Target;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACxD;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 1759, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/activity.js", "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/node_modules/lucide-react/src/icons/activity.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2',\n      key: '169zse',\n    },\n  ],\n];\n\n/**\n * @component @name Activity\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjIgMTJoLTIuNDhhMiAyIDAgMCAwLTEuOTMgMS40NmwtMi4zNSA4LjM2YS4yNS4yNSAwIDAgMS0uNDggMEw5LjI0IDIuMThhLjI1LjI1IDAgMCAwLS40OCAwbC0yLjM1IDguMzZBMiAyIDAgMCAxIDQuNDkgMTJIMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/activity\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Activity = createLucideIcon('activity', __iconNode);\n\nexport default Activity;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 1794, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/plus.js", "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/node_modules/lucide-react/src/icons/plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'M12 5v14', key: 's699le' }],\n];\n\n/**\n * @component @name Plus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJNMTIgNXYxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Plus = createLucideIcon('plus', __iconNode);\n\nexport default Plus;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}]}