// Weekly High Signal Service - Identifies stocks hitting weekly highs
import { yahooFinanceService } from './yahoo-finance';
import { stockNamesService } from './stock-names-service';
import { NIFTY_200_SYMBOLS, getYahooSymbol } from './nifty-stocks';
import { cacheService, CacheKeys } from './cache-service';

export interface WeeklyHighSignal {
  symbol: string;
  name: string;
  currentPrice: number;
  weeklyHigh: number;
  weeklyLow: number;
  breakoutPrice: number;
  volume: number;
  avgVolume: number;
  volumeRatio: number;
  strength: 'STRONG' | 'MODERATE' | 'WEAK';
  signalTime: Date;
  status: 'ACTIVE' | 'TRIGGERED' | 'EXPIRED';
  daysFromHigh: number;
  priceFromHigh: number;
  priceFromHighPercent: number;
}

export interface WeeklyHighConfig {
  lookbackDays: number;
  minVolumeRatio: number;
  signalExpiryHours: number;
  strongSignalThreshold: number; // % from weekly high
  moderateSignalThreshold: number; // % from weekly high
}

class WeeklyHighSignalService {
  private config: WeeklyHighConfig = {
    lookbackDays: 7,
    minVolumeRatio: 1.0, // Reduced from 1.2 to be more inclusive
    signalExpiryHours: 24,
    strongSignalThreshold: 3.0, // Within 3% of weekly high (more realistic)
    moderateSignalThreshold: 7.0, // Within 7% of weekly high (more inclusive)
  };

  private signals: WeeklyHighSignal[] = [];
  private lastUpdate: Date | null = null;
  private updateListeners = new Set<(signals: WeeklyHighSignal[]) => void>();

  // Update configuration
  updateConfig(newConfig: Partial<WeeklyHighConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('📊 Weekly high signal config updated:', this.config);
    
    // Trigger re-scan with new config
    this.scanForSignals();
  }

  // Get current configuration
  getConfig(): WeeklyHighConfig {
    return { ...this.config };
  }

  // Add listener for signal updates
  addListener(callback: (signals: WeeklyHighSignal[]) => void): void {
    this.updateListeners.add(callback);
    console.log(`📊 Listener added. Total listeners: ${this.updateListeners.size}`);
  }

  // Remove listener
  removeListener(callback: (signals: WeeklyHighSignal[]) => void): void {
    this.updateListeners.delete(callback);
    console.log(`📊 Listener removed. Total listeners: ${this.updateListeners.size}`);
  }

  // Notify all listeners
  private notifyListeners(): void {
    console.log(`📊 Notifying ${this.updateListeners.size} listeners with ${this.signals.length} signals`);
    this.updateListeners.forEach(callback => {
      try {
        callback([...this.signals]);
      } catch (error) {
        console.error('❌ Error in weekly high signal listener:', error);
      }
    });
  }

  // Calculate weekly high signal for a stock
  private async calculateWeeklyHighSignal(symbol: string): Promise<WeeklyHighSignal | null> {
    try {
      // Get current quote first
      const currentQuote = await yahooFinanceService.getQuoteWithCachedName(symbol);
      if (!currentQuote || currentQuote.price <= 0) {
        console.warn(`No valid quote found for ${symbol}`);
        return null;
      }

      // Get historical data for the past week + buffer
      const historicalData = await yahooFinanceService.getHistoricalData(
        symbol,
        this.config.lookbackDays + 2
      );

      if (!historicalData || historicalData.length < 3) {
        console.warn(`Insufficient historical data for ${symbol}`);
        return null;
      }

      // Calculate weekly metrics
      const weeklyData = historicalData.slice(-this.config.lookbackDays); // Take last N days
      const weeklyHigh = Math.max(...weeklyData.map(d => d.high));
      const weeklyLow = Math.min(...weeklyData.map(d => d.low));

      // Find the day of weekly high (from the end, so 0 = today)
      const highDayIndex = weeklyData.findIndex(d => d.high === weeklyHigh);
      const daysFromHigh = weeklyData.length - 1 - highDayIndex; // Days ago

      // Calculate price distance from weekly high
      const priceFromHigh = weeklyHigh - currentQuote.price;
      const priceFromHighPercent = Math.abs((priceFromHigh / weeklyHigh) * 100);

      // Calculate volume metrics
      const avgVolume = weeklyData.reduce((sum, d) => sum + (d.volume || 1000000), 0) / weeklyData.length;
      const currentVolume = currentQuote.volume || avgVolume;
      const volumeRatio = currentVolume / avgVolume;

      // Determine signal strength based on proximity to weekly high
      let strength: 'STRONG' | 'MODERATE' | 'WEAK';
      if (priceFromHighPercent <= this.config.strongSignalThreshold) {
        strength = 'STRONG';
      } else if (priceFromHighPercent <= this.config.moderateSignalThreshold) {
        strength = 'MODERATE';
      } else {
        strength = 'WEAK';
      }

      // Determine status - more inclusive logic
      let status: 'ACTIVE' | 'TRIGGERED' | 'EXPIRED';
      if (currentQuote.price >= weeklyHigh * 0.995) { // Within 0.5% of weekly high
        status = 'TRIGGERED';
      } else if (priceFromHighPercent <= 10.0) { // Within 10% of weekly high
        status = 'ACTIVE';
      } else {
        status = 'EXPIRED';
      }

      // Calculate breakout target (weekly high + 1%)
      const breakoutPrice = weeklyHigh * 1.01;

      const signal: WeeklyHighSignal = {
        symbol,
        name: currentQuote.name,
        currentPrice: currentQuote.price,
        weeklyHigh,
        weeklyLow,
        breakoutPrice,
        volume: currentVolume,
        avgVolume,
        volumeRatio,
        strength,
        signalTime: new Date(),
        status,
        daysFromHigh,
        priceFromHigh,
        priceFromHighPercent
      };

      console.log(`📊 ${symbol}: High=₹${weeklyHigh.toFixed(2)}, Current=₹${currentQuote.price.toFixed(2)}, Distance=${priceFromHighPercent.toFixed(2)}%, Status=${status}`);

      return signal;

    } catch (error) {
      console.error(`❌ Error calculating weekly high signal for ${symbol}:`, error);
      return null;
    }
  }

  // Scan all Nifty 200 stocks for weekly high signals
  async scanForSignals(): Promise<WeeklyHighSignal[]> {
    console.log('🔍 Scanning for weekly high signals...');

    try {
      const allSymbols = NIFTY_200_SYMBOLS.map(symbol => getYahooSymbol(symbol));
      const signals: WeeklyHighSignal[] = [];
      let processedCount = 0;
      let errorCount = 0;

      // Process in smaller batches to avoid overwhelming the API
      const batchSize = 5; // Reduced batch size
      for (let i = 0; i < allSymbols.length; i += batchSize) {
        const batch = allSymbols.slice(i, i + batchSize);
        console.log(`📊 Processing batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(allSymbols.length/batchSize)}: ${batch.join(', ')}`);

        // Process batch with individual error handling
        for (const symbol of batch) {
          try {
            const signal = await this.calculateWeeklyHighSignal(symbol);
            if (signal) {
              signals.push(signal);
            }
            processedCount++;
          } catch (error) {
            console.error(`❌ Error processing ${symbol}:`, error);
            errorCount++;
          }
        }

        // Delay between batches to be respectful to the API
        if (i + batchSize < allSymbols.length) {
          await new Promise(resolve => setTimeout(resolve, 500)); // Increased delay
        }
      }

      console.log(`📊 Processed ${processedCount} stocks, ${errorCount} errors, ${signals.length} signals found`);

      // Log sample signals for debugging
      if (signals.length > 0) {
        console.log('📊 Sample signals before filtering:');
        signals.slice(0, 5).forEach(signal => {
          console.log(`  ${signal.symbol}: ${signal.strength} (${signal.priceFromHighPercent.toFixed(2)}% from high, ${signal.status})`);
        });
      }

      // Filter signals based on criteria (much more inclusive)
      const filteredSignals = signals.filter(signal =>
        signal.status === 'TRIGGERED' ||
        signal.status === 'ACTIVE' ||
        signal.strength === 'STRONG' ||
        signal.strength === 'MODERATE' ||
        signal.priceFromHighPercent <= 15.0 // Include signals within 15% of weekly high
      );

      console.log(`📊 After filtering: ${filteredSignals.length} signals (from ${signals.length} total)`);

      // Log sample filtered signals
      if (filteredSignals.length > 0) {
        console.log('📊 Sample filtered signals:');
        filteredSignals.slice(0, 5).forEach(signal => {
          console.log(`  ${signal.symbol}: ${signal.strength} (${signal.priceFromHighPercent.toFixed(2)}% from high, ${signal.status})`);
        });
      }

      // Sort by strength and proximity to weekly high
      filteredSignals.sort((a, b) => {
        // First by status (TRIGGERED > ACTIVE > EXPIRED)
        const statusOrder = { 'TRIGGERED': 3, 'ACTIVE': 2, 'EXPIRED': 1 };
        if (statusOrder[a.status] !== statusOrder[b.status]) {
          return statusOrder[b.status] - statusOrder[a.status];
        }

        // Then by strength
        const strengthOrder = { 'STRONG': 3, 'MODERATE': 2, 'WEAK': 1 };
        if (strengthOrder[a.strength] !== strengthOrder[b.strength]) {
          return strengthOrder[b.strength] - strengthOrder[a.strength];
        }

        // Finally by proximity to weekly high
        return a.priceFromHighPercent - b.priceFromHighPercent;
      });

      this.signals = filteredSignals;
      this.lastUpdate = new Date();

      console.log(`✅ Found ${filteredSignals.length} weekly high signals (${signals.length} total processed)`);

      // Cache results
      cacheService.set('weekly_high_signals', filteredSignals, 5 * 60 * 1000); // 5 minutes

      // Notify listeners
      this.notifyListeners();

      return filteredSignals;

    } catch (error) {
      console.error('❌ Error scanning for weekly high signals:', error);
      return [];
    }
  }

  // Get current signals (from cache if available)
  getSignals(): WeeklyHighSignal[] {
    console.log(`📊 Service getSignals() called: returning ${this.signals.length} signals`);
    return [...this.signals];
  }

  // Get signals with optional filtering
  getFilteredSignals(status?: 'ACTIVE' | 'TRIGGERED' | 'EXPIRED'): WeeklyHighSignal[] {
    if (!status) return this.getSignals();
    return this.signals.filter(signal => signal.status === status);
  }

  // Get signal statistics
  getSignalStats() {
    const total = this.signals.length;
    const active = this.signals.filter(s => s.status === 'ACTIVE').length;
    const triggered = this.signals.filter(s => s.status === 'TRIGGERED').length;
    const expired = this.signals.filter(s => s.status === 'EXPIRED').length;
    
    const strong = this.signals.filter(s => s.strength === 'STRONG').length;
    const moderate = this.signals.filter(s => s.strength === 'MODERATE').length;
    const weak = this.signals.filter(s => s.strength === 'WEAK').length;

    return {
      total,
      active,
      triggered,
      expired,
      strong,
      moderate,
      weak,
      lastUpdate: this.lastUpdate
    };
  }

  // Force refresh signals
  async refreshSignals(): Promise<WeeklyHighSignal[]> {
    return await this.scanForSignals();
  }
}

// Create singleton instance
export const weeklyHighSignalService = new WeeklyHighSignalService();

// Auto-initialize on first import
setTimeout(() => {
  weeklyHighSignalService.scanForSignals();
}, 1000);
