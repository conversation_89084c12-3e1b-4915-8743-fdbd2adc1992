(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/hooks/useCentralData.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// React Hook for Central Data Manager Integration
// Provides real-time data access and automatic updates for all stock-related pages
__turbopack_context__.s({
    "useBOHEligibleStocks": ()=>useBOHEligibleStocks,
    "useCentralData": ()=>useCentralData,
    "useGTTOrders": ()=>useGTTOrders,
    "useNifty200Stocks": ()=>useNifty200Stocks,
    "useWeeklyHighSignals": ()=>useWeeklyHighSignals
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$service$2d$initializer$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/service-initializer.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/central-data-manager.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature();
;
;
function useCentralData() {
    _s();
    const [nifty200, setNifty200] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        data: [],
        lastUpdated: null,
        isLoading: false,
        error: null
    });
    const [bohEligible, setBohEligible] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        data: [],
        lastUpdated: null,
        isLoading: false,
        error: null
    });
    const [weeklyHighSignals, setWeeklyHighSignals] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        data: [],
        lastUpdated: null,
        isLoading: false,
        error: null
    });
    const [gttOrders, setGttOrders] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        data: [],
        lastUpdated: null,
        isLoading: false,
        error: null
    });
    const [isInitialized, setIsInitialized] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isServiceRunning, setIsServiceRunning] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const pollingIntervals = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(new Map());
    // Fetch data directly from services
    const fetchData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCentralData.useCallback[fetchData]": async (dataType)=>{
            try {
                let data = [];
                let lastUpdated = null;
                switch(dataType){
                    case 'nifty200':
                        data = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].getNifty200Stocks();
                        lastUpdated = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].getLastUpdated('nifty200');
                        setNifty200({
                            data,
                            lastUpdated,
                            isLoading: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].isDataLoading('nifty200'),
                            error: null
                        });
                        break;
                    case 'bohEligible':
                        data = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].getBOHEligibleStocks();
                        lastUpdated = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].getLastUpdated('bohEligible');
                        setBohEligible({
                            data,
                            lastUpdated,
                            isLoading: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].isDataLoading('bohEligible'),
                            error: null
                        });
                        break;
                    case 'weeklyHighSignals':
                        data = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].getWeeklyHighSignals();
                        lastUpdated = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].getLastUpdated('weeklyHighSignals');
                        setWeeklyHighSignals({
                            data,
                            lastUpdated,
                            isLoading: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].isDataLoading('weeklyHighSignals'),
                            error: null
                        });
                        break;
                    case 'gttOrders':
                        data = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].getGTTOrders();
                        lastUpdated = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].getLastUpdated('gttOrders');
                        setGttOrders({
                            data,
                            lastUpdated,
                            isLoading: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].isDataLoading('gttOrders'),
                            error: null
                        });
                        break;
                }
                console.log("📊 Updated ".concat(dataType, ": ").concat(data.length, " items"));
            } catch (error) {
                console.error("❌ Error fetching ".concat(dataType, ":"), error);
                const errorState = {
                    data: [],
                    lastUpdated: null,
                    isLoading: false,
                    error: error instanceof Error ? error.message : 'Unknown error'
                };
                switch(dataType){
                    case 'nifty200':
                        setNifty200({
                            "useCentralData.useCallback[fetchData]": (prev)=>({
                                    ...prev,
                                    ...errorState
                                })
                        }["useCentralData.useCallback[fetchData]"]);
                        break;
                    case 'bohEligible':
                        setBohEligible({
                            "useCentralData.useCallback[fetchData]": (prev)=>({
                                    ...prev,
                                    ...errorState
                                })
                        }["useCentralData.useCallback[fetchData]"]);
                        break;
                    case 'weeklyHighSignals':
                        setWeeklyHighSignals({
                            "useCentralData.useCallback[fetchData]": (prev)=>({
                                    ...prev,
                                    ...errorState
                                })
                        }["useCentralData.useCallback[fetchData]"]);
                        break;
                    case 'gttOrders':
                        setGttOrders({
                            "useCentralData.useCallback[fetchData]": (prev)=>({
                                    ...prev,
                                    ...errorState
                                })
                        }["useCentralData.useCallback[fetchData]"]);
                        break;
                }
            }
        }
    }["useCentralData.useCallback[fetchData]"], []);
    // Check service status directly
    const checkServiceStatus = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCentralData.useCallback[checkServiceStatus]": ()=>{
            try {
                const status = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].getStatus();
                setIsInitialized(status.isInitialized);
                setIsServiceRunning(status.isRunning);
                console.log('📊 Service status:', {
                    initialized: status.isInitialized,
                    running: status.isRunning,
                    marketOpen: status.isMarketOpen
                });
            } catch (error) {
                console.error('❌ Error checking service status:', error);
                setIsInitialized(false);
                setIsServiceRunning(false);
            }
        }
    }["useCentralData.useCallback[checkServiceStatus]"], []);
    // Initialize service directly if not already initialized
    const initializeService = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCentralData.useCallback[initializeService]": async ()=>{
            try {
                console.log('🚀 Initializing Central Data Manager directly...');
                if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].getStatus().isInitialized) {
                    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].initialize();
                    console.log('✅ Central Data Manager initialized');
                }
                if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].getStatus().isRunning) {
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].start();
                    console.log('✅ Central Data Manager started');
                }
                setIsInitialized(true);
                setIsServiceRunning(true);
            } catch (error) {
                console.error('❌ Error initializing service:', error);
                setIsInitialized(false);
                setIsServiceRunning(false);
            }
        }
    }["useCentralData.useCallback[initializeService]"], []);
    // Refresh specific data type
    const refreshData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCentralData.useCallback[refreshData]": async (dataType)=>{
            if (dataType) {
                await fetchData(dataType);
            } else {
                // Refresh all data
                await Promise.all([
                    fetchData('nifty200'),
                    fetchData('bohEligible'),
                    fetchData('weeklyHighSignals'),
                    fetchData('gttOrders')
                ]);
            }
        }
    }["useCentralData.useCallback[refreshData]"], [
        fetchData
    ]);
    // Set up polling for real-time updates
    const setupPolling = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCentralData.useCallback[setupPolling]": ()=>{
            // Clear existing intervals
            pollingIntervals.current.forEach({
                "useCentralData.useCallback[setupPolling]": (interval)=>clearInterval(interval)
            }["useCentralData.useCallback[setupPolling]"]);
            pollingIntervals.current.clear();
            // Set up new intervals
            const intervals = {
                nifty200: 30000,
                bohEligible: 60000,
                weeklyHighSignals: 300000,
                gttOrders: 30000 // 30 seconds
            };
            Object.entries(intervals).forEach({
                "useCentralData.useCallback[setupPolling]": (param)=>{
                    let [dataType, interval] = param;
                    const intervalId = setInterval({
                        "useCentralData.useCallback[setupPolling].intervalId": ()=>{
                            fetchData(dataType);
                        }
                    }["useCentralData.useCallback[setupPolling].intervalId"], interval);
                    pollingIntervals.current.set(dataType, intervalId);
                }
            }["useCentralData.useCallback[setupPolling]"]);
            console.log('⏰ Polling intervals set up for real-time updates');
        }
    }["useCentralData.useCallback[setupPolling]"], [
        fetchData
    ]);
    // Set up data listeners for real-time updates
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useCentralData.useEffect": ()=>{
            console.log('🔗 Setting up Central Data Manager listeners...');
            // Add listeners for each data type
            const nifty200Listener = {
                "useCentralData.useEffect.nifty200Listener": (data, timestamp)=>{
                    setNifty200({
                        data,
                        lastUpdated: timestamp,
                        isLoading: false,
                        error: null
                    });
                    console.log("📊 Nifty200 data updated: ".concat(data.length, " stocks"));
                }
            }["useCentralData.useEffect.nifty200Listener"];
            const bohEligibleListener = {
                "useCentralData.useEffect.bohEligibleListener": (data, timestamp)=>{
                    setBohEligible({
                        data,
                        lastUpdated: timestamp,
                        isLoading: false,
                        error: null
                    });
                    console.log("📊 BOH Eligible data updated: ".concat(data.length, " stocks"));
                }
            }["useCentralData.useEffect.bohEligibleListener"];
            const weeklyHighSignalsListener = {
                "useCentralData.useEffect.weeklyHighSignalsListener": (data, timestamp)=>{
                    setWeeklyHighSignals({
                        data,
                        lastUpdated: timestamp,
                        isLoading: false,
                        error: null
                    });
                    console.log("📊 Weekly High Signals updated: ".concat(data.length, " signals"));
                }
            }["useCentralData.useEffect.weeklyHighSignalsListener"];
            const gttOrdersListener = {
                "useCentralData.useEffect.gttOrdersListener": (data, timestamp)=>{
                    setGttOrders({
                        data,
                        lastUpdated: timestamp,
                        isLoading: false,
                        error: null
                    });
                    console.log("📊 GTT Orders updated: ".concat(data.length, " orders"));
                }
            }["useCentralData.useEffect.gttOrdersListener"];
            // Add listeners to Central Data Manager
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].addListener('nifty200', nifty200Listener);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].addListener('bohEligible', bohEligibleListener);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].addListener('weeklyHighSignals', weeklyHighSignalsListener);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].addListener('gttOrders', gttOrdersListener);
            // Initialize and load initial data
            const initialize = {
                "useCentralData.useEffect.initialize": async ()=>{
                    checkServiceStatus();
                    if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].getStatus().isInitialized) {
                        await initializeService();
                    }
                    // Load initial data
                    await refreshData();
                }
            }["useCentralData.useEffect.initialize"];
            initialize();
            // Cleanup listeners on unmount
            return ({
                "useCentralData.useEffect": ()=>{
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].removeListener('nifty200', nifty200Listener);
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].removeListener('bohEligible', bohEligibleListener);
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].removeListener('weeklyHighSignals', weeklyHighSignalsListener);
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$central$2d$data$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["centralDataManager"].removeListener('gttOrders', gttOrdersListener);
                    pollingIntervals.current.forEach({
                        "useCentralData.useEffect": (interval)=>clearInterval(interval)
                    }["useCentralData.useEffect"]);
                    pollingIntervals.current.clear();
                }
            })["useCentralData.useEffect"];
        }
    }["useCentralData.useEffect"], []);
    // Re-setup polling when service status changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useCentralData.useEffect": ()=>{
            if (isServiceRunning) {
                setupPolling();
            }
        }
    }["useCentralData.useEffect"], [
        isServiceRunning,
        setupPolling
    ]);
    return {
        nifty200,
        bohEligible,
        weeklyHighSignals,
        gttOrders,
        refreshData,
        isInitialized,
        isServiceRunning
    };
}
_s(useCentralData, "ocqP2Cajj1A1LQiUHchJlHIA+9I=");
function useNifty200Stocks() {
    _s1();
    const { nifty200, refreshData } = useCentralData();
    return {
        stocks: nifty200.data,
        lastUpdated: nifty200.lastUpdated,
        isLoading: nifty200.isLoading,
        error: nifty200.error,
        refresh: ()=>refreshData('nifty200')
    };
}
_s1(useNifty200Stocks, "hh1p3yrpgwOccNG5BxY+v1fol3k=", false, function() {
    return [
        useCentralData
    ];
});
function useBOHEligibleStocks() {
    _s2();
    const { bohEligible, refreshData } = useCentralData();
    return {
        stocks: bohEligible.data,
        lastUpdated: bohEligible.lastUpdated,
        isLoading: bohEligible.isLoading,
        error: bohEligible.error,
        refresh: ()=>refreshData('bohEligible')
    };
}
_s2(useBOHEligibleStocks, "ytLLh888rLsxvRKIg1QsrNHb7W0=", false, function() {
    return [
        useCentralData
    ];
});
function useWeeklyHighSignals() {
    _s3();
    const { weeklyHighSignals, refreshData } = useCentralData();
    return {
        signals: weeklyHighSignals.data,
        lastUpdated: weeklyHighSignals.lastUpdated,
        isLoading: weeklyHighSignals.isLoading,
        error: weeklyHighSignals.error,
        refresh: ()=>refreshData('weeklyHighSignals')
    };
}
_s3(useWeeklyHighSignals, "ENRZAmJYVh9S2v8soViOX18A5Y4=", false, function() {
    return [
        useCentralData
    ];
});
function useGTTOrders() {
    _s4();
    const { gttOrders, refreshData } = useCentralData();
    return {
        orders: gttOrders.data,
        lastUpdated: gttOrders.lastUpdated,
        isLoading: gttOrders.isLoading,
        error: gttOrders.error,
        refresh: ()=>refreshData('gttOrders')
    };
}
_s4(useGTTOrders, "S0WK/PRCgyd3/me34GBgMWdmcsc=", false, function() {
    return [
        useCentralData
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/dashboard/gtt-orders/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>GTTOrdersPage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-client] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trending-up.js [app-client] (ecmascript) <export default as TrendingUp>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingDown$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trending-down.js [app-client] (ecmascript) <export default as TrendingDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-check-big.js [app-client] (ecmascript) <export default as CheckCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__XCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-x.js [app-client] (ecmascript) <export default as XCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-alert.js [app-client] (ecmascript) <export default as AlertCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wifi$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wifi$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/wifi.js [app-client] (ecmascript) <export default as Wifi>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wifi$2d$off$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__WifiOff$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/wifi-off.js [app-client] (ecmascript) <export default as WifiOff>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/refresh-cw.js [app-client] (ecmascript) <export default as RefreshCw>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/calendar.js [app-client] (ecmascript) <export default as Calendar>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$target$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Target$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/target.js [app-client] (ecmascript) <export default as Target>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$activity$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Activity$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/activity.js [app-client] (ecmascript) <export default as Activity>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/plus.js [app-client] (ecmascript) <export default as Plus>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/settings.js [app-client] (ecmascript) <export default as Settings>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$play$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Play$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/play.js [app-client] (ecmascript) <export default as Play>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$pause$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Pause$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/pause.js [app-client] (ecmascript) <export default as Pause>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bell$2d$ring$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BellRing$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/bell-ring.js [app-client] (ecmascript) <export default as BellRing>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useCentralData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useCentralData.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
function GTTOrdersPage() {
    _s();
    const [activeTab, setActiveTab] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('SIGNAL');
    // Central Data Manager for real-time GTT orders
    const { orders: centralGTTOrders, lastUpdated: gttLastUpdated, isLoading: gttIsLoading, error: gttError, refresh: refreshGTTOrders } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useCentralData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useGTTOrders"])();
    const [orders, setOrders] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([
        // Sample GTT Buy on Signal orders
        {
            id: '1',
            symbol: 'RELIANCE',
            name: 'Reliance Industries Ltd',
            orderType: 'BUY',
            triggerPrice: 2400.05,
            quantity: 8,
            status: 'PENDING',
            createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
            source: 'SIGNAL'
        },
        {
            id: '2',
            symbol: 'TCS',
            name: 'Tata Consultancy Services',
            orderType: 'BUY',
            triggerPrice: 3200.05,
            quantity: 6,
            status: 'TRIGGERED',
            createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
            source: 'SIGNAL'
        },
        // Sample GTT Buy on Holding orders
        {
            id: '3',
            symbol: 'INFY',
            name: 'Infosys Limited',
            orderType: 'BUY',
            triggerPrice: 1350.00,
            quantity: 14,
            status: 'PENDING',
            createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
            source: 'HOLDING'
        },
        // Sample GTT Sale orders
        {
            id: '4',
            symbol: 'WIPRO',
            name: 'Wipro Limited',
            orderType: 'SELL',
            triggerPrice: 450.00,
            quantity: 44,
            status: 'PENDING',
            createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
            source: 'SALE'
        }
    ]);
    const [isAngelOneConnected, setIsAngelOneConnected] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [lastSync, setLastSync] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isCreatingOrders, setIsCreatingOrders] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [createOrdersError, setCreateOrdersError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Automatic GTT Service state
    const [autoGTTEnabled, setAutoGTTEnabled] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [autoGTTStatus, setAutoGTTStatus] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [showAutoGTTSettings, setShowAutoGTTSettings] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [autoGTTNotifications, setAutoGTTNotifications] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    // Filter orders by active tab
    const filteredOrders = orders.filter((order)=>order.source === activeTab);
    // Function to fetch BOH eligible stocks and create GTT orders using the API
    const fetchAndCreateGTTOrders = async ()=>{
        try {
            console.log('🔍 Fetching BOH eligible stocks and creating GTT orders via API...');
            // Use the dedicated API endpoint for creating signal orders
            const response = await fetch('/api/gtt/create-signal-orders', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            console.log("📡 API Response status: ".concat(response.status));
            if (!response.ok) {
                const errorText = await response.text();
                console.error("❌ API request failed: ".concat(response.status, " - ").concat(errorText));
                throw new Error("API request failed: ".concat(response.status));
            }
            const data = await response.json();
            console.log('📊 API Response data:', data);
            if (!data.success) {
                console.error('❌ API returned error:', data.error);
                throw new Error(data.error || 'Failed to fetch GTT order data');
            }
            console.log("✅ API returned ".concat(data.data.orders.length, " GTT orders to create"));
            console.log("📊 BOH Stats: Total BOH stocks: ".concat(data.data.totalBOHStocks, ", Valid for GTT: ").concat(data.data.validForGTT));
            if (data.data.stats.avgTriggerPrice > 0) {
                console.log("💰 Price Stats: Avg Trigger: ₹".concat(data.data.stats.avgTriggerPrice.toFixed(2), ", Total Value: ₹").concat(data.data.stats.totalValue.toFixed(2)));
            }
            return data.data.orders;
        } catch (error) {
            console.error('❌ Error fetching GTT orders from API:', error);
            throw error;
        }
    };
    // Function to create GTT orders for all BOH eligible stocks
    const createAllSignalOrders = async ()=>{
        setIsCreatingOrders(true);
        setCreateOrdersError(null);
        try {
            console.log('🚀 Starting to create GTT orders for all BOH eligible stocks...');
            // Fetch GTT order data from API
            const gttOrderRequests = await fetchAndCreateGTTOrders();
            console.log("🔍 Received ".concat(gttOrderRequests.length, " GTT order requests from API"));
            if (gttOrderRequests.length === 0) {
                const errorMsg = 'No BOH eligible stocks found for GTT orders. This could be due to:\n' + '• No stocks marked as BOH eligible\n' + '• All BOH stocks filtered out due to price/quantity constraints\n' + '• API data fetching issues';
                console.warn('⚠️ ' + errorMsg);
                setCreateOrdersError(errorMsg);
                return;
            }
            // Filter out stocks that already have pending signal orders
            const existingSignalSymbols = orders.filter((order)=>order.source === 'SIGNAL' && order.status === 'PENDING').map((order)=>order.symbol);
            console.log("🔍 Existing signal orders: ".concat(existingSignalSymbols.length, " symbols:"), existingSignalSymbols);
            const newOrderRequests = gttOrderRequests.filter((orderReq)=>!existingSignalSymbols.includes(orderReq.symbol));
            console.log("📊 Creating orders for ".concat(newOrderRequests.length, " new stocks (").concat(existingSignalSymbols.length, " already have orders)"));
            if (newOrderRequests.length === 0) {
                const errorMsg = "All ".concat(gttOrderRequests.length, " BOH eligible stocks already have pending GTT orders");
                console.warn('⚠️ ' + errorMsg);
                setCreateOrdersError(errorMsg);
                return;
            }
            // Create GTT orders for each stock
            const newOrders = [];
            let successCount = 0;
            let errorCount = 0;
            for (const orderReq of newOrderRequests){
                try {
                    const newOrder = {
                        id: "signal_".concat(Date.now(), "_").concat(Math.random().toString(36).substring(2, 11)),
                        symbol: orderReq.symbol,
                        name: orderReq.name,
                        orderType: orderReq.orderType,
                        triggerPrice: orderReq.triggerPrice,
                        quantity: orderReq.quantity,
                        status: 'PENDING',
                        createdAt: new Date(),
                        source: orderReq.source
                    };
                    newOrders.push(newOrder);
                    successCount++;
                    console.log("✅ Created GTT order for ".concat(orderReq.symbol, ": Trigger=₹").concat(orderReq.triggerPrice.toFixed(2), ", Qty=").concat(orderReq.quantity));
                } catch (error) {
                    console.error("❌ Failed to create order for ".concat(orderReq.symbol, ":"), error);
                    errorCount++;
                }
            }
            // Add new orders to the existing orders
            setOrders((prevOrders)=>[
                    ...prevOrders,
                    ...newOrders
                ]);
            setLastSync(new Date());
            console.log("🎉 Successfully created ".concat(successCount, " GTT orders (").concat(errorCount, " errors)"));
            // Show success message
            if (successCount > 0) {
                const totalValue = newOrders.reduce((sum, order)=>sum + order.triggerPrice * order.quantity, 0);
                const message = "🎉 Successfully created ".concat(successCount, " GTT orders for BOH eligible stocks!\n\n") + "📊 Summary:\n" + "• Total Orders: ".concat(successCount, "\n") + "• Total Investment: ₹".concat(totalValue.toLocaleString(), "\n") + "• Average Trigger Price: ₹".concat((totalValue / newOrders.reduce((sum, order)=>sum + order.quantity, 0)).toFixed(2), "\n\n") + "✅ All orders are now visible in the GTT Buy on Signal tab.";
                alert(message);
                console.log('🎉 GTT Orders Created Successfully:', {
                    successCount,
                    totalValue,
                    orders: newOrders.map((o)=>({
                            symbol: o.symbol,
                            trigger: o.triggerPrice,
                            qty: o.quantity
                        }))
                });
            }
            if (errorCount > 0) {
                const errorMsg = "Created ".concat(successCount, " orders successfully, but ").concat(errorCount, " failed");
                console.warn('⚠️ Some GTT orders failed:', errorMsg);
                setCreateOrdersError(errorMsg);
            }
        } catch (error) {
            console.error('❌ Error creating signal orders:', error);
            setCreateOrdersError(error instanceof Error ? error.message : 'Failed to create orders');
        } finally{
            setIsCreatingOrders(false);
        }
    };
    // Function to clear all pending signal orders (for weekly cleanup)
    const clearPendingSignalOrders = ()=>{
        setOrders((prevOrders)=>prevOrders.filter((order)=>!(order.source === 'SIGNAL' && order.status === 'PENDING')));
        console.log('🧹 Cleared all pending signal orders');
    };
    // Function to get signal orders statistics
    const getSignalOrdersStats = ()=>{
        const signalOrders = orders.filter((order)=>order.source === 'SIGNAL');
        return {
            total: signalOrders.length,
            pending: signalOrders.filter((order)=>order.status === 'PENDING').length,
            triggered: signalOrders.filter((order)=>order.status === 'TRIGGERED').length,
            cancelled: signalOrders.filter((order)=>order.status === 'CANCELLED').length,
            expired: signalOrders.filter((order)=>order.status === 'EXPIRED').length
        };
    };
    // Automatic GTT Service functions
    const initializeAutoGTTService = async ()=>{
        try {
            const response = await fetch('/api/auto-gtt/initialize', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            const data = await response.json();
            if (data.success) {
                console.log('✅ Auto GTT Service initialized');
                setAutoGTTEnabled(data.data.service.isEnabled);
                addAutoGTTNotification('🚀 Automatic GTT order detection started');
            } else {
                console.error('❌ Failed to initialize Auto GTT Service:', data.error);
            }
        } catch (error) {
            console.error('❌ Error initializing auto GTT service:', error);
        }
    };
    const fetchAutoGTTStatus = async ()=>{
        try {
            const response = await fetch('/api/auto-gtt?action=status');
            const data = await response.json();
            if (data.success) {
                setAutoGTTStatus(data.data);
                setAutoGTTEnabled(data.data.service.isEnabled);
            }
        } catch (error) {
            console.error('❌ Error fetching auto GTT status:', error);
        }
    };
    const toggleAutoGTTService = async ()=>{
        try {
            const action = autoGTTEnabled ? 'stop' : 'start';
            const response = await fetch('/api/auto-gtt', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action
                })
            });
            const data = await response.json();
            if (data.success) {
                setAutoGTTEnabled(!autoGTTEnabled);
                await fetchAutoGTTStatus();
                const message = autoGTTEnabled ? '⏹️ Automatic GTT order creation stopped' : '🚀 Automatic GTT order creation started';
                addAutoGTTNotification(message);
            }
        } catch (error) {
            console.error('❌ Error toggling auto GTT service:', error);
        }
    };
    const addAutoGTTNotification = (message)=>{
        setAutoGTTNotifications((prev)=>[
                message,
                ...prev.slice(0, 4)
            ]); // Keep last 5 notifications
        // Auto-remove notification after 5 seconds
        setTimeout(()=>{
            setAutoGTTNotifications((prev)=>prev.filter((n)=>n !== message));
        }, 5000);
    };
    // Use Central Data Manager for real-time GTT orders
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GTTOrdersPage.useEffect": ()=>{
            if (centralGTTOrders.length > 0) {
                // Convert central GTT orders to display format
                const displayOrders = centralGTTOrders.map({
                    "GTTOrdersPage.useEffect.displayOrders": (centralOrder)=>({
                            id: centralOrder.id,
                            symbol: centralOrder.symbol,
                            name: centralOrder.name,
                            orderType: centralOrder.orderType,
                            triggerPrice: centralOrder.triggerPrice,
                            quantity: centralOrder.quantity,
                            status: centralOrder.status,
                            createdAt: new Date(centralOrder.createdAt),
                            source: centralOrder.source,
                            autoCreated: centralOrder.autoCreated,
                            signalStrength: centralOrder.signalStrength
                        })
                }["GTTOrdersPage.useEffect.displayOrders"]);
                setOrders(displayOrders);
                console.log("📊 Updated orders from Central Data Manager: ".concat(displayOrders.length, " orders"));
            }
        }
    }["GTTOrdersPage.useEffect"], [
        centralGTTOrders
    ]);
    // Legacy load auto GTT orders function (now uses Central Data Manager)
    const loadAutoGTTOrders = async ()=>{
        console.log('🔄 Refreshing GTT orders via Central Data Manager...');
        await refreshGTTOrders();
    };
    // Initialize data on component mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GTTOrdersPage.useEffect": ()=>{
            const initializeData = {
                "GTTOrdersPage.useEffect.initializeData": async ()=>{
                    console.log('🚀 Initializing GTT Orders page...');
                    // Initialize with empty orders
                    setOrders([]);
                    setLastSync(new Date());
                    // Initialize automatic GTT service first
                    await initializeAutoGTTService();
                    // Then load orders and status
                    await Promise.all([
                        loadAutoGTTOrders(),
                        fetchAutoGTTStatus()
                    ]);
                    console.log('✅ GTT Orders page initialization complete');
                }
            }["GTTOrdersPage.useEffect.initializeData"];
            initializeData();
            // Set up periodic status updates
            const statusInterval = setInterval(fetchAutoGTTStatus, 30000); // Every 30 seconds
            // Set up periodic order refresh
            const orderInterval = setInterval(loadAutoGTTOrders, 60000); // Every 60 seconds
            return ({
                "GTTOrdersPage.useEffect": ()=>{
                    clearInterval(statusInterval);
                    clearInterval(orderInterval);
                }
            })["GTTOrdersPage.useEffect"];
        }
    }["GTTOrdersPage.useEffect"], []);
    const getStatusColor = (status)=>{
        switch(status){
            case 'PENDING':
                return 'text-yellow-600 bg-yellow-100';
            case 'TRIGGERED':
                return 'text-green-600 bg-green-100';
            case 'CANCELLED':
                return 'text-red-600 bg-red-100';
            case 'EXPIRED':
                return 'text-gray-600 bg-gray-100';
            default:
                return 'text-gray-600 bg-gray-100';
        }
    };
    const getStatusIcon = (status)=>{
        switch(status){
            case 'PENDING':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                    className: "h-4 w-4"
                }, void 0, false, {
                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                    lineNumber: 452,
                    columnNumber: 30
                }, this);
            case 'TRIGGERED':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"], {
                    className: "h-4 w-4"
                }, void 0, false, {
                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                    lineNumber: 453,
                    columnNumber: 32
                }, this);
            case 'CANCELLED':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__XCircle$3e$__["XCircle"], {
                    className: "h-4 w-4"
                }, void 0, false, {
                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                    lineNumber: 454,
                    columnNumber: 32
                }, this);
            case 'EXPIRED':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__["AlertCircle"], {
                    className: "h-4 w-4"
                }, void 0, false, {
                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                    lineNumber: 455,
                    columnNumber: 30
                }, this);
            default:
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                    className: "h-4 w-4"
                }, void 0, false, {
                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                    lineNumber: 456,
                    columnNumber: 23
                }, this);
        }
    };
    const getTabInfo = (tab)=>{
        switch(tab){
            case 'SIGNAL':
                return {
                    title: 'GTT Buy on Signal',
                    description: 'Automated buy orders based on Weekly High Signal data',
                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$activity$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Activity$3e$__["Activity"], {
                        className: "h-5 w-5"
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                        lineNumber: 466,
                        columnNumber: 17
                    }, this),
                    automation: 'Auto-created continuously during market hours (9:15 AM - 3:30 PM)'
                };
            case 'HOLDING':
                return {
                    title: 'GTT Buy on Holding',
                    description: 'Additional buy orders at lower support levels for existing holdings',
                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingDown$3e$__["TrendingDown"], {
                        className: "h-5 w-5"
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                        lineNumber: 473,
                        columnNumber: 17
                    }, this),
                    automation: 'Auto-created when Ignorable Lower Price is calculated'
                };
            case 'SALE':
                return {
                    title: 'GTT Sale',
                    description: 'Sell orders triggered when target prices are reached',
                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$target$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Target$3e$__["Target"], {
                        className: "h-5 w-5"
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                        lineNumber: 480,
                        columnNumber: 17
                    }, this),
                    automation: 'Auto-created when Target Price is set for holdings'
                };
        }
    };
    const OrdersTable = ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-white rounded-lg shadow-sm border border-gray-200",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "overflow-x-auto",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
                    className: "min-w-full divide-y divide-gray-200",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
                            className: "bg-gray-50",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                        className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",
                                        children: "Stock"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                        lineNumber: 492,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                        className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",
                                        children: "Order Type"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                        lineNumber: 495,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                        className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",
                                        children: "Trigger Price"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                        lineNumber: 498,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                        className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",
                                        children: "Quantity"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                        lineNumber: 501,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                        className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",
                                        children: "Status"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                        lineNumber: 504,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                        className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",
                                        children: "Created Date"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                        lineNumber: 507,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                        className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",
                                        children: "Actions"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                        lineNumber: 510,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                lineNumber: 491,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                            lineNumber: 490,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                            className: "bg-white divide-y divide-gray-200",
                            children: filteredOrders.length > 0 ? filteredOrders.map((order)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                    className: "hover:bg-gray-50",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                            className: "px-6 py-4 whitespace-nowrap",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-sm font-medium text-gray-900",
                                                        children: order.symbol
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                        lineNumber: 521,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-sm text-gray-500 truncate max-w-xs",
                                                        children: order.name
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                        lineNumber: 522,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                lineNumber: 520,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 519,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                            className: "px-6 py-4 whitespace-nowrap",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "p-1 rounded-full mr-2 ".concat(order.orderType === 'BUY' ? 'bg-green-100' : 'bg-red-100'),
                                                        children: order.orderType === 'BUY' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__["TrendingUp"], {
                                                            className: "h-3 w-3 text-green-600"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                            lineNumber: 529,
                                                            columnNumber: 27
                                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingDown$3e$__["TrendingDown"], {
                                                            className: "h-3 w-3 text-red-600"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                            lineNumber: 531,
                                                            columnNumber: 27
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                        lineNumber: 527,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-sm font-medium ".concat(order.orderType === 'BUY' ? 'text-green-600' : 'text-red-600'),
                                                        children: order.orderType
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                        lineNumber: 534,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                lineNumber: 526,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 525,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                            className: "px-6 py-4 whitespace-nowrap text-sm text-gray-900",
                                            children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatCurrency"])(order.triggerPrice)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 539,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                            className: "px-6 py-4 whitespace-nowrap text-sm text-gray-900",
                                            children: order.quantity
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 542,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                            className: "px-6 py-4 whitespace-nowrap",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(getStatusColor(order.status)),
                                                children: [
                                                    getStatusIcon(order.status),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "ml-1",
                                                        children: order.status
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                        lineNumber: 548,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                lineNumber: 546,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 545,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                            className: "px-6 py-4 whitespace-nowrap text-sm text-gray-500",
                                            children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatDateTime"])(order.createdAt)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 551,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                            className: "px-6 py-4 whitespace-nowrap text-sm font-medium",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    className: "text-red-600 hover:text-red-900 mr-3",
                                                    children: "Cancel"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                    lineNumber: 555,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    className: "text-blue-600 hover:text-blue-900",
                                                    children: "View"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                    lineNumber: 558,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 554,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, order.id, true, {
                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                    lineNumber: 518,
                                    columnNumber: 17
                                }, this)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    colSpan: 7,
                                    className: "px-6 py-12 text-center",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-gray-500",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                                                className: "h-12 w-12 mx-auto mb-4 text-gray-300"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                lineNumber: 568,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                children: [
                                                    "No GTT orders found for ",
                                                    getTabInfo(activeTab).title,
                                                    "."
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                lineNumber: 569,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm mt-1",
                                                children: getTabInfo(activeTab).automation
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                lineNumber: 570,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                        lineNumber: 567,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                    lineNumber: 566,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                lineNumber: 565,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                            lineNumber: 515,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                    lineNumber: 489,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                lineNumber: 488,
                columnNumber: 7
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
            lineNumber: 487,
            columnNumber: 5
        }, this);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-between",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                className: "text-2xl font-bold text-gray-900",
                                children: "GTT Orders"
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                lineNumber: 586,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-gray-600 mt-1",
                                children: "Automated Good Till Triggered order management"
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                lineNumber: 587,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                        lineNumber: 585,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center space-x-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-2",
                                children: isAngelOneConnected ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center space-x-2 text-green-600",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wifi$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wifi$3e$__["Wifi"], {
                                            className: "h-4 w-4"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 594,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-sm font-medium",
                                            children: "Angel One Connected"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 595,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                    lineNumber: 593,
                                    columnNumber: 15
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center space-x-2 text-red-600",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wifi$2d$off$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__WifiOff$3e$__["WifiOff"], {
                                            className: "h-4 w-4"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 599,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-sm font-medium",
                                            children: "Angel One Disconnected"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 600,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                    lineNumber: 598,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                lineNumber: 591,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: "px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__["RefreshCw"], {
                                        className: "h-4 w-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                        lineNumber: 605,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "Sync Orders"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                        lineNumber: 606,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                lineNumber: 604,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                        lineNumber: 589,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                lineNumber: 584,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-blue-50 border border-blue-200 rounded-lg p-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"], {
                                        className: "h-5 w-5 text-blue-600"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                        lineNumber: 615,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm font-medium text-blue-900",
                                                children: "Continuous Automation Active"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                lineNumber: 617,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-xs text-blue-700",
                                                children: [
                                                    "Orders are automatically created when new Weekly High Signals are detected during market hours",
                                                    lastSync && " • Last sync: ".concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatDateTime"])(lastSync))
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                lineNumber: 618,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                        lineNumber: 616,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                lineNumber: 614,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-3",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center space-x-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "w-2 h-2 rounded-full ".concat(autoGTTEnabled ? 'bg-green-500' : 'bg-gray-400')
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                lineNumber: 628,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-xs text-gray-600",
                                                children: [
                                                    "Real-time Auto GTT ",
                                                    autoGTTEnabled ? 'ON' : 'OFF'
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                lineNumber: 629,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                        lineNumber: 627,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: toggleAutoGTTService,
                                        className: "px-3 py-1 rounded-md text-xs font-medium flex items-center space-x-1 transition-colors ".concat(autoGTTEnabled ? 'bg-red-100 text-red-700 hover:bg-red-200' : 'bg-green-100 text-green-700 hover:bg-green-200'),
                                        children: [
                                            autoGTTEnabled ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$pause$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Pause$3e$__["Pause"], {
                                                className: "h-3 w-3"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                lineNumber: 642,
                                                columnNumber: 33
                                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$play$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Play$3e$__["Play"], {
                                                className: "h-3 w-3"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                lineNumber: 642,
                                                columnNumber: 65
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                children: autoGTTEnabled ? 'Stop' : 'Start'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                lineNumber: 643,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                        lineNumber: 634,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>setShowAutoGTTSettings(!showAutoGTTSettings),
                                        className: "px-3 py-1 bg-gray-100 text-gray-700 rounded-md text-xs font-medium hover:bg-gray-200 transition-colors flex items-center space-x-1",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__["Settings"], {
                                                className: "h-3 w-3"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                lineNumber: 650,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                children: "Settings"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                lineNumber: 651,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                        lineNumber: 646,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                lineNumber: 626,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                        lineNumber: 613,
                        columnNumber: 9
                    }, this),
                    autoGTTStatus && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-3 pt-3 border-t border-blue-200",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-4 gap-4 text-xs",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-blue-600 font-medium",
                                            children: "Market Status:"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 661,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "ml-1 ".concat(autoGTTStatus.detector.isMarketOpen ? 'text-green-600' : 'text-gray-600'),
                                            children: autoGTTStatus.detector.isMarketOpen ? 'Open' : 'Closed'
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 662,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                    lineNumber: 660,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-blue-600 font-medium",
                                            children: "Signals:"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 667,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "ml-1 text-gray-700",
                                            children: autoGTTStatus.detector.lastSignalCount || 0
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 668,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                    lineNumber: 666,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-blue-600 font-medium",
                                            children: "Auto Orders:"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 671,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "ml-1 text-gray-700",
                                            children: autoGTTStatus.service.autoCreatedOrders || 0
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 672,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                    lineNumber: 670,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-blue-600 font-medium",
                                            children: "Today:"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 675,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "ml-1 text-gray-700",
                                            children: [
                                                autoGTTStatus.service.todayOrders || 0,
                                                "/",
                                                autoGTTStatus.service.dailyLimit || 20
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 676,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                    lineNumber: 674,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                            lineNumber: 659,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                        lineNumber: 658,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                lineNumber: 612,
                columnNumber: 7
            }, this),
            autoGTTNotifications.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-2",
                children: autoGTTNotifications.map((notification, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-green-50 border border-green-200 rounded-lg p-3 flex items-center space-x-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bell$2d$ring$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BellRing$3e$__["BellRing"], {
                                className: "h-4 w-4 text-green-600"
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                lineNumber: 690,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-green-800 text-sm",
                                children: notification
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                lineNumber: 691,
                                columnNumber: 15
                            }, this)
                        ]
                    }, index, true, {
                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                        lineNumber: 689,
                        columnNumber: 13
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                lineNumber: 687,
                columnNumber: 9
            }, this),
            showAutoGTTSettings && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white rounded-lg p-6 w-full max-w-md",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-between mb-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-lg font-semibold text-gray-900",
                                    children: "Auto GTT Settings"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                    lineNumber: 702,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>setShowAutoGTTSettings(false),
                                    className: "text-gray-400 hover:text-gray-600",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__XCircle$3e$__["XCircle"], {
                                        className: "h-5 w-5"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                        lineNumber: 707,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                    lineNumber: 703,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                            lineNumber: 701,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "space-y-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                            className: "block text-sm font-medium text-gray-700 mb-1",
                                            children: "Polling Interval (minutes)"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 713,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                            type: "number",
                                            min: "1",
                                            max: "60",
                                            defaultValue: "5",
                                            className: "w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 716,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                    lineNumber: 712,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                            className: "block text-sm font-medium text-gray-700 mb-1",
                                            children: "Minimum Signal Strength"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 726,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                            className: "w-full px-3 py-2 border border-gray-300 rounded-md text-sm",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                    value: "MODERATE",
                                                    children: "Moderate"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                    lineNumber: 730,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                    value: "STRONG",
                                                    children: "Strong Only"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                    lineNumber: 731,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 729,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                    lineNumber: 725,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                            className: "block text-sm font-medium text-gray-700 mb-1",
                                            children: "Max Orders Per Day"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 736,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                            type: "number",
                                            min: "1",
                                            max: "50",
                                            defaultValue: "20",
                                            className: "w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 739,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                    lineNumber: 735,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center space-x-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                            type: "checkbox",
                                            id: "marketHoursOnly",
                                            defaultChecked: true,
                                            className: "rounded border-gray-300"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 749,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                            htmlFor: "marketHoursOnly",
                                            className: "text-sm text-gray-700",
                                            children: "Only during market hours (9:15 AM - 3:30 PM)"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 755,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                    lineNumber: 748,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center space-x-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                            type: "checkbox",
                                            id: "volumeConfirmation",
                                            defaultChecked: true,
                                            className: "rounded border-gray-300"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 761,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                            htmlFor: "volumeConfirmation",
                                            className: "text-sm text-gray-700",
                                            children: "Require volume confirmation (20% above average)"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 767,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                    lineNumber: 760,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                            lineNumber: 711,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex justify-end space-x-3 mt-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>setShowAutoGTTSettings(false),
                                    className: "px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors",
                                    children: "Cancel"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                    lineNumber: 774,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>{
                                        // TODO: Save settings
                                        setShowAutoGTTSettings(false);
                                        addAutoGTTNotification('⚙️ Auto GTT settings updated');
                                    },
                                    className: "px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",
                                    children: "Save Settings"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                    lineNumber: 780,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                            lineNumber: 773,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                    lineNumber: 700,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                lineNumber: 699,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white rounded-lg shadow-sm border border-gray-200",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "border-b border-gray-200",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                            className: "flex space-x-8 px-6",
                            children: [
                                'SIGNAL',
                                'HOLDING',
                                'SALE'
                            ].map((tab)=>{
                                const tabInfo = getTabInfo(tab);
                                const tabOrders = orders.filter((o)=>o.source === tab);
                                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>setActiveTab(tab),
                                    className: "py-4 px-1 border-b-2 font-medium text-sm transition-colors flex items-center space-x-2 ".concat(activeTab === tab ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),
                                    children: [
                                        tabInfo.icon,
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            children: tabInfo.title
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 813,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "bg-gray-100 text-gray-600 px-2 py-0.5 rounded-full text-xs",
                                            children: tabOrders.length
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 814,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, tab, true, {
                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                    lineNumber: 803,
                                    columnNumber: 17
                                }, this);
                            })
                        }, void 0, false, {
                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                            lineNumber: 798,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                        lineNumber: 797,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mb-6",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                    className: "text-lg font-semibold text-gray-900 mb-2",
                                                    children: getTabInfo(activeTab).title
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                    lineNumber: 828,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-gray-600 text-sm mb-1",
                                                    children: getTabInfo(activeTab).description
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                    lineNumber: 831,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-blue-600 text-xs font-medium",
                                                    children: getTabInfo(activeTab).automation
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                    lineNumber: 834,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 827,
                                            columnNumber: 15
                                        }, this),
                                        activeTab === 'SIGNAL' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex flex-col items-end space-y-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center space-x-2 px-3 py-2 bg-green-50 border border-green-200 rounded-lg",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center space-x-1",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "w-2 h-2 bg-green-500 rounded-full animate-pulse"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                                lineNumber: 844,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "text-green-700 text-sm font-medium",
                                                                children: "Automatic Creation Active"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                                lineNumber: 845,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                        lineNumber: 843,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                    lineNumber: 842,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: createAllSignalOrders,
                                                    disabled: isCreatingOrders,
                                                    className: "px-3 py-1.5 rounded-lg text-xs transition-colors flex items-center space-x-2 ".concat(isCreatingOrders ? 'bg-gray-300 text-gray-500 cursor-not-allowed' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'),
                                                    title: "Manual creation for testing purposes only - orders are created automatically",
                                                    children: isCreatingOrders ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__["RefreshCw"], {
                                                                className: "h-3 w-3 animate-spin"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                                lineNumber: 861,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                children: "Creating..."
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                                lineNumber: 862,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__["Plus"], {
                                                                className: "h-3 w-3"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                                lineNumber: 866,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                children: "Manual Create (Test)"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                                lineNumber: 867,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                    lineNumber: 849,
                                                    columnNumber: 19
                                                }, this),
                                                createOrdersError && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-red-600 text-xs max-w-xs text-right",
                                                    children: createOrdersError
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                    lineNumber: 873,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 841,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                    lineNumber: 826,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                lineNumber: 825,
                                columnNumber: 11
                            }, this),
                            activeTab === 'SIGNAL' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                    className: "text-sm font-medium text-blue-900 mb-1",
                                                    children: "Weekly High Signal Orders"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                    lineNumber: 887,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-xs text-blue-700",
                                                    children: "Orders are automatically created for BOH eligible stocks from the Weekly High Signal page. Trigger Price = Last Week's High + ₹0.05, Quantity = ₹2,000 ÷ Trigger Price"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                    lineNumber: 890,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 886,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-right",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-sm font-medium text-blue-900",
                                                    children: [
                                                        getSignalOrdersStats().pending,
                                                        " Pending"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                    lineNumber: 896,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-xs text-blue-700",
                                                    children: [
                                                        getSignalOrdersStats().triggered,
                                                        " Triggered"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                                    lineNumber: 899,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 895,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                    lineNumber: 885,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                lineNumber: 884,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(OrdersTable, {}, void 0, false, {
                                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                lineNumber: 908,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                        lineNumber: 824,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                lineNumber: 796,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid grid-cols-1 md:grid-cols-4 gap-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-white rounded-lg shadow-sm border border-gray-200 p-6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-between",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm font-medium text-gray-600",
                                            children: "Total Orders"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 917,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-2xl font-bold text-gray-900 mt-1",
                                            children: orders.length
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 918,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                    lineNumber: 916,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$activity$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Activity$3e$__["Activity"], {
                                    className: "h-8 w-8 text-gray-600"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                    lineNumber: 920,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                            lineNumber: 915,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                        lineNumber: 914,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-white rounded-lg shadow-sm border border-gray-200 p-6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-between",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm font-medium text-gray-600",
                                            children: "Pending Orders"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 927,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-2xl font-bold text-yellow-600 mt-1",
                                            children: orders.filter((o)=>o.status === 'PENDING').length
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 928,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                    lineNumber: 926,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                                    className: "h-8 w-8 text-yellow-600"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                    lineNumber: 932,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                            lineNumber: 925,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                        lineNumber: 924,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-white rounded-lg shadow-sm border border-gray-200 p-6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-between",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm font-medium text-gray-600",
                                            children: "Triggered Today"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 939,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-2xl font-bold text-green-600 mt-1",
                                            children: orders.filter((o)=>o.status === 'TRIGGERED').length
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 940,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                    lineNumber: 938,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"], {
                                    className: "h-8 w-8 text-green-600"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                    lineNumber: 944,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                            lineNumber: 937,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                        lineNumber: 936,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-white rounded-lg shadow-sm border border-gray-200 p-6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-between",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm font-medium text-gray-600",
                                            children: "Success Rate"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 951,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-2xl font-bold text-gray-900 mt-1",
                                            children: [
                                                orders.length > 0 ? Math.round(orders.filter((o)=>o.status === 'TRIGGERED').length / orders.length * 100) : 0,
                                                "%"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                            lineNumber: 952,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                    lineNumber: 950,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__["TrendingUp"], {
                                    className: "h-8 w-8 text-gray-600"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                                    lineNumber: 956,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                            lineNumber: 949,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                        lineNumber: 948,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
                lineNumber: 913,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/dashboard/gtt-orders/page.tsx",
        lineNumber: 582,
        columnNumber: 5
    }, this);
}
_s(GTTOrdersPage, "gxa85oYaxx6tNY13Z6domgtur+w=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useCentralData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useGTTOrders"]
    ];
});
_c = GTTOrdersPage;
var _c;
__turbopack_context__.k.register(_c, "GTTOrdersPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_6a047609._.js.map