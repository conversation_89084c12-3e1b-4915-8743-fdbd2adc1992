'use client';

import { useState } from 'react';
import { 
  TestTube, 
  Play, 
  Pause, 
  BarChart3, 
  TrendingUp,
  TrendingDown,
  Calendar,
  Settings,
  Download,
  RefreshCw
} from 'lucide-react';
import { formatCurrency, formatPercentage, getChangeColor } from '@/lib/utils';

interface BacktestResult {
  id: string;
  name: string;
  strategy: string;
  startDate: Date;
  endDate: Date;
  initialCapital: number;
  finalCapital: number;
  totalReturn: number;
  totalReturnPercent: number;
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  winRate: number;
  maxDrawdown: number;
  sharpeRatio: number;
  status: 'COMPLETED' | 'RUNNING' | 'FAILED';
  createdAt: Date;
}

interface BacktestConfig {
  strategy: string;
  startDate: string;
  endDate: string;
  initialCapital: number;
  riskPerTrade: number;
  maxPositions: number;
  stopLoss: number;
  target: number;
}

export default function BacktestingPage() {
  const [backtests, setBacktests] = useState<BacktestResult[]>([
    {
      id: '1',
      name: 'Darvas Box Strategy - 2023',
      strategy: 'DARVAS_BOX',
      startDate: new Date('2023-01-01'),
      endDate: new Date('2023-12-31'),
      initialCapital: 1000000,
      finalCapital: 1350000,
      totalReturn: 350000,
      totalReturnPercent: 35,
      totalTrades: 45,
      winningTrades: 28,
      losingTrades: 17,
      winRate: 62.22,
      maxDrawdown: -12.5,
      sharpeRatio: 1.45,
      status: 'COMPLETED',
      createdAt: new Date('2024-01-15')
    },
    {
      id: '2',
      name: 'Weekly High Breakout - 2023',
      strategy: 'WEEKLY_HIGH',
      startDate: new Date('2023-01-01'),
      endDate: new Date('2023-12-31'),
      initialCapital: 1000000,
      finalCapital: 1180000,
      totalReturn: 180000,
      totalReturnPercent: 18,
      totalTrades: 67,
      winningTrades: 38,
      losingTrades: 29,
      winRate: 56.72,
      maxDrawdown: -8.3,
      sharpeRatio: 1.12,
      status: 'COMPLETED',
      createdAt: new Date('2024-01-10')
    },
    {
      id: '3',
      name: 'BOH Filter Strategy - Q1 2024',
      strategy: 'BOH_FILTER',
      startDate: new Date('2024-01-01'),
      endDate: new Date('2024-03-31'),
      initialCapital: 1000000,
      finalCapital: 0,
      totalReturn: 0,
      totalReturnPercent: 0,
      totalTrades: 0,
      winningTrades: 0,
      losingTrades: 0,
      winRate: 0,
      maxDrawdown: 0,
      sharpeRatio: 0,
      status: 'RUNNING',
      createdAt: new Date()
    }
  ]);

  const [showCreateModal, setShowCreateModal] = useState(false);
  const [backtestConfig, setBacktestConfig] = useState<BacktestConfig>({
    strategy: 'DARVAS_BOX',
    startDate: '2023-01-01',
    endDate: '2023-12-31',
    initialCapital: 1000000,
    riskPerTrade: 2,
    maxPositions: 5,
    stopLoss: 5,
    target: 15
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED': return 'text-green-600 bg-green-100';
      case 'RUNNING': return 'text-blue-600 bg-blue-100';
      case 'FAILED': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const BacktestCard = ({ backtest }: { backtest: BacktestResult }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-start justify-between mb-4">
        <div>
          <h3 className="font-semibold text-gray-900">{backtest.name}</h3>
          <p className="text-sm text-gray-600 mt-1">{backtest.strategy.replace('_', ' ')}</p>
        </div>
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(backtest.status)}`}>
          {backtest.status}
        </span>
      </div>

      {backtest.status === 'COMPLETED' ? (
        <>
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <p className="text-sm text-gray-600">Total Return</p>
              <p className={`font-semibold text-lg ${getChangeColor(backtest.totalReturn)}`}>
                {formatCurrency(backtest.totalReturn)} ({formatPercentage(backtest.totalReturnPercent)})
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Win Rate</p>
              <p className="font-semibold text-lg">{backtest.winRate.toFixed(1)}%</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Total Trades</p>
              <p className="font-semibold text-lg">{backtest.totalTrades}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Max Drawdown</p>
              <p className="font-semibold text-lg text-red-600">{backtest.maxDrawdown}%</p>
            </div>
          </div>

          <div className="flex items-center justify-between pt-4 border-t border-gray-100">
            <div className="text-sm text-gray-600">
              {backtest.startDate.toLocaleDateString()} - {backtest.endDate.toLocaleDateString()}
            </div>
            <div className="flex space-x-2">
              <button className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors">
                View Details
              </button>
              <button className="px-3 py-1 border border-gray-300 text-gray-700 text-sm rounded hover:bg-gray-50 transition-colors">
                <Download className="h-3 w-3" />
              </button>
            </div>
          </div>
        </>
      ) : backtest.status === 'RUNNING' ? (
        <div className="text-center py-8">
          <RefreshCw className="h-8 w-8 mx-auto mb-4 text-blue-600 animate-spin" />
          <p className="text-gray-600">Backtest in progress...</p>
          <p className="text-sm text-gray-500 mt-1">This may take several minutes</p>
        </div>
      ) : (
        <div className="text-center py-8">
          <div className="h-8 w-8 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
            <span className="text-red-600 text-sm">!</span>
          </div>
          <p className="text-red-600">Backtest failed</p>
          <p className="text-sm text-gray-500 mt-1">Check configuration and try again</p>
        </div>
      )}
    </div>
  );

  const CreateBacktestModal = () => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-lg">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Create New Backtest</h3>
          <button
            onClick={() => setShowCreateModal(false)}
            className="text-gray-400 hover:text-gray-600"
          >
            ×
          </button>
        </div>
        
        <form className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Backtest Name</label>
            <input
              type="text"
              placeholder="e.g., Darvas Box Strategy - Q1 2024"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Strategy</label>
            <select 
              value={backtestConfig.strategy}
              onChange={(e) => setBacktestConfig(prev => ({ ...prev, strategy: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="DARVAS_BOX">Darvas Box</option>
              <option value="WEEKLY_HIGH">Weekly High Breakout</option>
              <option value="BOH_FILTER">BOH Filter</option>
            </select>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
              <input
                type="date"
                value={backtestConfig.startDate}
                onChange={(e) => setBacktestConfig(prev => ({ ...prev, startDate: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">End Date</label>
              <input
                type="date"
                value={backtestConfig.endDate}
                onChange={(e) => setBacktestConfig(prev => ({ ...prev, endDate: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Initial Capital (₹)</label>
            <input
              type="number"
              value={backtestConfig.initialCapital}
              onChange={(e) => setBacktestConfig(prev => ({ ...prev, initialCapital: Number(e.target.value) }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Risk per Trade (%)</label>
              <input
                type="number"
                step="0.1"
                value={backtestConfig.riskPerTrade}
                onChange={(e) => setBacktestConfig(prev => ({ ...prev, riskPerTrade: Number(e.target.value) }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Max Positions</label>
              <input
                type="number"
                value={backtestConfig.maxPositions}
                onChange={(e) => setBacktestConfig(prev => ({ ...prev, maxPositions: Number(e.target.value) }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Stop Loss (%)</label>
              <input
                type="number"
                step="0.1"
                value={backtestConfig.stopLoss}
                onChange={(e) => setBacktestConfig(prev => ({ ...prev, stopLoss: Number(e.target.value) }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Target (%)</label>
              <input
                type="number"
                step="0.1"
                value={backtestConfig.target}
                onChange={(e) => setBacktestConfig(prev => ({ ...prev, target: Number(e.target.value) }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={() => setShowCreateModal(false)}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Start Backtest
            </button>
          </div>
        </form>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Back Testing</h1>
          <p className="text-gray-600 mt-1">Strategy backtesting and performance analysis</p>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <TestTube className="h-4 w-4" />
          <span>New Backtest</span>
        </button>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Backtests</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{backtests.length}</p>
            </div>
            <BarChart3 className="h-8 w-8 text-blue-600" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Return</p>
              <p className="text-2xl font-bold text-green-600 mt-1">+26.5%</p>
            </div>
            <TrendingUp className="h-8 w-8 text-green-600" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Best Strategy</p>
              <p className="text-lg font-bold text-gray-900 mt-1">Darvas Box</p>
            </div>
            <TestTube className="h-8 w-8 text-gray-600" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Running</p>
              <p className="text-2xl font-bold text-blue-600 mt-1">
                {backtests.filter(b => b.status === 'RUNNING').length}
              </p>
            </div>
            <RefreshCw className="h-8 w-8 text-blue-600" />
          </div>
        </div>
      </div>

      {/* Backtests Grid */}
      <div>
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Recent Backtests</h3>
          <div className="flex space-x-2">
            <button className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 transition-colors">
              All
            </button>
            <button className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 transition-colors">
              Completed
            </button>
            <button className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 transition-colors">
              Running
            </button>
          </div>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {backtests.map((backtest) => (
            <BacktestCard key={backtest.id} backtest={backtest} />
          ))}
        </div>
      </div>

      {/* Create Backtest Modal */}
      {showCreateModal && <CreateBacktestModal />}
    </div>
  );
}
