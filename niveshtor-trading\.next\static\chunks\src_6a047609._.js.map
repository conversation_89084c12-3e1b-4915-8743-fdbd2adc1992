{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/hooks/useCentralData.ts"], "sourcesContent": ["// React Hook for Central Data Manager Integration\n// Provides real-time data access and automatic updates for all stock-related pages\n\nimport { useState, useEffect, useCallback, useRef } from 'react';\nimport { NiftyStock } from '@/lib/nifty-stocks';\nimport { WeeklyHighSignal } from '@/lib/weekly-high-signal-detector';\nimport { AutoGTTOrder } from '@/lib/automatic-gtt-service';\nimport { centralDataManager, automaticGTTService, weeklyHighSignalDetector } from '@/lib/service-initializer';\n\ntype DataType = 'nifty200' | 'bohEligible' | 'weeklyHighSignals' | 'gttOrders';\n\ninterface DataState<T> {\n  data: T[];\n  lastUpdated: Date | null;\n  isLoading: boolean;\n  error: string | null;\n}\n\ninterface CentralDataHook {\n  nifty200: DataState<NiftyStock>;\n  bohEligible: DataState<NiftyStock>;\n  weeklyHighSignals: DataState<WeeklyHighSignal>;\n  gttOrders: DataState<AutoGTTOrder>;\n  refreshData: (dataType?: DataType) => Promise<void>;\n  isInitialized: boolean;\n  isServiceRunning: boolean;\n}\n\nexport function useCentralData(): CentralDataHook {\n  const [nifty200, setNifty200] = useState<DataState<NiftyStock>>({\n    data: [],\n    lastUpdated: null,\n    isLoading: false,\n    error: null\n  });\n\n  const [bohEligible, setBohEligible] = useState<DataState<NiftyStock>>({\n    data: [],\n    lastUpdated: null,\n    isLoading: false,\n    error: null\n  });\n\n  const [weeklyHighSignals, setWeeklyHighSignals] = useState<DataState<WeeklyHighSignal>>({\n    data: [],\n    lastUpdated: null,\n    isLoading: false,\n    error: null\n  });\n\n  const [gttOrders, setGttOrders] = useState<DataState<AutoGTTOrder>>({\n    data: [],\n    lastUpdated: null,\n    isLoading: false,\n    error: null\n  });\n\n  const [isInitialized, setIsInitialized] = useState(false);\n  const [isServiceRunning, setIsServiceRunning] = useState(false);\n\n  const pollingIntervals = useRef<Map<DataType, NodeJS.Timeout>>(new Map());\n\n  // Fetch data from API\n  const fetchData = useCallback(async (dataType: DataType) => {\n    try {\n      const response = await fetch(`/api/data-manager?action=${dataType === 'bohEligible' ? 'boh-eligible' : dataType === 'weeklyHighSignals' ? 'weekly-high-signals' : dataType === 'gttOrders' ? 'gtt-orders' : dataType}`);\n      const result = await response.json();\n\n      if (result.success) {\n        const newState = {\n          data: result.data || [],\n          lastUpdated: result.lastUpdated ? new Date(result.lastUpdated) : new Date(),\n          isLoading: result.isLoading || false,\n          error: null\n        };\n\n        switch (dataType) {\n          case 'nifty200':\n            setNifty200(newState);\n            break;\n          case 'bohEligible':\n            setBohEligible(newState);\n            break;\n          case 'weeklyHighSignals':\n            setWeeklyHighSignals(newState);\n            break;\n          case 'gttOrders':\n            setGttOrders(newState);\n            break;\n        }\n\n        console.log(`📊 Updated ${dataType}: ${result.data?.length || 0} items`);\n      } else {\n        throw new Error(result.error || 'Failed to fetch data');\n      }\n    } catch (error) {\n      console.error(`❌ Error fetching ${dataType}:`, error);\n      \n      const errorState = {\n        data: [],\n        lastUpdated: null,\n        isLoading: false,\n        error: error instanceof Error ? error.message : 'Unknown error'\n      };\n\n      switch (dataType) {\n        case 'nifty200':\n          setNifty200(prev => ({ ...prev, ...errorState }));\n          break;\n        case 'bohEligible':\n          setBohEligible(prev => ({ ...prev, ...errorState }));\n          break;\n        case 'weeklyHighSignals':\n          setWeeklyHighSignals(prev => ({ ...prev, ...errorState }));\n          break;\n        case 'gttOrders':\n          setGttOrders(prev => ({ ...prev, ...errorState }));\n          break;\n      }\n    }\n  }, []);\n\n  // Check service status\n  const checkServiceStatus = useCallback(async () => {\n    try {\n      const response = await fetch('/api/data-manager?action=status');\n      const result = await response.json();\n\n      if (result.success) {\n        setIsInitialized(result.data.isInitialized);\n        setIsServiceRunning(result.data.isRunning);\n      }\n    } catch (error) {\n      console.error('❌ Error checking service status:', error);\n    }\n  }, []);\n\n  // Initialize service if not already initialized\n  const initializeService = useCallback(async () => {\n    try {\n      console.log('🚀 Initializing Central Data Manager...');\n      \n      const response = await fetch('/api/data-manager', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ action: 'initialize' })\n      });\n\n      const result = await response.json();\n      \n      if (result.success) {\n        console.log('✅ Central Data Manager initialized');\n        setIsInitialized(true);\n        \n        // Start the service\n        const startResponse = await fetch('/api/data-manager', {\n          method: 'POST',\n          headers: { 'Content-Type': 'application/json' },\n          body: JSON.stringify({ action: 'start' })\n        });\n\n        const startResult = await startResponse.json();\n        if (startResult.success) {\n          setIsServiceRunning(true);\n          console.log('✅ Central Data Manager started');\n        }\n      }\n    } catch (error) {\n      console.error('❌ Error initializing service:', error);\n    }\n  }, []);\n\n  // Refresh specific data type\n  const refreshData = useCallback(async (dataType?: DataType) => {\n    if (dataType) {\n      await fetchData(dataType);\n    } else {\n      // Refresh all data\n      await Promise.all([\n        fetchData('nifty200'),\n        fetchData('bohEligible'),\n        fetchData('weeklyHighSignals'),\n        fetchData('gttOrders')\n      ]);\n    }\n  }, [fetchData]);\n\n  // Set up polling for real-time updates\n  const setupPolling = useCallback(() => {\n    // Clear existing intervals\n    pollingIntervals.current.forEach(interval => clearInterval(interval));\n    pollingIntervals.current.clear();\n\n    // Set up new intervals\n    const intervals = {\n      nifty200: 30000, // 30 seconds\n      bohEligible: 60000, // 1 minute\n      weeklyHighSignals: 300000, // 5 minutes\n      gttOrders: 30000 // 30 seconds\n    };\n\n    Object.entries(intervals).forEach(([dataType, interval]) => {\n      const intervalId = setInterval(() => {\n        fetchData(dataType as DataType);\n      }, interval);\n      \n      pollingIntervals.current.set(dataType as DataType, intervalId);\n    });\n\n    console.log('⏰ Polling intervals set up for real-time updates');\n  }, [fetchData]);\n\n  // Initialize on mount\n  useEffect(() => {\n    const initialize = async () => {\n      // Check if service is already running\n      await checkServiceStatus();\n      \n      // Initialize service if needed\n      if (!isInitialized) {\n        await initializeService();\n      }\n\n      // Load initial data\n      await refreshData();\n\n      // Set up polling for real-time updates\n      setupPolling();\n    };\n\n    initialize();\n\n    // Cleanup on unmount\n    return () => {\n      pollingIntervals.current.forEach(interval => clearInterval(interval));\n      pollingIntervals.current.clear();\n    };\n  }, []);\n\n  // Re-setup polling when service status changes\n  useEffect(() => {\n    if (isServiceRunning) {\n      setupPolling();\n    }\n  }, [isServiceRunning, setupPolling]);\n\n  return {\n    nifty200,\n    bohEligible,\n    weeklyHighSignals,\n    gttOrders,\n    refreshData,\n    isInitialized,\n    isServiceRunning\n  };\n}\n\n// Specialized hooks for individual data types\nexport function useNifty200Stocks() {\n  const { nifty200, refreshData } = useCentralData();\n  \n  return {\n    stocks: nifty200.data,\n    lastUpdated: nifty200.lastUpdated,\n    isLoading: nifty200.isLoading,\n    error: nifty200.error,\n    refresh: () => refreshData('nifty200')\n  };\n}\n\nexport function useBOHEligibleStocks() {\n  const { bohEligible, refreshData } = useCentralData();\n  \n  return {\n    stocks: bohEligible.data,\n    lastUpdated: bohEligible.lastUpdated,\n    isLoading: bohEligible.isLoading,\n    error: bohEligible.error,\n    refresh: () => refreshData('bohEligible')\n  };\n}\n\nexport function useWeeklyHighSignals() {\n  const { weeklyHighSignals, refreshData } = useCentralData();\n  \n  return {\n    signals: weeklyHighSignals.data,\n    lastUpdated: weeklyHighSignals.lastUpdated,\n    isLoading: weeklyHighSignals.isLoading,\n    error: weeklyHighSignals.error,\n    refresh: () => refreshData('weeklyHighSignals')\n  };\n}\n\nexport function useGTTOrders() {\n  const { gttOrders, refreshData } = useCentralData();\n  \n  return {\n    orders: gttOrders.data,\n    lastUpdated: gttOrders.lastUpdated,\n    isLoading: gttOrders.isLoading,\n    error: gttOrders.error,\n    refresh: () => refreshData('gttOrders')\n  };\n}\n"], "names": [], "mappings": "AAAA,kDAAkD;AAClD,mFAAmF;;;;;;;;AAEnF;;;AAyBO,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;QAC9D,MAAM,EAAE;QACR,aAAa;QACb,WAAW;QACX,OAAO;IACT;IAEA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;QACpE,MAAM,EAAE;QACR,aAAa;QACb,WAAW;QACX,OAAO;IACT;IAEA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA+B;QACtF,MAAM,EAAE;QACR,aAAa;QACb,WAAW;QACX,OAAO;IACT;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;QAClE,MAAM,EAAE;QACR,aAAa;QACb,WAAW;QACX,OAAO;IACT;IAEA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiC,IAAI;IAEnE,sBAAsB;IACtB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,OAAO;YACnC,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,AAAC,4BAAuL,OAA5J,aAAa,gBAAgB,iBAAiB,aAAa,sBAAsB,wBAAwB,aAAa,cAAc,eAAe;gBAC5M,MAAM,SAAS,MAAM,SAAS,IAAI;gBAElC,IAAI,OAAO,OAAO,EAAE;wBAuBqB;oBAtBvC,MAAM,WAAW;wBACf,MAAM,OAAO,IAAI,IAAI,EAAE;wBACvB,aAAa,OAAO,WAAW,GAAG,IAAI,KAAK,OAAO,WAAW,IAAI,IAAI;wBACrE,WAAW,OAAO,SAAS,IAAI;wBAC/B,OAAO;oBACT;oBAEA,OAAQ;wBACN,KAAK;4BACH,YAAY;4BACZ;wBACF,KAAK;4BACH,eAAe;4BACf;wBACF,KAAK;4BACH,qBAAqB;4BACrB;wBACF,KAAK;4BACH,aAAa;4BACb;oBACJ;oBAEA,QAAQ,GAAG,CAAC,AAAC,cAA0B,OAAb,UAAS,MAA6B,OAAzB,EAAA,eAAA,OAAO,IAAI,cAAX,mCAAA,aAAa,MAAM,KAAI,GAAE;gBAClE,OAAO;oBACL,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;gBAClC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,AAAC,oBAA4B,OAAT,UAAS,MAAI;gBAE/C,MAAM,aAAa;oBACjB,MAAM,EAAE;oBACR,aAAa;oBACb,WAAW;oBACX,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD;gBAEA,OAAQ;oBACN,KAAK;wBACH;qEAAY,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,GAAG,UAAU;gCAAC,CAAC;;wBAC/C;oBACF,KAAK;wBACH;qEAAe,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,GAAG,UAAU;gCAAC,CAAC;;wBAClD;oBACF,KAAK;wBACH;qEAAqB,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,GAAG,UAAU;gCAAC,CAAC;;wBACxD;oBACF,KAAK;wBACH;qEAAa,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,GAAG,UAAU;gCAAC,CAAC;;wBAChD;gBACJ;YACF;QACF;gDAAG,EAAE;IAEL,uBAAuB;IACvB,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YACrC,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM;gBAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;gBAElC,IAAI,OAAO,OAAO,EAAE;oBAClB,iBAAiB,OAAO,IAAI,CAAC,aAAa;oBAC1C,oBAAoB,OAAO,IAAI,CAAC,SAAS;gBAC3C;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;YACpD;QACF;yDAAG,EAAE;IAEL,gDAAgD;IAChD,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE;YACpC,IAAI;gBACF,QAAQ,GAAG,CAAC;gBAEZ,MAAM,WAAW,MAAM,MAAM,qBAAqB;oBAChD,QAAQ;oBACR,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,MAAM,KAAK,SAAS,CAAC;wBAAE,QAAQ;oBAAa;gBAC9C;gBAEA,MAAM,SAAS,MAAM,SAAS,IAAI;gBAElC,IAAI,OAAO,OAAO,EAAE;oBAClB,QAAQ,GAAG,CAAC;oBACZ,iBAAiB;oBAEjB,oBAAoB;oBACpB,MAAM,gBAAgB,MAAM,MAAM,qBAAqB;wBACrD,QAAQ;wBACR,SAAS;4BAAE,gBAAgB;wBAAmB;wBAC9C,MAAM,KAAK,SAAS,CAAC;4BAAE,QAAQ;wBAAQ;oBACzC;oBAEA,MAAM,cAAc,MAAM,cAAc,IAAI;oBAC5C,IAAI,YAAY,OAAO,EAAE;wBACvB,oBAAoB;wBACpB,QAAQ,GAAG,CAAC;oBACd;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;YACjD;QACF;wDAAG,EAAE;IAEL,6BAA6B;IAC7B,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,OAAO;YACrC,IAAI,UAAU;gBACZ,MAAM,UAAU;YAClB,OAAO;gBACL,mBAAmB;gBACnB,MAAM,QAAQ,GAAG,CAAC;oBAChB,UAAU;oBACV,UAAU;oBACV,UAAU;oBACV,UAAU;iBACX;YACH;QACF;kDAAG;QAAC;KAAU;IAEd,uCAAuC;IACvC,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE;YAC/B,2BAA2B;YAC3B,iBAAiB,OAAO,CAAC,OAAO;4DAAC,CAAA,WAAY,cAAc;;YAC3D,iBAAiB,OAAO,CAAC,KAAK;YAE9B,uBAAuB;YACvB,MAAM,YAAY;gBAChB,UAAU;gBACV,aAAa;gBACb,mBAAmB;gBACnB,WAAW,MAAM,aAAa;YAChC;YAEA,OAAO,OAAO,CAAC,WAAW,OAAO;4DAAC;wBAAC,CAAC,UAAU,SAAS;oBACrD,MAAM,aAAa;+EAAY;4BAC7B,UAAU;wBACZ;8EAAG;oBAEH,iBAAiB,OAAO,CAAC,GAAG,CAAC,UAAsB;gBACrD;;YAEA,QAAQ,GAAG,CAAC;QACd;mDAAG;QAAC;KAAU;IAEd,sBAAsB;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;uDAAa;oBACjB,sCAAsC;oBACtC,MAAM;oBAEN,+BAA+B;oBAC/B,IAAI,CAAC,eAAe;wBAClB,MAAM;oBACR;oBAEA,oBAAoB;oBACpB,MAAM;oBAEN,uCAAuC;oBACvC;gBACF;;YAEA;YAEA,qBAAqB;YACrB;4CAAO;oBACL,iBAAiB,OAAO,CAAC,OAAO;oDAAC,CAAA,WAAY,cAAc;;oBAC3D,iBAAiB,OAAO,CAAC,KAAK;gBAChC;;QACF;mCAAG,EAAE;IAEL,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,kBAAkB;gBACpB;YACF;QACF;mCAAG;QAAC;QAAkB;KAAa;IAEnC,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAnOgB;AAsOT,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG;IAElC,OAAO;QACL,QAAQ,SAAS,IAAI;QACrB,aAAa,SAAS,WAAW;QACjC,WAAW,SAAS,SAAS;QAC7B,OAAO,SAAS,KAAK;QACrB,SAAS,IAAM,YAAY;IAC7B;AACF;IAVgB;;QACoB;;;AAW7B,SAAS;;IACd,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG;IAErC,OAAO;QACL,QAAQ,YAAY,IAAI;QACxB,aAAa,YAAY,WAAW;QACpC,WAAW,YAAY,SAAS;QAChC,OAAO,YAAY,KAAK;QACxB,SAAS,IAAM,YAAY;IAC7B;AACF;IAVgB;;QACuB;;;AAWhC,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,WAAW,EAAE,GAAG;IAE3C,OAAO;QACL,SAAS,kBAAkB,IAAI;QAC/B,aAAa,kBAAkB,WAAW;QAC1C,WAAW,kBAAkB,SAAS;QACtC,OAAO,kBAAkB,KAAK;QAC9B,SAAS,IAAM,YAAY;IAC7B;AACF;IAVgB;;QAC6B;;;AAWtC,SAAS;;IACd,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG;IAEnC,OAAO;QACL,QAAQ,UAAU,IAAI;QACtB,aAAa,UAAU,WAAW;QAClC,WAAW,UAAU,SAAS;QAC9B,OAAO,UAAU,KAAK;QACtB,SAAS,IAAM,YAAY;IAC7B;AACF;IAVgB;;QACqB", "debugId": null}}, {"offset": {"line": 352, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/app/dashboard/gtt-orders/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport {\n  Clock,\n  TrendingUp,\n  TrendingDown,\n  CheckCircle,\n  XCircle,\n  AlertCircle,\n  Wifi,\n  WifiOff,\n  RefreshCw,\n  Calendar,\n  Target,\n  Activity,\n  Plus,\n  Settings,\n  Play,\n  Pause,\n  BellRing\n} from 'lucide-react';\nimport { formatCurrency, formatDateTime } from '@/lib/utils';\nimport { useGTTOrders } from '@/hooks/useCentralData';\n\ninterface GTTOrder {\n  id: string;\n  gttId?: string; // Angel One GTT ID\n  symbol: string;\n  name: string;\n  orderType: 'BUY' | 'SELL';\n  triggerPrice: number;\n  quantity: number;\n  status: 'PENDING' | 'TRIGGERED' | 'CANCELLED' | 'EXPIRED';\n  createdAt: Date;\n  source: 'SIGNAL' | 'HOLDING' | 'SALE'; // Which tab this order belongs to\n}\n\ntype TabType = 'SIGNAL' | 'HOLDING' | 'SALE';\n\nexport default function GTTOrdersPage() {\n  const [activeTab, setActiveTab] = useState<TabType>('SIGNAL');\n\n  // Central Data Manager for real-time GTT orders\n  const {\n    orders: centralGTTOrders,\n    lastUpdated: gttLastUpdated,\n    isLoading: gttIsLoading,\n    error: gttError,\n    refresh: refreshGTTOrders\n  } = useGTTOrders();\n\n  const [orders, setOrders] = useState<GTTOrder[]>([\n    // Sample GTT Buy on Signal orders\n    {\n      id: '1',\n      symbol: 'RELIANCE',\n      name: 'Reliance Industries Ltd',\n      orderType: 'BUY',\n      triggerPrice: 2400.05,\n      quantity: 8,\n      status: 'PENDING',\n      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),\n      source: 'SIGNAL'\n    },\n    {\n      id: '2',\n      symbol: 'TCS',\n      name: 'Tata Consultancy Services',\n      orderType: 'BUY',\n      triggerPrice: 3200.05,\n      quantity: 6,\n      status: 'TRIGGERED',\n      createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),\n      source: 'SIGNAL'\n    },\n    // Sample GTT Buy on Holding orders\n    {\n      id: '3',\n      symbol: 'INFY',\n      name: 'Infosys Limited',\n      orderType: 'BUY',\n      triggerPrice: 1350.00,\n      quantity: 14,\n      status: 'PENDING',\n      createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),\n      source: 'HOLDING'\n    },\n    // Sample GTT Sale orders\n    {\n      id: '4',\n      symbol: 'WIPRO',\n      name: 'Wipro Limited',\n      orderType: 'SELL',\n      triggerPrice: 450.00,\n      quantity: 44,\n      status: 'PENDING',\n      createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),\n      source: 'SALE'\n    }\n  ]);\n\n  const [isAngelOneConnected, setIsAngelOneConnected] = useState(false);\n  const [lastSync, setLastSync] = useState<Date | null>(null);\n  const [isCreatingOrders, setIsCreatingOrders] = useState(false);\n  const [createOrdersError, setCreateOrdersError] = useState<string | null>(null);\n\n  // Automatic GTT Service state\n  const [autoGTTEnabled, setAutoGTTEnabled] = useState(false);\n  const [autoGTTStatus, setAutoGTTStatus] = useState<any>(null);\n  const [showAutoGTTSettings, setShowAutoGTTSettings] = useState(false);\n  const [autoGTTNotifications, setAutoGTTNotifications] = useState<string[]>([]);\n\n  // Filter orders by active tab\n  const filteredOrders = orders.filter(order => order.source === activeTab);\n\n  // Interface for Weekly High Signal data\n  interface WeeklyHighStock {\n    symbol: string;\n    name: string;\n    currentPrice: number;\n    lastWeekHighest: number;\n    suggestedBuyPrice: number;\n    percentDifference: number;\n    suggestedGTTQuantity: number;\n    isBOHEligible: boolean;\n    inHoldings: boolean;\n  }\n\n  // Function to fetch BOH eligible stocks and create GTT orders using the API\n  const fetchAndCreateGTTOrders = async () => {\n    try {\n      console.log('🔍 Fetching BOH eligible stocks and creating GTT orders via API...');\n\n      // Use the dedicated API endpoint for creating signal orders\n      const response = await fetch('/api/gtt/create-signal-orders', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      console.log(`📡 API Response status: ${response.status}`);\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error(`❌ API request failed: ${response.status} - ${errorText}`);\n        throw new Error(`API request failed: ${response.status}`);\n      }\n\n      const data = await response.json();\n      console.log('📊 API Response data:', data);\n\n      if (!data.success) {\n        console.error('❌ API returned error:', data.error);\n        throw new Error(data.error || 'Failed to fetch GTT order data');\n      }\n\n      console.log(`✅ API returned ${data.data.orders.length} GTT orders to create`);\n      console.log(`📊 BOH Stats: Total BOH stocks: ${data.data.totalBOHStocks}, Valid for GTT: ${data.data.validForGTT}`);\n\n      if (data.data.stats.avgTriggerPrice > 0) {\n        console.log(`💰 Price Stats: Avg Trigger: ₹${data.data.stats.avgTriggerPrice.toFixed(2)}, Total Value: ₹${data.data.stats.totalValue.toFixed(2)}`);\n      }\n\n      return data.data.orders;\n    } catch (error) {\n      console.error('❌ Error fetching GTT orders from API:', error);\n      throw error;\n    }\n  };\n\n  // Function to create GTT orders for all BOH eligible stocks\n  const createAllSignalOrders = async () => {\n    setIsCreatingOrders(true);\n    setCreateOrdersError(null);\n\n    try {\n      console.log('🚀 Starting to create GTT orders for all BOH eligible stocks...');\n\n      // Fetch GTT order data from API\n      const gttOrderRequests = await fetchAndCreateGTTOrders();\n\n      console.log(`🔍 Received ${gttOrderRequests.length} GTT order requests from API`);\n\n      if (gttOrderRequests.length === 0) {\n        const errorMsg = 'No BOH eligible stocks found for GTT orders. This could be due to:\\n' +\n                        '• No stocks marked as BOH eligible\\n' +\n                        '• All BOH stocks filtered out due to price/quantity constraints\\n' +\n                        '• API data fetching issues';\n        console.warn('⚠️ ' + errorMsg);\n        setCreateOrdersError(errorMsg);\n        return;\n      }\n\n      // Filter out stocks that already have pending signal orders\n      const existingSignalSymbols = orders\n        .filter(order => order.source === 'SIGNAL' && order.status === 'PENDING')\n        .map(order => order.symbol);\n\n      console.log(`🔍 Existing signal orders: ${existingSignalSymbols.length} symbols:`, existingSignalSymbols);\n\n      const newOrderRequests = gttOrderRequests.filter((orderReq: any) =>\n        !existingSignalSymbols.includes(orderReq.symbol)\n      );\n\n      console.log(`📊 Creating orders for ${newOrderRequests.length} new stocks (${existingSignalSymbols.length} already have orders)`);\n\n      if (newOrderRequests.length === 0) {\n        const errorMsg = `All ${gttOrderRequests.length} BOH eligible stocks already have pending GTT orders`;\n        console.warn('⚠️ ' + errorMsg);\n        setCreateOrdersError(errorMsg);\n        return;\n      }\n\n      // Create GTT orders for each stock\n      const newOrders: GTTOrder[] = [];\n      let successCount = 0;\n      let errorCount = 0;\n\n      for (const orderReq of newOrderRequests) {\n        try {\n          const newOrder: GTTOrder = {\n            id: `signal_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,\n            symbol: orderReq.symbol,\n            name: orderReq.name,\n            orderType: orderReq.orderType,\n            triggerPrice: orderReq.triggerPrice,\n            quantity: orderReq.quantity,\n            status: 'PENDING',\n            createdAt: new Date(),\n            source: orderReq.source\n          };\n\n          newOrders.push(newOrder);\n          successCount++;\n\n          console.log(`✅ Created GTT order for ${orderReq.symbol}: Trigger=₹${orderReq.triggerPrice.toFixed(2)}, Qty=${orderReq.quantity}`);\n        } catch (error) {\n          console.error(`❌ Failed to create order for ${orderReq.symbol}:`, error);\n          errorCount++;\n        }\n      }\n\n      // Add new orders to the existing orders\n      setOrders(prevOrders => [...prevOrders, ...newOrders]);\n      setLastSync(new Date());\n\n      console.log(`🎉 Successfully created ${successCount} GTT orders (${errorCount} errors)`);\n\n      // Show success message\n      if (successCount > 0) {\n        const totalValue = newOrders.reduce((sum, order) => sum + (order.triggerPrice * order.quantity), 0);\n        const message = `🎉 Successfully created ${successCount} GTT orders for BOH eligible stocks!\\n\\n` +\n                       `📊 Summary:\\n` +\n                       `• Total Orders: ${successCount}\\n` +\n                       `• Total Investment: ₹${totalValue.toLocaleString()}\\n` +\n                       `• Average Trigger Price: ₹${(totalValue / newOrders.reduce((sum, order) => sum + order.quantity, 0)).toFixed(2)}\\n\\n` +\n                       `✅ All orders are now visible in the GTT Buy on Signal tab.`;\n\n        alert(message);\n        console.log('🎉 GTT Orders Created Successfully:', {\n          successCount,\n          totalValue,\n          orders: newOrders.map(o => ({ symbol: o.symbol, trigger: o.triggerPrice, qty: o.quantity }))\n        });\n      }\n\n      if (errorCount > 0) {\n        const errorMsg = `Created ${successCount} orders successfully, but ${errorCount} failed`;\n        console.warn('⚠️ Some GTT orders failed:', errorMsg);\n        setCreateOrdersError(errorMsg);\n      }\n\n    } catch (error) {\n      console.error('❌ Error creating signal orders:', error);\n      setCreateOrdersError(error instanceof Error ? error.message : 'Failed to create orders');\n    } finally {\n      setIsCreatingOrders(false);\n    }\n  };\n\n  // Function to clear all pending signal orders (for weekly cleanup)\n  const clearPendingSignalOrders = () => {\n    setOrders(prevOrders =>\n      prevOrders.filter(order =>\n        !(order.source === 'SIGNAL' && order.status === 'PENDING')\n      )\n    );\n    console.log('🧹 Cleared all pending signal orders');\n  };\n\n  // Function to get signal orders statistics\n  const getSignalOrdersStats = () => {\n    const signalOrders = orders.filter(order => order.source === 'SIGNAL');\n    return {\n      total: signalOrders.length,\n      pending: signalOrders.filter(order => order.status === 'PENDING').length,\n      triggered: signalOrders.filter(order => order.status === 'TRIGGERED').length,\n      cancelled: signalOrders.filter(order => order.status === 'CANCELLED').length,\n      expired: signalOrders.filter(order => order.status === 'EXPIRED').length\n    };\n  };\n\n  // Automatic GTT Service functions\n  const initializeAutoGTTService = async () => {\n    try {\n      const response = await fetch('/api/auto-gtt/initialize', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' }\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        console.log('✅ Auto GTT Service initialized');\n        setAutoGTTEnabled(data.data.service.isEnabled);\n        addAutoGTTNotification('🚀 Automatic GTT order detection started');\n      } else {\n        console.error('❌ Failed to initialize Auto GTT Service:', data.error);\n      }\n    } catch (error) {\n      console.error('❌ Error initializing auto GTT service:', error);\n    }\n  };\n\n  const fetchAutoGTTStatus = async () => {\n    try {\n      const response = await fetch('/api/auto-gtt?action=status');\n      const data = await response.json();\n\n      if (data.success) {\n        setAutoGTTStatus(data.data);\n        setAutoGTTEnabled(data.data.service.isEnabled);\n      }\n    } catch (error) {\n      console.error('❌ Error fetching auto GTT status:', error);\n    }\n  };\n\n  const toggleAutoGTTService = async () => {\n    try {\n      const action = autoGTTEnabled ? 'stop' : 'start';\n      const response = await fetch('/api/auto-gtt', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ action })\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        setAutoGTTEnabled(!autoGTTEnabled);\n        await fetchAutoGTTStatus();\n\n        const message = autoGTTEnabled ?\n          '⏹️ Automatic GTT order creation stopped' :\n          '🚀 Automatic GTT order creation started';\n\n        addAutoGTTNotification(message);\n      }\n    } catch (error) {\n      console.error('❌ Error toggling auto GTT service:', error);\n    }\n  };\n\n  const addAutoGTTNotification = (message: string) => {\n    setAutoGTTNotifications(prev => [message, ...prev.slice(0, 4)]); // Keep last 5 notifications\n\n    // Auto-remove notification after 5 seconds\n    setTimeout(() => {\n      setAutoGTTNotifications(prev => prev.filter(n => n !== message));\n    }, 5000);\n  };\n\n  // Use Central Data Manager for real-time GTT orders\n  useEffect(() => {\n    if (centralGTTOrders.length > 0) {\n      // Convert central GTT orders to display format\n      const displayOrders: GTTOrder[] = centralGTTOrders.map((centralOrder: any) => ({\n        id: centralOrder.id,\n        symbol: centralOrder.symbol,\n        name: centralOrder.name,\n        orderType: centralOrder.orderType,\n        triggerPrice: centralOrder.triggerPrice,\n        quantity: centralOrder.quantity,\n        status: centralOrder.status,\n        createdAt: new Date(centralOrder.createdAt),\n        source: centralOrder.source,\n        autoCreated: centralOrder.autoCreated,\n        signalStrength: centralOrder.signalStrength\n      }));\n\n      setOrders(displayOrders);\n      console.log(`📊 Updated orders from Central Data Manager: ${displayOrders.length} orders`);\n    }\n  }, [centralGTTOrders]);\n\n  // Legacy load auto GTT orders function (now uses Central Data Manager)\n  const loadAutoGTTOrders = async () => {\n    console.log('🔄 Refreshing GTT orders via Central Data Manager...');\n    await refreshGTTOrders();\n  };\n\n  // Initialize data on component mount\n  useEffect(() => {\n    const initializeData = async () => {\n      console.log('🚀 Initializing GTT Orders page...');\n\n      // Initialize with empty orders\n      setOrders([]);\n      setLastSync(new Date());\n\n      // Initialize automatic GTT service first\n      await initializeAutoGTTService();\n\n      // Then load orders and status\n      await Promise.all([\n        loadAutoGTTOrders(),\n        fetchAutoGTTStatus()\n      ]);\n\n      console.log('✅ GTT Orders page initialization complete');\n    };\n\n    initializeData();\n\n    // Set up periodic status updates\n    const statusInterval = setInterval(fetchAutoGTTStatus, 30000); // Every 30 seconds\n\n    // Set up periodic order refresh\n    const orderInterval = setInterval(loadAutoGTTOrders, 60000); // Every 60 seconds\n\n    return () => {\n      clearInterval(statusInterval);\n      clearInterval(orderInterval);\n    };\n  }, []);\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'PENDING': return 'text-yellow-600 bg-yellow-100';\n      case 'TRIGGERED': return 'text-green-600 bg-green-100';\n      case 'CANCELLED': return 'text-red-600 bg-red-100';\n      case 'EXPIRED': return 'text-gray-600 bg-gray-100';\n      default: return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'PENDING': return <Clock className=\"h-4 w-4\" />;\n      case 'TRIGGERED': return <CheckCircle className=\"h-4 w-4\" />;\n      case 'CANCELLED': return <XCircle className=\"h-4 w-4\" />;\n      case 'EXPIRED': return <AlertCircle className=\"h-4 w-4\" />;\n      default: return <Clock className=\"h-4 w-4\" />;\n    }\n  };\n\n  const getTabInfo = (tab: TabType) => {\n    switch (tab) {\n      case 'SIGNAL':\n        return {\n          title: 'GTT Buy on Signal',\n          description: 'Automated buy orders based on Weekly High Signal data',\n          icon: <Activity className=\"h-5 w-5\" />,\n          automation: 'Auto-created every Friday 8:00 PM'\n        };\n      case 'HOLDING':\n        return {\n          title: 'GTT Buy on Holding',\n          description: 'Additional buy orders at lower support levels for existing holdings',\n          icon: <TrendingDown className=\"h-5 w-5\" />,\n          automation: 'Auto-created when Ignorable Lower Price is calculated'\n        };\n      case 'SALE':\n        return {\n          title: 'GTT Sale',\n          description: 'Sell orders triggered when target prices are reached',\n          icon: <Target className=\"h-5 w-5\" />,\n          automation: 'Auto-created when Target Price is set for holdings'\n        };\n    }\n  };\n\n  const OrdersTable = () => (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n      <div className=\"overflow-x-auto\">\n        <table className=\"min-w-full divide-y divide-gray-200\">\n          <thead className=\"bg-gray-50\">\n            <tr>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Stock\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Order Type\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Trigger Price\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Quantity\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Status\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Created Date\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Actions\n              </th>\n            </tr>\n          </thead>\n          <tbody className=\"bg-white divide-y divide-gray-200\">\n            {filteredOrders.length > 0 ? (\n              filteredOrders.map((order) => (\n                <tr key={order.id} className=\"hover:bg-gray-50\">\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div>\n                      <div className=\"text-sm font-medium text-gray-900\">{order.symbol}</div>\n                      <div className=\"text-sm text-gray-500 truncate max-w-xs\">{order.name}</div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      <div className={`p-1 rounded-full mr-2 ${order.orderType === 'BUY' ? 'bg-green-100' : 'bg-red-100'}`}>\n                        {order.orderType === 'BUY' ? (\n                          <TrendingUp className=\"h-3 w-3 text-green-600\" />\n                        ) : (\n                          <TrendingDown className=\"h-3 w-3 text-red-600\" />\n                        )}\n                      </div>\n                      <span className={`text-sm font-medium ${order.orderType === 'BUY' ? 'text-green-600' : 'text-red-600'}`}>\n                        {order.orderType}\n                      </span>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {formatCurrency(order.triggerPrice)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {order.quantity}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>\n                      {getStatusIcon(order.status)}\n                      <span className=\"ml-1\">{order.status}</span>\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    {formatDateTime(order.createdAt)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <button className=\"text-red-600 hover:text-red-900 mr-3\">\n                      Cancel\n                    </button>\n                    <button className=\"text-blue-600 hover:text-blue-900\">\n                      View\n                    </button>\n                  </td>\n                </tr>\n              ))\n            ) : (\n              <tr>\n                <td colSpan={7} className=\"px-6 py-12 text-center\">\n                  <div className=\"text-gray-500\">\n                    <Clock className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n                    <p>No GTT orders found for {getTabInfo(activeTab).title}.</p>\n                    <p className=\"text-sm mt-1\">{getTabInfo(activeTab).automation}</p>\n                  </div>\n                </td>\n              </tr>\n            )}\n          </tbody>\n        </table>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Page Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">GTT Orders</h1>\n          <p className=\"text-gray-600 mt-1\">Automated Good Till Triggered order management</p>\n        </div>\n        <div className=\"flex items-center space-x-4\">\n          {/* Angel One Connection Status */}\n          <div className=\"flex items-center space-x-2\">\n            {isAngelOneConnected ? (\n              <div className=\"flex items-center space-x-2 text-green-600\">\n                <Wifi className=\"h-4 w-4\" />\n                <span className=\"text-sm font-medium\">Angel One Connected</span>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-2 text-red-600\">\n                <WifiOff className=\"h-4 w-4\" />\n                <span className=\"text-sm font-medium\">Angel One Disconnected</span>\n              </div>\n            )}\n          </div>\n          <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2\">\n            <RefreshCw className=\"h-4 w-4\" />\n            <span>Sync Orders</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Automation Status */}\n      <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-2\">\n            <Calendar className=\"h-5 w-5 text-blue-600\" />\n            <div>\n              <p className=\"text-sm font-medium text-blue-900\">Next Automation: Friday 8:00 PM</p>\n              <p className=\"text-xs text-blue-700\">\n                Weekly High Signal orders will be automatically created/updated\n                {lastSync && ` • Last sync: ${formatDateTime(lastSync)}`}\n              </p>\n            </div>\n          </div>\n\n          {/* Real-time Auto GTT Controls */}\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"flex items-center space-x-2\">\n              <div className={`w-2 h-2 rounded-full ${autoGTTEnabled ? 'bg-green-500' : 'bg-gray-400'}`}></div>\n              <span className=\"text-xs text-gray-600\">\n                Real-time Auto GTT {autoGTTEnabled ? 'ON' : 'OFF'}\n              </span>\n            </div>\n\n            <button\n              onClick={toggleAutoGTTService}\n              className={`px-3 py-1 rounded-md text-xs font-medium flex items-center space-x-1 transition-colors ${\n                autoGTTEnabled\n                  ? 'bg-red-100 text-red-700 hover:bg-red-200'\n                  : 'bg-green-100 text-green-700 hover:bg-green-200'\n              }`}\n            >\n              {autoGTTEnabled ? <Pause className=\"h-3 w-3\" /> : <Play className=\"h-3 w-3\" />}\n              <span>{autoGTTEnabled ? 'Stop' : 'Start'}</span>\n            </button>\n\n            <button\n              onClick={() => setShowAutoGTTSettings(!showAutoGTTSettings)}\n              className=\"px-3 py-1 bg-gray-100 text-gray-700 rounded-md text-xs font-medium hover:bg-gray-200 transition-colors flex items-center space-x-1\"\n            >\n              <Settings className=\"h-3 w-3\" />\n              <span>Settings</span>\n            </button>\n          </div>\n        </div>\n\n        {/* Auto GTT Status Details */}\n        {autoGTTStatus && (\n          <div className=\"mt-3 pt-3 border-t border-blue-200\">\n            <div className=\"grid grid-cols-4 gap-4 text-xs\">\n              <div>\n                <span className=\"text-blue-600 font-medium\">Market Status:</span>\n                <span className={`ml-1 ${autoGTTStatus.detector.isMarketOpen ? 'text-green-600' : 'text-gray-600'}`}>\n                  {autoGTTStatus.detector.isMarketOpen ? 'Open' : 'Closed'}\n                </span>\n              </div>\n              <div>\n                <span className=\"text-blue-600 font-medium\">Signals:</span>\n                <span className=\"ml-1 text-gray-700\">{autoGTTStatus.detector.lastSignalCount || 0}</span>\n              </div>\n              <div>\n                <span className=\"text-blue-600 font-medium\">Auto Orders:</span>\n                <span className=\"ml-1 text-gray-700\">{autoGTTStatus.service.autoCreatedOrders || 0}</span>\n              </div>\n              <div>\n                <span className=\"text-blue-600 font-medium\">Today:</span>\n                <span className=\"ml-1 text-gray-700\">\n                  {autoGTTStatus.service.todayOrders || 0}/{autoGTTStatus.service.dailyLimit || 20}\n                </span>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Auto GTT Notifications */}\n      {autoGTTNotifications.length > 0 && (\n        <div className=\"space-y-2\">\n          {autoGTTNotifications.map((notification, index) => (\n            <div key={index} className=\"bg-green-50 border border-green-200 rounded-lg p-3 flex items-center space-x-2\">\n              <BellRing className=\"h-4 w-4 text-green-600\" />\n              <span className=\"text-green-800 text-sm\">{notification}</span>\n            </div>\n          ))}\n        </div>\n      )}\n\n      {/* Auto GTT Settings Modal */}\n      {showAutoGTTSettings && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-lg p-6 w-full max-w-md\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h3 className=\"text-lg font-semibold text-gray-900\">Auto GTT Settings</h3>\n              <button\n                onClick={() => setShowAutoGTTSettings(false)}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <XCircle className=\"h-5 w-5\" />\n              </button>\n            </div>\n\n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Polling Interval (minutes)\n                </label>\n                <input\n                  type=\"number\"\n                  min=\"1\"\n                  max=\"60\"\n                  defaultValue=\"5\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Minimum Signal Strength\n                </label>\n                <select className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                  <option value=\"MODERATE\">Moderate</option>\n                  <option value=\"STRONG\">Strong Only</option>\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Max Orders Per Day\n                </label>\n                <input\n                  type=\"number\"\n                  min=\"1\"\n                  max=\"50\"\n                  defaultValue=\"20\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\"\n                />\n              </div>\n\n              <div className=\"flex items-center space-x-2\">\n                <input\n                  type=\"checkbox\"\n                  id=\"marketHoursOnly\"\n                  defaultChecked\n                  className=\"rounded border-gray-300\"\n                />\n                <label htmlFor=\"marketHoursOnly\" className=\"text-sm text-gray-700\">\n                  Only during market hours (9:15 AM - 3:30 PM)\n                </label>\n              </div>\n\n              <div className=\"flex items-center space-x-2\">\n                <input\n                  type=\"checkbox\"\n                  id=\"volumeConfirmation\"\n                  defaultChecked\n                  className=\"rounded border-gray-300\"\n                />\n                <label htmlFor=\"volumeConfirmation\" className=\"text-sm text-gray-700\">\n                  Require volume confirmation (20% above average)\n                </label>\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3 mt-6\">\n              <button\n                onClick={() => setShowAutoGTTSettings(false)}\n                className=\"px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors\"\n              >\n                Cancel\n              </button>\n              <button\n                onClick={() => {\n                  // TODO: Save settings\n                  setShowAutoGTTSettings(false);\n                  addAutoGTTNotification('⚙️ Auto GTT settings updated');\n                }}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\"\n              >\n                Save Settings\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Tab Navigation */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n        <div className=\"border-b border-gray-200\">\n          <nav className=\"flex space-x-8 px-6\">\n            {(['SIGNAL', 'HOLDING', 'SALE'] as const).map((tab) => {\n              const tabInfo = getTabInfo(tab);\n              const tabOrders = orders.filter(o => o.source === tab);\n              return (\n                <button\n                  key={tab}\n                  onClick={() => setActiveTab(tab)}\n                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors flex items-center space-x-2 ${\n                    activeTab === tab\n                      ? 'border-blue-500 text-blue-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  {tabInfo.icon}\n                  <span>{tabInfo.title}</span>\n                  <span className=\"bg-gray-100 text-gray-600 px-2 py-0.5 rounded-full text-xs\">\n                    {tabOrders.length}\n                  </span>\n                </button>\n              );\n            })}\n          </nav>\n        </div>\n\n        {/* Tab Content */}\n        <div className=\"p-6\">\n          <div className=\"mb-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                  {getTabInfo(activeTab).title}\n                </h3>\n                <p className=\"text-gray-600 text-sm mb-1\">\n                  {getTabInfo(activeTab).description}\n                </p>\n                <p className=\"text-blue-600 text-xs font-medium\">\n                  {getTabInfo(activeTab).automation}\n                </p>\n              </div>\n\n              {/* Create All Signal Orders Button - Only show for SIGNAL tab */}\n              {activeTab === 'SIGNAL' && (\n                <div className=\"flex flex-col items-end space-y-2\">\n                  <button\n                    onClick={createAllSignalOrders}\n                    disabled={isCreatingOrders}\n                    className={`px-4 py-2 rounded-lg font-medium text-sm transition-colors flex items-center space-x-2 ${\n                      isCreatingOrders\n                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                        : 'bg-green-600 text-white hover:bg-green-700'\n                    }`}\n                  >\n                    {isCreatingOrders ? (\n                      <>\n                        <RefreshCw className=\"h-4 w-4 animate-spin\" />\n                        <span>Creating Orders...</span>\n                      </>\n                    ) : (\n                      <>\n                        <Plus className=\"h-4 w-4\" />\n                        <span>Create All Signal Orders</span>\n                      </>\n                    )}\n                  </button>\n\n                  {createOrdersError && (\n                    <p className=\"text-red-600 text-xs max-w-xs text-right\">\n                      {createOrdersError}\n                    </p>\n                  )}\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Signal Orders Info - Only show for SIGNAL tab */}\n          {activeTab === 'SIGNAL' && (\n            <div className=\"mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h4 className=\"text-sm font-medium text-blue-900 mb-1\">\n                    Weekly High Signal Orders\n                  </h4>\n                  <p className=\"text-xs text-blue-700\">\n                    Orders are automatically created for BOH eligible stocks from the Weekly High Signal page.\n                    Trigger Price = Last Week's High + ₹0.05, Quantity = ₹2,000 ÷ Trigger Price\n                  </p>\n                </div>\n                <div className=\"text-right\">\n                  <div className=\"text-sm font-medium text-blue-900\">\n                    {getSignalOrdersStats().pending} Pending\n                  </div>\n                  <div className=\"text-xs text-blue-700\">\n                    {getSignalOrdersStats().triggered} Triggered\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Orders Table */}\n          <OrdersTable />\n        </div>\n      </div>\n\n      {/* Summary Statistics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Total Orders</p>\n              <p className=\"text-2xl font-bold text-gray-900 mt-1\">{orders.length}</p>\n            </div>\n            <Activity className=\"h-8 w-8 text-gray-600\" />\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Pending Orders</p>\n              <p className=\"text-2xl font-bold text-yellow-600 mt-1\">\n                {orders.filter(o => o.status === 'PENDING').length}\n              </p>\n            </div>\n            <Clock className=\"h-8 w-8 text-yellow-600\" />\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Triggered Today</p>\n              <p className=\"text-2xl font-bold text-green-600 mt-1\">\n                {orders.filter(o => o.status === 'TRIGGERED').length}\n              </p>\n            </div>\n            <CheckCircle className=\"h-8 w-8 text-green-600\" />\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Success Rate</p>\n              <p className=\"text-2xl font-bold text-gray-900 mt-1\">\n                {orders.length > 0 ? Math.round((orders.filter(o => o.status === 'TRIGGERED').length / orders.length) * 100) : 0}%\n              </p>\n            </div>\n            <TrendingUp className=\"h-8 w-8 text-gray-600\" />\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmBA;AACA;;;AAvBA;;;;;AAwCe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAEpD,gDAAgD;IAChD,MAAM,EACJ,QAAQ,gBAAgB,EACxB,aAAa,cAAc,EAC3B,WAAW,YAAY,EACvB,OAAO,QAAQ,EACf,SAAS,gBAAgB,EAC1B,GAAG,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;IAEf,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;QAC/C,kCAAkC;QAClC;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,WAAW;YACX,cAAc;YACd,UAAU;YACV,QAAQ;YACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;YACpD,QAAQ;QACV;QACA;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,WAAW;YACX,cAAc;YACd,UAAU;YACV,QAAQ;YACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;YACpD,QAAQ;QACV;QACA,mCAAmC;QACnC;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,WAAW;YACX,cAAc;YACd,UAAU;YACV,QAAQ;YACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;YACpD,QAAQ;QACV;QACA,yBAAyB;QACzB;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,WAAW;YACX,cAAc;YACd,UAAU;YACV,QAAQ;YACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;YACpD,QAAQ;QACV;KACD;IAED,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1E,8BAA8B;IAC9B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACxD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE7E,8BAA8B;IAC9B,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;IAe/D,4EAA4E;IAC5E,MAAM,0BAA0B;QAC9B,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,4DAA4D;YAC5D,MAAM,WAAW,MAAM,MAAM,iCAAiC;gBAC5D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,QAAQ,GAAG,CAAC,AAAC,2BAA0C,OAAhB,SAAS,MAAM;YAEtD,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,AAAC,yBAA6C,OAArB,SAAS,MAAM,EAAC,OAAe,OAAV;gBAC5D,MAAM,IAAI,MAAM,AAAC,uBAAsC,OAAhB,SAAS,MAAM;YACxD;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,yBAAyB;YAErC,IAAI,CAAC,KAAK,OAAO,EAAE;gBACjB,QAAQ,KAAK,CAAC,yBAAyB,KAAK,KAAK;gBACjD,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,QAAQ,GAAG,CAAC,AAAC,kBAAyC,OAAxB,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,EAAC;YACtD,QAAQ,GAAG,CAAC,AAAC,mCAA8E,OAA5C,KAAK,IAAI,CAAC,cAAc,EAAC,qBAAyC,OAAtB,KAAK,IAAI,CAAC,WAAW;YAEhH,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,GAAG;gBACvC,QAAQ,GAAG,CAAC,AAAC,iCAA6F,OAA7D,KAAK,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,OAAO,CAAC,IAAG,oBAAwD,OAAtC,KAAK,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC;YAC/I;YAEA,OAAO,KAAK,IAAI,CAAC,MAAM;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM;QACR;IACF;IAEA,4DAA4D;IAC5D,MAAM,wBAAwB;QAC5B,oBAAoB;QACpB,qBAAqB;QAErB,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,gCAAgC;YAChC,MAAM,mBAAmB,MAAM;YAE/B,QAAQ,GAAG,CAAC,AAAC,eAAsC,OAAxB,iBAAiB,MAAM,EAAC;YAEnD,IAAI,iBAAiB,MAAM,KAAK,GAAG;gBACjC,MAAM,WAAW,yEACD,yCACA,sEACA;gBAChB,QAAQ,IAAI,CAAC,QAAQ;gBACrB,qBAAqB;gBACrB;YACF;YAEA,4DAA4D;YAC5D,MAAM,wBAAwB,OAC3B,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK,YAAY,MAAM,MAAM,KAAK,WAC9D,GAAG,CAAC,CAAA,QAAS,MAAM,MAAM;YAE5B,QAAQ,GAAG,CAAC,AAAC,8BAA0D,OAA7B,sBAAsB,MAAM,EAAC,cAAY;YAEnF,MAAM,mBAAmB,iBAAiB,MAAM,CAAC,CAAC,WAChD,CAAC,sBAAsB,QAAQ,CAAC,SAAS,MAAM;YAGjD,QAAQ,GAAG,CAAC,AAAC,0BAAgE,OAAvC,iBAAiB,MAAM,EAAC,iBAA4C,OAA7B,sBAAsB,MAAM,EAAC;YAE1G,IAAI,iBAAiB,MAAM,KAAK,GAAG;gBACjC,MAAM,WAAW,AAAC,OAA8B,OAAxB,iBAAiB,MAAM,EAAC;gBAChD,QAAQ,IAAI,CAAC,QAAQ;gBACrB,qBAAqB;gBACrB;YACF;YAEA,mCAAmC;YACnC,MAAM,YAAwB,EAAE;YAChC,IAAI,eAAe;YACnB,IAAI,aAAa;YAEjB,KAAK,MAAM,YAAY,iBAAkB;gBACvC,IAAI;oBACF,MAAM,WAAqB;wBACzB,IAAI,AAAC,UAAuB,OAAd,KAAK,GAAG,IAAG,KAA+C,OAA5C,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;wBACpE,QAAQ,SAAS,MAAM;wBACvB,MAAM,SAAS,IAAI;wBACnB,WAAW,SAAS,SAAS;wBAC7B,cAAc,SAAS,YAAY;wBACnC,UAAU,SAAS,QAAQ;wBAC3B,QAAQ;wBACR,WAAW,IAAI;wBACf,QAAQ,SAAS,MAAM;oBACzB;oBAEA,UAAU,IAAI,CAAC;oBACf;oBAEA,QAAQ,GAAG,CAAC,AAAC,2BAAuD,OAA7B,SAAS,MAAM,EAAC,eAAsD,OAAzC,SAAS,YAAY,CAAC,OAAO,CAAC,IAAG,UAA0B,OAAlB,SAAS,QAAQ;gBAChI,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,AAAC,gCAA+C,OAAhB,SAAS,MAAM,EAAC,MAAI;oBAClE;gBACF;YACF;YAEA,wCAAwC;YACxC,UAAU,CAAA,aAAc;uBAAI;uBAAe;iBAAU;YACrD,YAAY,IAAI;YAEhB,QAAQ,GAAG,CAAC,AAAC,2BAAsD,OAA5B,cAAa,iBAA0B,OAAX,YAAW;YAE9E,uBAAuB;YACvB,IAAI,eAAe,GAAG;gBACpB,MAAM,aAAa,UAAU,MAAM,CAAC,CAAC,KAAK,QAAU,MAAO,MAAM,YAAY,GAAG,MAAM,QAAQ,EAAG;gBACjG,MAAM,UAAU,AAAC,2BAAuC,OAAb,cAAa,8CACxC,kBACD,AAAC,mBAA+B,OAAb,cAAa,QAChC,AAAC,wBAAmD,OAA5B,WAAW,cAAc,IAAG,QACpD,AAAC,6BAAgH,OAApF,CAAC,aAAa,UAAU,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,QAAQ,EAAE,EAAE,EAAE,OAAO,CAAC,IAAG,UAChH;gBAEhB,MAAM;gBACN,QAAQ,GAAG,CAAC,uCAAuC;oBACjD;oBACA;oBACA,QAAQ,UAAU,GAAG,CAAC,CAAA,IAAK,CAAC;4BAAE,QAAQ,EAAE,MAAM;4BAAE,SAAS,EAAE,YAAY;4BAAE,KAAK,EAAE,QAAQ;wBAAC,CAAC;gBAC5F;YACF;YAEA,IAAI,aAAa,GAAG;gBAClB,MAAM,WAAW,AAAC,WAAmD,OAAzC,cAAa,8BAAuC,OAAX,YAAW;gBAChF,QAAQ,IAAI,CAAC,8BAA8B;gBAC3C,qBAAqB;YACvB;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,qBAAqB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAChE,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,mEAAmE;IACnE,MAAM,2BAA2B;QAC/B,UAAU,CAAA,aACR,WAAW,MAAM,CAAC,CAAA,QAChB,CAAC,CAAC,MAAM,MAAM,KAAK,YAAY,MAAM,MAAM,KAAK,SAAS;QAG7D,QAAQ,GAAG,CAAC;IACd;IAEA,2CAA2C;IAC3C,MAAM,uBAAuB;QAC3B,MAAM,eAAe,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;QAC7D,OAAO;YACL,OAAO,aAAa,MAAM;YAC1B,SAAS,aAAa,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK,WAAW,MAAM;YACxE,WAAW,aAAa,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK,aAAa,MAAM;YAC5E,WAAW,aAAa,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK,aAAa,MAAM;YAC5E,SAAS,aAAa,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK,WAAW,MAAM;QAC1E;IACF;IAEA,kCAAkC;IAClC,MAAM,2BAA2B;QAC/B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,4BAA4B;gBACvD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,QAAQ,GAAG,CAAC;gBACZ,kBAAkB,KAAK,IAAI,CAAC,OAAO,CAAC,SAAS;gBAC7C,uBAAuB;YACzB,OAAO;gBACL,QAAQ,KAAK,CAAC,4CAA4C,KAAK,KAAK;YACtE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;QAC1D;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,iBAAiB,KAAK,IAAI;gBAC1B,kBAAkB,KAAK,IAAI,CAAC,OAAO,CAAC,SAAS;YAC/C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,SAAS,iBAAiB,SAAS;YACzC,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAO;YAChC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,kBAAkB,CAAC;gBACnB,MAAM;gBAEN,MAAM,UAAU,iBACd,4CACA;gBAEF,uBAAuB;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;QACtD;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,wBAAwB,CAAA,OAAQ;gBAAC;mBAAY,KAAK,KAAK,CAAC,GAAG;aAAG,GAAG,4BAA4B;QAE7F,2CAA2C;QAC3C,WAAW;YACT,wBAAwB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,MAAM;QACzD,GAAG;IACL;IAEA,oDAAoD;IACpD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,iBAAiB,MAAM,GAAG,GAAG;gBAC/B,+CAA+C;gBAC/C,MAAM,gBAA4B,iBAAiB,GAAG;6DAAC,CAAC,eAAsB,CAAC;4BAC7E,IAAI,aAAa,EAAE;4BACnB,QAAQ,aAAa,MAAM;4BAC3B,MAAM,aAAa,IAAI;4BACvB,WAAW,aAAa,SAAS;4BACjC,cAAc,aAAa,YAAY;4BACvC,UAAU,aAAa,QAAQ;4BAC/B,QAAQ,aAAa,MAAM;4BAC3B,WAAW,IAAI,KAAK,aAAa,SAAS;4BAC1C,QAAQ,aAAa,MAAM;4BAC3B,aAAa,aAAa,WAAW;4BACrC,gBAAgB,aAAa,cAAc;wBAC7C,CAAC;;gBAED,UAAU;gBACV,QAAQ,GAAG,CAAC,AAAC,gDAAoE,OAArB,cAAc,MAAM,EAAC;YACnF;QACF;kCAAG;QAAC;KAAiB;IAErB,uEAAuE;IACvE,MAAM,oBAAoB;QACxB,QAAQ,GAAG,CAAC;QACZ,MAAM;IACR;IAEA,qCAAqC;IACrC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;0DAAiB;oBACrB,QAAQ,GAAG,CAAC;oBAEZ,+BAA+B;oBAC/B,UAAU,EAAE;oBACZ,YAAY,IAAI;oBAEhB,yCAAyC;oBACzC,MAAM;oBAEN,8BAA8B;oBAC9B,MAAM,QAAQ,GAAG,CAAC;wBAChB;wBACA;qBACD;oBAED,QAAQ,GAAG,CAAC;gBACd;;YAEA;YAEA,iCAAiC;YACjC,MAAM,iBAAiB,YAAY,oBAAoB,QAAQ,mBAAmB;YAElF,gCAAgC;YAChC,MAAM,gBAAgB,YAAY,mBAAmB,QAAQ,mBAAmB;YAEhF;2CAAO;oBACL,cAAc;oBACd,cAAc;gBAChB;;QACF;kCAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAW,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YACxC,KAAK;gBAAa,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChD,KAAK;gBAAa,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5C,KAAK;gBAAW,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAC9C;gBAAS,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QACnC;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBAC1B,YAAY;gBACd;YACF,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,oBAAM,6LAAC,yNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;oBAC9B,YAAY;gBACd;YACF,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,oBAAM,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;oBACxB,YAAY;gBACd;QACJ;IACF;IAEA,MAAM,cAAc,kBAClB,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAM,WAAU;;sCACf,6LAAC;4BAAM,WAAU;sCACf,cAAA,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;;;;;;;;;;;;sCAKnG,6LAAC;4BAAM,WAAU;sCACd,eAAe,MAAM,GAAG,IACvB,eAAe,GAAG,CAAC,CAAC,sBAClB,6LAAC;oCAAkB,WAAU;;sDAC3B,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;kEAAqC,MAAM,MAAM;;;;;;kEAChE,6LAAC;wDAAI,WAAU;kEAA2C,MAAM,IAAI;;;;;;;;;;;;;;;;;sDAGxE,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,AAAC,yBAAkF,OAA1D,MAAM,SAAS,KAAK,QAAQ,iBAAiB;kEACnF,MAAM,SAAS,KAAK,sBACnB,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;iFAEtB,6LAAC,yNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;kEAG5B,6LAAC;wDAAK,WAAW,AAAC,uBAAoF,OAA9D,MAAM,SAAS,KAAK,QAAQ,mBAAmB;kEACpF,MAAM,SAAS;;;;;;;;;;;;;;;;;sDAItB,6LAAC;4CAAG,WAAU;sDACX,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,YAAY;;;;;;sDAEpC,6LAAC;4CAAG,WAAU;sDACX,MAAM,QAAQ;;;;;;sDAEjB,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAK,WAAW,AAAC,2EAAuG,OAA7B,eAAe,MAAM,MAAM;;oDACpH,cAAc,MAAM,MAAM;kEAC3B,6LAAC;wDAAK,WAAU;kEAAQ,MAAM,MAAM;;;;;;;;;;;;;;;;;sDAGxC,6LAAC;4CAAG,WAAU;sDACX,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,SAAS;;;;;;sDAEjC,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAO,WAAU;8DAAuC;;;;;;8DAGzD,6LAAC;oDAAO,WAAU;8DAAoC;;;;;;;;;;;;;mCAxCjD,MAAM,EAAE;;;;0DA+CnB,6LAAC;0CACC,cAAA,6LAAC;oCAAG,SAAS;oCAAG,WAAU;8CACxB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;;oDAAE;oDAAyB,WAAW,WAAW,KAAK;oDAAC;;;;;;;0DACxD,6LAAC;gDAAE,WAAU;0DAAgB,WAAW,WAAW,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAW/E,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAEpC,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACZ,oCACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;yDAGxC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,6LAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;;;;;;0CAI5C,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;;oDAAwB;oDAElC,YAAY,AAAC,iBAAyC,OAAzB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;;;;;;;;0CAMnD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,AAAC,wBAAuE,OAAhD,iBAAiB,iBAAiB;;;;;;0DAC1E,6LAAC;gDAAK,WAAU;;oDAAwB;oDAClB,iBAAiB,OAAO;;;;;;;;;;;;;kDAIhD,6LAAC;wCACC,SAAS;wCACT,WAAW,AAAC,0FAIX,OAHC,iBACI,6CACA;;4CAGL,+BAAiB,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;qEAAe,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAClE,6LAAC;0DAAM,iBAAiB,SAAS;;;;;;;;;;;;kDAGnC,6LAAC;wCACC,SAAS,IAAM,uBAAuB,CAAC;wCACvC,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;oBAMX,+BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAK,WAAU;sDAA4B;;;;;;sDAC5C,6LAAC;4CAAK,WAAW,AAAC,QAAgF,OAAzE,cAAc,QAAQ,CAAC,YAAY,GAAG,mBAAmB;sDAC/E,cAAc,QAAQ,CAAC,YAAY,GAAG,SAAS;;;;;;;;;;;;8CAGpD,6LAAC;;sDACC,6LAAC;4CAAK,WAAU;sDAA4B;;;;;;sDAC5C,6LAAC;4CAAK,WAAU;sDAAsB,cAAc,QAAQ,CAAC,eAAe,IAAI;;;;;;;;;;;;8CAElF,6LAAC;;sDACC,6LAAC;4CAAK,WAAU;sDAA4B;;;;;;sDAC5C,6LAAC;4CAAK,WAAU;sDAAsB,cAAc,OAAO,CAAC,iBAAiB,IAAI;;;;;;;;;;;;8CAEnF,6LAAC;;sDACC,6LAAC;4CAAK,WAAU;sDAA4B;;;;;;sDAC5C,6LAAC;4CAAK,WAAU;;gDACb,cAAc,OAAO,CAAC,WAAW,IAAI;gDAAE;gDAAE,cAAc,OAAO,CAAC,UAAU,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASzF,qBAAqB,MAAM,GAAG,mBAC7B,6LAAC;gBAAI,WAAU;0BACZ,qBAAqB,GAAG,CAAC,CAAC,cAAc,sBACvC,6LAAC;wBAAgB,WAAU;;0CACzB,6LAAC,iNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;gCAAK,WAAU;0CAA0B;;;;;;;uBAFlC;;;;;;;;;;YASf,qCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,6LAAC;oCACC,SAAS,IAAM,uBAAuB;oCACtC,WAAU;8CAEV,cAAA,6LAAC,+MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAIvB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACC,MAAK;4CACL,KAAI;4CACJ,KAAI;4CACJ,cAAa;4CACb,WAAU;;;;;;;;;;;;8CAId,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CAAO,WAAU;;8DAChB,6LAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,6LAAC;oDAAO,OAAM;8DAAS;;;;;;;;;;;;;;;;;;8CAI3B,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACC,MAAK;4CACL,KAAI;4CACJ,KAAI;4CACJ,cAAa;4CACb,WAAU;;;;;;;;;;;;8CAId,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,cAAc;4CACd,WAAU;;;;;;sDAEZ,6LAAC;4CAAM,SAAQ;4CAAkB,WAAU;sDAAwB;;;;;;;;;;;;8CAKrE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,cAAc;4CACd,WAAU;;;;;;sDAEZ,6LAAC;4CAAM,SAAQ;4CAAqB,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAM1E,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,uBAAuB;oCACtC,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS;wCACP,sBAAsB;wCACtB,uBAAuB;wCACvB,uBAAuB;oCACzB;oCACA,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,AAAC;gCAAC;gCAAU;gCAAW;6BAAO,CAAW,GAAG,CAAC,CAAC;gCAC7C,MAAM,UAAU,WAAW;gCAC3B,MAAM,YAAY,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;gCAClD,qBACE,6LAAC;oCAEC,SAAS,IAAM,aAAa;oCAC5B,WAAW,AAAC,0FAIX,OAHC,cAAc,MACV,kCACA;;wCAGL,QAAQ,IAAI;sDACb,6LAAC;sDAAM,QAAQ,KAAK;;;;;;sDACpB,6LAAC;4CAAK,WAAU;sDACb,UAAU,MAAM;;;;;;;mCAXd;;;;;4BAeX;;;;;;;;;;;kCAKJ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DACX,WAAW,WAAW,KAAK;;;;;;8DAE9B,6LAAC;oDAAE,WAAU;8DACV,WAAW,WAAW,WAAW;;;;;;8DAEpC,6LAAC;oDAAE,WAAU;8DACV,WAAW,WAAW,UAAU;;;;;;;;;;;;wCAKpC,cAAc,0BACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS;oDACT,UAAU;oDACV,WAAW,AAAC,0FAIX,OAHC,mBACI,iDACA;8DAGL,iCACC;;0EACE,6LAAC,mNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;0EACrB,6LAAC;0EAAK;;;;;;;qFAGR;;0EACE,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC;0EAAK;;;;;;;;;;;;;gDAKX,mCACC,6LAAC;oDAAE,WAAU;8DACV;;;;;;;;;;;;;;;;;;;;;;;4BASZ,cAAc,0BACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAyC;;;;;;8DAGvD,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAKvC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;wDACZ,uBAAuB,OAAO;wDAAC;;;;;;;8DAElC,6LAAC;oDAAI,WAAU;;wDACZ,uBAAuB,SAAS;wDAAC;;;;;;;;;;;;;;;;;;;;;;;;0CAQ5C,6LAAC;;;;;;;;;;;;;;;;;0BAKL,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAyC,OAAO,MAAM;;;;;;;;;;;;8CAErE,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIxB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDACV,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;;;;;;;;;;;;8CAGtD,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIrB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDACV,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;;;;;;;;;;;;8CAGxD,6LAAC,8NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAI3B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;;gDACV,OAAO,MAAM,GAAG,IAAI,KAAK,KAAK,CAAC,AAAC,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM,GAAG,OAAO,MAAM,GAAI,OAAO;gDAAE;;;;;;;;;;;;;8CAGrH,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;GAj5BwB;;QAUlB,iIAAA,CAAA,eAAY;;;KAVM", "debugId": null}}]}