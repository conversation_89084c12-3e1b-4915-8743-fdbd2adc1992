'use client';

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { backgroundDataService } from '@/lib/background-data-service';

interface BackgroundDataContextType {
  isInitialized: boolean;
  isUpdating: boolean;
  error: Error | null;
  forceUpdate: () => Promise<void>;
}

const BackgroundDataContext = createContext<BackgroundDataContextType | undefined>(undefined);

interface BackgroundDataProviderProps {
  children: ReactNode;
}

export function BackgroundDataProvider({ children }: BackgroundDataProviderProps) {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    let mounted = true;

    const initializeBackgroundService = async () => {
      try {
        console.log('🚀 Initializing background data service from provider...');

        // Initialize the background service
        await backgroundDataService.initialize();

        if (mounted) {
          setIsInitialized(true);
          setError(null);
          console.log('✅ Background data service initialized successfully');
        }
      } catch (err) {
        console.error('❌ Failed to initialize background data service:', err);
        if (mounted) {
          setError(err as Error);
          // Still set as initialized to allow pages to work
          setIsInitialized(true);
        }
      }
    };

    // Start initialization after a short delay to avoid blocking initial render
    const initTimer = setTimeout(() => {
      initializeBackgroundService();
    }, 500);

    // Cleanup
    return () => {
      mounted = false;
      clearTimeout(initTimer);
    };
  }, []);

  const forceUpdate = async () => {
    setIsUpdating(true);
    setError(null);
    
    try {
      await backgroundDataService.forceUpdate();
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setIsUpdating(false);
    }
  };

  const contextValue: BackgroundDataContextType = {
    isInitialized,
    isUpdating,
    error,
    forceUpdate
  };

  return (
    <BackgroundDataContext.Provider value={contextValue}>
      {children}
    </BackgroundDataContext.Provider>
  );
}

export function useBackgroundDataContext() {
  const context = useContext(BackgroundDataContext);
  if (context === undefined) {
    throw new Error('useBackgroundDataContext must be used within a BackgroundDataProvider');
  }
  return context;
}
