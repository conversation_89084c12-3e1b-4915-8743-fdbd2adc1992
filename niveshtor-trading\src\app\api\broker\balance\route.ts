import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || 'default-user';

    // Try to fetch from SmartAPI backend
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch('http://localhost:8000/api/smartapi/profile', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const profileData = await response.json();

        if (profileData.status === 'success' && profileData.data) {
          // Extract balance data from SmartAPI profile
          const balanceData = {
            id: `balance-${userId}-${Date.now()}`,
            availableCash: profileData.data.availablecash || 0,
            marginUsed: profileData.data.marginused || 0,
            marginAvailable: profileData.data.marginAvailable || 0,
            totalBalance: (profileData.data.availablecash || 0) + (profileData.data.marginused || 0),
            lastSyncAt: new Date().toISOString()
          };

          return NextResponse.json({
            success: true,
            data: balanceData,
            source: 'smartapi'
          });
        }
      }
    } catch (backendError) {
      console.log('SmartAPI backend not available:', backendError);
    }

    // If SmartAPI is not available, try to get data from local storage or return empty state
    const fallbackBalance = {
      id: `balance-${userId}-fallback`,
      availableCash: 0,
      marginUsed: 0,
      marginAvailable: 0,
      totalBalance: 0,
      lastSyncAt: new Date().toISOString()
    };

    return NextResponse.json({
      success: false,
      error: 'Broker not connected. Please connect your broker account.',
      data: fallbackBalance,
      source: 'fallback'
    });

  } catch (error) {
    console.error('Error fetching broker balance:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch broker balance',
      data: null
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || 'default-user';

    // Force refresh balance from broker by calling GET endpoint
    const baseUrl = request.nextUrl.origin;
    const response = await fetch(`${baseUrl}/api/broker/balance?userId=${userId}`, {
      method: 'GET'
    });

    const data = await response.json();

    return NextResponse.json({
      success: data.success,
      message: data.success ? 'Balance refreshed successfully' : 'Failed to refresh balance',
      data: data.data,
      source: data.source
    });

  } catch (error) {
    console.error('Error refreshing broker balance:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to refresh broker balance'
    }, { status: 500 });
  }
}
