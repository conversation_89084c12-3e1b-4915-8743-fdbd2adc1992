'use client';

import { useState, useEffect } from 'react';
import { 
  Search, 
  RefreshCw,
  Filter,
  AlertCircle,
  Loader
} from 'lucide-react';
import { formatCurrency, formatPercentage, getChangeColor } from '@/lib/utils';
import { NiftyStock } from '@/lib/nifty-stocks';
import { cacheService, CacheKeys } from '@/lib/cache-service';
import { StockListSkeleton } from '@/components/ui/LoadingStates';
import { RealTimeIndicator } from '@/components/ui/RealTimeIndicator';
import { useBackgroundData } from '@/hooks/useBackgroundData';
import { useRealTimeStocks } from '@/hooks/useRealTimeStocks';
import { useBOHEligibleStocks } from '@/hooks/useCentralData';

export default function BOHEligiblePage() {
  // Central Data Manager for instant BOH eligible stocks
  const {
    stocks: bohStocks,
    lastUpdated: bohLastUpdated,
    isLoading: bohIsLoading,
    error: bohError,
    refresh: refreshBOHData
  } = useBOHEligibleStocks();

  // Background data hook for stock names (fallback)
  const {
    isNamesReady,
    getStockName,
    forceUpdate: forceBackgroundUpdate,
    error: backgroundError
  } = useBackgroundData();

  // Search state
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<NiftyStock[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  // Nifty 200 stocks state - now loads instantly with cached names
  const [niftyStocks, setNiftyStocks] = useState<NiftyStock[]>([]);
  const [loadingPriceData, setLoadingPriceData] = useState(false);
  const [niftyError, setNiftyError] = useState<string | null>(null);

  // Filter state
  const [showFilters, setShowFilters] = useState(false);
  const [priceFilter, setPriceFilter] = useState({ min: 0, max: 10000 });
  const [showOnlyHoldings, setShowOnlyHoldings] = useState(false);

  // Load price data only (names are handled by background service)
  const loadPriceData = async (forceRefresh = false) => {
    setLoadingPriceData(true);
    setNiftyError(null);

    try {
      if (forceRefresh) {
        // Force background update for both names and prices
        await forceBackgroundUpdate();
      }

      const batchSize = 25;
      const totalBatches = Math.ceil(200 / batchSize);
      const allStocks: NiftyStock[] = [];

      console.log(`🚀 Loading price data for ${totalBatches} batches${forceRefresh ? ' (force refresh)' : ''}...`);

      for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
        try {
          let batchData;
          if (!forceRefresh) {
            const cacheKey = CacheKeys.niftyStocks(batchIndex);
            const cached = cacheService.get<any>(cacheKey);
            if (cached) {
              batchData = cached;
            }
          }

          if (!batchData) {
            try {
              // Pass forceRefresh parameter to API to control name caching
              const url = `/api/stocks/nifty200?batchIndex=${batchIndex}&batchSize=${batchSize}${forceRefresh ? '&forceRefresh=true' : ''}`;
              console.log(`🔄 Fetching batch ${batchIndex + 1}/${totalBatches}: ${url}`);

              const response = await fetch(url, {
                method: 'GET',
                headers: {
                  'Content-Type': 'application/json',
                },
                // Add timeout to prevent hanging
                signal: AbortSignal.timeout(30000) // 30 second timeout
              });

              if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
              }

              const data = await response.json();

              if (data.success) {
                batchData = data.data;

                // Cache the batch data (but names are cached separately in the service)
                if (!forceRefresh) {
                  const cacheKey = CacheKeys.niftyStocks(batchIndex);
                  cacheService.set(cacheKey, batchData);
                }

                console.log(`✅ Successfully fetched batch ${batchIndex + 1}: ${batchData.stocks?.length || 0} stocks`);
              } else {
                console.error(`❌ API returned error for batch ${batchIndex}:`, data.error);
                continue;
              }
            } catch (fetchError) {
              console.error(`❌ Network error fetching batch ${batchIndex}:`, fetchError);
              // Continue with next batch instead of failing completely
              continue;
            }
          }

          if (batchData && batchData.stocks) {
            // Use cached names from background service for instant display
            const stocksWithCachedNames = batchData.stocks.map((stock: NiftyStock) => ({
              ...stock,
              name: getStockName(stock.symbol) || stock.name
            }));

            allStocks.push(...stocksWithCachedNames);
            setNiftyStocks([...allStocks]);
            console.log(`✅ Loaded batch ${batchIndex + 1}/${totalBatches}: ${stocksWithCachedNames.length} stocks (Total: ${allStocks.length})`);
          }

          if (batchIndex < totalBatches - 1) {
            await new Promise(resolve => setTimeout(resolve, 200));
          }

        } catch (batchError) {
          console.error(`Error loading batch ${batchIndex}:`, batchError);
        }
      }

      setNiftyStocks(allStocks);
      console.log(`🎉 Loaded all price data: ${allStocks.length} total stocks`);

    } catch (error) {
      console.error('❌ Error loading price data:', error);

      // If we have no stocks at all, create fallback entries with cached names
      if (niftyStocks.length === 0 && isNamesReady) {
        console.log('🔄 Creating fallback stock entries with cached names...');

        // Import NIFTY_200_SYMBOLS and create basic entries
        import('@/lib/nifty-stocks').then(({ NIFTY_200_SYMBOLS, getYahooSymbol }) => {
          const fallbackStocks = NIFTY_200_SYMBOLS.slice(0, 50).map(symbol => {
            const yahooSymbol = getYahooSymbol(symbol);
            return {
              symbol: yahooSymbol || symbol,
              name: getStockName(yahooSymbol || symbol),
              price: 0,
              change: 0,
              changePercent: 0,
              volume: 0,
              high52Week: 0,
              low52Week: 0,
              high52WeekDate: undefined,
              low52WeekDate: undefined,
              isBOHEligible: false,
              avgVolume: 0,
              isEligible: true,
              inHoldings: false
            };
          });

          setNiftyStocks(fallbackStocks);
          console.log(`✅ Created ${fallbackStocks.length} fallback stock entries`);
        });
      }

      setNiftyError('Some data may be unavailable. Showing cached information.');
    } finally {
      setLoadingPriceData(false);
    }
  };

  // Search within Nifty 200 stocks
  const handleSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      const response = await fetch('/api/stocks/nifty200', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ searchQuery: query }),
      });
      
      const data = await response.json();
      if (data.success) {
        setSearchResults(data.data.stocks);
      } else {
        setSearchResults([]);
      }
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Filter stocks based on current filters
  const getFilteredStocks = (stocks: NiftyStock[]) => {
    return stocks.filter(stock => {
      // Only show BOH eligible stocks
      if (!stock.isBOHEligible) return false;
      
      // General filters
      if (stock.price < priceFilter.min || stock.price > priceFilter.max) return false;
      if (showOnlyHoldings && !stock.inHoldings) return false;
      return true;
    });
  };

  // Get BOH eligible stocks
  const getBOHEligibleStocks = (stocks: NiftyStock[]) => {
    return stocks.filter(stock => stock.isBOHEligible);
  };

  // Load initial data when names are ready
  useEffect(() => {
    if (isNamesReady) {
      loadPriceData(false);
    }
  }, [isNamesReady]);

  // Handle search with debouncing
  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      handleSearch(searchQuery);
    }, 300);
    return () => clearTimeout(debounceTimer);
  }, [searchQuery]);

  // Real-time stock updates for BOH page
  const realTimeStocks = useRealTimeStocks({
    onUpdate: (quotes) => {
      console.log(`⚡ BOH page received real-time update: ${quotes.length} quotes`);

      // Update existing stocks with new price data
      setNiftyStocks(currentStocks => {
        const updatedStocks = currentStocks.map(stock => {
          const updatedQuote = quotes.find(quote => quote.symbol === stock.symbol);
          if (updatedQuote) {
            return {
              ...stock,
              price: updatedQuote.price || stock.price,
              change: updatedQuote.change || stock.change,
              changePercent: updatedQuote.changePercent || stock.changePercent,
              volume: updatedQuote.volume || stock.volume,
              high52Week: updatedQuote.high52Week || stock.high52Week,
              low52Week: updatedQuote.low52Week || stock.low52Week,
              high52WeekDate: updatedQuote.high52WeekDate || stock.high52WeekDate,
              low52WeekDate: updatedQuote.low52WeekDate || stock.low52WeekDate,
              isBOHEligible: updatedQuote.isBOHEligible || stock.isBOHEligible
            };
          }
          return stock;
        });

        return updatedStocks;
      });
    },
    onError: (error) => {
      console.error('❌ BOH real-time update error:', error);
      setNiftyError('Real-time updates temporarily unavailable');
    }
  });

  // Start real-time updates when names are ready
  useEffect(() => {
    if (isNamesReady && !realTimeStocks.isActive) {
      console.log('🚀 Starting real-time updates for BOH Eligible page');
      realTimeStocks.start();
    }
  }, [isNamesReady, realTimeStocks]);

  // BOH Stock row component with required columns
  const BOHStockRow = ({ stock }: { stock: NiftyStock }) => (
    <div className="flex items-center justify-between p-4 hover:bg-gray-50 border-b border-gray-100">
      <div className="flex-1 grid grid-cols-5 gap-4 items-center">
        {/* Stock Name */}
        <div>
          <div className="flex items-center space-x-2">
            <h4 className="font-medium text-gray-900">{stock.symbol}</h4>
            {stock.isBOHEligible && (
              <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                BOH
              </span>
            )}
          </div>
          <p className="text-sm text-gray-600 truncate">{stock.name}</p>
        </div>

        {/* CMP */}
        <div className="text-center">
          <div className="font-semibold text-gray-900">
            {formatCurrency(stock.price)}
          </div>
          <div className={`text-sm ${getChangeColor(stock.change)}`}>
            {stock.change >= 0 ? '+' : ''}{formatPercentage(stock.changePercent)}
          </div>
        </div>

        {/* 52-Week Low Date */}
        <div className="text-center">
          <div className="text-sm text-gray-900">
            {stock.low52WeekDate || 'N/A'}
          </div>
          <div className="text-xs text-gray-500">
            ₹{stock.low52Week?.toFixed(2) || 'N/A'}
          </div>
        </div>

        {/* 52-Week High Date */}
        <div className="text-center">
          <div className="text-sm text-gray-900">
            {stock.high52WeekDate || 'N/A'}
          </div>
          <div className="text-xs text-gray-500">
            ₹{stock.high52Week?.toFixed(2) || 'N/A'}
          </div>
        </div>

        {/* BOH Eligible */}
        <div className="text-center">
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${
            stock.isBOHEligible 
              ? 'bg-green-100 text-green-800' 
              : 'bg-red-100 text-red-800'
          }`}>
            {stock.isBOHEligible ? 'Yes' : 'No'}
          </span>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">BOH Eligible Stocks</h1>
          <p className="text-gray-600 mt-1">
            Stocks with Boom-Bust-Recovery pattern (52-week low after 52-week high) • {getBOHEligibleStocks(niftyStocks).length} eligible stocks
            {loadingPriceData && niftyStocks.length > 0 && (
              <span className="text-blue-600"> (Loading {niftyStocks.length}/200...)</span>
            )}
          </p>
          <div className="mt-2">
            <RealTimeIndicator
              isActive={realTimeStocks.isActive}
              lastUpdate={realTimeStocks.lastUpdate}
              updateCount={realTimeStocks.updateCount}
              error={realTimeStocks.error}
            />
          </div>
        </div>
        <div className="flex space-x-3">
          <button 
            onClick={() => setShowFilters(!showFilters)}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2"
          >
            <Filter className="h-4 w-4" />
            <span>Filters</span>
          </button>
          <button
            onClick={() => loadPriceData(true)}
            disabled={loadingPriceData}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
          >
            <RefreshCw className={`h-4 w-4 ${loadingPriceData ? 'animate-spin' : ''}`} />
            <span>Refresh All</span>
          </button>
        </div>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
        <input
          type="text"
          placeholder="Search stocks by symbol or name..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
        {isSearching && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <RefreshCw className="h-5 w-5 text-gray-400 animate-spin" />
          </div>
        )}
      </div>

      {/* Filters Panel */}
      {showFilters && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Filter Options</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Price Range</label>
              <div className="flex space-x-2">
                <input
                  type="number"
                  placeholder="Min"
                  value={priceFilter.min}
                  onChange={(e) => setPriceFilter(prev => ({ ...prev, min: Number(e.target.value) }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <input
                  type="number"
                  placeholder="Max"
                  value={priceFilter.max}
                  onChange={(e) => setPriceFilter(prev => ({ ...prev, max: Number(e.target.value) }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Stock Filters</label>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={showOnlyHoldings}
                    onChange={(e) => setShowOnlyHoldings(e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-600">Show only stocks in holdings</span>
                </label>
              </div>
            </div>
            
            <div className="flex items-end">
              <button
                onClick={() => {
                  setPriceFilter({ min: 0, max: 10000 });
                  setShowOnlyHoldings(false);
                }}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Reset Filters
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Error Display */}
      {niftyError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
            <span className="text-red-800">{niftyError}</span>
          </div>
        </div>
      )}

      {/* BOH Eligible Stocks List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {/* BOH Column Headers */}
        <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
          <div className="grid grid-cols-5 gap-4 text-sm font-medium text-gray-700">
            <div>Stock Name</div>
            <div className="text-center">CMP</div>
            <div className="text-center">52-Week Low Date</div>
            <div className="text-center">52-Week High Date</div>
            <div className="text-center">BOH Eligible</div>
          </div>
        </div>

        <div>
          {!isNamesReady ? (
            <div className="p-8 text-center text-gray-500">
              <Loader className="h-8 w-8 mx-auto mb-4 animate-spin text-blue-600" />
              <p>Initializing stock data...</p>
              <p className="text-sm mt-1">Loading stock names for the first time</p>
            </div>
          ) : loadingPriceData && niftyStocks.length === 0 ? (
            <StockListSkeleton count={10} />
          ) : (
            <>
              {getFilteredStocks(niftyStocks).map((stock, index) => (
                <BOHStockRow key={`boh-${stock.symbol}-${index}`} stock={stock} />
              ))}

              {loadingPriceData && niftyStocks.length > 0 && (
                <div className="p-4 border-t border-gray-100 bg-blue-50">
                  <div className="flex items-center justify-center space-x-2">
                    <Loader className="h-4 w-4 animate-spin text-blue-600" />
                    <span className="text-blue-700 text-sm">
                      Updating price data... ({niftyStocks.length}/200)
                    </span>
                  </div>
                </div>
              )}

              {niftyStocks.length === 0 && !loadingPriceData && isNamesReady && (
                <div className="p-8 text-center text-gray-500">
                  <AlertCircle className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>No stocks loaded.</p>
                  <p className="text-sm mt-1">Try refreshing the data.</p>
                </div>
              )}

              {getFilteredStocks(niftyStocks).length === 0 && niftyStocks.length > 0 && (
                <div className="p-8 text-center text-gray-500">
                  <AlertCircle className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>No BOH eligible stocks found.</p>
                  <p className="text-sm mt-1">BOH stocks have 52-week low after 52-week high (Boom → Bust → Recovery pattern).</p>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}
