import { NextRequest, NextResponse } from 'next/server';

interface WeeklyHighStock {
  symbol: string;
  name: string;
  currentPrice: number;
  lastWeekHighest: number;
  suggestedBuyPrice: number;
  percentDifference: number;
  suggestedGTTQuantity: number;
  isBOHEligible: boolean;
  inHoldings: boolean;
}

interface GTTOrderRequest {
  symbol: string;
  name: string;
  orderType: 'BUY' | 'SELL';
  triggerPrice: number;
  quantity: number;
  source: 'SIGNAL' | 'HOLDING' | 'SALE';
}

// Generate mock OHLC data for calculating last week's high
const generateMockOHLCData = (currentPrice: number) => {
  const data = [];
  let price = currentPrice * 0.95; // Start 5% below current price
  
  for (let i = 0; i < 7; i++) {
    const open = price;
    const high = price * (1 + Math.random() * 0.08); // Up to 8% higher
    const low = price * (1 - Math.random() * 0.05); // Up to 5% lower
    const close = low + Math.random() * (high - low);
    
    data.push({ open, high, low, close });
    price = close;
  }
  
  return data;
};

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 API: Creating GTT orders for Weekly High Signal stocks...');

    // Fetch BOH eligible stocks from the Nifty 200 API
    const stocksResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/stocks/nifty200?batchIndex=0&batchSize=200`);
    const stocksData = await stocksResponse.json();

    if (!stocksData.success) {
      throw new Error(stocksData.error || 'Failed to fetch stock data');
    }

    // Filter for BOH eligible stocks with valid prices
    const bohEligibleStocks = stocksData.data.stocks.filter((stock: any) => 
      stock.isBOHEligible && stock.price > 0 && stock.price < 2000 // Under ₹2000 as per business rules
    );

    console.log(`✅ Found ${bohEligibleStocks.length} BOH eligible stocks under ₹2000`);

    // Calculate weekly high data for each stock
    const weeklyHighStocks: WeeklyHighStock[] = bohEligibleStocks.map((stock: any) => {
      // Generate mock OHLC data for last week
      const ohlcData = generateMockOHLCData(stock.price);
      const lastWeekHighest = Math.max(...ohlcData.map(d => d.high));
      const suggestedBuyPrice = lastWeekHighest + 0.05;
      const percentDifference = ((stock.price - suggestedBuyPrice) / suggestedBuyPrice) * 100;
      const suggestedGTTQuantity = Math.floor(2000 / suggestedBuyPrice);

      return {
        symbol: stock.symbol,
        name: stock.name,
        currentPrice: stock.price,
        lastWeekHighest,
        suggestedBuyPrice,
        percentDifference,
        suggestedGTTQuantity,
        isBOHEligible: stock.isBOHEligible,
        inHoldings: stock.inHoldings || false
      };
    });

    // Filter stocks that are good candidates for GTT orders
    const validStocks = weeklyHighStocks.filter(stock => 
      stock.suggestedGTTQuantity > 0 && 
      stock.suggestedBuyPrice > 0 &&
      stock.suggestedBuyPrice <= 2000 && // Keep within price limit
      stock.percentDifference > -20 && // Not too far below suggested price
      stock.percentDifference < 10 // Not too far above suggested price
    );

    console.log(`📊 ${validStocks.length} stocks are valid for GTT order creation`);

    // Create GTT order requests
    const gttOrderRequests: GTTOrderRequest[] = validStocks.map(stock => ({
      symbol: stock.symbol,
      name: stock.name,
      orderType: 'BUY' as const,
      triggerPrice: stock.suggestedBuyPrice,
      quantity: stock.suggestedGTTQuantity,
      source: 'SIGNAL' as const
    }));

    // Return the orders data for the frontend to process
    return NextResponse.json({
      success: true,
      message: `Found ${gttOrderRequests.length} BOH eligible stocks for GTT orders`,
      data: {
        totalBOHStocks: bohEligibleStocks.length,
        validForGTT: gttOrderRequests.length,
        orders: gttOrderRequests,
        stats: {
          avgTriggerPrice: gttOrderRequests.reduce((sum, order) => sum + order.triggerPrice, 0) / gttOrderRequests.length,
          totalQuantity: gttOrderRequests.reduce((sum, order) => sum + order.quantity, 0),
          totalValue: gttOrderRequests.reduce((sum, order) => sum + (order.triggerPrice * order.quantity), 0)
        }
      }
    });

  } catch (error) {
    console.error('❌ Error creating signal orders:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to create signal orders' 
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    success: true,
    message: 'GTT Signal Orders API is working',
    endpoints: {
      POST: 'Create GTT orders for all BOH eligible stocks from Weekly High Signal'
    }
  });
}
