import { NextRequest, NextResponse } from 'next/server';

interface WeeklyHighStock {
  symbol: string;
  name: string;
  currentPrice: number;
  lastWeekHighest: number;
  suggestedBuyPrice: number;
  percentDifference: number;
  suggestedGTTQuantity: number;
  isBOHEligible: boolean;
  inHoldings: boolean;
}

interface GTTOrderRequest {
  symbol: string;
  name: string;
  orderType: 'BUY' | 'SELL';
  triggerPrice: number;
  quantity: number;
  source: 'SIGNAL' | 'HOLDING' | 'SALE';
}

// Generate mock OHLC data for calculating last week's high
const generateMockOHLCData = (currentPrice: number) => {
  const data = [];
  let price = currentPrice * 0.95; // Start 5% below current price
  
  for (let i = 0; i < 7; i++) {
    const open = price;
    const high = price * (1 + Math.random() * 0.08); // Up to 8% higher
    const low = price * (1 - Math.random() * 0.05); // Up to 5% lower
    const close = low + Math.random() * (high - low);
    
    data.push({ open, high, low, close });
    price = close;
  }
  
  return data;
};

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 API: Creating GTT orders for Weekly High Signal stocks...');

    // Get the correct base URL (use current request URL to determine port)
    const requestUrl = new URL(request.url);
    const baseUrl = `${requestUrl.protocol}//${requestUrl.host}`;

    console.log(`📡 Using base URL: ${baseUrl}`);

    // Fetch BOH eligible stocks from the Nifty 200 API
    const stocksResponse = await fetch(`${baseUrl}/api/stocks/nifty200?batchIndex=0&batchSize=200`);
    const stocksData = await stocksResponse.json();

    console.log(`📊 API Response: success=${stocksData.success}, stocks=${stocksData.data?.stocks?.length || 0}`);

    if (!stocksData.success) {
      throw new Error(stocksData.error || 'Failed to fetch stock data');
    }

    // Filter for BOH eligible stocks with valid prices (same logic as Weekly High Signal page)
    const bohEligibleStocks = stocksData.data.stocks.filter((stock: any) => {
      const isBOHEligible = stock.isBOHEligible === true;
      const hasValidPrice = stock.price > 0;

      console.log(`🔍 ${stock.symbol}: BOH=${isBOHEligible}, Price=₹${stock.price}, Valid=${hasValidPrice}`);

      return isBOHEligible && hasValidPrice;
    });

    console.log(`✅ Found ${bohEligibleStocks.length} BOH eligible stocks with valid prices`);

    // Calculate weekly high data for each stock (same logic as Weekly High Signal page)
    const weeklyHighStocks: WeeklyHighStock[] = bohEligibleStocks.map((stock: any) => {
      // Generate mock OHLC data for last week (same as Weekly High Signal page)
      const ohlcData = generateMockOHLCData(stock.price);
      const lastWeekHighest = Math.max(...ohlcData.map(d => d.high));
      const suggestedBuyPrice = lastWeekHighest + 0.05;
      const percentDifference = ((stock.price - suggestedBuyPrice) / suggestedBuyPrice) * 100;
      const suggestedGTTQuantity = Math.floor(2000 / suggestedBuyPrice);

      console.log(`📈 ${stock.symbol}: Current=₹${stock.price}, LastWeekHigh=₹${lastWeekHighest.toFixed(2)}, SuggestedBuy=₹${suggestedBuyPrice.toFixed(2)}, Qty=${suggestedGTTQuantity}`);

      return {
        symbol: stock.symbol,
        name: stock.name,
        currentPrice: stock.price,
        lastWeekHighest,
        suggestedBuyPrice,
        percentDifference,
        suggestedGTTQuantity,
        isBOHEligible: stock.isBOHEligible,
        inHoldings: stock.inHoldings || false
      };
    });

    // Filter stocks that are valid for GTT orders (less restrictive to match Weekly High Signal page)
    const validStocks = weeklyHighStocks.filter(stock => {
      const hasValidQuantity = stock.suggestedGTTQuantity > 0;
      const hasValidPrice = stock.suggestedBuyPrice > 0;
      const isAffordable = stock.suggestedBuyPrice <= 3000; // Increased limit to be less restrictive

      const isValid = hasValidQuantity && hasValidPrice && isAffordable;

      if (!isValid) {
        console.log(`❌ ${stock.symbol} filtered out: Qty=${stock.suggestedGTTQuantity}, Price=₹${stock.suggestedBuyPrice.toFixed(2)}, Affordable=${isAffordable}`);
      }

      return isValid;
    });

    console.log(`📊 ${validStocks.length} stocks are valid for GTT order creation out of ${weeklyHighStocks.length} BOH eligible stocks`);

    // Create GTT order requests
    const gttOrderRequests: GTTOrderRequest[] = validStocks.map(stock => {
      const order = {
        symbol: stock.symbol,
        name: stock.name,
        orderType: 'BUY' as const,
        triggerPrice: stock.suggestedBuyPrice,
        quantity: stock.suggestedGTTQuantity,
        source: 'SIGNAL' as const
      };

      console.log(`✅ Creating GTT order: ${stock.symbol} - Trigger: ₹${stock.suggestedBuyPrice.toFixed(2)}, Qty: ${stock.suggestedGTTQuantity}`);
      return order;
    });

    console.log(`🎯 Final result: ${gttOrderRequests.length} GTT orders ready to create`);

    // Calculate stats
    const stats = gttOrderRequests.length > 0 ? {
      avgTriggerPrice: gttOrderRequests.reduce((sum, order) => sum + order.triggerPrice, 0) / gttOrderRequests.length,
      totalQuantity: gttOrderRequests.reduce((sum, order) => sum + order.quantity, 0),
      totalValue: gttOrderRequests.reduce((sum, order) => sum + (order.triggerPrice * order.quantity), 0)
    } : {
      avgTriggerPrice: 0,
      totalQuantity: 0,
      totalValue: 0
    };

    // Return the orders data for the frontend to process
    return NextResponse.json({
      success: true,
      message: `Found ${gttOrderRequests.length} BOH eligible stocks for GTT orders`,
      data: {
        totalBOHStocks: bohEligibleStocks.length,
        validForGTT: gttOrderRequests.length,
        orders: gttOrderRequests,
        stats
      }
    });

  } catch (error) {
    console.error('❌ Error creating signal orders:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to create signal orders' 
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    success: true,
    message: 'GTT Signal Orders API is working',
    endpoints: {
      POST: 'Create GTT orders for all BOH eligible stocks from Weekly High Signal'
    }
  });
}
