{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/lib/holdings-service.ts"], "sourcesContent": ["// Holdings service to manage current holdings across strategies\n\nexport interface Holding {\n  symbol: string;\n  strategy: string;\n  quantity: number;\n  avgPrice: number;\n  currentPrice: number;\n  purchaseDate: Date;\n}\n\nclass HoldingsService {\n  private holdings: Holding[] = [\n    // Sample holdings for demonstration - in real app, this would come from database\n    {\n      symbol: 'RELIANCE',\n      strategy: 'DARVAS_BOX',\n      quantity: 50,\n      avgPrice: 2200.00,\n      currentPrice: 2456.75,\n      purchaseDate: new Date('2024-01-15')\n    },\n    {\n      symbol: 'TCS',\n      strategy: 'DARVAS_BOX',\n      quantity: 25,\n      avgPrice: 3400.00,\n      currentPrice: 3234.50,\n      purchaseDate: new Date('2024-01-20')\n    },\n    {\n      symbol: 'HDFC',\n      strategy: 'WEEKLY_HIGH',\n      quantity: 40,\n      avgPrice: 1600.00,\n      currentPrice: 1678.90,\n      purchaseDate: new Date('2024-02-01')\n    },\n    {\n      symbol: 'INFY',\n      strategy: 'BOH_FILTER',\n      quantity: 60,\n      avgPrice: 1500.00,\n      currentPrice: 1456.80,\n      purchaseDate: new Date('2024-02-10')\n    }\n  ];\n\n  // Get all current holdings\n  getAllHoldings(): Holding[] {\n    return [...this.holdings];\n  }\n\n  // Get holdings for a specific strategy\n  getHoldingsByStrategy(strategy: string): Holding[] {\n    return this.holdings.filter(holding => holding.strategy === strategy);\n  }\n\n  // Check if a stock is currently held in any strategy\n  isStockInHoldings(symbol: string): boolean {\n    return this.holdings.some(holding => holding.symbol === symbol);\n  }\n\n  // Get all unique symbols in holdings\n  getHoldingSymbols(): string[] {\n    return [...new Set(this.holdings.map(holding => holding.symbol))];\n  }\n\n  // Add a new holding\n  addHolding(holding: Omit<Holding, 'purchaseDate'>): void {\n    const existingIndex = this.holdings.findIndex(\n      h => h.symbol === holding.symbol && h.strategy === holding.strategy\n    );\n\n    if (existingIndex >= 0) {\n      // Update existing holding (average price calculation)\n      const existing = this.holdings[existingIndex];\n      const totalQuantity = existing.quantity + holding.quantity;\n      const totalValue = (existing.quantity * existing.avgPrice) + (holding.quantity * holding.avgPrice);\n      \n      this.holdings[existingIndex] = {\n        ...existing,\n        quantity: totalQuantity,\n        avgPrice: totalValue / totalQuantity,\n        currentPrice: holding.currentPrice\n      };\n    } else {\n      // Add new holding\n      this.holdings.push({\n        ...holding,\n        purchaseDate: new Date()\n      });\n    }\n  }\n\n  // Remove a holding\n  removeHolding(symbol: string, strategy: string): void {\n    this.holdings = this.holdings.filter(\n      holding => !(holding.symbol === symbol && holding.strategy === strategy)\n    );\n  }\n\n  // Update current price for a holding\n  updateCurrentPrice(symbol: string, currentPrice: number): void {\n    this.holdings.forEach(holding => {\n      if (holding.symbol === symbol) {\n        holding.currentPrice = currentPrice;\n      }\n    });\n  }\n\n  // Get stocks that were bought above ₹2000 and are still in holdings\n  getStocksAbove2000InHoldings(): string[] {\n    return this.holdings\n      .filter(holding => holding.avgPrice > 2000 || holding.currentPrice > 2000)\n      .map(holding => holding.symbol);\n  }\n\n  // Check if a stock should be eligible for trading\n  // (CMP < 2000 OR currently in holdings)\n  isStockEligibleForTrading(symbol: string, currentPrice: number): boolean {\n    return currentPrice < 2000 || this.isStockInHoldings(symbol);\n  }\n}\n\nexport const holdingsService = new HoldingsService();\n"], "names": [], "mappings": "AAAA,gEAAgE;;;;;;AAWhE,MAAM;IAqCJ,2BAA2B;IAC3B,iBAA4B;QAC1B,OAAO;eAAI,IAAI,CAAC,QAAQ;SAAC;IAC3B;IAEA,uCAAuC;IACvC,sBAAsB,QAAgB,EAAa;QACjD,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;IAC9D;IAEA,qDAAqD;IACrD,kBAAkB,MAAc,EAAW;QACzC,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA,UAAW,QAAQ,MAAM,KAAK;IAC1D;IAEA,qCAAqC;IACrC,oBAA8B;QAC5B,OAAO;eAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA,UAAW,QAAQ,MAAM;SAAG;IACnE;IAEA,oBAAoB;IACpB,WAAW,OAAsC,EAAQ;QACvD,MAAM,gBAAgB,IAAI,CAAC,QAAQ,CAAC,SAAS,CAC3C,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM,IAAI,EAAE,QAAQ,KAAK,QAAQ,QAAQ;QAGrE,IAAI,iBAAiB,GAAG;YACtB,sDAAsD;YACtD,MAAM,WAAW,IAAI,CAAC,QAAQ,CAAC,cAAc;YAC7C,MAAM,gBAAgB,SAAS,QAAQ,GAAG,QAAQ,QAAQ;YAC1D,MAAM,aAAa,AAAC,SAAS,QAAQ,GAAG,SAAS,QAAQ,GAAK,QAAQ,QAAQ,GAAG,QAAQ,QAAQ;YAEjG,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG;gBAC7B,GAAG,QAAQ;gBACX,UAAU;gBACV,UAAU,aAAa;gBACvB,cAAc,QAAQ,YAAY;YACpC;QACF,OAAO;YACL,kBAAkB;YAClB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACjB,GAAG,OAAO;gBACV,cAAc,IAAI;YACpB;QACF;IACF;IAEA,mBAAmB;IACnB,cAAc,MAAc,EAAE,QAAgB,EAAQ;QACpD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAClC,CAAA,UAAW,CAAC,CAAC,QAAQ,MAAM,KAAK,UAAU,QAAQ,QAAQ,KAAK,QAAQ;IAE3E;IAEA,qCAAqC;IACrC,mBAAmB,MAAc,EAAE,YAAoB,EAAQ;QAC7D,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;YACpB,IAAI,QAAQ,MAAM,KAAK,QAAQ;gBAC7B,QAAQ,YAAY,GAAG;YACzB;QACF;IACF;IAEA,oEAAoE;IACpE,+BAAyC;QACvC,OAAO,IAAI,CAAC,QAAQ,CACjB,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,GAAG,QAAQ,QAAQ,YAAY,GAAG,MACpE,GAAG,CAAC,CAAA,UAAW,QAAQ,MAAM;IAClC;IAEA,kDAAkD;IAClD,wCAAwC;IACxC,0BAA0B,MAAc,EAAE,YAAoB,EAAW;QACvE,OAAO,eAAe,QAAQ,IAAI,CAAC,iBAAiB,CAAC;IACvD;;QA9GA,+KAAQ,YAAsB;YAC5B,iFAAiF;YACjF;gBACE,QAAQ;gBACR,UAAU;gBACV,UAAU;gBACV,UAAU;gBACV,cAAc;gBACd,cAAc,IAAI,KAAK;YACzB;YACA;gBACE,QAAQ;gBACR,UAAU;gBACV,UAAU;gBACV,UAAU;gBACV,cAAc;gBACd,cAAc,IAAI,KAAK;YACzB;YACA;gBACE,QAAQ;gBACR,UAAU;gBACV,UAAU;gBACV,UAAU;gBACV,cAAc;gBACd,cAAc,IAAI,KAAK;YACzB;YACA;gBACE,QAAQ;gBACR,UAAU;gBACV,UAAU;gBACV,UAAU;gBACV,cAAc;gBACd,cAAc,IAAI,KAAK;YACzB;SACD;;AA6EH;AAEO,MAAM,kBAAkB,IAAI", "debugId": null}}, {"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/lib/weekly-high-signal-detector.ts"], "sourcesContent": ["// Weekly High Signal Detection Service\n// Monitors for new Weekly High Signals and triggers automatic GTT order creation\n\nimport { yahooFinanceService } from './yahoo-finance';\nimport { stockNamesService } from './stock-names-service';\nimport { NIFTY_200_SYMBOLS, getYahooSymbol, addBOHEligibility, NiftyStock } from './nifty-stocks';\nimport { holdingsService } from './holdings-service';\n\nexport interface WeeklyHighSignal {\n  symbol: string;\n  name: string;\n  currentPrice: number;\n  lastWeekHighest: number;\n  suggestedBuyPrice: number;\n  percentDifference: number;\n  suggestedGTTQuantity: number;\n  isBOHEligible: boolean;\n  inHoldings: boolean;\n  signalStrength: 'STRONG' | 'MODERATE' | 'WEAK';\n  detectedAt: Date;\n  volume: number;\n  avgVolume: number;\n  volumeRatio: number;\n}\n\nexport interface SignalDetectionConfig {\n  enabled: boolean;\n  pollingIntervalMinutes: number;\n  marketStartHour: number; // 9 for 9:15 AM\n  marketEndHour: number;   // 15 for 3:30 PM\n  strongSignalThreshold: number; // % within weekly high\n  moderateSignalThreshold: number;\n  minVolumeRatio: number;\n  maxInvestmentPerStock: number;\n  investmentPerOrder: number;\n}\n\nclass WeeklyHighSignalDetector {\n  private config: SignalDetectionConfig = {\n    enabled: true,\n    pollingIntervalMinutes: 5,\n    marketStartHour: 9,\n    marketEndHour: 15,\n    strongSignalThreshold: 2.0, // Within 2% of weekly high\n    moderateSignalThreshold: 5.0, // Within 5% of weekly high\n    minVolumeRatio: 1.2, // 20% above average volume\n    maxInvestmentPerStock: 10000,\n    investmentPerOrder: 2000\n  };\n\n  private isRunning = false;\n  private pollingInterval: NodeJS.Timeout | null = null;\n  private lastSignals: Map<string, WeeklyHighSignal> = new Map();\n  private signalListeners = new Set<(signals: WeeklyHighSignal[]) => void>();\n  private newSignalListeners = new Set<(signal: WeeklyHighSignal) => void>();\n\n  constructor() {\n    console.log('📡 Weekly High Signal Detector initialized');\n  }\n\n  // Configuration management\n  updateConfig(newConfig: Partial<SignalDetectionConfig>): void {\n    this.config = { ...this.config, ...newConfig };\n    console.log('⚙️ Signal detector config updated:', this.config);\n    \n    if (this.isRunning) {\n      this.stop();\n      this.start();\n    }\n  }\n\n  getConfig(): SignalDetectionConfig {\n    return { ...this.config };\n  }\n\n  // Check if market is currently open\n  private isMarketOpen(): boolean {\n    const now = new Date();\n    const currentHour = now.getHours();\n    const currentMinute = now.getMinutes();\n    \n    // Market hours: 9:15 AM to 3:30 PM (Monday to Friday)\n    const isWeekday = now.getDay() >= 1 && now.getDay() <= 5;\n    const isAfterStart = currentHour > this.config.marketStartHour || \n                       (currentHour === this.config.marketStartHour && currentMinute >= 15);\n    const isBeforeEnd = currentHour < this.config.marketEndHour || \n                       (currentHour === this.config.marketEndHour && currentMinute <= 30);\n    \n    return isWeekday && isAfterStart && isBeforeEnd;\n  }\n\n  // Generate mock OHLC data for weekly high calculation\n  private generateOHLCData(currentPrice: number) {\n    const data = [];\n    let price = currentPrice * 0.95; // Start 5% below current price\n    \n    for (let i = 0; i < 7; i++) {\n      const open = price;\n      const high = price * (1 + Math.random() * 0.08); // Up to 8% higher\n      const low = price * (1 - Math.random() * 0.05); // Up to 5% lower\n      const close = low + Math.random() * (high - low);\n      \n      data.push({ open, high, low, close });\n      price = close;\n    }\n    \n    return data;\n  }\n\n  // Calculate signal strength based on proximity to weekly high and volume\n  private calculateSignalStrength(\n    currentPrice: number, \n    weeklyHigh: number, \n    volumeRatio: number\n  ): 'STRONG' | 'MODERATE' | 'WEAK' {\n    const percentFromHigh = Math.abs((currentPrice - weeklyHigh) / weeklyHigh * 100);\n    \n    if (percentFromHigh <= this.config.strongSignalThreshold && volumeRatio >= this.config.minVolumeRatio) {\n      return 'STRONG';\n    } else if (percentFromHigh <= this.config.moderateSignalThreshold) {\n      return 'MODERATE';\n    } else {\n      return 'WEAK';\n    }\n  }\n\n  // Scan for Weekly High Signals\n  async scanForSignals(): Promise<WeeklyHighSignal[]> {\n    try {\n      console.log('🔍 Scanning for Weekly High Signals...');\n\n      // Fetch all Nifty 200 stocks with current prices\n      const yahooSymbols = NIFTY_200_SYMBOLS.map(getYahooSymbol);\n      const quotes = await yahooFinanceService.getMultipleQuotesWithCachedNames(yahooSymbols);\n      \n      console.log(`📊 Got quotes for ${quotes.length}/${yahooSymbols.length} symbols`);\n\n      // Get current holdings\n      const holdingSymbols = holdingsService.getAllHoldings().map(h => h.symbol);\n\n      // Process each stock for signal detection\n      const signals: WeeklyHighSignal[] = [];\n      \n      for (let i = 0; i < NIFTY_200_SYMBOLS.length; i++) {\n        const nseSymbol = NIFTY_200_SYMBOLS[i];\n        const yahooSymbol = getYahooSymbol(nseSymbol);\n        const quote = quotes.find(q => q.symbol === yahooSymbol);\n        \n        if (!quote || quote.price <= 0) continue;\n\n        const price = quote.price;\n        const inHoldings = holdingSymbols.includes(nseSymbol);\n        const isEligible = holdingsService.isStockEligibleForTrading(nseSymbol, price);\n\n        // Create stock object for BOH eligibility check\n        const stock: NiftyStock = {\n          symbol: nseSymbol,\n          name: quote.name,\n          price,\n          change: quote.change || 0,\n          changePercent: quote.changePercent || 0,\n          volume: quote.volume || 0,\n          marketCap: quote.marketCap,\n          high52Week: quote.high52Week,\n          low52Week: quote.low52Week,\n          high52WeekDate: quote.high52WeekDate,\n          low52WeekDate: quote.low52WeekDate,\n          isEligible,\n          inHoldings\n        };\n\n        const stockWithBOH = addBOHEligibility(stock);\n        \n        // Only process BOH eligible stocks\n        if (!stockWithBOH.isBOHEligible) continue;\n\n        // Calculate weekly high data\n        const ohlcData = this.generateOHLCData(price);\n        const lastWeekHighest = Math.max(...ohlcData.map(d => d.high));\n        const suggestedBuyPrice = lastWeekHighest + 0.05;\n        const percentDifference = ((price - suggestedBuyPrice) / suggestedBuyPrice) * 100;\n        const suggestedGTTQuantity = Math.floor(this.config.investmentPerOrder / suggestedBuyPrice);\n\n        // Calculate volume metrics\n        const avgVolume = quote.avgVolume || quote.volume || 1;\n        const volumeRatio = quote.volume / avgVolume;\n\n        // Calculate signal strength\n        const signalStrength = this.calculateSignalStrength(price, lastWeekHighest, volumeRatio);\n\n        // Only include signals that are MODERATE or STRONG\n        if (signalStrength === 'WEAK') continue;\n\n        const signal: WeeklyHighSignal = {\n          symbol: nseSymbol,\n          name: quote.name,\n          currentPrice: price,\n          lastWeekHighest,\n          suggestedBuyPrice,\n          percentDifference,\n          suggestedGTTQuantity,\n          isBOHEligible: true,\n          inHoldings,\n          signalStrength,\n          detectedAt: new Date(),\n          volume: quote.volume || 0,\n          avgVolume,\n          volumeRatio\n        };\n\n        signals.push(signal);\n      }\n\n      console.log(`✅ Found ${signals.length} Weekly High Signals`);\n      return signals;\n\n    } catch (error) {\n      console.error('❌ Error scanning for signals:', error);\n      return [];\n    }\n  }\n\n  // Detect new signals by comparing with previous scan\n  private detectNewSignals(currentSignals: WeeklyHighSignal[]): WeeklyHighSignal[] {\n    const newSignals: WeeklyHighSignal[] = [];\n    \n    for (const signal of currentSignals) {\n      const previousSignal = this.lastSignals.get(signal.symbol);\n      \n      // Consider it a new signal if:\n      // 1. Stock wasn't in previous signals, OR\n      // 2. Signal strength improved (MODERATE -> STRONG), OR\n      // 3. Price moved significantly closer to weekly high\n      const isNewSignal = !previousSignal ||\n                         (signal.signalStrength === 'STRONG' && previousSignal.signalStrength !== 'STRONG') ||\n                         (Math.abs(signal.percentDifference) < Math.abs(previousSignal.percentDifference) - 1);\n      \n      if (isNewSignal) {\n        newSignals.push(signal);\n        console.log(`🆕 New signal detected: ${signal.symbol} (${signal.signalStrength})`);\n      }\n    }\n    \n    return newSignals;\n  }\n\n  // Main polling function\n  private async poll(): Promise<void> {\n    if (!this.config.enabled) {\n      console.log('⏸️ Signal detection is disabled');\n      return;\n    }\n\n    if (!this.isMarketOpen()) {\n      console.log('🕐 Market is closed, skipping signal detection');\n      return;\n    }\n\n    try {\n      console.log('🔄 Polling for Weekly High Signals...');\n      \n      const currentSignals = await this.scanForSignals();\n      const newSignals = this.detectNewSignals(currentSignals);\n      \n      // Update last signals cache\n      this.lastSignals.clear();\n      currentSignals.forEach(signal => {\n        this.lastSignals.set(signal.symbol, signal);\n      });\n      \n      // Notify listeners about all current signals\n      this.signalListeners.forEach(listener => {\n        try {\n          listener(currentSignals);\n        } catch (error) {\n          console.error('❌ Error in signal listener:', error);\n        }\n      });\n      \n      // Notify listeners about new signals\n      if (newSignals.length > 0) {\n        console.log(`🚨 ${newSignals.length} new signals detected!`);\n        \n        newSignals.forEach(signal => {\n          this.newSignalListeners.forEach(listener => {\n            try {\n              listener(signal);\n            } catch (error) {\n              console.error('❌ Error in new signal listener:', error);\n            }\n          });\n        });\n      }\n      \n    } catch (error) {\n      console.error('❌ Error in signal polling:', error);\n    }\n  }\n\n  // Start the detection service\n  start(): void {\n    if (this.isRunning) {\n      console.log('⚠️ Signal detector is already running');\n      return;\n    }\n\n    console.log(`🚀 Starting Weekly High Signal Detector (polling every ${this.config.pollingIntervalMinutes} minutes)`);\n    \n    this.isRunning = true;\n    \n    // Initial scan\n    this.poll();\n    \n    // Set up polling interval\n    this.pollingInterval = setInterval(() => {\n      this.poll();\n    }, this.config.pollingIntervalMinutes * 60 * 1000);\n  }\n\n  // Stop the detection service\n  stop(): void {\n    if (!this.isRunning) {\n      console.log('⚠️ Signal detector is not running');\n      return;\n    }\n\n    console.log('⏹️ Stopping Weekly High Signal Detector');\n    \n    this.isRunning = false;\n    \n    if (this.pollingInterval) {\n      clearInterval(this.pollingInterval);\n      this.pollingInterval = null;\n    }\n  }\n\n  // Add listener for all signals\n  addSignalListener(listener: (signals: WeeklyHighSignal[]) => void): void {\n    this.signalListeners.add(listener);\n  }\n\n  // Remove listener for all signals\n  removeSignalListener(listener: (signals: WeeklyHighSignal[]) => void): void {\n    this.signalListeners.delete(listener);\n  }\n\n  // Add listener for new signals only\n  addNewSignalListener(listener: (signal: WeeklyHighSignal) => void): void {\n    this.newSignalListeners.add(listener);\n  }\n\n  // Remove listener for new signals\n  removeNewSignalListener(listener: (signal: WeeklyHighSignal) => void): void {\n    this.newSignalListeners.delete(listener);\n  }\n\n  // Get current status\n  getStatus() {\n    return {\n      isRunning: this.isRunning,\n      isMarketOpen: this.isMarketOpen(),\n      config: this.config,\n      lastSignalCount: this.lastSignals.size,\n      listenerCount: this.signalListeners.size + this.newSignalListeners.size\n    };\n  }\n\n  // Manual trigger for testing\n  async triggerManualScan(): Promise<WeeklyHighSignal[]> {\n    console.log('🔧 Manual signal scan triggered');\n    return await this.scanForSignals();\n  }\n}\n\n// Export singleton instance\nexport const weeklyHighSignalDetector = new WeeklyHighSignalDetector();\n\n// Auto-start the service when imported\nif (typeof window !== 'undefined') {\n  // Only run in browser environment\n  console.log('📡 Auto-starting Weekly High Signal Detector...');\n\n  weeklyHighSignalDetector.start();\n  console.log('✅ Weekly High Signal Detector auto-started successfully');\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,iFAAiF;;;;;AAEjF;AAEA;AACA;;;;;AA+BA,MAAM;IAuBJ,2BAA2B;IAC3B,aAAa,SAAyC,EAAQ;QAC5D,IAAI,CAAC,MAAM,GAAG;YAAE,GAAG,IAAI,CAAC,MAAM;YAAE,GAAG,SAAS;QAAC;QAC7C,QAAQ,GAAG,CAAC,sCAAsC,IAAI,CAAC,MAAM;QAE7D,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,IAAI;YACT,IAAI,CAAC,KAAK;QACZ;IACF;IAEA,YAAmC;QACjC,OAAO;YAAE,GAAG,IAAI,CAAC,MAAM;QAAC;IAC1B;IAEA,oCAAoC;IAC5B,eAAwB;QAC9B,MAAM,MAAM,IAAI;QAChB,MAAM,cAAc,IAAI,QAAQ;QAChC,MAAM,gBAAgB,IAAI,UAAU;QAEpC,sDAAsD;QACtD,MAAM,YAAY,IAAI,MAAM,MAAM,KAAK,IAAI,MAAM,MAAM;QACvD,MAAM,eAAe,cAAc,IAAI,CAAC,MAAM,CAAC,eAAe,IAC1C,gBAAgB,IAAI,CAAC,MAAM,CAAC,eAAe,IAAI,iBAAiB;QACpF,MAAM,cAAc,cAAc,IAAI,CAAC,MAAM,CAAC,aAAa,IACvC,gBAAgB,IAAI,CAAC,MAAM,CAAC,aAAa,IAAI,iBAAiB;QAElF,OAAO,aAAa,gBAAgB;IACtC;IAEA,sDAAsD;IAC9C,iBAAiB,YAAoB,EAAE;QAC7C,MAAM,OAAO,EAAE;QACf,IAAI,QAAQ,eAAe,MAAM,+BAA+B;QAEhE,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YAC1B,MAAM,OAAO;YACb,MAAM,OAAO,QAAQ,CAAC,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG,kBAAkB;YACnE,MAAM,MAAM,QAAQ,CAAC,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG,iBAAiB;YACjE,MAAM,QAAQ,MAAM,KAAK,MAAM,KAAK,CAAC,OAAO,GAAG;YAE/C,KAAK,IAAI,CAAC;gBAAE;gBAAM;gBAAM;gBAAK;YAAM;YACnC,QAAQ;QACV;QAEA,OAAO;IACT;IAEA,yEAAyE;IACjE,wBACN,YAAoB,EACpB,UAAkB,EAClB,WAAmB,EACa;QAChC,MAAM,kBAAkB,KAAK,GAAG,CAAC,CAAC,eAAe,UAAU,IAAI,aAAa;QAE5E,IAAI,mBAAmB,IAAI,CAAC,MAAM,CAAC,qBAAqB,IAAI,eAAe,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE;YACrG,OAAO;QACT,OAAO,IAAI,mBAAmB,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE;YACjE,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF;IAEA,+BAA+B;IAC/B,MAAM,iBAA8C;QAClD,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,iDAAiD;YACjD,MAAM,eAAe,gIAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,gIAAA,CAAA,iBAAc;YACzD,MAAM,SAAS,MAAM,iIAAA,CAAA,sBAAmB,CAAC,gCAAgC,CAAC;YAE1E,QAAQ,GAAG,CAAC,AAAC,qBAAqC,OAAjB,OAAO,MAAM,EAAC,KAAuB,OAApB,aAAa,MAAM,EAAC;YAEtE,uBAAuB;YACvB,MAAM,iBAAiB,oIAAA,CAAA,kBAAe,CAAC,cAAc,GAAG,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;YAEzE,0CAA0C;YAC1C,MAAM,UAA8B,EAAE;YAEtC,IAAK,IAAI,IAAI,GAAG,IAAI,gIAAA,CAAA,oBAAiB,CAAC,MAAM,EAAE,IAAK;gBACjD,MAAM,YAAY,gIAAA,CAAA,oBAAiB,CAAC,EAAE;gBACtC,MAAM,cAAc,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD,EAAE;gBACnC,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;gBAE5C,IAAI,CAAC,SAAS,MAAM,KAAK,IAAI,GAAG;gBAEhC,MAAM,QAAQ,MAAM,KAAK;gBACzB,MAAM,aAAa,eAAe,QAAQ,CAAC;gBAC3C,MAAM,aAAa,oIAAA,CAAA,kBAAe,CAAC,yBAAyB,CAAC,WAAW;gBAExE,gDAAgD;gBAChD,MAAM,QAAoB;oBACxB,QAAQ;oBACR,MAAM,MAAM,IAAI;oBAChB;oBACA,QAAQ,MAAM,MAAM,IAAI;oBACxB,eAAe,MAAM,aAAa,IAAI;oBACtC,QAAQ,MAAM,MAAM,IAAI;oBACxB,WAAW,MAAM,SAAS;oBAC1B,YAAY,MAAM,UAAU;oBAC5B,WAAW,MAAM,SAAS;oBAC1B,gBAAgB,MAAM,cAAc;oBACpC,eAAe,MAAM,aAAa;oBAClC;oBACA;gBACF;gBAEA,MAAM,eAAe,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE;gBAEvC,mCAAmC;gBACnC,IAAI,CAAC,aAAa,aAAa,EAAE;gBAEjC,6BAA6B;gBAC7B,MAAM,WAAW,IAAI,CAAC,gBAAgB,CAAC;gBACvC,MAAM,kBAAkB,KAAK,GAAG,IAAI,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;gBAC5D,MAAM,oBAAoB,kBAAkB;gBAC5C,MAAM,oBAAoB,AAAC,CAAC,QAAQ,iBAAiB,IAAI,oBAAqB;gBAC9E,MAAM,uBAAuB,KAAK,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,GAAG;gBAEzE,2BAA2B;gBAC3B,MAAM,YAAY,MAAM,SAAS,IAAI,MAAM,MAAM,IAAI;gBACrD,MAAM,cAAc,MAAM,MAAM,GAAG;gBAEnC,4BAA4B;gBAC5B,MAAM,iBAAiB,IAAI,CAAC,uBAAuB,CAAC,OAAO,iBAAiB;gBAE5E,mDAAmD;gBACnD,IAAI,mBAAmB,QAAQ;gBAE/B,MAAM,SAA2B;oBAC/B,QAAQ;oBACR,MAAM,MAAM,IAAI;oBAChB,cAAc;oBACd;oBACA;oBACA;oBACA;oBACA,eAAe;oBACf;oBACA;oBACA,YAAY,IAAI;oBAChB,QAAQ,MAAM,MAAM,IAAI;oBACxB;oBACA;gBACF;gBAEA,QAAQ,IAAI,CAAC;YACf;YAEA,QAAQ,GAAG,CAAC,AAAC,WAAyB,OAAf,QAAQ,MAAM,EAAC;YACtC,OAAO;QAET,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO,EAAE;QACX;IACF;IAEA,qDAAqD;IAC7C,iBAAiB,cAAkC,EAAsB;QAC/E,MAAM,aAAiC,EAAE;QAEzC,KAAK,MAAM,UAAU,eAAgB;YACnC,MAAM,iBAAiB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,MAAM;YAEzD,+BAA+B;YAC/B,0CAA0C;YAC1C,uDAAuD;YACvD,qDAAqD;YACrD,MAAM,cAAc,CAAC,kBACD,OAAO,cAAc,KAAK,YAAY,eAAe,cAAc,KAAK,YACxE,KAAK,GAAG,CAAC,OAAO,iBAAiB,IAAI,KAAK,GAAG,CAAC,eAAe,iBAAiB,IAAI;YAEtG,IAAI,aAAa;gBACf,WAAW,IAAI,CAAC;gBAChB,QAAQ,GAAG,CAAC,AAAC,2BAA4C,OAAlB,OAAO,MAAM,EAAC,MAA0B,OAAtB,OAAO,cAAc,EAAC;YACjF;QACF;QAEA,OAAO;IACT;IAEA,wBAAwB;IACxB,MAAc,OAAsB;QAClC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;YACxB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI;YACxB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,iBAAiB,MAAM,IAAI,CAAC,cAAc;YAChD,MAAM,aAAa,IAAI,CAAC,gBAAgB,CAAC;YAEzC,4BAA4B;YAC5B,IAAI,CAAC,WAAW,CAAC,KAAK;YACtB,eAAe,OAAO,CAAC,CAAA;gBACrB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,MAAM,EAAE;YACtC;YAEA,6CAA6C;YAC7C,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAA;gBAC3B,IAAI;oBACF,SAAS;gBACX,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC/C;YACF;YAEA,qCAAqC;YACrC,IAAI,WAAW,MAAM,GAAG,GAAG;gBACzB,QAAQ,GAAG,CAAC,AAAC,MAAuB,OAAlB,WAAW,MAAM,EAAC;gBAEpC,WAAW,OAAO,CAAC,CAAA;oBACjB,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAA;wBAC9B,IAAI;4BACF,SAAS;wBACX,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,mCAAmC;wBACnD;oBACF;gBACF;YACF;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,8BAA8B;IAC9B,QAAc;QACZ,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,QAAQ,GAAG,CAAC,AAAC,0DAA4F,OAAnC,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAC;QAEzG,IAAI,CAAC,SAAS,GAAG;QAEjB,eAAe;QACf,IAAI,CAAC,IAAI;QAET,0BAA0B;QAC1B,IAAI,CAAC,eAAe,GAAG,YAAY;YACjC,IAAI,CAAC,IAAI;QACX,GAAG,IAAI,CAAC,MAAM,CAAC,sBAAsB,GAAG,KAAK;IAC/C;IAEA,6BAA6B;IAC7B,OAAa;QACX,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,QAAQ,GAAG,CAAC;QAEZ,IAAI,CAAC,SAAS,GAAG;QAEjB,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,cAAc,IAAI,CAAC,eAAe;YAClC,IAAI,CAAC,eAAe,GAAG;QACzB;IACF;IAEA,+BAA+B;IAC/B,kBAAkB,QAA+C,EAAQ;QACvE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;IAC3B;IAEA,kCAAkC;IAClC,qBAAqB,QAA+C,EAAQ;QAC1E,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;IAC9B;IAEA,oCAAoC;IACpC,qBAAqB,QAA4C,EAAQ;QACvE,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC;IAC9B;IAEA,kCAAkC;IAClC,wBAAwB,QAA4C,EAAQ;QAC1E,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;IACjC;IAEA,qBAAqB;IACrB,YAAY;QACV,OAAO;YACL,WAAW,IAAI,CAAC,SAAS;YACzB,cAAc,IAAI,CAAC,YAAY;YAC/B,QAAQ,IAAI,CAAC,MAAM;YACnB,iBAAiB,IAAI,CAAC,WAAW,CAAC,IAAI;YACtC,eAAe,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI;QACzE;IACF;IAEA,6BAA6B;IAC7B,MAAM,oBAAiD;QACrD,QAAQ,GAAG,CAAC;QACZ,OAAO,MAAM,IAAI,CAAC,cAAc;IAClC;IA3TA,aAAc;QAlBd,+KAAQ,UAAgC;YACtC,SAAS;YACT,wBAAwB;YACxB,iBAAiB;YACjB,eAAe;YACf,uBAAuB;YACvB,yBAAyB;YACzB,gBAAgB;YAChB,uBAAuB;YACvB,oBAAoB;QACtB;QAEA,+KAAQ,aAAY;QACpB,+KAAQ,mBAAyC;QACjD,+KAAQ,eAA6C,IAAI;QACzD,+KAAQ,mBAAkB,IAAI;QAC9B,+KAAQ,sBAAqB,IAAI;QAG/B,QAAQ,GAAG,CAAC;IACd;AA0TF;AAGO,MAAM,2BAA2B,IAAI;AAE5C,uCAAuC;AACvC,wCAAmC;IACjC,kCAAkC;IAClC,QAAQ,GAAG,CAAC;IAEZ,yBAAyB,KAAK;IAC9B,QAAQ,GAAG,CAAC;AACd", "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/lib/automatic-gtt-service.ts"], "sourcesContent": ["// Automatic GTT Order Creation Service\n// Creates GTT buy orders automatically when new Weekly High Signals are detected\n\nimport { weeklyHighSignalDetector, WeeklyHighSignal } from './weekly-high-signal-detector';\n\nexport interface AutoGTTOrder {\n  id: string;\n  symbol: string;\n  name: string;\n  orderType: 'BUY' | 'SELL';\n  triggerPrice: number;\n  quantity: number;\n  status: 'PENDING' | 'TRIGGERED' | 'CANCELLED' | 'EXPIRED';\n  createdAt: Date;\n  source: 'SIGNAL' | 'HOLDING' | 'SALE';\n  signalStrength?: 'STRONG' | 'MODERATE' | 'WEAK';\n  autoCreated: boolean;\n  originalSignal?: WeeklyHighSignal;\n}\n\nexport interface AutoGTTConfig {\n  enabled: boolean;\n  maxOrdersPerDay: number;\n  maxInvestmentPerStock: number;\n  investmentPerOrder: number;\n  minSignalStrength: 'STRONG' | 'MODERATE' | 'WEAK';\n  requireVolumeConfirmation: boolean;\n  onlyDuringMarketHours: boolean;\n}\n\nclass AutomaticGTTService {\n  private config: AutoGTTConfig = {\n    enabled: true,\n    maxOrdersPerDay: 20,\n    maxInvestmentPerStock: 10000,\n    investmentPerOrder: 2000,\n    minSignalStrength: 'MODERATE',\n    requireVolumeConfirmation: true,\n    onlyDuringMarketHours: true\n  };\n\n  private orders: AutoGTTOrder[] = [];\n  private dailyOrderCount = 0;\n  private lastResetDate = new Date().toDateString();\n  private orderListeners = new Set<(order: AutoGTTOrder) => void>();\n  private isInitialized = false;\n\n  constructor() {\n    console.log('🤖 Automatic GTT Service initialized');\n  }\n\n  // Initialize the service and start listening for signals\n  async initialize(): Promise<void> {\n    if (this.isInitialized) {\n      console.log('⚠️ Automatic GTT Service already initialized');\n      return;\n    }\n\n    console.log('🚀 Initializing Automatic GTT Service...');\n\n    // Load existing orders from localStorage or API\n    await this.loadExistingOrders();\n\n    // Start listening for new signals\n    weeklyHighSignalDetector.addNewSignalListener(this.handleNewSignal.bind(this));\n\n    // Reset daily counter if it's a new day\n    this.resetDailyCounterIfNeeded();\n\n    this.isInitialized = true;\n    console.log('✅ Automatic GTT Service initialized successfully');\n  }\n\n  // Configuration management\n  updateConfig(newConfig: Partial<AutoGTTConfig>): void {\n    this.config = { ...this.config, ...newConfig };\n    console.log('⚙️ Auto GTT config updated:', this.config);\n  }\n\n  getConfig(): AutoGTTConfig {\n    return { ...this.config };\n  }\n\n  // Load existing orders (from localStorage for now, can be replaced with API)\n  private async loadExistingOrders(): Promise<void> {\n    try {\n      const stored = localStorage.getItem('autoGTTOrders');\n      if (stored) {\n        const parsedOrders = JSON.parse(stored);\n        this.orders = parsedOrders.map((order: any) => ({\n          ...order,\n          createdAt: new Date(order.createdAt)\n        }));\n        console.log(`📂 Loaded ${this.orders.length} existing auto GTT orders`);\n      }\n\n      // Load daily counter\n      const storedCounter = localStorage.getItem('autoGTTDailyCount');\n      const storedDate = localStorage.getItem('autoGTTLastResetDate');\n      \n      if (storedCounter && storedDate === new Date().toDateString()) {\n        this.dailyOrderCount = parseInt(storedCounter, 10);\n      }\n    } catch (error) {\n      console.error('❌ Error loading existing orders:', error);\n    }\n  }\n\n  // Save orders to localStorage\n  private saveOrders(): void {\n    try {\n      localStorage.setItem('autoGTTOrders', JSON.stringify(this.orders));\n      localStorage.setItem('autoGTTDailyCount', this.dailyOrderCount.toString());\n      localStorage.setItem('autoGTTLastResetDate', this.lastResetDate);\n    } catch (error) {\n      console.error('❌ Error saving orders:', error);\n    }\n  }\n\n  // Reset daily counter if it's a new day\n  private resetDailyCounterIfNeeded(): void {\n    const today = new Date().toDateString();\n    if (this.lastResetDate !== today) {\n      this.dailyOrderCount = 0;\n      this.lastResetDate = today;\n      console.log('🔄 Daily order counter reset for new day');\n    }\n  }\n\n  // Check if we can create a new order\n  private canCreateOrder(signal: WeeklyHighSignal): { canCreate: boolean; reason?: string } {\n    // Check if service is enabled\n    if (!this.config.enabled) {\n      return { canCreate: false, reason: 'Automatic GTT service is disabled' };\n    }\n\n    // Check market hours if required\n    if (this.config.onlyDuringMarketHours) {\n      const now = new Date();\n      const currentHour = now.getHours();\n      const currentMinute = now.getMinutes();\n      const isWeekday = now.getDay() >= 1 && now.getDay() <= 5;\n      const isMarketOpen = isWeekday && \n                          (currentHour > 9 || (currentHour === 9 && currentMinute >= 15)) &&\n                          (currentHour < 15 || (currentHour === 15 && currentMinute <= 30));\n      \n      if (!isMarketOpen) {\n        return { canCreate: false, reason: 'Market is closed' };\n      }\n    }\n\n    // Check daily limit\n    this.resetDailyCounterIfNeeded();\n    if (this.dailyOrderCount >= this.config.maxOrdersPerDay) {\n      return { canCreate: false, reason: 'Daily order limit reached' };\n    }\n\n    // Check signal strength\n    const strengthOrder = { 'WEAK': 1, 'MODERATE': 2, 'STRONG': 3 };\n    if (strengthOrder[signal.signalStrength] < strengthOrder[this.config.minSignalStrength]) {\n      return { canCreate: false, reason: `Signal strength ${signal.signalStrength} below minimum ${this.config.minSignalStrength}` };\n    }\n\n    // Check volume confirmation if required\n    if (this.config.requireVolumeConfirmation && signal.volumeRatio < 1.2) {\n      return { canCreate: false, reason: 'Insufficient volume confirmation' };\n    }\n\n    // Check for existing pending orders for this stock\n    const existingOrder = this.orders.find(order => \n      order.symbol === signal.symbol && \n      order.status === 'PENDING' && \n      order.source === 'SIGNAL'\n    );\n    \n    if (existingOrder) {\n      return { canCreate: false, reason: 'Pending order already exists for this stock' };\n    }\n\n    // Check investment limits\n    const existingInvestment = this.orders\n      .filter(order => order.symbol === signal.symbol && order.status === 'PENDING')\n      .reduce((sum, order) => sum + (order.triggerPrice * order.quantity), 0);\n    \n    const newInvestment = signal.suggestedBuyPrice * signal.suggestedGTTQuantity;\n    \n    if (existingInvestment + newInvestment > this.config.maxInvestmentPerStock) {\n      return { canCreate: false, reason: 'Would exceed maximum investment per stock' };\n    }\n\n    // Check if quantity is valid\n    if (signal.suggestedGTTQuantity <= 0) {\n      return { canCreate: false, reason: 'Invalid quantity calculated' };\n    }\n\n    // Check if trigger price is reasonable\n    if (signal.suggestedBuyPrice <= 0 || signal.suggestedBuyPrice > 5000) {\n      return { canCreate: false, reason: 'Invalid trigger price' };\n    }\n\n    return { canCreate: true };\n  }\n\n  // Handle new signal detection\n  private async handleNewSignal(signal: WeeklyHighSignal): Promise<void> {\n    console.log(`🔔 New signal received: ${signal.symbol} (${signal.signalStrength})`);\n\n    const validation = this.canCreateOrder(signal);\n    \n    if (!validation.canCreate) {\n      console.log(`⏭️ Skipping order creation for ${signal.symbol}: ${validation.reason}`);\n      return;\n    }\n\n    try {\n      await this.createAutoGTTOrder(signal);\n    } catch (error) {\n      console.error(`❌ Failed to create auto GTT order for ${signal.symbol}:`, error);\n    }\n  }\n\n  // Create automatic GTT order\n  private async createAutoGTTOrder(signal: WeeklyHighSignal): Promise<AutoGTTOrder> {\n    console.log(`🤖 Creating automatic GTT order for ${signal.symbol}...`);\n\n    const order: AutoGTTOrder = {\n      id: `auto_gtt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n      symbol: signal.symbol,\n      name: signal.name,\n      orderType: 'BUY',\n      triggerPrice: signal.suggestedBuyPrice,\n      quantity: signal.suggestedGTTQuantity,\n      status: 'PENDING',\n      createdAt: new Date(),\n      source: 'SIGNAL',\n      signalStrength: signal.signalStrength,\n      autoCreated: true,\n      originalSignal: signal\n    };\n\n    // Add to orders list\n    this.orders.push(order);\n    this.dailyOrderCount++;\n\n    // Save to storage\n    this.saveOrders();\n\n    // Log the creation\n    console.log(`✅ Auto GTT order created: ${order.symbol} - Trigger: ₹${order.triggerPrice.toFixed(2)}, Qty: ${order.quantity}`);\n    console.log(`📊 Daily orders: ${this.dailyOrderCount}/${this.config.maxOrdersPerDay}`);\n\n    // Notify listeners\n    this.orderListeners.forEach(listener => {\n      try {\n        listener(order);\n      } catch (error) {\n        console.error('❌ Error in order listener:', error);\n      }\n    });\n\n    return order;\n  }\n\n  // Get all orders\n  getAllOrders(): AutoGTTOrder[] {\n    return [...this.orders];\n  }\n\n  // Get orders by status\n  getOrdersByStatus(status: AutoGTTOrder['status']): AutoGTTOrder[] {\n    return this.orders.filter(order => order.status === status);\n  }\n\n  // Get orders by source\n  getOrdersBySource(source: AutoGTTOrder['source']): AutoGTTOrder[] {\n    return this.orders.filter(order => order.source === source);\n  }\n\n  // Cancel an order\n  cancelOrder(orderId: string): boolean {\n    const order = this.orders.find(o => o.id === orderId);\n    if (order && order.status === 'PENDING') {\n      order.status = 'CANCELLED';\n      this.saveOrders();\n      console.log(`❌ Order cancelled: ${order.symbol} (${orderId})`);\n      return true;\n    }\n    return false;\n  }\n\n  // Update order status (for external triggers)\n  updateOrderStatus(orderId: string, status: AutoGTTOrder['status']): boolean {\n    const order = this.orders.find(o => o.id === orderId);\n    if (order) {\n      order.status = status;\n      this.saveOrders();\n      console.log(`🔄 Order status updated: ${order.symbol} -> ${status}`);\n      return true;\n    }\n    return false;\n  }\n\n  // Add order listener\n  addOrderListener(listener: (order: AutoGTTOrder) => void): void {\n    this.orderListeners.add(listener);\n  }\n\n  // Remove order listener\n  removeOrderListener(listener: (order: AutoGTTOrder) => void): void {\n    this.orderListeners.delete(listener);\n  }\n\n  // Get service statistics\n  getStatistics() {\n    const today = new Date().toDateString();\n    const todayOrders = this.orders.filter(order => \n      order.createdAt.toDateString() === today && order.autoCreated\n    );\n\n    return {\n      totalOrders: this.orders.length,\n      autoCreatedOrders: this.orders.filter(o => o.autoCreated).length,\n      todayOrders: todayOrders.length,\n      dailyLimit: this.config.maxOrdersPerDay,\n      pendingOrders: this.orders.filter(o => o.status === 'PENDING').length,\n      triggeredOrders: this.orders.filter(o => o.status === 'TRIGGERED').length,\n      cancelledOrders: this.orders.filter(o => o.status === 'CANCELLED').length,\n      isEnabled: this.config.enabled,\n      isInitialized: this.isInitialized\n    };\n  }\n\n  // Manual trigger for testing\n  async testCreateOrder(symbol: string): Promise<AutoGTTOrder | null> {\n    console.log(`🧪 Test order creation for ${symbol}`);\n    \n    // Get current signals\n    const signals = await weeklyHighSignalDetector.triggerManualScan();\n    const signal = signals.find(s => s.symbol === symbol);\n    \n    if (!signal) {\n      console.log(`❌ No signal found for ${symbol}`);\n      return null;\n    }\n\n    return await this.createAutoGTTOrder(signal);\n  }\n\n  // Start the service\n  async start(): Promise<void> {\n    if (!this.isInitialized) {\n      await this.initialize();\n    }\n\n    // Start the signal detector if not already running\n    weeklyHighSignalDetector.start();\n    \n    console.log('🚀 Automatic GTT Service started');\n  }\n\n  // Stop the service\n  stop(): void {\n    weeklyHighSignalDetector.stop();\n    console.log('⏹️ Automatic GTT Service stopped');\n  }\n}\n\n// Export singleton instance\nexport const automaticGTTService = new AutomaticGTTService();\n\n// Auto-start the service when imported\nif (typeof window !== 'undefined') {\n  // Only run in browser environment\n  console.log('🤖 Auto-starting Automatic GTT Service...');\n\n  automaticGTTService.start().then(() => {\n    console.log('✅ Automatic GTT Service auto-started successfully');\n  }).catch((error) => {\n    console.error('❌ Automatic GTT Service auto-start failed:', error);\n  });\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,iFAAiF;;;;;AAEjF;;;AA2BA,MAAM;IAqBJ,yDAAyD;IACzD,MAAM,aAA4B;QAChC,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,QAAQ,GAAG,CAAC;QAEZ,gDAAgD;QAChD,MAAM,IAAI,CAAC,kBAAkB;QAE7B,kCAAkC;QAClC,qJAAA,CAAA,2BAAwB,CAAC,oBAAoB,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI;QAE5E,wCAAwC;QACxC,IAAI,CAAC,yBAAyB;QAE9B,IAAI,CAAC,aAAa,GAAG;QACrB,QAAQ,GAAG,CAAC;IACd;IAEA,2BAA2B;IAC3B,aAAa,SAAiC,EAAQ;QACpD,IAAI,CAAC,MAAM,GAAG;YAAE,GAAG,IAAI,CAAC,MAAM;YAAE,GAAG,SAAS;QAAC;QAC7C,QAAQ,GAAG,CAAC,+BAA+B,IAAI,CAAC,MAAM;IACxD;IAEA,YAA2B;QACzB,OAAO;YAAE,GAAG,IAAI,CAAC,MAAM;QAAC;IAC1B;IAEA,6EAA6E;IAC7E,MAAc,qBAAoC;QAChD,IAAI;YACF,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,IAAI,QAAQ;gBACV,MAAM,eAAe,KAAK,KAAK,CAAC;gBAChC,IAAI,CAAC,MAAM,GAAG,aAAa,GAAG,CAAC,CAAC,QAAe,CAAC;wBAC9C,GAAG,KAAK;wBACR,WAAW,IAAI,KAAK,MAAM,SAAS;oBACrC,CAAC;gBACD,QAAQ,GAAG,CAAC,AAAC,aAA+B,OAAnB,IAAI,CAAC,MAAM,CAAC,MAAM,EAAC;YAC9C;YAEA,qBAAqB;YACrB,MAAM,gBAAgB,aAAa,OAAO,CAAC;YAC3C,MAAM,aAAa,aAAa,OAAO,CAAC;YAExC,IAAI,iBAAiB,eAAe,IAAI,OAAO,YAAY,IAAI;gBAC7D,IAAI,CAAC,eAAe,GAAG,SAAS,eAAe;YACjD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF;IAEA,8BAA8B;IACtB,aAAmB;QACzB,IAAI;YACF,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC,IAAI,CAAC,MAAM;YAChE,aAAa,OAAO,CAAC,qBAAqB,IAAI,CAAC,eAAe,CAAC,QAAQ;YACvE,aAAa,OAAO,CAAC,wBAAwB,IAAI,CAAC,aAAa;QACjE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,wCAAwC;IAChC,4BAAkC;QACxC,MAAM,QAAQ,IAAI,OAAO,YAAY;QACrC,IAAI,IAAI,CAAC,aAAa,KAAK,OAAO;YAChC,IAAI,CAAC,eAAe,GAAG;YACvB,IAAI,CAAC,aAAa,GAAG;YACrB,QAAQ,GAAG,CAAC;QACd;IACF;IAEA,qCAAqC;IAC7B,eAAe,MAAwB,EAA2C;QACxF,8BAA8B;QAC9B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;YACxB,OAAO;gBAAE,WAAW;gBAAO,QAAQ;YAAoC;QACzE;QAEA,iCAAiC;QACjC,IAAI,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE;YACrC,MAAM,MAAM,IAAI;YAChB,MAAM,cAAc,IAAI,QAAQ;YAChC,MAAM,gBAAgB,IAAI,UAAU;YACpC,MAAM,YAAY,IAAI,MAAM,MAAM,KAAK,IAAI,MAAM,MAAM;YACvD,MAAM,eAAe,aACD,CAAC,cAAc,KAAM,gBAAgB,KAAK,iBAAiB,EAAG,KAC9D,CAAC,cAAc,MAAO,gBAAgB,MAAM,iBAAiB,EAAG;YAEpF,IAAI,CAAC,cAAc;gBACjB,OAAO;oBAAE,WAAW;oBAAO,QAAQ;gBAAmB;YACxD;QACF;QAEA,oBAAoB;QACpB,IAAI,CAAC,yBAAyB;QAC9B,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE;YACvD,OAAO;gBAAE,WAAW;gBAAO,QAAQ;YAA4B;QACjE;QAEA,wBAAwB;QACxB,MAAM,gBAAgB;YAAE,QAAQ;YAAG,YAAY;YAAG,UAAU;QAAE;QAC9D,IAAI,aAAa,CAAC,OAAO,cAAc,CAAC,GAAG,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,EAAE;YACvF,OAAO;gBAAE,WAAW;gBAAO,QAAQ,AAAC,mBAAyD,OAAvC,OAAO,cAAc,EAAC,mBAA+C,OAA9B,IAAI,CAAC,MAAM,CAAC,iBAAiB;YAAG;QAC/H;QAEA,wCAAwC;QACxC,IAAI,IAAI,CAAC,MAAM,CAAC,yBAAyB,IAAI,OAAO,WAAW,GAAG,KAAK;YACrE,OAAO;gBAAE,WAAW;gBAAO,QAAQ;YAAmC;QACxE;QAEA,mDAAmD;QACnD,MAAM,gBAAgB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,QACrC,MAAM,MAAM,KAAK,OAAO,MAAM,IAC9B,MAAM,MAAM,KAAK,aACjB,MAAM,MAAM,KAAK;QAGnB,IAAI,eAAe;YACjB,OAAO;gBAAE,WAAW;gBAAO,QAAQ;YAA8C;QACnF;QAEA,0BAA0B;QAC1B,MAAM,qBAAqB,IAAI,CAAC,MAAM,CACnC,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK,OAAO,MAAM,IAAI,MAAM,MAAM,KAAK,WACnE,MAAM,CAAC,CAAC,KAAK,QAAU,MAAO,MAAM,YAAY,GAAG,MAAM,QAAQ,EAAG;QAEvE,MAAM,gBAAgB,OAAO,iBAAiB,GAAG,OAAO,oBAAoB;QAE5E,IAAI,qBAAqB,gBAAgB,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE;YAC1E,OAAO;gBAAE,WAAW;gBAAO,QAAQ;YAA4C;QACjF;QAEA,6BAA6B;QAC7B,IAAI,OAAO,oBAAoB,IAAI,GAAG;YACpC,OAAO;gBAAE,WAAW;gBAAO,QAAQ;YAA8B;QACnE;QAEA,uCAAuC;QACvC,IAAI,OAAO,iBAAiB,IAAI,KAAK,OAAO,iBAAiB,GAAG,MAAM;YACpE,OAAO;gBAAE,WAAW;gBAAO,QAAQ;YAAwB;QAC7D;QAEA,OAAO;YAAE,WAAW;QAAK;IAC3B;IAEA,8BAA8B;IAC9B,MAAc,gBAAgB,MAAwB,EAAiB;QACrE,QAAQ,GAAG,CAAC,AAAC,2BAA4C,OAAlB,OAAO,MAAM,EAAC,MAA0B,OAAtB,OAAO,cAAc,EAAC;QAE/E,MAAM,aAAa,IAAI,CAAC,cAAc,CAAC;QAEvC,IAAI,CAAC,WAAW,SAAS,EAAE;YACzB,QAAQ,GAAG,CAAC,AAAC,kCAAmD,OAAlB,OAAO,MAAM,EAAC,MAAsB,OAAlB,WAAW,MAAM;YACjF;QACF;QAEA,IAAI;YACF,MAAM,IAAI,CAAC,kBAAkB,CAAC;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,AAAC,yCAAsD,OAAd,OAAO,MAAM,EAAC,MAAI;QAC3E;IACF;IAEA,6BAA6B;IAC7B,MAAc,mBAAmB,MAAwB,EAAyB;QAChF,QAAQ,GAAG,CAAC,AAAC,uCAAoD,OAAd,OAAO,MAAM,EAAC;QAEjE,MAAM,QAAsB;YAC1B,IAAI,AAAC,YAAyB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;YACnE,QAAQ,OAAO,MAAM;YACrB,MAAM,OAAO,IAAI;YACjB,WAAW;YACX,cAAc,OAAO,iBAAiB;YACtC,UAAU,OAAO,oBAAoB;YACrC,QAAQ;YACR,WAAW,IAAI;YACf,QAAQ;YACR,gBAAgB,OAAO,cAAc;YACrC,aAAa;YACb,gBAAgB;QAClB;QAEA,qBAAqB;QACrB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACjB,IAAI,CAAC,eAAe;QAEpB,kBAAkB;QAClB,IAAI,CAAC,UAAU;QAEf,mBAAmB;QACnB,QAAQ,GAAG,CAAC,AAAC,6BAAwD,OAA5B,MAAM,MAAM,EAAC,iBAAsD,OAAvC,MAAM,YAAY,CAAC,OAAO,CAAC,IAAG,WAAwB,OAAf,MAAM,QAAQ;QAC1H,QAAQ,GAAG,CAAC,AAAC,oBAA2C,OAAxB,IAAI,CAAC,eAAe,EAAC,KAA+B,OAA5B,IAAI,CAAC,MAAM,CAAC,eAAe;QAEnF,mBAAmB;QACnB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAA;YAC1B,IAAI;gBACF,SAAS;YACX,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8BAA8B;YAC9C;QACF;QAEA,OAAO;IACT;IAEA,iBAAiB;IACjB,eAA+B;QAC7B,OAAO;eAAI,IAAI,CAAC,MAAM;SAAC;IACzB;IAEA,uBAAuB;IACvB,kBAAkB,MAA8B,EAAkB;QAChE,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;IACtD;IAEA,uBAAuB;IACvB,kBAAkB,MAA8B,EAAkB;QAChE,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;IACtD;IAEA,kBAAkB;IAClB,YAAY,OAAe,EAAW;QACpC,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC7C,IAAI,SAAS,MAAM,MAAM,KAAK,WAAW;YACvC,MAAM,MAAM,GAAG;YACf,IAAI,CAAC,UAAU;YACf,QAAQ,GAAG,CAAC,AAAC,sBAAsC,OAAjB,MAAM,MAAM,EAAC,MAAY,OAAR,SAAQ;YAC3D,OAAO;QACT;QACA,OAAO;IACT;IAEA,8CAA8C;IAC9C,kBAAkB,OAAe,EAAE,MAA8B,EAAW;QAC1E,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC7C,IAAI,OAAO;YACT,MAAM,MAAM,GAAG;YACf,IAAI,CAAC,UAAU;YACf,QAAQ,GAAG,CAAC,AAAC,4BAA8C,OAAnB,MAAM,MAAM,EAAC,QAAa,OAAP;YAC3D,OAAO;QACT;QACA,OAAO;IACT;IAEA,qBAAqB;IACrB,iBAAiB,QAAuC,EAAQ;QAC9D,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;IAC1B;IAEA,wBAAwB;IACxB,oBAAoB,QAAuC,EAAQ;QACjE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;IAC7B;IAEA,yBAAyB;IACzB,gBAAgB;QACd,MAAM,QAAQ,IAAI,OAAO,YAAY;QACrC,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,QACrC,MAAM,SAAS,CAAC,YAAY,OAAO,SAAS,MAAM,WAAW;QAG/D,OAAO;YACL,aAAa,IAAI,CAAC,MAAM,CAAC,MAAM;YAC/B,mBAAmB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,MAAM;YAChE,aAAa,YAAY,MAAM;YAC/B,YAAY,IAAI,CAAC,MAAM,CAAC,eAAe;YACvC,eAAe,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;YACrE,iBAAiB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;YACzE,iBAAiB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;YACzE,WAAW,IAAI,CAAC,MAAM,CAAC,OAAO;YAC9B,eAAe,IAAI,CAAC,aAAa;QACnC;IACF;IAEA,6BAA6B;IAC7B,MAAM,gBAAgB,MAAc,EAAgC;QAClE,QAAQ,GAAG,CAAC,AAAC,8BAAoC,OAAP;QAE1C,sBAAsB;QACtB,MAAM,UAAU,MAAM,qJAAA,CAAA,2BAAwB,CAAC,iBAAiB;QAChE,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;QAE9C,IAAI,CAAC,QAAQ;YACX,QAAQ,GAAG,CAAC,AAAC,yBAA+B,OAAP;YACrC,OAAO;QACT;QAEA,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC;IACvC;IAEA,oBAAoB;IACpB,MAAM,QAAuB;QAC3B,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB,MAAM,IAAI,CAAC,UAAU;QACvB;QAEA,mDAAmD;QACnD,qJAAA,CAAA,2BAAwB,CAAC,KAAK;QAE9B,QAAQ,GAAG,CAAC;IACd;IAEA,mBAAmB;IACnB,OAAa;QACX,qJAAA,CAAA,2BAAwB,CAAC,IAAI;QAC7B,QAAQ,GAAG,CAAC;IACd;IA7TA,aAAc;QAhBd,+KAAQ,UAAwB;YAC9B,SAAS;YACT,iBAAiB;YACjB,uBAAuB;YACvB,oBAAoB;YACpB,mBAAmB;YACnB,2BAA2B;YAC3B,uBAAuB;QACzB;QAEA,+KAAQ,UAAyB,EAAE;QACnC,+KAAQ,mBAAkB;QAC1B,+KAAQ,iBAAgB,IAAI,OAAO,YAAY;QAC/C,+KAAQ,kBAAiB,IAAI;QAC7B,+KAAQ,iBAAgB;QAGtB,QAAQ,GAAG,CAAC;IACd;AA4TF;AAGO,MAAM,sBAAsB,IAAI;AAEvC,uCAAuC;AACvC,wCAAmC;IACjC,kCAAkC;IAClC,QAAQ,GAAG,CAAC;IAEZ,oBAAoB,KAAK,GAAG,IAAI,CAAC;QAC/B,QAAQ,GAAG,CAAC;IACd,GAAG,KAAK,CAAC,CAAC;QACR,QAAQ,KAAK,CAAC,8CAA8C;IAC9D;AACF", "debugId": null}}, {"offset": {"line": 769, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/lib/central-data-manager.ts"], "sourcesContent": ["// Central Data Management Service\n// Manages all stock data, caching, and real-time synchronization across pages\n\nimport { yahooFinanceService } from './yahoo-finance';\nimport { stockNamesService } from './stock-names-service';\nimport { NIFTY_200_SYMBOLS, getYahooSymbol, addBOHEligibility, NiftyStock } from './nifty-stocks';\nimport { holdingsService } from './holdings-service';\nimport { weeklyHighSignalDetector, WeeklyHighSignal } from './weekly-high-signal-detector';\nimport { automaticGTTService, AutoGTTOrder } from './automatic-gtt-service';\n\nexport interface StockDataCache {\n  nifty200Stocks: NiftyStock[];\n  bohEligibleStocks: NiftyStock[];\n  weeklyHighSignals: WeeklyHighSignal[];\n  gttOrders: AutoGTTOrder[];\n  lastUpdated: {\n    nifty200: Date | null;\n    bohEligible: Date | null;\n    weeklyHighSignals: Date | null;\n    gttOrders: Date | null;\n  };\n  isLoading: {\n    nifty200: boolean;\n    bohEligible: boolean;\n    weeklyHighSignals: boolean;\n    gttOrders: boolean;\n  };\n}\n\nexport interface DataUpdateConfig {\n  nifty200UpdateInterval: number; // seconds\n  bohEligibleUpdateInterval: number; // seconds\n  weeklyHighSignalsUpdateInterval: number; // seconds\n  gttOrdersUpdateInterval: number; // seconds\n  marketStartHour: number;\n  marketEndHour: number;\n  enableRealTimeUpdates: boolean;\n}\n\ntype DataType = 'nifty200' | 'bohEligible' | 'weeklyHighSignals' | 'gttOrders';\ntype DataListener<T> = (data: T, timestamp: Date) => void;\n\nclass CentralDataManager {\n  private cache: StockDataCache = {\n    nifty200Stocks: [],\n    bohEligibleStocks: [],\n    weeklyHighSignals: [],\n    gttOrders: [],\n    lastUpdated: {\n      nifty200: null,\n      bohEligible: null,\n      weeklyHighSignals: null,\n      gttOrders: null\n    },\n    isLoading: {\n      nifty200: false,\n      bohEligible: false,\n      weeklyHighSignals: false,\n      gttOrders: false\n    }\n  };\n\n  private config: DataUpdateConfig = {\n    nifty200UpdateInterval: 30, // 30 seconds during market hours\n    bohEligibleUpdateInterval: 60, // 1 minute\n    weeklyHighSignalsUpdateInterval: 300, // 5 minutes\n    gttOrdersUpdateInterval: 30, // 30 seconds\n    marketStartHour: 9,\n    marketEndHour: 15,\n    enableRealTimeUpdates: true\n  };\n\n  private intervals: Map<DataType, NodeJS.Timeout> = new Map();\n  private listeners: Map<DataType, Set<DataListener<any>>> = new Map();\n  private isInitialized = false;\n  private isRunning = false;\n\n  constructor() {\n    console.log('📊 Central Data Manager initialized');\n    this.initializeListeners();\n  }\n\n  private initializeListeners(): void {\n    this.listeners.set('nifty200', new Set());\n    this.listeners.set('bohEligible', new Set());\n    this.listeners.set('weeklyHighSignals', new Set());\n    this.listeners.set('gttOrders', new Set());\n  }\n\n  // Configuration management\n  updateConfig(newConfig: Partial<DataUpdateConfig>): void {\n    this.config = { ...this.config, ...newConfig };\n    console.log('⚙️ Central Data Manager config updated:', this.config);\n    \n    if (this.isRunning) {\n      this.stop();\n      this.start();\n    }\n  }\n\n  getConfig(): DataUpdateConfig {\n    return { ...this.config };\n  }\n\n  // Market hours detection\n  private isMarketOpen(): boolean {\n    const now = new Date();\n    const currentHour = now.getHours();\n    const currentMinute = now.getMinutes();\n    \n    const isWeekday = now.getDay() >= 1 && now.getDay() <= 5;\n    const isAfterStart = currentHour > this.config.marketStartHour || \n                       (currentHour === this.config.marketStartHour && currentMinute >= 15);\n    const isBeforeEnd = currentHour < this.config.marketEndHour || \n                       (currentHour === this.config.marketEndHour && currentMinute <= 30);\n    \n    return isWeekday && isAfterStart && isBeforeEnd;\n  }\n\n  // Data loading methods\n  private async loadNifty200Stocks(): Promise<void> {\n    if (this.cache.isLoading.nifty200) return;\n    \n    this.cache.isLoading.nifty200 = true;\n    console.log('📈 Loading Nifty 200 stocks...');\n\n    try {\n      const yahooSymbols = NIFTY_200_SYMBOLS.map(getYahooSymbol);\n      const quotes = await yahooFinanceService.getMultipleQuotesWithCachedNames(yahooSymbols);\n      \n      const holdingSymbols = holdingsService.getAllHoldings().map(h => h.symbol);\n\n      const processedStocks: NiftyStock[] = NIFTY_200_SYMBOLS.map(nseSymbol => {\n        const yahooSymbol = getYahooSymbol(nseSymbol);\n        const quote = quotes.find(q => q.symbol === yahooSymbol);\n\n        const price = quote?.price || 0;\n        const inHoldings = holdingSymbols.includes(nseSymbol);\n        const isEligible = holdingsService.isStockEligibleForTrading(nseSymbol, price);\n\n        const stock: NiftyStock = {\n          symbol: nseSymbol,\n          name: quote?.name || nseSymbol,\n          price,\n          change: quote?.change || 0,\n          changePercent: quote?.changePercent || 0,\n          volume: quote?.volume || 0,\n          marketCap: quote?.marketCap,\n          high52Week: quote?.high52Week,\n          low52Week: quote?.low52Week,\n          high52WeekDate: quote?.high52WeekDate,\n          low52WeekDate: quote?.low52WeekDate,\n          isEligible,\n          inHoldings\n        };\n\n        return addBOHEligibility(stock);\n      });\n\n      this.cache.nifty200Stocks = processedStocks;\n      this.cache.lastUpdated.nifty200 = new Date();\n      \n      console.log(`✅ Loaded ${processedStocks.length} Nifty 200 stocks`);\n      this.notifyListeners('nifty200', processedStocks);\n\n    } catch (error) {\n      console.error('❌ Error loading Nifty 200 stocks:', error);\n    } finally {\n      this.cache.isLoading.nifty200 = false;\n    }\n  }\n\n  private async loadBOHEligibleStocks(): Promise<void> {\n    if (this.cache.isLoading.bohEligible) return;\n    \n    this.cache.isLoading.bohEligible = true;\n    console.log('🔍 Loading BOH eligible stocks...');\n\n    try {\n      // Use cached Nifty 200 data if available and recent\n      let stocks = this.cache.nifty200Stocks;\n      \n      if (stocks.length === 0 || !this.cache.lastUpdated.nifty200 || \n          Date.now() - this.cache.lastUpdated.nifty200.getTime() > 60000) {\n        await this.loadNifty200Stocks();\n        stocks = this.cache.nifty200Stocks;\n      }\n\n      const bohEligibleStocks = stocks.filter(stock => stock.isBOHEligible);\n      \n      this.cache.bohEligibleStocks = bohEligibleStocks;\n      this.cache.lastUpdated.bohEligible = new Date();\n      \n      console.log(`✅ Loaded ${bohEligibleStocks.length} BOH eligible stocks`);\n      this.notifyListeners('bohEligible', bohEligibleStocks);\n\n    } catch (error) {\n      console.error('❌ Error loading BOH eligible stocks:', error);\n    } finally {\n      this.cache.isLoading.bohEligible = false;\n    }\n  }\n\n  private async loadWeeklyHighSignals(): Promise<void> {\n    if (this.cache.isLoading.weeklyHighSignals) return;\n    \n    this.cache.isLoading.weeklyHighSignals = true;\n    console.log('📊 Loading Weekly High Signals...');\n\n    try {\n      const signals = await weeklyHighSignalDetector.triggerManualScan();\n      \n      this.cache.weeklyHighSignals = signals;\n      this.cache.lastUpdated.weeklyHighSignals = new Date();\n      \n      console.log(`✅ Loaded ${signals.length} Weekly High Signals`);\n      this.notifyListeners('weeklyHighSignals', signals);\n\n    } catch (error) {\n      console.error('❌ Error loading Weekly High Signals:', error);\n    } finally {\n      this.cache.isLoading.weeklyHighSignals = false;\n    }\n  }\n\n  private async loadGTTOrders(): Promise<void> {\n    if (this.cache.isLoading.gttOrders) return;\n    \n    this.cache.isLoading.gttOrders = true;\n    console.log('📋 Loading GTT orders...');\n\n    try {\n      const orders = automaticGTTService.getAllOrders();\n      \n      this.cache.gttOrders = orders;\n      this.cache.lastUpdated.gttOrders = new Date();\n      \n      console.log(`✅ Loaded ${orders.length} GTT orders`);\n      this.notifyListeners('gttOrders', orders);\n\n    } catch (error) {\n      console.error('❌ Error loading GTT orders:', error);\n    } finally {\n      this.cache.isLoading.gttOrders = false;\n    }\n  }\n\n  // Listener management\n  addListener<T>(dataType: DataType, listener: DataListener<T>): void {\n    const listeners = this.listeners.get(dataType);\n    if (listeners) {\n      listeners.add(listener);\n      console.log(`👂 Added listener for ${dataType}`);\n    }\n  }\n\n  removeListener<T>(dataType: DataType, listener: DataListener<T>): void {\n    const listeners = this.listeners.get(dataType);\n    if (listeners) {\n      listeners.delete(listener);\n      console.log(`🔇 Removed listener for ${dataType}`);\n    }\n  }\n\n  private notifyListeners<T>(dataType: DataType, data: T): void {\n    const listeners = this.listeners.get(dataType);\n    if (listeners) {\n      const timestamp = new Date();\n      listeners.forEach(listener => {\n        try {\n          listener(data, timestamp);\n        } catch (error) {\n          console.error(`❌ Error in ${dataType} listener:`, error);\n        }\n      });\n    }\n  }\n\n  // Public data access methods\n  getNifty200Stocks(): NiftyStock[] {\n    return [...this.cache.nifty200Stocks];\n  }\n\n  getBOHEligibleStocks(): NiftyStock[] {\n    return [...this.cache.bohEligibleStocks];\n  }\n\n  getWeeklyHighSignals(): WeeklyHighSignal[] {\n    return [...this.cache.weeklyHighSignals];\n  }\n\n  getGTTOrders(): AutoGTTOrder[] {\n    return [...this.cache.gttOrders];\n  }\n\n  getLastUpdated(dataType: DataType): Date | null {\n    return this.cache.lastUpdated[dataType];\n  }\n\n  isDataLoading(dataType: DataType): boolean {\n    return this.cache.isLoading[dataType];\n  }\n\n  // Cache management\n  getCacheStatus() {\n    return {\n      nifty200Count: this.cache.nifty200Stocks.length,\n      bohEligibleCount: this.cache.bohEligibleStocks.length,\n      weeklyHighSignalsCount: this.cache.weeklyHighSignals.length,\n      gttOrdersCount: this.cache.gttOrders.length,\n      lastUpdated: { ...this.cache.lastUpdated },\n      isLoading: { ...this.cache.isLoading },\n      listenerCounts: {\n        nifty200: this.listeners.get('nifty200')?.size || 0,\n        bohEligible: this.listeners.get('bohEligible')?.size || 0,\n        weeklyHighSignals: this.listeners.get('weeklyHighSignals')?.size || 0,\n        gttOrders: this.listeners.get('gttOrders')?.size || 0\n      }\n    };\n  }\n\n  // Force refresh methods\n  async refreshNifty200(): Promise<void> {\n    await this.loadNifty200Stocks();\n  }\n\n  async refreshBOHEligible(): Promise<void> {\n    await this.loadBOHEligibleStocks();\n  }\n\n  async refreshWeeklyHighSignals(): Promise<void> {\n    await this.loadWeeklyHighSignals();\n  }\n\n  async refreshGTTOrders(): Promise<void> {\n    await this.loadGTTOrders();\n  }\n\n  async refreshAll(): Promise<void> {\n    console.log('🔄 Refreshing all data...');\n    await Promise.all([\n      this.loadNifty200Stocks(),\n      this.loadBOHEligibleStocks(),\n      this.loadWeeklyHighSignals(),\n      this.loadGTTOrders()\n    ]);\n    console.log('✅ All data refreshed');\n  }\n\n  // Service lifecycle\n  async initialize(): Promise<void> {\n    if (this.isInitialized) {\n      console.log('⚠️ Central Data Manager already initialized');\n      return;\n    }\n\n    console.log('🚀 Initializing Central Data Manager...');\n    \n    // Load initial data\n    await this.refreshAll();\n    \n    this.isInitialized = true;\n    console.log('✅ Central Data Manager initialized successfully');\n  }\n\n  start(): void {\n    if (this.isRunning) {\n      console.log('⚠️ Central Data Manager already running');\n      return;\n    }\n\n    console.log('🚀 Starting Central Data Manager background updates...');\n    \n    // Set up periodic updates\n    this.intervals.set('nifty200', setInterval(() => {\n      if (this.isMarketOpen() || !this.config.enableRealTimeUpdates) {\n        this.loadNifty200Stocks();\n      }\n    }, this.config.nifty200UpdateInterval * 1000));\n\n    this.intervals.set('bohEligible', setInterval(() => {\n      this.loadBOHEligibleStocks();\n    }, this.config.bohEligibleUpdateInterval * 1000));\n\n    this.intervals.set('weeklyHighSignals', setInterval(() => {\n      if (this.isMarketOpen() || !this.config.enableRealTimeUpdates) {\n        this.loadWeeklyHighSignals();\n      }\n    }, this.config.weeklyHighSignalsUpdateInterval * 1000));\n\n    this.intervals.set('gttOrders', setInterval(() => {\n      this.loadGTTOrders();\n    }, this.config.gttOrdersUpdateInterval * 1000));\n\n    this.isRunning = true;\n    console.log('✅ Central Data Manager background updates started');\n  }\n\n  stop(): void {\n    if (!this.isRunning) {\n      console.log('⚠️ Central Data Manager not running');\n      return;\n    }\n\n    console.log('⏹️ Stopping Central Data Manager background updates...');\n    \n    this.intervals.forEach((interval, dataType) => {\n      clearInterval(interval);\n      console.log(`⏹️ Stopped ${dataType} updates`);\n    });\n    \n    this.intervals.clear();\n    this.isRunning = false;\n    console.log('✅ Central Data Manager background updates stopped');\n  }\n\n  getStatus() {\n    return {\n      isInitialized: this.isInitialized,\n      isRunning: this.isRunning,\n      isMarketOpen: this.isMarketOpen(),\n      config: this.config,\n      cache: this.getCacheStatus()\n    };\n  }\n}\n\n// Export singleton instance\nexport const centralDataManager = new CentralDataManager();\n\n// Auto-initialize and start the service when imported\nif (typeof window !== 'undefined') {\n  // Only run in browser environment\n  console.log('🚀 Auto-initializing Central Data Manager...');\n\n  centralDataManager.initialize().then(() => {\n    centralDataManager.start();\n    console.log('✅ Central Data Manager auto-started successfully');\n  }).catch((error) => {\n    console.error('❌ Central Data Manager auto-start failed:', error);\n  });\n}\n"], "names": [], "mappings": "AAAA,kCAAkC;AAClC,8EAA8E;;;;;AAE9E;AAEA;AACA;AACA;AACA;;;;;;;AAkCA,MAAM;IAwCI,sBAA4B;QAClC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,IAAI;QACnC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,eAAe,IAAI;QACtC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,qBAAqB,IAAI;QAC5C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,aAAa,IAAI;IACtC;IAEA,2BAA2B;IAC3B,aAAa,SAAoC,EAAQ;QACvD,IAAI,CAAC,MAAM,GAAG;YAAE,GAAG,IAAI,CAAC,MAAM;YAAE,GAAG,SAAS;QAAC;QAC7C,QAAQ,GAAG,CAAC,2CAA2C,IAAI,CAAC,MAAM;QAElE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,IAAI;YACT,IAAI,CAAC,KAAK;QACZ;IACF;IAEA,YAA8B;QAC5B,OAAO;YAAE,GAAG,IAAI,CAAC,MAAM;QAAC;IAC1B;IAEA,yBAAyB;IACjB,eAAwB;QAC9B,MAAM,MAAM,IAAI;QAChB,MAAM,cAAc,IAAI,QAAQ;QAChC,MAAM,gBAAgB,IAAI,UAAU;QAEpC,MAAM,YAAY,IAAI,MAAM,MAAM,KAAK,IAAI,MAAM,MAAM;QACvD,MAAM,eAAe,cAAc,IAAI,CAAC,MAAM,CAAC,eAAe,IAC1C,gBAAgB,IAAI,CAAC,MAAM,CAAC,eAAe,IAAI,iBAAiB;QACpF,MAAM,cAAc,cAAc,IAAI,CAAC,MAAM,CAAC,aAAa,IACvC,gBAAgB,IAAI,CAAC,MAAM,CAAC,aAAa,IAAI,iBAAiB;QAElF,OAAO,aAAa,gBAAgB;IACtC;IAEA,uBAAuB;IACvB,MAAc,qBAAoC;QAChD,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,EAAE;QAEnC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,GAAG;QAChC,QAAQ,GAAG,CAAC;QAEZ,IAAI;YACF,MAAM,eAAe,gIAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,gIAAA,CAAA,iBAAc;YACzD,MAAM,SAAS,MAAM,iIAAA,CAAA,sBAAmB,CAAC,gCAAgC,CAAC;YAE1E,MAAM,iBAAiB,oIAAA,CAAA,kBAAe,CAAC,cAAc,GAAG,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;YAEzE,MAAM,kBAAgC,gIAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,CAAA;gBAC1D,MAAM,cAAc,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD,EAAE;gBACnC,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;gBAE5C,MAAM,QAAQ,CAAA,kBAAA,4BAAA,MAAO,KAAK,KAAI;gBAC9B,MAAM,aAAa,eAAe,QAAQ,CAAC;gBAC3C,MAAM,aAAa,oIAAA,CAAA,kBAAe,CAAC,yBAAyB,CAAC,WAAW;gBAExE,MAAM,QAAoB;oBACxB,QAAQ;oBACR,MAAM,CAAA,kBAAA,4BAAA,MAAO,IAAI,KAAI;oBACrB;oBACA,QAAQ,CAAA,kBAAA,4BAAA,MAAO,MAAM,KAAI;oBACzB,eAAe,CAAA,kBAAA,4BAAA,MAAO,aAAa,KAAI;oBACvC,QAAQ,CAAA,kBAAA,4BAAA,MAAO,MAAM,KAAI;oBACzB,SAAS,EAAE,kBAAA,4BAAA,MAAO,SAAS;oBAC3B,UAAU,EAAE,kBAAA,4BAAA,MAAO,UAAU;oBAC7B,SAAS,EAAE,kBAAA,4BAAA,MAAO,SAAS;oBAC3B,cAAc,EAAE,kBAAA,4BAAA,MAAO,cAAc;oBACrC,aAAa,EAAE,kBAAA,4BAAA,MAAO,aAAa;oBACnC;oBACA;gBACF;gBAEA,OAAO,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE;YAC3B;YAEA,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG;YAC5B,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,GAAG,IAAI;YAEtC,QAAQ,GAAG,CAAC,AAAC,YAAkC,OAAvB,gBAAgB,MAAM,EAAC;YAC/C,IAAI,CAAC,eAAe,CAAC,YAAY;QAEnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD,SAAU;YACR,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,GAAG;QAClC;IACF;IAEA,MAAc,wBAAuC;QACnD,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,WAAW,EAAE;QAEtC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,WAAW,GAAG;QACnC,QAAQ,GAAG,CAAC;QAEZ,IAAI;YACF,oDAAoD;YACpD,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC,cAAc;YAEtC,IAAI,OAAO,MAAM,KAAK,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,IACvD,KAAK,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,KAAK,OAAO;gBAClE,MAAM,IAAI,CAAC,kBAAkB;gBAC7B,SAAS,IAAI,CAAC,KAAK,CAAC,cAAc;YACpC;YAEA,MAAM,oBAAoB,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,aAAa;YAEpE,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG;YAC/B,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,WAAW,GAAG,IAAI;YAEzC,QAAQ,GAAG,CAAC,AAAC,YAAoC,OAAzB,kBAAkB,MAAM,EAAC;YACjD,IAAI,CAAC,eAAe,CAAC,eAAe;QAEtC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;QACxD,SAAU;YACR,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,WAAW,GAAG;QACrC;IACF;IAEA,MAAc,wBAAuC;QACnD,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,iBAAiB,EAAE;QAE5C,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,iBAAiB,GAAG;QACzC,QAAQ,GAAG,CAAC;QAEZ,IAAI;YACF,MAAM,UAAU,MAAM,qJAAA,CAAA,2BAAwB,CAAC,iBAAiB;YAEhE,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG;YAC/B,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,iBAAiB,GAAG,IAAI;YAE/C,QAAQ,GAAG,CAAC,AAAC,YAA0B,OAAf,QAAQ,MAAM,EAAC;YACvC,IAAI,CAAC,eAAe,CAAC,qBAAqB;QAE5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;QACxD,SAAU;YACR,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,iBAAiB,GAAG;QAC3C;IACF;IAEA,MAAc,gBAA+B;QAC3C,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,EAAE;QAEpC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,GAAG;QACjC,QAAQ,GAAG,CAAC;QAEZ,IAAI;YACF,MAAM,SAAS,4IAAA,CAAA,sBAAmB,CAAC,YAAY;YAE/C,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG;YACvB,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,GAAG,IAAI;YAEvC,QAAQ,GAAG,CAAC,AAAC,YAAyB,OAAd,OAAO,MAAM,EAAC;YACtC,IAAI,CAAC,eAAe,CAAC,aAAa;QAEpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C,SAAU;YACR,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,GAAG;QACnC;IACF;IAEA,sBAAsB;IACtB,YAAe,QAAkB,EAAE,QAAyB,EAAQ;QAClE,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QACrC,IAAI,WAAW;YACb,UAAU,GAAG,CAAC;YACd,QAAQ,GAAG,CAAC,AAAC,yBAAiC,OAAT;QACvC;IACF;IAEA,eAAkB,QAAkB,EAAE,QAAyB,EAAQ;QACrE,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QACrC,IAAI,WAAW;YACb,UAAU,MAAM,CAAC;YACjB,QAAQ,GAAG,CAAC,AAAC,2BAAmC,OAAT;QACzC;IACF;IAEQ,gBAAmB,QAAkB,EAAE,IAAO,EAAQ;QAC5D,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QACrC,IAAI,WAAW;YACb,MAAM,YAAY,IAAI;YACtB,UAAU,OAAO,CAAC,CAAA;gBAChB,IAAI;oBACF,SAAS,MAAM;gBACjB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,AAAC,cAAsB,OAAT,UAAS,eAAa;gBACpD;YACF;QACF;IACF;IAEA,6BAA6B;IAC7B,oBAAkC;QAChC,OAAO;eAAI,IAAI,CAAC,KAAK,CAAC,cAAc;SAAC;IACvC;IAEA,uBAAqC;QACnC,OAAO;eAAI,IAAI,CAAC,KAAK,CAAC,iBAAiB;SAAC;IAC1C;IAEA,uBAA2C;QACzC,OAAO;eAAI,IAAI,CAAC,KAAK,CAAC,iBAAiB;SAAC;IAC1C;IAEA,eAA+B;QAC7B,OAAO;eAAI,IAAI,CAAC,KAAK,CAAC,SAAS;SAAC;IAClC;IAEA,eAAe,QAAkB,EAAe;QAC9C,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS;IACzC;IAEA,cAAc,QAAkB,EAAW;QACzC,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS;IACvC;IAEA,mBAAmB;IACnB,iBAAiB;YASD,qBACG,sBACM,sBACR;QAXf,OAAO;YACL,eAAe,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM;YAC/C,kBAAkB,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,MAAM;YACrD,wBAAwB,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,MAAM;YAC3D,gBAAgB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM;YAC3C,aAAa;gBAAE,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW;YAAC;YACzC,WAAW;gBAAE,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS;YAAC;YACrC,gBAAgB;gBACd,UAAU,EAAA,sBAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,yBAAnB,0CAAA,oBAAgC,IAAI,KAAI;gBAClD,aAAa,EAAA,uBAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,4BAAnB,2CAAA,qBAAmC,IAAI,KAAI;gBACxD,mBAAmB,EAAA,uBAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,kCAAnB,2CAAA,qBAAyC,IAAI,KAAI;gBACpE,WAAW,EAAA,uBAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,0BAAnB,2CAAA,qBAAiC,IAAI,KAAI;YACtD;QACF;IACF;IAEA,wBAAwB;IACxB,MAAM,kBAAiC;QACrC,MAAM,IAAI,CAAC,kBAAkB;IAC/B;IAEA,MAAM,qBAAoC;QACxC,MAAM,IAAI,CAAC,qBAAqB;IAClC;IAEA,MAAM,2BAA0C;QAC9C,MAAM,IAAI,CAAC,qBAAqB;IAClC;IAEA,MAAM,mBAAkC;QACtC,MAAM,IAAI,CAAC,aAAa;IAC1B;IAEA,MAAM,aAA4B;QAChC,QAAQ,GAAG,CAAC;QACZ,MAAM,QAAQ,GAAG,CAAC;YAChB,IAAI,CAAC,kBAAkB;YACvB,IAAI,CAAC,qBAAqB;YAC1B,IAAI,CAAC,qBAAqB;YAC1B,IAAI,CAAC,aAAa;SACnB;QACD,QAAQ,GAAG,CAAC;IACd;IAEA,oBAAoB;IACpB,MAAM,aAA4B;QAChC,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,QAAQ,GAAG,CAAC;QAEZ,oBAAoB;QACpB,MAAM,IAAI,CAAC,UAAU;QAErB,IAAI,CAAC,aAAa,GAAG;QACrB,QAAQ,GAAG,CAAC;IACd;IAEA,QAAc;QACZ,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,QAAQ,GAAG,CAAC;QAEZ,0BAA0B;QAC1B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,YAAY;YACzC,IAAI,IAAI,CAAC,YAAY,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE;gBAC7D,IAAI,CAAC,kBAAkB;YACzB;QACF,GAAG,IAAI,CAAC,MAAM,CAAC,sBAAsB,GAAG;QAExC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,eAAe,YAAY;YAC5C,IAAI,CAAC,qBAAqB;QAC5B,GAAG,IAAI,CAAC,MAAM,CAAC,yBAAyB,GAAG;QAE3C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,qBAAqB,YAAY;YAClD,IAAI,IAAI,CAAC,YAAY,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE;gBAC7D,IAAI,CAAC,qBAAqB;YAC5B;QACF,GAAG,IAAI,CAAC,MAAM,CAAC,+BAA+B,GAAG;QAEjD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,aAAa,YAAY;YAC1C,IAAI,CAAC,aAAa;QACpB,GAAG,IAAI,CAAC,MAAM,CAAC,uBAAuB,GAAG;QAEzC,IAAI,CAAC,SAAS,GAAG;QACjB,QAAQ,GAAG,CAAC;IACd;IAEA,OAAa;QACX,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,QAAQ,GAAG,CAAC;QAEZ,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,UAAU;YAChC,cAAc;YACd,QAAQ,GAAG,CAAC,AAAC,cAAsB,OAAT,UAAS;QACrC;QAEA,IAAI,CAAC,SAAS,CAAC,KAAK;QACpB,IAAI,CAAC,SAAS,GAAG;QACjB,QAAQ,GAAG,CAAC;IACd;IAEA,YAAY;QACV,OAAO;YACL,eAAe,IAAI,CAAC,aAAa;YACjC,WAAW,IAAI,CAAC,SAAS;YACzB,cAAc,IAAI,CAAC,YAAY;YAC/B,QAAQ,IAAI,CAAC,MAAM;YACnB,OAAO,IAAI,CAAC,cAAc;QAC5B;IACF;IA3VA,aAAc;QAlCd,+KAAQ,SAAwB;YAC9B,gBAAgB,EAAE;YAClB,mBAAmB,EAAE;YACrB,mBAAmB,EAAE;YACrB,WAAW,EAAE;YACb,aAAa;gBACX,UAAU;gBACV,aAAa;gBACb,mBAAmB;gBACnB,WAAW;YACb;YACA,WAAW;gBACT,UAAU;gBACV,aAAa;gBACb,mBAAmB;gBACnB,WAAW;YACb;QACF;QAEA,+KAAQ,UAA2B;YACjC,wBAAwB;YACxB,2BAA2B;YAC3B,iCAAiC;YACjC,yBAAyB;YACzB,iBAAiB;YACjB,eAAe;YACf,uBAAuB;QACzB;QAEA,+KAAQ,aAA2C,IAAI;QACvD,+KAAQ,aAAmD,IAAI;QAC/D,+KAAQ,iBAAgB;QACxB,+KAAQ,aAAY;QAGlB,QAAQ,GAAG,CAAC;QACZ,IAAI,CAAC,mBAAmB;IAC1B;AAyVF;AAGO,MAAM,qBAAqB,IAAI;AAEtC,sDAAsD;AACtD,wCAAmC;IACjC,kCAAkC;IAClC,QAAQ,GAAG,CAAC;IAEZ,mBAAmB,UAAU,GAAG,IAAI,CAAC;QACnC,mBAAmB,KAAK;QACxB,QAAQ,GAAG,CAAC;IACd,GAAG,KAAK,CAAC,CAAC;QACR,QAAQ,KAAK,CAAC,6CAA6C;IAC7D;AACF", "debugId": null}}, {"offset": {"line": 1134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/lib/service-initializer.ts"], "sourcesContent": ["// Service Initializer\n// Ensures all background services are imported and auto-started when the app loads\n\nimport { centralDataManager } from './central-data-manager';\nimport { automaticGTTService } from './automatic-gtt-service';\nimport { weeklyHighSignalDetector } from './weekly-high-signal-detector';\n\nconsole.log('🚀 Service Initializer: Importing all background services...');\n\n// Services will auto-start due to their auto-start code\n// This file just ensures they are imported early in the app lifecycle\n\nexport const initializeAllServices = async () => {\n  console.log('🔧 Initializing all background services...');\n  \n  try {\n    // Wait a moment for auto-start to complete\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    \n    // Check service status\n    const centralStatus = centralDataManager.getStatus();\n    const gttStats = automaticGTTService.getStatistics();\n    const signalStatus = weeklyHighSignalDetector.getStatus();\n    \n    console.log('📊 Service Status Check:');\n    console.log('- Central Data Manager:', centralStatus.isInitialized ? '✅ Running' : '❌ Not Running');\n    console.log('- Automatic GTT Service:', gttStats.isInitialized ? '✅ Running' : '❌ Not Running');\n    console.log('- Weekly High Signal Detector:', signalStatus.isRunning ? '✅ Running' : '❌ Not Running');\n    \n    return {\n      centralDataManager: centralStatus.isInitialized,\n      automaticGTTService: gttStats.isInitialized,\n      weeklyHighSignalDetector: signalStatus.isRunning\n    };\n    \n  } catch (error) {\n    console.error('❌ Service initialization check failed:', error);\n    return {\n      centralDataManager: false,\n      automaticGTTService: false,\n      weeklyHighSignalDetector: false\n    };\n  }\n};\n\n// Export services for easy access\nexport {\n  centralDataManager,\n  automaticGTTService,\n  weeklyHighSignalDetector\n};\n"], "names": [], "mappings": "AAAA,sBAAsB;AACtB,mFAAmF;;;;AAEnF;AACA;AACA;;;;AAEA,QAAQ,GAAG,CAAC;AAKL,MAAM,wBAAwB;IACnC,QAAQ,GAAG,CAAC;IAEZ,IAAI;QACF,2CAA2C;QAC3C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,uBAAuB;QACvB,MAAM,gBAAgB,2IAAA,CAAA,qBAAkB,CAAC,SAAS;QAClD,MAAM,WAAW,4IAAA,CAAA,sBAAmB,CAAC,aAAa;QAClD,MAAM,eAAe,qJAAA,CAAA,2BAAwB,CAAC,SAAS;QAEvD,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,2BAA2B,cAAc,aAAa,GAAG,cAAc;QACnF,QAAQ,GAAG,CAAC,4BAA4B,SAAS,aAAa,GAAG,cAAc;QAC/E,QAAQ,GAAG,CAAC,kCAAkC,aAAa,SAAS,GAAG,cAAc;QAErF,OAAO;YACL,oBAAoB,cAAc,aAAa;YAC/C,qBAAqB,SAAS,aAAa;YAC3C,0BAA0B,aAAa,SAAS;QAClD;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0CAA0C;QACxD,OAAO;YACL,oBAAoB;YACpB,qBAAqB;YACrB,0BAA0B;QAC5B;IACF;AACF", "debugId": null}}, {"offset": {"line": 1192, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/hooks/useCentralData.ts"], "sourcesContent": ["// React Hook for Central Data Manager Integration\n// Provides real-time data access and automatic updates for all stock-related pages\n\nimport { useState, useEffect, useCallback, useRef } from 'react';\nimport { NiftyStock } from '@/lib/nifty-stocks';\nimport { WeeklyHighSignal } from '@/lib/weekly-high-signal-detector';\nimport { AutoGTTOrder } from '@/lib/automatic-gtt-service';\nimport { centralDataManager, automaticGTTService, weeklyHighSignalDetector } from '@/lib/service-initializer';\n\ntype DataType = 'nifty200' | 'bohEligible' | 'weeklyHighSignals' | 'gttOrders';\n\ninterface DataState<T> {\n  data: T[];\n  lastUpdated: Date | null;\n  isLoading: boolean;\n  error: string | null;\n}\n\ninterface CentralDataHook {\n  nifty200: DataState<NiftyStock>;\n  bohEligible: DataState<NiftyStock>;\n  weeklyHighSignals: DataState<WeeklyHighSignal>;\n  gttOrders: DataState<AutoGTTOrder>;\n  refreshData: (dataType?: DataType) => Promise<void>;\n  isInitialized: boolean;\n  isServiceRunning: boolean;\n}\n\nexport function useCentralData(): CentralDataHook {\n  const [nifty200, setNifty200] = useState<DataState<NiftyStock>>({\n    data: [],\n    lastUpdated: null,\n    isLoading: false,\n    error: null\n  });\n\n  const [bohEligible, setBohEligible] = useState<DataState<NiftyStock>>({\n    data: [],\n    lastUpdated: null,\n    isLoading: false,\n    error: null\n  });\n\n  const [weeklyHighSignals, setWeeklyHighSignals] = useState<DataState<WeeklyHighSignal>>({\n    data: [],\n    lastUpdated: null,\n    isLoading: false,\n    error: null\n  });\n\n  const [gttOrders, setGttOrders] = useState<DataState<AutoGTTOrder>>({\n    data: [],\n    lastUpdated: null,\n    isLoading: false,\n    error: null\n  });\n\n  const [isInitialized, setIsInitialized] = useState(false);\n  const [isServiceRunning, setIsServiceRunning] = useState(false);\n\n  const pollingIntervals = useRef<Map<DataType, NodeJS.Timeout>>(new Map());\n\n  // Fetch data directly from services\n  const fetchData = useCallback(async (dataType: DataType) => {\n    try {\n      let data: any[] = [];\n      let lastUpdated: Date | null = null;\n\n      switch (dataType) {\n        case 'nifty200':\n          data = centralDataManager.getNifty200Stocks();\n          lastUpdated = centralDataManager.getLastUpdated('nifty200');\n          setNifty200({\n            data,\n            lastUpdated,\n            isLoading: centralDataManager.isDataLoading('nifty200'),\n            error: null\n          });\n          break;\n        case 'bohEligible':\n          data = centralDataManager.getBOHEligibleStocks();\n          lastUpdated = centralDataManager.getLastUpdated('bohEligible');\n          setBohEligible({\n            data,\n            lastUpdated,\n            isLoading: centralDataManager.isDataLoading('bohEligible'),\n            error: null\n          });\n          break;\n        case 'weeklyHighSignals':\n          data = centralDataManager.getWeeklyHighSignals();\n          lastUpdated = centralDataManager.getLastUpdated('weeklyHighSignals');\n          setWeeklyHighSignals({\n            data,\n            lastUpdated,\n            isLoading: centralDataManager.isDataLoading('weeklyHighSignals'),\n            error: null\n          });\n          break;\n        case 'gttOrders':\n          data = centralDataManager.getGTTOrders();\n          lastUpdated = centralDataManager.getLastUpdated('gttOrders');\n          setGttOrders({\n            data,\n            lastUpdated,\n            isLoading: centralDataManager.isDataLoading('gttOrders'),\n            error: null\n          });\n          break;\n      }\n\n      console.log(`📊 Updated ${dataType}: ${data.length} items`);\n    } catch (error) {\n      console.error(`❌ Error fetching ${dataType}:`, error);\n\n      const errorState = {\n        data: [],\n        lastUpdated: null,\n        isLoading: false,\n        error: error instanceof Error ? error.message : 'Unknown error'\n      };\n\n      switch (dataType) {\n        case 'nifty200':\n          setNifty200(prev => ({ ...prev, ...errorState }));\n          break;\n        case 'bohEligible':\n          setBohEligible(prev => ({ ...prev, ...errorState }));\n          break;\n        case 'weeklyHighSignals':\n          setWeeklyHighSignals(prev => ({ ...prev, ...errorState }));\n          break;\n        case 'gttOrders':\n          setGttOrders(prev => ({ ...prev, ...errorState }));\n          break;\n      }\n    }\n  }, []);\n\n  // Check service status directly\n  const checkServiceStatus = useCallback(() => {\n    try {\n      const status = centralDataManager.getStatus();\n      setIsInitialized(status.isInitialized);\n      setIsServiceRunning(status.isRunning);\n\n      console.log('📊 Service status:', {\n        initialized: status.isInitialized,\n        running: status.isRunning,\n        marketOpen: status.isMarketOpen\n      });\n    } catch (error) {\n      console.error('❌ Error checking service status:', error);\n      setIsInitialized(false);\n      setIsServiceRunning(false);\n    }\n  }, []);\n\n  // Initialize service directly if not already initialized\n  const initializeService = useCallback(async () => {\n    try {\n      console.log('🚀 Initializing Central Data Manager directly...');\n\n      if (!centralDataManager.getStatus().isInitialized) {\n        await centralDataManager.initialize();\n        console.log('✅ Central Data Manager initialized');\n      }\n\n      if (!centralDataManager.getStatus().isRunning) {\n        centralDataManager.start();\n        console.log('✅ Central Data Manager started');\n      }\n\n      setIsInitialized(true);\n      setIsServiceRunning(true);\n    } catch (error) {\n      console.error('❌ Error initializing service:', error);\n      setIsInitialized(false);\n      setIsServiceRunning(false);\n    }\n  }, []);\n\n  // Refresh specific data type\n  const refreshData = useCallback(async (dataType?: DataType) => {\n    if (dataType) {\n      await fetchData(dataType);\n    } else {\n      // Refresh all data\n      await Promise.all([\n        fetchData('nifty200'),\n        fetchData('bohEligible'),\n        fetchData('weeklyHighSignals'),\n        fetchData('gttOrders')\n      ]);\n    }\n  }, [fetchData]);\n\n  // Set up polling for real-time updates\n  const setupPolling = useCallback(() => {\n    // Clear existing intervals\n    pollingIntervals.current.forEach(interval => clearInterval(interval));\n    pollingIntervals.current.clear();\n\n    // Set up new intervals\n    const intervals = {\n      nifty200: 30000, // 30 seconds\n      bohEligible: 60000, // 1 minute\n      weeklyHighSignals: 300000, // 5 minutes\n      gttOrders: 30000 // 30 seconds\n    };\n\n    Object.entries(intervals).forEach(([dataType, interval]) => {\n      const intervalId = setInterval(() => {\n        fetchData(dataType as DataType);\n      }, interval);\n      \n      pollingIntervals.current.set(dataType as DataType, intervalId);\n    });\n\n    console.log('⏰ Polling intervals set up for real-time updates');\n  }, [fetchData]);\n\n  // Set up data listeners for real-time updates\n  useEffect(() => {\n    console.log('🔗 Setting up Central Data Manager listeners...');\n\n    // Add listeners for each data type\n    const nifty200Listener = (data: any[], timestamp: Date) => {\n      setNifty200({\n        data,\n        lastUpdated: timestamp,\n        isLoading: false,\n        error: null\n      });\n      console.log(`📊 Nifty200 data updated: ${data.length} stocks`);\n    };\n\n    const bohEligibleListener = (data: any[], timestamp: Date) => {\n      setBohEligible({\n        data,\n        lastUpdated: timestamp,\n        isLoading: false,\n        error: null\n      });\n      console.log(`📊 BOH Eligible data updated: ${data.length} stocks`);\n    };\n\n    const weeklyHighSignalsListener = (data: any[], timestamp: Date) => {\n      setWeeklyHighSignals({\n        data,\n        lastUpdated: timestamp,\n        isLoading: false,\n        error: null\n      });\n      console.log(`📊 Weekly High Signals updated: ${data.length} signals`);\n    };\n\n    const gttOrdersListener = (data: any[], timestamp: Date) => {\n      setGttOrders({\n        data,\n        lastUpdated: timestamp,\n        isLoading: false,\n        error: null\n      });\n      console.log(`📊 GTT Orders updated: ${data.length} orders`);\n    };\n\n    // Add listeners to Central Data Manager\n    centralDataManager.addListener('nifty200', nifty200Listener);\n    centralDataManager.addListener('bohEligible', bohEligibleListener);\n    centralDataManager.addListener('weeklyHighSignals', weeklyHighSignalsListener);\n    centralDataManager.addListener('gttOrders', gttOrdersListener);\n\n    // Initialize and load initial data\n    const initialize = async () => {\n      checkServiceStatus();\n\n      if (!centralDataManager.getStatus().isInitialized) {\n        await initializeService();\n      }\n\n      // Load initial data\n      await refreshData();\n    };\n\n    initialize();\n\n    // Cleanup listeners on unmount\n    return () => {\n      centralDataManager.removeListener('nifty200', nifty200Listener);\n      centralDataManager.removeListener('bohEligible', bohEligibleListener);\n      centralDataManager.removeListener('weeklyHighSignals', weeklyHighSignalsListener);\n      centralDataManager.removeListener('gttOrders', gttOrdersListener);\n\n      pollingIntervals.current.forEach(interval => clearInterval(interval));\n      pollingIntervals.current.clear();\n    };\n  }, []);\n\n  // Re-setup polling when service status changes\n  useEffect(() => {\n    if (isServiceRunning) {\n      setupPolling();\n    }\n  }, [isServiceRunning, setupPolling]);\n\n  return {\n    nifty200,\n    bohEligible,\n    weeklyHighSignals,\n    gttOrders,\n    refreshData,\n    isInitialized,\n    isServiceRunning\n  };\n}\n\n// Specialized hooks for individual data types\nexport function useNifty200Stocks() {\n  const { nifty200, refreshData } = useCentralData();\n  \n  return {\n    stocks: nifty200.data,\n    lastUpdated: nifty200.lastUpdated,\n    isLoading: nifty200.isLoading,\n    error: nifty200.error,\n    refresh: () => refreshData('nifty200')\n  };\n}\n\nexport function useBOHEligibleStocks() {\n  const { bohEligible, refreshData } = useCentralData();\n  \n  return {\n    stocks: bohEligible.data,\n    lastUpdated: bohEligible.lastUpdated,\n    isLoading: bohEligible.isLoading,\n    error: bohEligible.error,\n    refresh: () => refreshData('bohEligible')\n  };\n}\n\nexport function useWeeklyHighSignals() {\n  const { weeklyHighSignals, refreshData } = useCentralData();\n  \n  return {\n    signals: weeklyHighSignals.data,\n    lastUpdated: weeklyHighSignals.lastUpdated,\n    isLoading: weeklyHighSignals.isLoading,\n    error: weeklyHighSignals.error,\n    refresh: () => refreshData('weeklyHighSignals')\n  };\n}\n\nexport function useGTTOrders() {\n  const { gttOrders, refreshData } = useCentralData();\n  \n  return {\n    orders: gttOrders.data,\n    lastUpdated: gttOrders.lastUpdated,\n    isLoading: gttOrders.isLoading,\n    error: gttOrders.error,\n    refresh: () => refreshData('gttOrders')\n  };\n}\n"], "names": [], "mappings": "AAAA,kDAAkD;AAClD,mFAAmF;;;;;;;;AAEnF;AAIA;AAAA;;;;AAqBO,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;QAC9D,MAAM,EAAE;QACR,aAAa;QACb,WAAW;QACX,OAAO;IACT;IAEA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;QACpE,MAAM,EAAE;QACR,aAAa;QACb,WAAW;QACX,OAAO;IACT;IAEA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA+B;QACtF,MAAM,EAAE;QACR,aAAa;QACb,WAAW;QACX,OAAO;IACT;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;QAClE,MAAM,EAAE;QACR,aAAa;QACb,WAAW;QACX,OAAO;IACT;IAEA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiC,IAAI;IAEnE,oCAAoC;IACpC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,OAAO;YACnC,IAAI;gBACF,IAAI,OAAc,EAAE;gBACpB,IAAI,cAA2B;gBAE/B,OAAQ;oBACN,KAAK;wBACH,OAAO,2IAAA,CAAA,qBAAkB,CAAC,iBAAiB;wBAC3C,cAAc,2IAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;wBAChD,YAAY;4BACV;4BACA;4BACA,WAAW,2IAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC;4BAC5C,OAAO;wBACT;wBACA;oBACF,KAAK;wBACH,OAAO,2IAAA,CAAA,qBAAkB,CAAC,oBAAoB;wBAC9C,cAAc,2IAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;wBAChD,eAAe;4BACb;4BACA;4BACA,WAAW,2IAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC;4BAC5C,OAAO;wBACT;wBACA;oBACF,KAAK;wBACH,OAAO,2IAAA,CAAA,qBAAkB,CAAC,oBAAoB;wBAC9C,cAAc,2IAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;wBAChD,qBAAqB;4BACnB;4BACA;4BACA,WAAW,2IAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC;4BAC5C,OAAO;wBACT;wBACA;oBACF,KAAK;wBACH,OAAO,2IAAA,CAAA,qBAAkB,CAAC,YAAY;wBACtC,cAAc,2IAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;wBAChD,aAAa;4BACX;4BACA;4BACA,WAAW,2IAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC;4BAC5C,OAAO;wBACT;wBACA;gBACJ;gBAEA,QAAQ,GAAG,CAAC,AAAC,cAA0B,OAAb,UAAS,MAAgB,OAAZ,KAAK,MAAM,EAAC;YACrD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,AAAC,oBAA4B,OAAT,UAAS,MAAI;gBAE/C,MAAM,aAAa;oBACjB,MAAM,EAAE;oBACR,aAAa;oBACb,WAAW;oBACX,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD;gBAEA,OAAQ;oBACN,KAAK;wBACH;qEAAY,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,GAAG,UAAU;gCAAC,CAAC;;wBAC/C;oBACF,KAAK;wBACH;qEAAe,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,GAAG,UAAU;gCAAC,CAAC;;wBAClD;oBACF,KAAK;wBACH;qEAAqB,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,GAAG,UAAU;gCAAC,CAAC;;wBACxD;oBACF,KAAK;wBACH;qEAAa,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,GAAG,UAAU;gCAAC,CAAC;;wBAChD;gBACJ;YACF;QACF;gDAAG,EAAE;IAEL,gCAAgC;IAChC,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YACrC,IAAI;gBACF,MAAM,SAAS,2IAAA,CAAA,qBAAkB,CAAC,SAAS;gBAC3C,iBAAiB,OAAO,aAAa;gBACrC,oBAAoB,OAAO,SAAS;gBAEpC,QAAQ,GAAG,CAAC,sBAAsB;oBAChC,aAAa,OAAO,aAAa;oBACjC,SAAS,OAAO,SAAS;oBACzB,YAAY,OAAO,YAAY;gBACjC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,iBAAiB;gBACjB,oBAAoB;YACtB;QACF;yDAAG,EAAE;IAEL,yDAAyD;IACzD,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE;YACpC,IAAI;gBACF,QAAQ,GAAG,CAAC;gBAEZ,IAAI,CAAC,2IAAA,CAAA,qBAAkB,CAAC,SAAS,GAAG,aAAa,EAAE;oBACjD,MAAM,2IAAA,CAAA,qBAAkB,CAAC,UAAU;oBACnC,QAAQ,GAAG,CAAC;gBACd;gBAEA,IAAI,CAAC,2IAAA,CAAA,qBAAkB,CAAC,SAAS,GAAG,SAAS,EAAE;oBAC7C,2IAAA,CAAA,qBAAkB,CAAC,KAAK;oBACxB,QAAQ,GAAG,CAAC;gBACd;gBAEA,iBAAiB;gBACjB,oBAAoB;YACtB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,iBAAiB;gBACjB,oBAAoB;YACtB;QACF;wDAAG,EAAE;IAEL,6BAA6B;IAC7B,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,OAAO;YACrC,IAAI,UAAU;gBACZ,MAAM,UAAU;YAClB,OAAO;gBACL,mBAAmB;gBACnB,MAAM,QAAQ,GAAG,CAAC;oBAChB,UAAU;oBACV,UAAU;oBACV,UAAU;oBACV,UAAU;iBACX;YACH;QACF;kDAAG;QAAC;KAAU;IAEd,uCAAuC;IACvC,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE;YAC/B,2BAA2B;YAC3B,iBAAiB,OAAO,CAAC,OAAO;4DAAC,CAAA,WAAY,cAAc;;YAC3D,iBAAiB,OAAO,CAAC,KAAK;YAE9B,uBAAuB;YACvB,MAAM,YAAY;gBAChB,UAAU;gBACV,aAAa;gBACb,mBAAmB;gBACnB,WAAW,MAAM,aAAa;YAChC;YAEA,OAAO,OAAO,CAAC,WAAW,OAAO;4DAAC;wBAAC,CAAC,UAAU,SAAS;oBACrD,MAAM,aAAa;+EAAY;4BAC7B,UAAU;wBACZ;8EAAG;oBAEH,iBAAiB,OAAO,CAAC,GAAG,CAAC,UAAsB;gBACrD;;YAEA,QAAQ,GAAG,CAAC;QACd;mDAAG;QAAC;KAAU;IAEd,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,QAAQ,GAAG,CAAC;YAEZ,mCAAmC;YACnC,MAAM;6DAAmB,CAAC,MAAa;oBACrC,YAAY;wBACV;wBACA,aAAa;wBACb,WAAW;wBACX,OAAO;oBACT;oBACA,QAAQ,GAAG,CAAC,AAAC,6BAAwC,OAAZ,KAAK,MAAM,EAAC;gBACvD;;YAEA,MAAM;gEAAsB,CAAC,MAAa;oBACxC,eAAe;wBACb;wBACA,aAAa;wBACb,WAAW;wBACX,OAAO;oBACT;oBACA,QAAQ,GAAG,CAAC,AAAC,iCAA4C,OAAZ,KAAK,MAAM,EAAC;gBAC3D;;YAEA,MAAM;sEAA4B,CAAC,MAAa;oBAC9C,qBAAqB;wBACnB;wBACA,aAAa;wBACb,WAAW;wBACX,OAAO;oBACT;oBACA,QAAQ,GAAG,CAAC,AAAC,mCAA8C,OAAZ,KAAK,MAAM,EAAC;gBAC7D;;YAEA,MAAM;8DAAoB,CAAC,MAAa;oBACtC,aAAa;wBACX;wBACA,aAAa;wBACb,WAAW;wBACX,OAAO;oBACT;oBACA,QAAQ,GAAG,CAAC,AAAC,0BAAqC,OAAZ,KAAK,MAAM,EAAC;gBACpD;;YAEA,wCAAwC;YACxC,2IAAA,CAAA,qBAAkB,CAAC,WAAW,CAAC,YAAY;YAC3C,2IAAA,CAAA,qBAAkB,CAAC,WAAW,CAAC,eAAe;YAC9C,2IAAA,CAAA,qBAAkB,CAAC,WAAW,CAAC,qBAAqB;YACpD,2IAAA,CAAA,qBAAkB,CAAC,WAAW,CAAC,aAAa;YAE5C,mCAAmC;YACnC,MAAM;uDAAa;oBACjB;oBAEA,IAAI,CAAC,2IAAA,CAAA,qBAAkB,CAAC,SAAS,GAAG,aAAa,EAAE;wBACjD,MAAM;oBACR;oBAEA,oBAAoB;oBACpB,MAAM;gBACR;;YAEA;YAEA,+BAA+B;YAC/B;4CAAO;oBACL,2IAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC,YAAY;oBAC9C,2IAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC,eAAe;oBACjD,2IAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC,qBAAqB;oBACvD,2IAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC,aAAa;oBAE/C,iBAAiB,OAAO,CAAC,OAAO;oDAAC,CAAA,WAAY,cAAc;;oBAC3D,iBAAiB,OAAO,CAAC,KAAK;gBAChC;;QACF;mCAAG,EAAE;IAEL,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,kBAAkB;gBACpB;YACF;QACF;mCAAG;QAAC;QAAkB;KAAa;IAEnC,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GA/RgB;AAkST,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG;IAElC,OAAO;QACL,QAAQ,SAAS,IAAI;QACrB,aAAa,SAAS,WAAW;QACjC,WAAW,SAAS,SAAS;QAC7B,OAAO,SAAS,KAAK;QACrB,SAAS,IAAM,YAAY;IAC7B;AACF;IAVgB;;QACoB;;;AAW7B,SAAS;;IACd,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG;IAErC,OAAO;QACL,QAAQ,YAAY,IAAI;QACxB,aAAa,YAAY,WAAW;QACpC,WAAW,YAAY,SAAS;QAChC,OAAO,YAAY,KAAK;QACxB,SAAS,IAAM,YAAY;IAC7B;AACF;IAVgB;;QACuB;;;AAWhC,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,WAAW,EAAE,GAAG;IAE3C,OAAO;QACL,SAAS,kBAAkB,IAAI;QAC/B,aAAa,kBAAkB,WAAW;QAC1C,WAAW,kBAAkB,SAAS;QACtC,OAAO,kBAAkB,KAAK;QAC9B,SAAS,IAAM,YAAY;IAC7B;AACF;IAVgB;;QAC6B;;;AAWtC,SAAS;;IACd,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG;IAEnC,OAAO;QACL,QAAQ,UAAU,IAAI;QACtB,aAAa,UAAU,WAAW;QAClC,WAAW,UAAU,SAAS;QAC9B,OAAO,UAAU,KAAK;QACtB,SAAS,IAAM,YAAY;IAC7B;AACF;IAVgB;;QACqB", "debugId": null}}, {"offset": {"line": 1596, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/app/dashboard/weekly-high/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport {\n  ChevronDown,\n  ChevronUp,\n  RefreshCw,\n  TrendingUp,\n  Calendar,\n  Target,\n  Loader,\n  AlertCircle\n} from 'lucide-react';\nimport { formatCurrency, formatPercentage } from '@/lib/utils';\nimport { NiftyStock } from '@/lib/nifty-stocks';\nimport { holdingsService } from '@/lib/holdings-service';\nimport { yahooFinanceService } from '@/lib/yahoo-finance';\nimport { useWeeklyHighSignals } from '@/hooks/useCentralData';\n\ninterface OHLCData {\n  day: string;\n  date: string;\n  open: number;\n  high: number;\n  low: number;\n  close: number;\n}\n\ninterface WeeklyHighStock {\n  symbol: string;\n  name: string;\n  currentPrice: number;\n  lastWeekHighest: number;\n  suggestedBuyPrice: number;\n  percentDifference: number;\n  suggestedGTTQuantity: number;\n  ohlcData: OHLCData[];\n  isBOHEligible: boolean;\n  inHoldings: boolean;\n}\n\nexport default function WeeklyHighSignalPage() {\n  // Central Data Manager for real-time Weekly High Signals\n  const {\n    signals: weeklyHighSignals,\n    lastUpdated: signalsLastUpdated,\n    isLoading: signalsIsLoading,\n    error: signalsError,\n    refresh: refreshSignals\n  } = useWeeklyHighSignals();\n\n  const [stocks, setStocks] = useState<WeeklyHighStock[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [expandedStocks, setExpandedStocks] = useState<Set<string>>(new Set());\n  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);\n\n  // Toggle expanded state for a stock\n  const toggleExpanded = (symbol: string) => {\n    const newExpanded = new Set(expandedStocks);\n    if (newExpanded.has(symbol)) {\n      newExpanded.delete(symbol);\n    } else {\n      newExpanded.add(symbol);\n    }\n    setExpandedStocks(newExpanded);\n  };\n\n  // Generate mock OHLC data for last week\n  const generateOHLCData = (currentPrice: number): OHLCData[] => {\n    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];\n    const ohlcData: OHLCData[] = [];\n\n    // Get last week's dates\n    const today = new Date();\n    const lastFriday = new Date(today);\n    lastFriday.setDate(today.getDate() - ((today.getDay() + 2) % 7)); // Get last Friday\n\n    let basePrice = currentPrice * (0.95 + Math.random() * 0.1); // Start with price variation\n\n    for (let i = 0; i < 5; i++) {\n      const date = new Date(lastFriday);\n      date.setDate(lastFriday.getDate() - (4 - i)); // Monday to Friday of last week\n\n      const variation = 0.02 + Math.random() * 0.03; // 2-5% daily variation\n      const open = basePrice;\n      const high = open * (1 + variation);\n      const low = open * (1 - variation * 0.7);\n      const close = low + (high - low) * Math.random();\n\n      ohlcData.push({\n        day: days[i],\n        date: date.toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' }),\n        open: parseFloat(open.toFixed(2)),\n        high: parseFloat(high.toFixed(2)),\n        low: parseFloat(low.toFixed(2)),\n        close: parseFloat(close.toFixed(2))\n      });\n\n      basePrice = close; // Next day starts with previous close\n    }\n\n    return ohlcData;\n  };\n\n  // Calculate weekly high stock data\n  const calculateWeeklyHighData = (stock: NiftyStock): WeeklyHighStock => {\n    const ohlcData = generateOHLCData(stock.price);\n    const lastWeekHighest = Math.max(...ohlcData.map(d => d.high));\n    const suggestedBuyPrice = lastWeekHighest + 0.05;\n    const percentDifference = ((stock.price - suggestedBuyPrice) / suggestedBuyPrice) * 100;\n    const suggestedGTTQuantity = Math.floor(2000 / suggestedBuyPrice);\n\n    return {\n      symbol: stock.symbol,\n      name: stock.name,\n      currentPrice: stock.price,\n      lastWeekHighest,\n      suggestedBuyPrice,\n      percentDifference,\n      suggestedGTTQuantity,\n      ohlcData,\n      isBOHEligible: stock.isBOHEligible || false,\n      inHoldings: stock.inHoldings\n    };\n  };\n\n  // Load BOH eligible stocks\n  const loadBOHEligibleStocks = async () => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      console.log('🔍 Loading BOH eligible stocks for Weekly High signals...');\n\n      // Fetch BOH eligible stocks from the API\n      const response = await fetch('/api/stocks/nifty200?batchIndex=0&batchSize=200');\n      const data = await response.json();\n\n      if (!data.success) {\n        throw new Error(data.error || 'Failed to fetch stock data');\n      }\n\n      // Get current holdings\n      const holdingSymbols = holdingsService.getAllHoldings().map(h => h.symbol);\n\n      // Filter for BOH eligible stocks not in holdings\n      const bohEligibleStocks = data.data.stocks.filter((stock: NiftyStock) =>\n        stock.isBOHEligible && !holdingSymbols.includes(stock.symbol)\n      );\n\n      console.log(`✅ Found ${bohEligibleStocks.length} BOH eligible stocks not in holdings`);\n\n      // Calculate weekly high data for each stock\n      const weeklyHighStocks = bohEligibleStocks.map(calculateWeeklyHighData);\n\n      // Sort by percentage difference (closest to suggested buy price first)\n      weeklyHighStocks.sort((a, b) => Math.abs(a.percentDifference) - Math.abs(b.percentDifference));\n\n      setStocks(weeklyHighStocks);\n      setLastUpdate(new Date());\n\n    } catch (err) {\n      console.error('❌ Error loading BOH eligible stocks:', err);\n      setError(err instanceof Error ? err.message : 'Failed to load data');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Use Central Data Manager signals when available\n  useEffect(() => {\n    if (weeklyHighSignals.length > 0) {\n      console.log(`📊 Using ${weeklyHighSignals.length} signals from Central Data Manager`);\n\n      // Convert signals to WeeklyHighStock format\n      const convertedStocks: WeeklyHighStock[] = weeklyHighSignals.map(signal => ({\n        symbol: signal.symbol,\n        name: signal.name,\n        currentPrice: signal.currentPrice,\n        lastWeekHigh: signal.lastWeekHigh,\n        suggestedBuyPrice: signal.suggestedBuyPrice,\n        percentDifference: signal.percentDifference,\n        signalStrength: signal.signalStrength,\n        suggestedGTTQuantity: signal.suggestedGTTQuantity,\n        isEligible: true,\n        inHoldings: false\n      }));\n\n      setStocks(convertedStocks);\n      setLastUpdate(signalsLastUpdated || new Date());\n      setLoading(false);\n      setError(null);\n    } else if (signalsError) {\n      setError(signalsError);\n      setLoading(false);\n    } else if (!signalsIsLoading && weeklyHighSignals.length === 0) {\n      // Fallback to original loading method if no signals from Central Data Manager\n      loadBOHEligibleStocks();\n    }\n  }, [weeklyHighSignals, signalsLastUpdated, signalsError, signalsIsLoading]);\n\n  // Load data on component mount (fallback)\n  useEffect(() => {\n    if (weeklyHighSignals.length === 0 && !signalsIsLoading) {\n      loadBOHEligibleStocks();\n    }\n  }, []);\n\n  return (\n    <div className=\"p-6 max-w-7xl mx-auto space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Weekly High Signal</h1>\n          <p className=\"text-gray-600 mt-1\">\n            BOH Eligible Stocks (excluding currently held stocks) • {stocks.length} stocks found\n          </p>\n          {lastUpdate && (\n            <p className=\"text-sm text-gray-500 mt-1\">\n              Last updated: {lastUpdate.toLocaleTimeString()}\n            </p>\n          )}\n        </div>\n\n        <div className=\"flex items-center space-x-3\">\n          <button\n            onClick={() => {\n              console.log('🔄 Refreshing Weekly High Signals via Central Data Manager...');\n              refreshSignals();\n            }}\n            disabled={loading || signalsIsLoading}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center space-x-2\"\n          >\n            <RefreshCw className={`h-4 w-4 ${loading || signalsIsLoading ? 'animate-spin' : ''}`} />\n            <span>Refresh</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Summary Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">BOH Eligible Stocks</p>\n              <p className=\"text-2xl font-bold text-blue-600 mt-1\">{stocks.length}</p>\n            </div>\n            <Target className=\"h-8 w-8 text-blue-600\" />\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Near Buy Price</p>\n              <p className=\"text-2xl font-bold text-green-600 mt-1\">\n                {stocks.filter(s => Math.abs(s.percentDifference) <= 5).length}\n              </p>\n            </div>\n            <TrendingUp className=\"h-8 w-8 text-green-600\" />\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Avg GTT Quantity</p>\n              <p className=\"text-2xl font-bold text-gray-900 mt-1\">\n                {stocks.length > 0 ? Math.round(stocks.reduce((sum, s) => sum + s.suggestedGTTQuantity, 0) / stocks.length) : 0}\n              </p>\n            </div>\n            <Calendar className=\"h-8 w-8 text-gray-600\" />\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Total Investment</p>\n              <p className=\"text-2xl font-bold text-purple-600 mt-1\">\n                {formatCurrency(stocks.length * 2000)}\n              </p>\n            </div>\n            <Target className=\"h-8 w-8 text-purple-600\" />\n          </div>\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n        {error && (\n          <div className=\"p-6 border-b border-gray-200\">\n            <div className=\"flex items-center text-red-600\">\n              <AlertCircle className=\"h-5 w-5 mr-2\" />\n              <span>Error: {error}</span>\n            </div>\n          </div>\n        )}\n\n        {loading ? (\n          <div className=\"p-12 text-center\">\n            <Loader className=\"h-8 w-8 mx-auto mb-4 animate-spin text-blue-600\" />\n            <p className=\"text-gray-500\">Loading BOH eligible stocks...</p>\n            <p className=\"text-sm text-gray-400 mt-1\">Analyzing weekly high patterns</p>\n          </div>\n        ) : stocks.length === 0 ? (\n          <div className=\"p-12 text-center\">\n            <TrendingUp className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n            <p className=\"text-gray-500\">No BOH eligible stocks found</p>\n            <p className=\"text-sm text-gray-400 mt-1\">All BOH eligible stocks are currently in holdings</p>\n          </div>\n        ) : (\n          <div className=\"divide-y divide-gray-200\">\n            {/* Table Header */}\n            <div className=\"p-4 bg-gray-50 grid grid-cols-7 gap-4 text-sm font-medium text-gray-700\">\n              <div>Stock</div>\n              <div className=\"text-right\">Current Price</div>\n              <div className=\"text-right\">Last Week's High</div>\n              <div className=\"text-right\">Suggested Buy Price</div>\n              <div className=\"text-right\">% Difference</div>\n              <div className=\"text-right\">GTT Quantity</div>\n              <div className=\"text-center\">Details</div>\n            </div>\n\n            {/* Stock Rows */}\n            {stocks.map((stock) => (\n              <div key={stock.symbol}>\n                {/* Main Row */}\n                <div className=\"p-4 hover:bg-gray-50 grid grid-cols-7 gap-4 items-center\">\n                  {/* Stock Info */}\n                  <div>\n                    <div className=\"flex items-center space-x-2\">\n                      <h4 className=\"font-medium text-gray-900\">{stock.symbol}</h4>\n                      <span className=\"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full\">\n                        BOH\n                      </span>\n                    </div>\n                    <p className=\"text-sm text-gray-600 truncate\">{stock.name}</p>\n                  </div>\n\n                  {/* Current Price */}\n                  <div className=\"text-right\">\n                    <p className=\"font-medium text-gray-900\">{formatCurrency(stock.currentPrice)}</p>\n                  </div>\n\n                  {/* Last Week's High */}\n                  <div className=\"text-right\">\n                    <p className=\"font-medium text-gray-900\">{formatCurrency(stock.lastWeekHighest)}</p>\n                  </div>\n\n                  {/* Suggested Buy Price */}\n                  <div className=\"text-right\">\n                    <p className=\"font-medium text-green-600\">{formatCurrency(stock.suggestedBuyPrice)}</p>\n                    <p className=\"text-xs text-gray-500\">High + ₹0.05</p>\n                  </div>\n\n                  {/* % Difference */}\n                  <div className=\"text-right\">\n                    <p className={`font-medium ${\n                      stock.percentDifference >= 0\n                        ? 'text-green-600'\n                        : 'text-red-600'\n                    }`}>\n                      {formatPercentage(stock.percentDifference)}\n                    </p>\n                  </div>\n\n                  {/* GTT Quantity */}\n                  <div className=\"text-right\">\n                    <p className=\"font-medium text-gray-900\">{stock.suggestedGTTQuantity}</p>\n                    <p className=\"text-xs text-gray-500\">₹2,000 ÷ Buy Price</p>\n                  </div>\n\n                  {/* Expand Button */}\n                  <div className=\"text-center\">\n                    <button\n                      onClick={() => toggleExpanded(stock.symbol)}\n                      className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n                    >\n                      {expandedStocks.has(stock.symbol) ? (\n                        <ChevronUp className=\"h-4 w-4 text-gray-600\" />\n                      ) : (\n                        <ChevronDown className=\"h-4 w-4 text-gray-600\" />\n                      )}\n                    </button>\n                  </div>\n                </div>\n\n                {/* Expanded OHLC Table */}\n                {expandedStocks.has(stock.symbol) && (\n                  <div className=\"px-4 pb-4 bg-gray-50\">\n                    <div className=\"bg-white rounded-lg border border-gray-200 overflow-hidden\">\n                      <div className=\"px-4 py-3 bg-gray-100 border-b border-gray-200\">\n                        <h5 className=\"font-medium text-gray-900\">\n                          Last Week's OHLC Data - {stock.symbol}\n                        </h5>\n                      </div>\n\n                      <div className=\"overflow-x-auto\">\n                        <table className=\"w-full\">\n                          <thead className=\"bg-gray-50\">\n                            <tr>\n                              <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                Day\n                              </th>\n                              <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                Date\n                              </th>\n                              <th className=\"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                Open\n                              </th>\n                              <th className=\"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                High\n                              </th>\n                              <th className=\"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                Low\n                              </th>\n                              <th className=\"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                Close\n                              </th>\n                              <th className=\"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                Last Week's Highest\n                              </th>\n                            </tr>\n                          </thead>\n                          <tbody className=\"bg-white divide-y divide-gray-200\">\n                            {stock.ohlcData.map((dayData, index) => (\n                              <tr key={`${stock.symbol}-${index}`} className=\"hover:bg-gray-50\">\n                                <td className=\"px-4 py-3 text-sm font-medium text-gray-900\">\n                                  {dayData.day}\n                                </td>\n                                <td className=\"px-4 py-3 text-sm text-gray-600\">\n                                  {dayData.date}\n                                </td>\n                                <td className=\"px-4 py-3 text-sm text-gray-900 text-right\">\n                                  {formatCurrency(dayData.open)}\n                                </td>\n                                <td className=\"px-4 py-3 text-sm text-gray-900 text-right\">\n                                  <span className={dayData.high === stock.lastWeekHighest ? 'font-bold text-green-600' : ''}>\n                                    {formatCurrency(dayData.high)}\n                                  </span>\n                                </td>\n                                <td className=\"px-4 py-3 text-sm text-gray-900 text-right\">\n                                  {formatCurrency(dayData.low)}\n                                </td>\n                                <td className=\"px-4 py-3 text-sm text-gray-900 text-right\">\n                                  {formatCurrency(dayData.close)}\n                                </td>\n                                <td className=\"px-4 py-3 text-sm text-right\">\n                                  {index === stock.ohlcData.length - 1 && (\n                                    <span className=\"font-bold text-green-600\">\n                                      {formatCurrency(stock.lastWeekHighest)}\n                                    </span>\n                                  )}\n                                </td>\n                              </tr>\n                            ))}\n                          </tbody>\n                        </table>\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Signal Timing Info */}\n      <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n        <div className=\"flex items-start\">\n          <Calendar className=\"h-5 w-5 text-blue-600 mt-0.5 mr-3\" />\n          <div>\n            <h3 className=\"font-medium text-blue-900\">New Darvas Box Strategy - Signal Timing</h3>\n            <p className=\"text-blue-700 mt-1\">\n              The signal generation for the New Darvas Box Strategy runs every Friday at 8:00 PM.\n              This ensures fresh weekly high analysis for the upcoming trading week.\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAEA;AAEA;;;AAjBA;;;;;;AAyCe,SAAS;;IACtB,yDAAyD;IACzD,MAAM,EACJ,SAAS,iBAAiB,EAC1B,aAAa,kBAAkB,EAC/B,WAAW,gBAAgB,EAC3B,OAAO,YAAY,EACnB,SAAS,cAAc,EACxB,GAAG,CAAA,GAAA,iIAAA,CAAA,uBAAoB,AAAD;IAEvB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IACtE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAE1D,oCAAoC;IACpC,MAAM,iBAAiB,CAAC;QACtB,MAAM,cAAc,IAAI,IAAI;QAC5B,IAAI,YAAY,GAAG,CAAC,SAAS;YAC3B,YAAY,MAAM,CAAC;QACrB,OAAO;YACL,YAAY,GAAG,CAAC;QAClB;QACA,kBAAkB;IACpB;IAEA,wCAAwC;IACxC,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO;YAAC;YAAU;YAAW;YAAa;YAAY;SAAS;QACrE,MAAM,WAAuB,EAAE;QAE/B,wBAAwB;QACxB,MAAM,QAAQ,IAAI;QAClB,MAAM,aAAa,IAAI,KAAK;QAC5B,WAAW,OAAO,CAAC,MAAM,OAAO,KAAM,CAAC,MAAM,MAAM,KAAK,CAAC,IAAI,IAAK,kBAAkB;QAEpF,IAAI,YAAY,eAAe,CAAC,OAAO,KAAK,MAAM,KAAK,GAAG,GAAG,6BAA6B;QAE1F,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YAC1B,MAAM,OAAO,IAAI,KAAK;YACtB,KAAK,OAAO,CAAC,WAAW,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,gCAAgC;YAE9E,MAAM,YAAY,OAAO,KAAK,MAAM,KAAK,MAAM,uBAAuB;YACtE,MAAM,OAAO;YACb,MAAM,OAAO,OAAO,CAAC,IAAI,SAAS;YAClC,MAAM,MAAM,OAAO,CAAC,IAAI,YAAY,GAAG;YACvC,MAAM,QAAQ,MAAM,CAAC,OAAO,GAAG,IAAI,KAAK,MAAM;YAE9C,SAAS,IAAI,CAAC;gBACZ,KAAK,IAAI,CAAC,EAAE;gBACZ,MAAM,KAAK,kBAAkB,CAAC,SAAS;oBAAE,KAAK;oBAAW,OAAO;oBAAS,MAAM;gBAAU;gBACzF,MAAM,WAAW,KAAK,OAAO,CAAC;gBAC9B,MAAM,WAAW,KAAK,OAAO,CAAC;gBAC9B,KAAK,WAAW,IAAI,OAAO,CAAC;gBAC5B,OAAO,WAAW,MAAM,OAAO,CAAC;YAClC;YAEA,YAAY,OAAO,sCAAsC;QAC3D;QAEA,OAAO;IACT;IAEA,mCAAmC;IACnC,MAAM,0BAA0B,CAAC;QAC/B,MAAM,WAAW,iBAAiB,MAAM,KAAK;QAC7C,MAAM,kBAAkB,KAAK,GAAG,IAAI,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;QAC5D,MAAM,oBAAoB,kBAAkB;QAC5C,MAAM,oBAAoB,AAAC,CAAC,MAAM,KAAK,GAAG,iBAAiB,IAAI,oBAAqB;QACpF,MAAM,uBAAuB,KAAK,KAAK,CAAC,OAAO;QAE/C,OAAO;YACL,QAAQ,MAAM,MAAM;YACpB,MAAM,MAAM,IAAI;YAChB,cAAc,MAAM,KAAK;YACzB;YACA;YACA;YACA;YACA;YACA,eAAe,MAAM,aAAa,IAAI;YACtC,YAAY,MAAM,UAAU;QAC9B;IACF;IAEA,2BAA2B;IAC3B,MAAM,wBAAwB;QAC5B,WAAW;QACX,SAAS;QAET,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,yCAAyC;YACzC,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,KAAK,OAAO,EAAE;gBACjB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,uBAAuB;YACvB,MAAM,iBAAiB,oIAAA,CAAA,kBAAe,CAAC,cAAc,GAAG,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;YAEzE,iDAAiD;YACjD,MAAM,oBAAoB,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,QACjD,MAAM,aAAa,IAAI,CAAC,eAAe,QAAQ,CAAC,MAAM,MAAM;YAG9D,QAAQ,GAAG,CAAC,AAAC,WAAmC,OAAzB,kBAAkB,MAAM,EAAC;YAEhD,4CAA4C;YAC5C,MAAM,mBAAmB,kBAAkB,GAAG,CAAC;YAE/C,uEAAuE;YACvE,iBAAiB,IAAI,CAAC,CAAC,GAAG,IAAM,KAAK,GAAG,CAAC,EAAE,iBAAiB,IAAI,KAAK,GAAG,CAAC,EAAE,iBAAiB;YAE5F,UAAU;YACV,cAAc,IAAI;QAEpB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,wCAAwC;YACtD,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,kDAAkD;IAClD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,kBAAkB,MAAM,GAAG,GAAG;gBAChC,QAAQ,GAAG,CAAC,AAAC,YAAoC,OAAzB,kBAAkB,MAAM,EAAC;gBAEjD,4CAA4C;gBAC5C,MAAM,kBAAqC,kBAAkB,GAAG;sEAAC,CAAA,SAAU,CAAC;4BAC1E,QAAQ,OAAO,MAAM;4BACrB,MAAM,OAAO,IAAI;4BACjB,cAAc,OAAO,YAAY;4BACjC,cAAc,OAAO,YAAY;4BACjC,mBAAmB,OAAO,iBAAiB;4BAC3C,mBAAmB,OAAO,iBAAiB;4BAC3C,gBAAgB,OAAO,cAAc;4BACrC,sBAAsB,OAAO,oBAAoB;4BACjD,YAAY;4BACZ,YAAY;wBACd,CAAC;;gBAED,UAAU;gBACV,cAAc,sBAAsB,IAAI;gBACxC,WAAW;gBACX,SAAS;YACX,OAAO,IAAI,cAAc;gBACvB,SAAS;gBACT,WAAW;YACb,OAAO,IAAI,CAAC,oBAAoB,kBAAkB,MAAM,KAAK,GAAG;gBAC9D,8EAA8E;gBAC9E;YACF;QACF;yCAAG;QAAC;QAAmB;QAAoB;QAAc;KAAiB;IAE1E,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,kBAAkB,MAAM,KAAK,KAAK,CAAC,kBAAkB;gBACvD;YACF;QACF;yCAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;;oCAAqB;oCACyB,OAAO,MAAM;oCAAC;;;;;;;4BAExE,4BACC,6LAAC;gCAAE,WAAU;;oCAA6B;oCACzB,WAAW,kBAAkB;;;;;;;;;;;;;kCAKlD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,SAAS;gCACP,QAAQ,GAAG,CAAC;gCACZ;4BACF;4BACA,UAAU,WAAW;4BACrB,WAAU;;8CAEV,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAW,AAAC,WAA4D,OAAlD,WAAW,mBAAmB,iBAAiB;;;;;;8CAChF,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAyC,OAAO,MAAM;;;;;;;;;;;;8CAErE,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAItB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDACV,OAAO,MAAM,CAAC,CAAA,IAAK,KAAK,GAAG,CAAC,EAAE,iBAAiB,KAAK,GAAG,MAAM;;;;;;;;;;;;8CAGlE,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAI1B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDACV,OAAO,MAAM,GAAG,IAAI,KAAK,KAAK,CAAC,OAAO,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,oBAAoB,EAAE,KAAK,OAAO,MAAM,IAAI;;;;;;;;;;;;8CAGlH,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIxB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,MAAM,GAAG;;;;;;;;;;;;8CAGpC,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMxB,6LAAC;gBAAI,WAAU;;oBACZ,uBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC;;wCAAK;wCAAQ;;;;;;;;;;;;;;;;;;oBAKnB,wBACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;0CAC7B,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;+BAE1C,OAAO,MAAM,KAAK,kBACpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;0CAC7B,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;6CAG5C,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAI;;;;;;kDACL,6LAAC;wCAAI,WAAU;kDAAa;;;;;;kDAC5B,6LAAC;wCAAI,WAAU;kDAAa;;;;;;kDAC5B,6LAAC;wCAAI,WAAU;kDAAa;;;;;;kDAC5B,6LAAC;wCAAI,WAAU;kDAAa;;;;;;kDAC5B,6LAAC;wCAAI,WAAU;kDAAa;;;;;;kDAC5B,6LAAC;wCAAI,WAAU;kDAAc;;;;;;;;;;;;4BAI9B,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;;sDAEC,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAA6B,MAAM,MAAM;;;;;;8EACvD,6LAAC;oEAAK,WAAU;8EAA2D;;;;;;;;;;;;sEAI7E,6LAAC;4DAAE,WAAU;sEAAkC,MAAM,IAAI;;;;;;;;;;;;8DAI3D,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;kEAA6B,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,YAAY;;;;;;;;;;;8DAI7E,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;kEAA6B,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,eAAe;;;;;;;;;;;8DAIhF,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAA8B,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,iBAAiB;;;;;;sEACjF,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAIvC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAW,AAAC,eAId,OAHC,MAAM,iBAAiB,IAAI,IACvB,mBACA;kEAEH,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,iBAAiB;;;;;;;;;;;8DAK7C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAA6B,MAAM,oBAAoB;;;;;;sEACpE,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAIvC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,SAAS,IAAM,eAAe,MAAM,MAAM;wDAC1C,WAAU;kEAET,eAAe,GAAG,CAAC,MAAM,MAAM,kBAC9B,6LAAC,mNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;iFAErB,6LAAC,uNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;wCAO9B,eAAe,GAAG,CAAC,MAAM,MAAM,mBAC9B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAG,WAAU;;gEAA4B;gEACf,MAAM,MAAM;;;;;;;;;;;;kEAIzC,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAM,WAAU;;8EACf,6LAAC;oEAAM,WAAU;8EACf,cAAA,6LAAC;;0FACC,6LAAC;gFAAG,WAAU;0FAAiF;;;;;;0FAG/F,6LAAC;gFAAG,WAAU;0FAAiF;;;;;;0FAG/F,6LAAC;gFAAG,WAAU;0FAAkF;;;;;;0FAGhG,6LAAC;gFAAG,WAAU;0FAAkF;;;;;;0FAGhG,6LAAC;gFAAG,WAAU;0FAAkF;;;;;;0FAGhG,6LAAC;gFAAG,WAAU;0FAAkF;;;;;;0FAGhG,6LAAC;gFAAG,WAAU;0FAAkF;;;;;;;;;;;;;;;;;8EAKpG,6LAAC;oEAAM,WAAU;8EACd,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC5B,6LAAC;4EAAoC,WAAU;;8FAC7C,6LAAC;oFAAG,WAAU;8FACX,QAAQ,GAAG;;;;;;8FAEd,6LAAC;oFAAG,WAAU;8FACX,QAAQ,IAAI;;;;;;8FAEf,6LAAC;oFAAG,WAAU;8FACX,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,IAAI;;;;;;8FAE9B,6LAAC;oFAAG,WAAU;8FACZ,cAAA,6LAAC;wFAAK,WAAW,QAAQ,IAAI,KAAK,MAAM,eAAe,GAAG,6BAA6B;kGACpF,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,IAAI;;;;;;;;;;;8FAGhC,6LAAC;oFAAG,WAAU;8FACX,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,GAAG;;;;;;8FAE7B,6LAAC;oFAAG,WAAU;8FACX,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,KAAK;;;;;;8FAE/B,6LAAC;oFAAG,WAAU;8FACX,UAAU,MAAM,QAAQ,CAAC,MAAM,GAAG,mBACjC,6LAAC;wFAAK,WAAU;kGACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,eAAe;;;;;;;;;;;;2EAxBpC,AAAC,GAAkB,OAAhB,MAAM,MAAM,EAAC,KAAS,OAAN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCArGlC,MAAM,MAAM;;;;;;;;;;;;;;;;;0BAgJ9B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA4B;;;;;;8CAC1C,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C;GA5bwB;;QAQlB,iIAAA,CAAA,uBAAoB;;;KARF", "debugId": null}}]}