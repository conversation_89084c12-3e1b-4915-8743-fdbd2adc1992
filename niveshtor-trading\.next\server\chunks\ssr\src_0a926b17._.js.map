{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/lib/holdings-service.ts"], "sourcesContent": ["// Holdings service to manage current holdings across strategies\n\nexport interface Holding {\n  symbol: string;\n  strategy: string;\n  quantity: number;\n  avgPrice: number;\n  currentPrice: number;\n  purchaseDate: Date;\n}\n\nclass HoldingsService {\n  private holdings: Holding[] = [\n    // Sample holdings for demonstration - in real app, this would come from database\n    {\n      symbol: 'RELIANCE',\n      strategy: 'DARVAS_BOX',\n      quantity: 50,\n      avgPrice: 2200.00,\n      currentPrice: 2456.75,\n      purchaseDate: new Date('2024-01-15')\n    },\n    {\n      symbol: 'TCS',\n      strategy: 'DARVAS_BOX',\n      quantity: 25,\n      avgPrice: 3400.00,\n      currentPrice: 3234.50,\n      purchaseDate: new Date('2024-01-20')\n    },\n    {\n      symbol: 'HDFC',\n      strategy: 'WEEKLY_HIGH',\n      quantity: 40,\n      avgPrice: 1600.00,\n      currentPrice: 1678.90,\n      purchaseDate: new Date('2024-02-01')\n    },\n    {\n      symbol: 'INFY',\n      strategy: 'BOH_FILTER',\n      quantity: 60,\n      avgPrice: 1500.00,\n      currentPrice: 1456.80,\n      purchaseDate: new Date('2024-02-10')\n    }\n  ];\n\n  // Get all current holdings\n  getAllHoldings(): Holding[] {\n    return [...this.holdings];\n  }\n\n  // Get holdings for a specific strategy\n  getHoldingsByStrategy(strategy: string): Holding[] {\n    return this.holdings.filter(holding => holding.strategy === strategy);\n  }\n\n  // Check if a stock is currently held in any strategy\n  isStockInHoldings(symbol: string): boolean {\n    return this.holdings.some(holding => holding.symbol === symbol);\n  }\n\n  // Get all unique symbols in holdings\n  getHoldingSymbols(): string[] {\n    return [...new Set(this.holdings.map(holding => holding.symbol))];\n  }\n\n  // Add a new holding\n  addHolding(holding: Omit<Holding, 'purchaseDate'>): void {\n    const existingIndex = this.holdings.findIndex(\n      h => h.symbol === holding.symbol && h.strategy === holding.strategy\n    );\n\n    if (existingIndex >= 0) {\n      // Update existing holding (average price calculation)\n      const existing = this.holdings[existingIndex];\n      const totalQuantity = existing.quantity + holding.quantity;\n      const totalValue = (existing.quantity * existing.avgPrice) + (holding.quantity * holding.avgPrice);\n      \n      this.holdings[existingIndex] = {\n        ...existing,\n        quantity: totalQuantity,\n        avgPrice: totalValue / totalQuantity,\n        currentPrice: holding.currentPrice\n      };\n    } else {\n      // Add new holding\n      this.holdings.push({\n        ...holding,\n        purchaseDate: new Date()\n      });\n    }\n  }\n\n  // Remove a holding\n  removeHolding(symbol: string, strategy: string): void {\n    this.holdings = this.holdings.filter(\n      holding => !(holding.symbol === symbol && holding.strategy === strategy)\n    );\n  }\n\n  // Update current price for a holding\n  updateCurrentPrice(symbol: string, currentPrice: number): void {\n    this.holdings.forEach(holding => {\n      if (holding.symbol === symbol) {\n        holding.currentPrice = currentPrice;\n      }\n    });\n  }\n\n  // Get stocks that were bought above ₹2000 and are still in holdings\n  getStocksAbove2000InHoldings(): string[] {\n    return this.holdings\n      .filter(holding => holding.avgPrice > 2000 || holding.currentPrice > 2000)\n      .map(holding => holding.symbol);\n  }\n\n  // Check if a stock should be eligible for trading\n  // (CMP < 2000 OR currently in holdings)\n  isStockEligibleForTrading(symbol: string, currentPrice: number): boolean {\n    return currentPrice < 2000 || this.isStockInHoldings(symbol);\n  }\n}\n\nexport const holdingsService = new HoldingsService();\n"], "names": [], "mappings": "AAAA,gEAAgE;;;;AAWhE,MAAM;IACI,WAAsB;QAC5B,iFAAiF;QACjF;YACE,QAAQ;YACR,UAAU;YACV,UAAU;YACV,UAAU;YACV,cAAc;YACd,cAAc,IAAI,KAAK;QACzB;QACA;YACE,QAAQ;YACR,UAAU;YACV,UAAU;YACV,UAAU;YACV,cAAc;YACd,cAAc,IAAI,KAAK;QACzB;QACA;YACE,QAAQ;YACR,UAAU;YACV,UAAU;YACV,UAAU;YACV,cAAc;YACd,cAAc,IAAI,KAAK;QACzB;QACA;YACE,QAAQ;YACR,UAAU;YACV,UAAU;YACV,UAAU;YACV,cAAc;YACd,cAAc,IAAI,KAAK;QACzB;KACD,CAAC;IAEF,2BAA2B;IAC3B,iBAA4B;QAC1B,OAAO;eAAI,IAAI,CAAC,QAAQ;SAAC;IAC3B;IAEA,uCAAuC;IACvC,sBAAsB,QAAgB,EAAa;QACjD,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;IAC9D;IAEA,qDAAqD;IACrD,kBAAkB,MAAc,EAAW;QACzC,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA,UAAW,QAAQ,MAAM,KAAK;IAC1D;IAEA,qCAAqC;IACrC,oBAA8B;QAC5B,OAAO;eAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA,UAAW,QAAQ,MAAM;SAAG;IACnE;IAEA,oBAAoB;IACpB,WAAW,OAAsC,EAAQ;QACvD,MAAM,gBAAgB,IAAI,CAAC,QAAQ,CAAC,SAAS,CAC3C,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM,IAAI,EAAE,QAAQ,KAAK,QAAQ,QAAQ;QAGrE,IAAI,iBAAiB,GAAG;YACtB,sDAAsD;YACtD,MAAM,WAAW,IAAI,CAAC,QAAQ,CAAC,cAAc;YAC7C,MAAM,gBAAgB,SAAS,QAAQ,GAAG,QAAQ,QAAQ;YAC1D,MAAM,aAAa,AAAC,SAAS,QAAQ,GAAG,SAAS,QAAQ,GAAK,QAAQ,QAAQ,GAAG,QAAQ,QAAQ;YAEjG,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG;gBAC7B,GAAG,QAAQ;gBACX,UAAU;gBACV,UAAU,aAAa;gBACvB,cAAc,QAAQ,YAAY;YACpC;QACF,OAAO;YACL,kBAAkB;YAClB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACjB,GAAG,OAAO;gBACV,cAAc,IAAI;YACpB;QACF;IACF;IAEA,mBAAmB;IACnB,cAAc,MAAc,EAAE,QAAgB,EAAQ;QACpD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAClC,CAAA,UAAW,CAAC,CAAC,QAAQ,MAAM,KAAK,UAAU,QAAQ,QAAQ,KAAK,QAAQ;IAE3E;IAEA,qCAAqC;IACrC,mBAAmB,MAAc,EAAE,YAAoB,EAAQ;QAC7D,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;YACpB,IAAI,QAAQ,MAAM,KAAK,QAAQ;gBAC7B,QAAQ,YAAY,GAAG;YACzB;QACF;IACF;IAEA,oEAAoE;IACpE,+BAAyC;QACvC,OAAO,IAAI,CAAC,QAAQ,CACjB,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,GAAG,QAAQ,QAAQ,YAAY,GAAG,MACpE,GAAG,CAAC,CAAA,UAAW,QAAQ,MAAM;IAClC;IAEA,kDAAkD;IAClD,wCAAwC;IACxC,0BAA0B,MAAc,EAAE,YAAoB,EAAW;QACvE,OAAO,eAAe,QAAQ,IAAI,CAAC,iBAAiB,CAAC;IACvD;AACF;AAEO,MAAM,kBAAkB,IAAI", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/app/dashboard/weekly-high/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport {\n  ChevronDown,\n  ChevronUp,\n  RefreshCw,\n  TrendingUp,\n  Calendar,\n  Target,\n  Loader,\n  AlertCircle\n} from 'lucide-react';\nimport { formatCurrency, formatPercentage } from '@/lib/utils';\nimport { NiftyStock } from '@/lib/nifty-stocks';\nimport { holdingsService } from '@/lib/holdings-service';\nimport { yahooFinanceService } from '@/lib/yahoo-finance';\n\ninterface OHLCData {\n  day: string;\n  date: string;\n  open: number;\n  high: number;\n  low: number;\n  close: number;\n}\n\ninterface WeeklyHighStock {\n  symbol: string;\n  name: string;\n  currentPrice: number;\n  lastWeekHighest: number;\n  suggestedBuyPrice: number;\n  percentDifference: number;\n  suggestedGTTQuantity: number;\n  ohlcData: OHLCData[];\n  isBOHEligible: boolean;\n  inHoldings: boolean;\n}\n\nexport default function WeeklyHighSignalPage() {\n  const [stocks, setStocks] = useState<WeeklyHighStock[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [expandedStocks, setExpandedStocks] = useState<Set<string>>(new Set());\n  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);\n\n  // Toggle expanded state for a stock\n  const toggleExpanded = (symbol: string) => {\n    const newExpanded = new Set(expandedStocks);\n    if (newExpanded.has(symbol)) {\n      newExpanded.delete(symbol);\n    } else {\n      newExpanded.add(symbol);\n    }\n    setExpandedStocks(newExpanded);\n  };\n\n  // Generate mock OHLC data for last week\n  const generateOHLCData = (currentPrice: number): OHLCData[] => {\n    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];\n    const ohlcData: OHLCData[] = [];\n\n    // Get last week's dates\n    const today = new Date();\n    const lastFriday = new Date(today);\n    lastFriday.setDate(today.getDate() - ((today.getDay() + 2) % 7)); // Get last Friday\n\n    let basePrice = currentPrice * (0.95 + Math.random() * 0.1); // Start with price variation\n\n    for (let i = 0; i < 5; i++) {\n      const date = new Date(lastFriday);\n      date.setDate(lastFriday.getDate() - (4 - i)); // Monday to Friday of last week\n\n      const variation = 0.02 + Math.random() * 0.03; // 2-5% daily variation\n      const open = basePrice;\n      const high = open * (1 + variation);\n      const low = open * (1 - variation * 0.7);\n      const close = low + (high - low) * Math.random();\n\n      ohlcData.push({\n        day: days[i],\n        date: date.toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' }),\n        open: parseFloat(open.toFixed(2)),\n        high: parseFloat(high.toFixed(2)),\n        low: parseFloat(low.toFixed(2)),\n        close: parseFloat(close.toFixed(2))\n      });\n\n      basePrice = close; // Next day starts with previous close\n    }\n\n    return ohlcData;\n  };\n\n  // Calculate weekly high stock data\n  const calculateWeeklyHighData = (stock: NiftyStock): WeeklyHighStock => {\n    const ohlcData = generateOHLCData(stock.price);\n    const lastWeekHighest = Math.max(...ohlcData.map(d => d.high));\n    const suggestedBuyPrice = lastWeekHighest + 0.05;\n    const percentDifference = ((stock.price - suggestedBuyPrice) / suggestedBuyPrice) * 100;\n    const suggestedGTTQuantity = Math.floor(2000 / suggestedBuyPrice);\n\n    return {\n      symbol: stock.symbol,\n      name: stock.name,\n      currentPrice: stock.price,\n      lastWeekHighest,\n      suggestedBuyPrice,\n      percentDifference,\n      suggestedGTTQuantity,\n      ohlcData,\n      isBOHEligible: stock.isBOHEligible || false,\n      inHoldings: stock.inHoldings\n    };\n  };\n\n  // Load BOH eligible stocks\n  const loadBOHEligibleStocks = async () => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      console.log('🔍 Loading BOH eligible stocks for Weekly High signals...');\n\n      // Fetch BOH eligible stocks from the API\n      const response = await fetch('/api/stocks/nifty200?batchIndex=0&batchSize=200');\n      const data = await response.json();\n\n      if (!data.success) {\n        throw new Error(data.error || 'Failed to fetch stock data');\n      }\n\n      // Get current holdings\n      const holdingSymbols = holdingsService.getAllHoldings().map(h => h.symbol);\n\n      // Filter for BOH eligible stocks not in holdings\n      const bohEligibleStocks = data.data.stocks.filter((stock: NiftyStock) =>\n        stock.isBOHEligible && !holdingSymbols.includes(stock.symbol)\n      );\n\n      console.log(`✅ Found ${bohEligibleStocks.length} BOH eligible stocks not in holdings`);\n\n      // Calculate weekly high data for each stock\n      const weeklyHighStocks = bohEligibleStocks.map(calculateWeeklyHighData);\n\n      // Sort by percentage difference (closest to suggested buy price first)\n      weeklyHighStocks.sort((a, b) => Math.abs(a.percentDifference) - Math.abs(b.percentDifference));\n\n      setStocks(weeklyHighStocks);\n      setLastUpdate(new Date());\n\n    } catch (err) {\n      console.error('❌ Error loading BOH eligible stocks:', err);\n      setError(err instanceof Error ? err.message : 'Failed to load data');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Load data on component mount\n  useEffect(() => {\n    loadBOHEligibleStocks();\n  }, []);\n\n  return (\n    <div className=\"p-6 max-w-7xl mx-auto space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Weekly High Signal</h1>\n          <p className=\"text-gray-600 mt-1\">\n            BOH Eligible Stocks (excluding currently held stocks) • {stocks.length} stocks found\n          </p>\n          {lastUpdate && (\n            <p className=\"text-sm text-gray-500 mt-1\">\n              Last updated: {lastUpdate.toLocaleTimeString()}\n            </p>\n          )}\n        </div>\n\n        <div className=\"flex items-center space-x-3\">\n          <button\n            onClick={loadBOHEligibleStocks}\n            disabled={loading}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center space-x-2\"\n          >\n            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />\n            <span>Refresh</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Summary Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">BOH Eligible Stocks</p>\n              <p className=\"text-2xl font-bold text-blue-600 mt-1\">{stocks.length}</p>\n            </div>\n            <Target className=\"h-8 w-8 text-blue-600\" />\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Near Buy Price</p>\n              <p className=\"text-2xl font-bold text-green-600 mt-1\">\n                {stocks.filter(s => Math.abs(s.percentDifference) <= 5).length}\n              </p>\n            </div>\n            <TrendingUp className=\"h-8 w-8 text-green-600\" />\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Avg GTT Quantity</p>\n              <p className=\"text-2xl font-bold text-gray-900 mt-1\">\n                {stocks.length > 0 ? Math.round(stocks.reduce((sum, s) => sum + s.suggestedGTTQuantity, 0) / stocks.length) : 0}\n              </p>\n            </div>\n            <Calendar className=\"h-8 w-8 text-gray-600\" />\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Total Investment</p>\n              <p className=\"text-2xl font-bold text-purple-600 mt-1\">\n                {formatCurrency(stocks.length * 2000)}\n              </p>\n            </div>\n            <Target className=\"h-8 w-8 text-purple-600\" />\n          </div>\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n        {error && (\n          <div className=\"p-6 border-b border-gray-200\">\n            <div className=\"flex items-center text-red-600\">\n              <AlertCircle className=\"h-5 w-5 mr-2\" />\n              <span>Error: {error}</span>\n            </div>\n          </div>\n        )}\n\n        {loading ? (\n          <div className=\"p-12 text-center\">\n            <Loader className=\"h-8 w-8 mx-auto mb-4 animate-spin text-blue-600\" />\n            <p className=\"text-gray-500\">Loading BOH eligible stocks...</p>\n            <p className=\"text-sm text-gray-400 mt-1\">Analyzing weekly high patterns</p>\n          </div>\n        ) : stocks.length === 0 ? (\n          <div className=\"p-12 text-center\">\n            <TrendingUp className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n            <p className=\"text-gray-500\">No BOH eligible stocks found</p>\n            <p className=\"text-sm text-gray-400 mt-1\">All BOH eligible stocks are currently in holdings</p>\n          </div>\n        ) : (\n          <div className=\"divide-y divide-gray-200\">\n            {/* Table Header */}\n            <div className=\"p-4 bg-gray-50 grid grid-cols-7 gap-4 text-sm font-medium text-gray-700\">\n              <div>Stock</div>\n              <div className=\"text-right\">Current Price</div>\n              <div className=\"text-right\">Last Week's High</div>\n              <div className=\"text-right\">Suggested Buy Price</div>\n              <div className=\"text-right\">% Difference</div>\n              <div className=\"text-right\">GTT Quantity</div>\n              <div className=\"text-center\">Details</div>\n            </div>\n\n            {/* Stock Rows */}\n            {stocks.map((stock) => (\n              <div key={stock.symbol}>\n                {/* Main Row */}\n                <div className=\"p-4 hover:bg-gray-50 grid grid-cols-7 gap-4 items-center\">\n                  {/* Stock Info */}\n                  <div>\n                    <div className=\"flex items-center space-x-2\">\n                      <h4 className=\"font-medium text-gray-900\">{stock.symbol}</h4>\n                      <span className=\"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full\">\n                        BOH\n                      </span>\n                    </div>\n                    <p className=\"text-sm text-gray-600 truncate\">{stock.name}</p>\n                  </div>\n\n                  {/* Current Price */}\n                  <div className=\"text-right\">\n                    <p className=\"font-medium text-gray-900\">{formatCurrency(stock.currentPrice)}</p>\n                  </div>\n\n                  {/* Last Week's High */}\n                  <div className=\"text-right\">\n                    <p className=\"font-medium text-gray-900\">{formatCurrency(stock.lastWeekHighest)}</p>\n                  </div>\n\n                  {/* Suggested Buy Price */}\n                  <div className=\"text-right\">\n                    <p className=\"font-medium text-green-600\">{formatCurrency(stock.suggestedBuyPrice)}</p>\n                    <p className=\"text-xs text-gray-500\">High + ₹0.05</p>\n                  </div>\n\n                  {/* % Difference */}\n                  <div className=\"text-right\">\n                    <p className={`font-medium ${\n                      stock.percentDifference >= 0\n                        ? 'text-green-600'\n                        : 'text-red-600'\n                    }`}>\n                      {formatPercentage(stock.percentDifference)}\n                    </p>\n                  </div>\n\n                  {/* GTT Quantity */}\n                  <div className=\"text-right\">\n                    <p className=\"font-medium text-gray-900\">{stock.suggestedGTTQuantity}</p>\n                    <p className=\"text-xs text-gray-500\">₹2,000 ÷ Buy Price</p>\n                  </div>\n\n                  {/* Expand Button */}\n                  <div className=\"text-center\">\n                    <button\n                      onClick={() => toggleExpanded(stock.symbol)}\n                      className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n                    >\n                      {expandedStocks.has(stock.symbol) ? (\n                        <ChevronUp className=\"h-4 w-4 text-gray-600\" />\n                      ) : (\n                        <ChevronDown className=\"h-4 w-4 text-gray-600\" />\n                      )}\n                    </button>\n                  </div>\n                </div>\n\n                {/* Expanded OHLC Table */}\n                {expandedStocks.has(stock.symbol) && (\n                  <div className=\"px-4 pb-4 bg-gray-50\">\n                    <div className=\"bg-white rounded-lg border border-gray-200 overflow-hidden\">\n                      <div className=\"px-4 py-3 bg-gray-100 border-b border-gray-200\">\n                        <h5 className=\"font-medium text-gray-900\">\n                          Last Week's OHLC Data - {stock.symbol}\n                        </h5>\n                      </div>\n\n                      <div className=\"overflow-x-auto\">\n                        <table className=\"w-full\">\n                          <thead className=\"bg-gray-50\">\n                            <tr>\n                              <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                Day\n                              </th>\n                              <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                Date\n                              </th>\n                              <th className=\"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                Open\n                              </th>\n                              <th className=\"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                High\n                              </th>\n                              <th className=\"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                Low\n                              </th>\n                              <th className=\"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                Close\n                              </th>\n                              <th className=\"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                Last Week's Highest\n                              </th>\n                            </tr>\n                          </thead>\n                          <tbody className=\"bg-white divide-y divide-gray-200\">\n                            {stock.ohlcData.map((dayData, index) => (\n                              <tr key={`${stock.symbol}-${index}`} className=\"hover:bg-gray-50\">\n                                <td className=\"px-4 py-3 text-sm font-medium text-gray-900\">\n                                  {dayData.day}\n                                </td>\n                                <td className=\"px-4 py-3 text-sm text-gray-600\">\n                                  {dayData.date}\n                                </td>\n                                <td className=\"px-4 py-3 text-sm text-gray-900 text-right\">\n                                  {formatCurrency(dayData.open)}\n                                </td>\n                                <td className=\"px-4 py-3 text-sm text-gray-900 text-right\">\n                                  <span className={dayData.high === stock.lastWeekHighest ? 'font-bold text-green-600' : ''}>\n                                    {formatCurrency(dayData.high)}\n                                  </span>\n                                </td>\n                                <td className=\"px-4 py-3 text-sm text-gray-900 text-right\">\n                                  {formatCurrency(dayData.low)}\n                                </td>\n                                <td className=\"px-4 py-3 text-sm text-gray-900 text-right\">\n                                  {formatCurrency(dayData.close)}\n                                </td>\n                                <td className=\"px-4 py-3 text-sm text-right\">\n                                  {index === stock.ohlcData.length - 1 && (\n                                    <span className=\"font-bold text-green-600\">\n                                      {formatCurrency(stock.lastWeekHighest)}\n                                    </span>\n                                  )}\n                                </td>\n                              </tr>\n                            ))}\n                          </tbody>\n                        </table>\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Signal Timing Info */}\n      <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n        <div className=\"flex items-start\">\n          <Calendar className=\"h-5 w-5 text-blue-600 mt-0.5 mr-3\" />\n          <div>\n            <h3 className=\"font-medium text-blue-900\">New Darvas Box Strategy - Signal Timing</h3>\n            <p className=\"text-blue-700 mt-1\">\n              The signal generation for the New Darvas Box Strategy runs every Friday at 8:00 PM.\n              This ensures fresh weekly high analysis for the upcoming trading week.\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAEA;AAfA;;;;;;AAwCe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IACtE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAE1D,oCAAoC;IACpC,MAAM,iBAAiB,CAAC;QACtB,MAAM,cAAc,IAAI,IAAI;QAC5B,IAAI,YAAY,GAAG,CAAC,SAAS;YAC3B,YAAY,MAAM,CAAC;QACrB,OAAO;YACL,YAAY,GAAG,CAAC;QAClB;QACA,kBAAkB;IACpB;IAEA,wCAAwC;IACxC,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO;YAAC;YAAU;YAAW;YAAa;YAAY;SAAS;QACrE,MAAM,WAAuB,EAAE;QAE/B,wBAAwB;QACxB,MAAM,QAAQ,IAAI;QAClB,MAAM,aAAa,IAAI,KAAK;QAC5B,WAAW,OAAO,CAAC,MAAM,OAAO,KAAM,CAAC,MAAM,MAAM,KAAK,CAAC,IAAI,IAAK,kBAAkB;QAEpF,IAAI,YAAY,eAAe,CAAC,OAAO,KAAK,MAAM,KAAK,GAAG,GAAG,6BAA6B;QAE1F,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YAC1B,MAAM,OAAO,IAAI,KAAK;YACtB,KAAK,OAAO,CAAC,WAAW,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,gCAAgC;YAE9E,MAAM,YAAY,OAAO,KAAK,MAAM,KAAK,MAAM,uBAAuB;YACtE,MAAM,OAAO;YACb,MAAM,OAAO,OAAO,CAAC,IAAI,SAAS;YAClC,MAAM,MAAM,OAAO,CAAC,IAAI,YAAY,GAAG;YACvC,MAAM,QAAQ,MAAM,CAAC,OAAO,GAAG,IAAI,KAAK,MAAM;YAE9C,SAAS,IAAI,CAAC;gBACZ,KAAK,IAAI,CAAC,EAAE;gBACZ,MAAM,KAAK,kBAAkB,CAAC,SAAS;oBAAE,KAAK;oBAAW,OAAO;oBAAS,MAAM;gBAAU;gBACzF,MAAM,WAAW,KAAK,OAAO,CAAC;gBAC9B,MAAM,WAAW,KAAK,OAAO,CAAC;gBAC9B,KAAK,WAAW,IAAI,OAAO,CAAC;gBAC5B,OAAO,WAAW,MAAM,OAAO,CAAC;YAClC;YAEA,YAAY,OAAO,sCAAsC;QAC3D;QAEA,OAAO;IACT;IAEA,mCAAmC;IACnC,MAAM,0BAA0B,CAAC;QAC/B,MAAM,WAAW,iBAAiB,MAAM,KAAK;QAC7C,MAAM,kBAAkB,KAAK,GAAG,IAAI,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;QAC5D,MAAM,oBAAoB,kBAAkB;QAC5C,MAAM,oBAAoB,AAAC,CAAC,MAAM,KAAK,GAAG,iBAAiB,IAAI,oBAAqB;QACpF,MAAM,uBAAuB,KAAK,KAAK,CAAC,OAAO;QAE/C,OAAO;YACL,QAAQ,MAAM,MAAM;YACpB,MAAM,MAAM,IAAI;YAChB,cAAc,MAAM,KAAK;YACzB;YACA;YACA;YACA;YACA;YACA,eAAe,MAAM,aAAa,IAAI;YACtC,YAAY,MAAM,UAAU;QAC9B;IACF;IAEA,2BAA2B;IAC3B,MAAM,wBAAwB;QAC5B,WAAW;QACX,SAAS;QAET,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,yCAAyC;YACzC,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,KAAK,OAAO,EAAE;gBACjB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,uBAAuB;YACvB,MAAM,iBAAiB,iIAAA,CAAA,kBAAe,CAAC,cAAc,GAAG,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;YAEzE,iDAAiD;YACjD,MAAM,oBAAoB,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,QACjD,MAAM,aAAa,IAAI,CAAC,eAAe,QAAQ,CAAC,MAAM,MAAM;YAG9D,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,kBAAkB,MAAM,CAAC,oCAAoC,CAAC;YAErF,4CAA4C;YAC5C,MAAM,mBAAmB,kBAAkB,GAAG,CAAC;YAE/C,uEAAuE;YACvE,iBAAiB,IAAI,CAAC,CAAC,GAAG,IAAM,KAAK,GAAG,CAAC,EAAE,iBAAiB,IAAI,KAAK,GAAG,CAAC,EAAE,iBAAiB;YAE5F,UAAU;YACV,cAAc,IAAI;QAEpB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,wCAAwC;YACtD,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;;oCAAqB;oCACyB,OAAO,MAAM;oCAAC;;;;;;;4BAExE,4BACC,8OAAC;gCAAE,WAAU;;oCAA6B;oCACzB,WAAW,kBAAkB;;;;;;;;;;;;;kCAKlD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;;8CAEV,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAW,CAAC,QAAQ,EAAE,UAAU,iBAAiB,IAAI;;;;;;8CAChE,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAyC,OAAO,MAAM;;;;;;;;;;;;8CAErE,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAItB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDACV,OAAO,MAAM,CAAC,CAAA,IAAK,KAAK,GAAG,CAAC,EAAE,iBAAiB,KAAK,GAAG,MAAM;;;;;;;;;;;;8CAGlE,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAI1B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDACV,OAAO,MAAM,GAAG,IAAI,KAAK,KAAK,CAAC,OAAO,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,oBAAoB,EAAE,KAAK,OAAO,MAAM,IAAI;;;;;;;;;;;;8CAGlH,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIxB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDACV,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,MAAM,GAAG;;;;;;;;;;;;8CAGpC,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMxB,8OAAC;gBAAI,WAAU;;oBACZ,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC;;wCAAK;wCAAQ;;;;;;;;;;;;;;;;;;oBAKnB,wBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;0CAC7B,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;+BAE1C,OAAO,MAAM,KAAK,kBACpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;0CAC7B,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;6CAG5C,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAI;;;;;;kDACL,8OAAC;wCAAI,WAAU;kDAAa;;;;;;kDAC5B,8OAAC;wCAAI,WAAU;kDAAa;;;;;;kDAC5B,8OAAC;wCAAI,WAAU;kDAAa;;;;;;kDAC5B,8OAAC;wCAAI,WAAU;kDAAa;;;;;;kDAC5B,8OAAC;wCAAI,WAAU;kDAAa;;;;;;kDAC5B,8OAAC;wCAAI,WAAU;kDAAc;;;;;;;;;;;;4BAI9B,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;;sDAEC,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAA6B,MAAM,MAAM;;;;;;8EACvD,8OAAC;oEAAK,WAAU;8EAA2D;;;;;;;;;;;;sEAI7E,8OAAC;4DAAE,WAAU;sEAAkC,MAAM,IAAI;;;;;;;;;;;;8DAI3D,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;kEAA6B,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,YAAY;;;;;;;;;;;8DAI7E,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;kEAA6B,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,eAAe;;;;;;;;;;;8DAIhF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAA8B,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,iBAAiB;;;;;;sEACjF,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAIvC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAW,CAAC,YAAY,EACzB,MAAM,iBAAiB,IAAI,IACvB,mBACA,gBACJ;kEACC,CAAA,GAAA,mHAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,iBAAiB;;;;;;;;;;;8DAK7C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAA6B,MAAM,oBAAoB;;;;;;sEACpE,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAIvC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,SAAS,IAAM,eAAe,MAAM,MAAM;wDAC1C,WAAU;kEAET,eAAe,GAAG,CAAC,MAAM,MAAM,kBAC9B,8OAAC,gNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;iFAErB,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;wCAO9B,eAAe,GAAG,CAAC,MAAM,MAAM,mBAC9B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAG,WAAU;;gEAA4B;gEACf,MAAM,MAAM;;;;;;;;;;;;kEAIzC,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAM,WAAU;;8EACf,8OAAC;oEAAM,WAAU;8EACf,cAAA,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAiF;;;;;;0FAG/F,8OAAC;gFAAG,WAAU;0FAAiF;;;;;;0FAG/F,8OAAC;gFAAG,WAAU;0FAAkF;;;;;;0FAGhG,8OAAC;gFAAG,WAAU;0FAAkF;;;;;;0FAGhG,8OAAC;gFAAG,WAAU;0FAAkF;;;;;;0FAGhG,8OAAC;gFAAG,WAAU;0FAAkF;;;;;;0FAGhG,8OAAC;gFAAG,WAAU;0FAAkF;;;;;;;;;;;;;;;;;8EAKpG,8OAAC;oEAAM,WAAU;8EACd,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC5B,8OAAC;4EAAoC,WAAU;;8FAC7C,8OAAC;oFAAG,WAAU;8FACX,QAAQ,GAAG;;;;;;8FAEd,8OAAC;oFAAG,WAAU;8FACX,QAAQ,IAAI;;;;;;8FAEf,8OAAC;oFAAG,WAAU;8FACX,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,IAAI;;;;;;8FAE9B,8OAAC;oFAAG,WAAU;8FACZ,cAAA,8OAAC;wFAAK,WAAW,QAAQ,IAAI,KAAK,MAAM,eAAe,GAAG,6BAA6B;kGACpF,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,IAAI;;;;;;;;;;;8FAGhC,8OAAC;oFAAG,WAAU;8FACX,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,GAAG;;;;;;8FAE7B,8OAAC;oFAAG,WAAU;8FACX,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,KAAK;;;;;;8FAE/B,8OAAC;oFAAG,WAAU;8FACX,UAAU,MAAM,QAAQ,CAAC,MAAM,GAAG,mBACjC,8OAAC;wFAAK,WAAU;kGACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,eAAe;;;;;;;;;;;;2EAxBpC,GAAG,MAAM,MAAM,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCArGzC,MAAM,MAAM;;;;;;;;;;;;;;;;;0BAgJ9B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA4B;;;;;;8CAC1C,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C", "debugId": null}}]}