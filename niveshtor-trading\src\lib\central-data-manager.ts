// Central Data Management Service
// Manages all stock data, caching, and real-time synchronization across pages

import { yahooFinanceService } from './yahoo-finance';
import { stockNamesService } from './stock-names-service';
import { NIFTY_200_SYMBOLS, getYahooSymbol, addBOHEligibility, NiftyStock } from './nifty-stocks';
import { holdingsService } from './holdings-service';
import { weeklyHighSignalDetector, WeeklyHighSignal } from './weekly-high-signal-detector';
import { automaticGTTService, AutoGTTOrder } from './automatic-gtt-service';

export interface StockDataCache {
  nifty200Stocks: NiftyStock[];
  bohEligibleStocks: NiftyStock[];
  weeklyHighSignals: WeeklyHighSignal[];
  gttOrders: AutoGTTOrder[];
  lastUpdated: {
    nifty200: Date | null;
    bohEligible: Date | null;
    weeklyHighSignals: Date | null;
    gttOrders: Date | null;
  };
  isLoading: {
    nifty200: boolean;
    bohEligible: boolean;
    weeklyHighSignals: boolean;
    gttOrders: boolean;
  };
}

export interface DataUpdateConfig {
  nifty200UpdateInterval: number; // seconds
  bohEligibleUpdateInterval: number; // seconds
  weeklyHighSignalsUpdateInterval: number; // seconds
  gttOrdersUpdateInterval: number; // seconds
  marketStartHour: number;
  marketEndHour: number;
  enableRealTimeUpdates: boolean;
}

type DataType = 'nifty200' | 'bohEligible' | 'weeklyHighSignals' | 'gttOrders';
type DataListener<T> = (data: T, timestamp: Date) => void;

class CentralDataManager {
  private cache: StockDataCache = {
    nifty200Stocks: [],
    bohEligibleStocks: [],
    weeklyHighSignals: [],
    gttOrders: [],
    lastUpdated: {
      nifty200: null,
      bohEligible: null,
      weeklyHighSignals: null,
      gttOrders: null
    },
    isLoading: {
      nifty200: false,
      bohEligible: false,
      weeklyHighSignals: false,
      gttOrders: false
    }
  };

  private config: DataUpdateConfig = {
    nifty200UpdateInterval: 30, // 30 seconds during market hours
    bohEligibleUpdateInterval: 60, // 1 minute
    weeklyHighSignalsUpdateInterval: 300, // 5 minutes
    gttOrdersUpdateInterval: 30, // 30 seconds
    marketStartHour: 9,
    marketEndHour: 15,
    enableRealTimeUpdates: true
  };

  private intervals: Map<DataType, NodeJS.Timeout> = new Map();
  private listeners: Map<DataType, Set<DataListener<any>>> = new Map();
  private isInitialized = false;
  private isRunning = false;

  constructor() {
    console.log('📊 Central Data Manager initialized');
    this.initializeListeners();
  }

  private initializeListeners(): void {
    this.listeners.set('nifty200', new Set());
    this.listeners.set('bohEligible', new Set());
    this.listeners.set('weeklyHighSignals', new Set());
    this.listeners.set('gttOrders', new Set());
  }

  // Configuration management
  updateConfig(newConfig: Partial<DataUpdateConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('⚙️ Central Data Manager config updated:', this.config);
    
    if (this.isRunning) {
      this.stop();
      this.start();
    }
  }

  getConfig(): DataUpdateConfig {
    return { ...this.config };
  }

  // Market hours detection
  private isMarketOpen(): boolean {
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    
    const isWeekday = now.getDay() >= 1 && now.getDay() <= 5;
    const isAfterStart = currentHour > this.config.marketStartHour || 
                       (currentHour === this.config.marketStartHour && currentMinute >= 15);
    const isBeforeEnd = currentHour < this.config.marketEndHour || 
                       (currentHour === this.config.marketEndHour && currentMinute <= 30);
    
    return isWeekday && isAfterStart && isBeforeEnd;
  }

  // Data loading methods
  private async loadNifty200Stocks(): Promise<void> {
    if (this.cache.isLoading.nifty200) return;
    
    this.cache.isLoading.nifty200 = true;
    console.log('📈 Loading Nifty 200 stocks...');

    try {
      const yahooSymbols = NIFTY_200_SYMBOLS.map(getYahooSymbol);
      const quotes = await yahooFinanceService.getMultipleQuotesWithCachedNames(yahooSymbols);
      
      const holdingSymbols = holdingsService.getAllHoldings().map(h => h.symbol);

      const processedStocks: NiftyStock[] = NIFTY_200_SYMBOLS.map(nseSymbol => {
        const yahooSymbol = getYahooSymbol(nseSymbol);
        const quote = quotes.find(q => q.symbol === yahooSymbol);

        const price = quote?.price || 0;
        const inHoldings = holdingSymbols.includes(nseSymbol);
        const isEligible = holdingsService.isStockEligibleForTrading(nseSymbol, price);

        const stock: NiftyStock = {
          symbol: nseSymbol,
          name: quote?.name || nseSymbol,
          price,
          change: quote?.change || 0,
          changePercent: quote?.changePercent || 0,
          volume: quote?.volume || 0,
          marketCap: quote?.marketCap,
          high52Week: quote?.high52Week,
          low52Week: quote?.low52Week,
          high52WeekDate: quote?.high52WeekDate,
          low52WeekDate: quote?.low52WeekDate,
          isEligible,
          inHoldings
        };

        return addBOHEligibility(stock);
      });

      this.cache.nifty200Stocks = processedStocks;
      this.cache.lastUpdated.nifty200 = new Date();
      
      console.log(`✅ Loaded ${processedStocks.length} Nifty 200 stocks`);
      this.notifyListeners('nifty200', processedStocks);

    } catch (error) {
      console.error('❌ Error loading Nifty 200 stocks:', error);
    } finally {
      this.cache.isLoading.nifty200 = false;
    }
  }

  private async loadBOHEligibleStocks(): Promise<void> {
    if (this.cache.isLoading.bohEligible) return;
    
    this.cache.isLoading.bohEligible = true;
    console.log('🔍 Loading BOH eligible stocks...');

    try {
      // Use cached Nifty 200 data if available and recent
      let stocks = this.cache.nifty200Stocks;
      
      if (stocks.length === 0 || !this.cache.lastUpdated.nifty200 || 
          Date.now() - this.cache.lastUpdated.nifty200.getTime() > 60000) {
        await this.loadNifty200Stocks();
        stocks = this.cache.nifty200Stocks;
      }

      const bohEligibleStocks = stocks.filter(stock => stock.isBOHEligible);
      
      this.cache.bohEligibleStocks = bohEligibleStocks;
      this.cache.lastUpdated.bohEligible = new Date();
      
      console.log(`✅ Loaded ${bohEligibleStocks.length} BOH eligible stocks`);
      this.notifyListeners('bohEligible', bohEligibleStocks);

    } catch (error) {
      console.error('❌ Error loading BOH eligible stocks:', error);
    } finally {
      this.cache.isLoading.bohEligible = false;
    }
  }

  private async loadWeeklyHighSignals(): Promise<void> {
    if (this.cache.isLoading.weeklyHighSignals) return;
    
    this.cache.isLoading.weeklyHighSignals = true;
    console.log('📊 Loading Weekly High Signals...');

    try {
      const signals = await weeklyHighSignalDetector.triggerManualScan();
      
      this.cache.weeklyHighSignals = signals;
      this.cache.lastUpdated.weeklyHighSignals = new Date();
      
      console.log(`✅ Loaded ${signals.length} Weekly High Signals`);
      this.notifyListeners('weeklyHighSignals', signals);

    } catch (error) {
      console.error('❌ Error loading Weekly High Signals:', error);
    } finally {
      this.cache.isLoading.weeklyHighSignals = false;
    }
  }

  private async loadGTTOrders(): Promise<void> {
    if (this.cache.isLoading.gttOrders) return;
    
    this.cache.isLoading.gttOrders = true;
    console.log('📋 Loading GTT orders...');

    try {
      const orders = automaticGTTService.getAllOrders();
      
      this.cache.gttOrders = orders;
      this.cache.lastUpdated.gttOrders = new Date();
      
      console.log(`✅ Loaded ${orders.length} GTT orders`);
      this.notifyListeners('gttOrders', orders);

    } catch (error) {
      console.error('❌ Error loading GTT orders:', error);
    } finally {
      this.cache.isLoading.gttOrders = false;
    }
  }

  // Listener management
  addListener<T>(dataType: DataType, listener: DataListener<T>): void {
    const listeners = this.listeners.get(dataType);
    if (listeners) {
      listeners.add(listener);
      console.log(`👂 Added listener for ${dataType}`);
    }
  }

  removeListener<T>(dataType: DataType, listener: DataListener<T>): void {
    const listeners = this.listeners.get(dataType);
    if (listeners) {
      listeners.delete(listener);
      console.log(`🔇 Removed listener for ${dataType}`);
    }
  }

  private notifyListeners<T>(dataType: DataType, data: T): void {
    const listeners = this.listeners.get(dataType);
    if (listeners) {
      const timestamp = new Date();
      listeners.forEach(listener => {
        try {
          listener(data, timestamp);
        } catch (error) {
          console.error(`❌ Error in ${dataType} listener:`, error);
        }
      });
    }
  }

  // Public data access methods
  getNifty200Stocks(): NiftyStock[] {
    return [...this.cache.nifty200Stocks];
  }

  getBOHEligibleStocks(): NiftyStock[] {
    return [...this.cache.bohEligibleStocks];
  }

  getWeeklyHighSignals(): WeeklyHighSignal[] {
    return [...this.cache.weeklyHighSignals];
  }

  getGTTOrders(): AutoGTTOrder[] {
    return [...this.cache.gttOrders];
  }

  getLastUpdated(dataType: DataType): Date | null {
    return this.cache.lastUpdated[dataType];
  }

  isDataLoading(dataType: DataType): boolean {
    return this.cache.isLoading[dataType];
  }

  // Cache management
  getCacheStatus() {
    return {
      nifty200Count: this.cache.nifty200Stocks.length,
      bohEligibleCount: this.cache.bohEligibleStocks.length,
      weeklyHighSignalsCount: this.cache.weeklyHighSignals.length,
      gttOrdersCount: this.cache.gttOrders.length,
      lastUpdated: { ...this.cache.lastUpdated },
      isLoading: { ...this.cache.isLoading },
      listenerCounts: {
        nifty200: this.listeners.get('nifty200')?.size || 0,
        bohEligible: this.listeners.get('bohEligible')?.size || 0,
        weeklyHighSignals: this.listeners.get('weeklyHighSignals')?.size || 0,
        gttOrders: this.listeners.get('gttOrders')?.size || 0
      }
    };
  }

  // Force refresh methods
  async refreshNifty200(): Promise<void> {
    await this.loadNifty200Stocks();
  }

  async refreshBOHEligible(): Promise<void> {
    await this.loadBOHEligibleStocks();
  }

  async refreshWeeklyHighSignals(): Promise<void> {
    await this.loadWeeklyHighSignals();
  }

  async refreshGTTOrders(): Promise<void> {
    await this.loadGTTOrders();
  }

  async refreshAll(): Promise<void> {
    console.log('🔄 Refreshing all data...');
    await Promise.all([
      this.loadNifty200Stocks(),
      this.loadBOHEligibleStocks(),
      this.loadWeeklyHighSignals(),
      this.loadGTTOrders()
    ]);
    console.log('✅ All data refreshed');
  }

  // Service lifecycle
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('⚠️ Central Data Manager already initialized');
      return;
    }

    console.log('🚀 Initializing Central Data Manager...');
    
    // Load initial data
    await this.refreshAll();
    
    this.isInitialized = true;
    console.log('✅ Central Data Manager initialized successfully');
  }

  start(): void {
    if (this.isRunning) {
      console.log('⚠️ Central Data Manager already running');
      return;
    }

    console.log('🚀 Starting Central Data Manager background updates...');
    
    // Set up periodic updates
    this.intervals.set('nifty200', setInterval(() => {
      if (this.isMarketOpen() || !this.config.enableRealTimeUpdates) {
        this.loadNifty200Stocks();
      }
    }, this.config.nifty200UpdateInterval * 1000));

    this.intervals.set('bohEligible', setInterval(() => {
      this.loadBOHEligibleStocks();
    }, this.config.bohEligibleUpdateInterval * 1000));

    this.intervals.set('weeklyHighSignals', setInterval(() => {
      if (this.isMarketOpen() || !this.config.enableRealTimeUpdates) {
        this.loadWeeklyHighSignals();
      }
    }, this.config.weeklyHighSignalsUpdateInterval * 1000));

    this.intervals.set('gttOrders', setInterval(() => {
      this.loadGTTOrders();
    }, this.config.gttOrdersUpdateInterval * 1000));

    this.isRunning = true;
    console.log('✅ Central Data Manager background updates started');
  }

  stop(): void {
    if (!this.isRunning) {
      console.log('⚠️ Central Data Manager not running');
      return;
    }

    console.log('⏹️ Stopping Central Data Manager background updates...');
    
    this.intervals.forEach((interval, dataType) => {
      clearInterval(interval);
      console.log(`⏹️ Stopped ${dataType} updates`);
    });
    
    this.intervals.clear();
    this.isRunning = false;
    console.log('✅ Central Data Manager background updates stopped');
  }

  getStatus() {
    return {
      isInitialized: this.isInitialized,
      isRunning: this.isRunning,
      isMarketOpen: this.isMarketOpen(),
      config: this.config,
      cache: this.getCacheStatus()
    };
  }
}

// Export singleton instance
export const centralDataManager = new CentralDataManager();
