// Cache service for optimizing data fetching and reducing API calls

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

class CacheService {
  private cache = new Map<string, CacheEntry<any>>();
  private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes
  private readonly STOCK_DATA_TTL = 30 * 1000; // 30 seconds for stock data
  private readonly STOCK_NAMES_TTL = 24 * 60 * 60 * 1000; // 24 hours for stock names
  private readonly BROKER_DATA_TTL = 10 * 1000; // 10 seconds for broker data
  private readonly PORTFOLIO_DATA_TTL = 60 * 1000; // 1 minute for portfolio data

  // Get TTL based on data type
  private getTTL(key: string): number {
    if (key.includes('stock-name') || key.includes('names-map')) {
      return this.STOCK_NAMES_TTL;
    }
    if (key.includes('stock') || key.includes('nifty')) {
      return this.STOCK_DATA_TTL;
    }
    if (key.includes('broker') || key.includes('balance')) {
      return this.BROKER_DATA_TTL;
    }
    if (key.includes('portfolio') || key.includes('holdings')) {
      return this.PORTFOLIO_DATA_TTL;
    }
    return this.DEFAULT_TTL;
  }

  // Set cache entry
  set<T>(key: string, data: T, customTTL?: number): void {
    const ttl = customTTL || this.getTTL(key);
    const now = Date.now();
    
    this.cache.set(key, {
      data,
      timestamp: now,
      expiresAt: now + ttl
    });
  }

  // Get cache entry
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    // Check if expired
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return null;
    }

    return entry.data as T;
  }

  // Check if key exists and is valid
  has(key: string): boolean {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return false;
    }

    // Check if expired
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  // Clear specific key
  delete(key: string): void {
    this.cache.delete(key);
  }

  // Clear all cache
  clear(): void {
    this.cache.clear();
  }

  // Clear expired entries
  cleanup(): void {
    const now = Date.now();
    
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiresAt) {
        this.cache.delete(key);
      }
    }
  }

  // Get cache stats
  getStats(): {
    size: number;
    keys: string[];
    hitRate: number;
  } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
      hitRate: 0 // TODO: Implement hit rate tracking
    };
  }

  // Cached fetch wrapper
  async cachedFetch<T>(
    key: string,
    fetchFn: () => Promise<T>,
    customTTL?: number
  ): Promise<T> {
    // Try to get from cache first
    const cached = this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    // Fetch fresh data
    try {
      const data = await fetchFn();
      this.set(key, data, customTTL);
      return data;
    } catch (error) {
      // If fetch fails, try to return stale data if available
      const staleEntry = this.cache.get(key);
      if (staleEntry) {
        console.warn(`Using stale data for ${key} due to fetch error:`, error);
        return staleEntry.data as T;
      }
      throw error;
    }
  }

  // Prefetch data in background
  async prefetch<T>(
    key: string,
    fetchFn: () => Promise<T>,
    customTTL?: number
  ): Promise<void> {
    // Only prefetch if not already cached
    if (!this.has(key)) {
      try {
        const data = await fetchFn();
        this.set(key, data, customTTL);
      } catch (error) {
        console.warn(`Prefetch failed for ${key}:`, error);
      }
    }
  }

  // Batch fetch with caching
  async batchFetch<T>(
    requests: Array<{
      key: string;
      fetchFn: () => Promise<T>;
      ttl?: number;
    }>
  ): Promise<T[]> {
    const results: T[] = [];
    const fetchPromises: Promise<T>[] = [];

    for (const request of requests) {
      const cached = this.get<T>(request.key);
      if (cached !== null) {
        results.push(cached);
      } else {
        fetchPromises.push(
          request.fetchFn().then(data => {
            this.set(request.key, data, request.ttl);
            return data;
          })
        );
      }
    }

    // Wait for all fetches to complete
    const fetchedResults = await Promise.all(fetchPromises);
    results.push(...fetchedResults);

    return results;
  }

  // Invalidate cache by pattern
  invalidatePattern(pattern: string): void {
    const regex = new RegExp(pattern);
    
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        this.cache.delete(key);
      }
    }
  }

  // Auto cleanup interval
  startAutoCleanup(intervalMs: number = 5 * 60 * 1000): void {
    setInterval(() => {
      this.cleanup();
    }, intervalMs);
  }
}

// Create singleton instance
export const cacheService = new CacheService();

// Start auto cleanup
if (typeof window !== 'undefined') {
  cacheService.startAutoCleanup();
}

// Cache key generators
export const CacheKeys = {
  brokerBalance: (userId: string) => `broker-balance-${userId}`,
  fundAllocation: (userId: string, strategy: string) => `fund-allocation-${userId}-${strategy}`,
  portfolioSummary: (userId: string) => `portfolio-summary-${userId}`,
  niftyStocks: (batchIndex: number) => `nifty-stocks-batch-${batchIndex}`,
  stockQuote: (symbol: string) => `stock-quote-${symbol}`,
  stockName: (symbol: string) => `stock-name-${symbol}`,
  stockNamesMap: () => 'stock-names-map',
  stockPriceData: (symbol: string) => `stock-price-data-${symbol}`,
  stockSearch: (query: string) => `stock-search-${query}`,
  yahooQuotes: (symbols: string[]) => `yahoo-quotes-${symbols.sort().join(',')}`,
};
