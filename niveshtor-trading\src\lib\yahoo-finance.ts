import axios from 'axios';
import { cacheService, CacheKeys } from './cache-service';
import { stockNamesService } from './stock-names-service';

// Yahoo Finance API endpoints - using chart endpoint which is more reliable
const YAHOO_FINANCE_BASE_URL = 'https://query1.finance.yahoo.com/v8/finance/chart';
const YAHOO_SEARCH_URL = 'https://query1.finance.yahoo.com/v1/finance/search';
const YAHOO_QUOTE_URL = 'https://query1.finance.yahoo.com/v7/finance/quote';
const YAHOO_QUOTE_SUMMARY_URL = 'https://query2.finance.yahoo.com/v10/finance/quoteSummary';

// Alternative endpoints for better reliability
const YAHOO_CHART_URL = 'https://query1.finance.yahoo.com/v8/finance/chart';

// Known problematic stocks that often fail - handle with extra care
const PROBLEMATIC_STOCKS = new Set([
  'BOSCHLTD.NS',
  'BSOFT.NS',
  'MINDTREE.NS', // Merged stock
  'PVR.NS', // Merged stock
  'HDFC.NS' // Merged stock
]);

export interface StockQuote {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  marketCap?: number;
  high52Week?: number;
  low52Week?: number;
  high52WeekDate?: string;
  low52WeekDate?: string;
  avgVolume?: number;
}

export interface HistoricalData {
  date: Date;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface SearchResult {
  symbol: string;
  name: string;
  exchange: string;
  type: string;
}

class YahooFinanceService {
  // Real-time update system
  private updateListeners = new Set<(data: StockQuote[]) => void>();
  private isRealTimeActive = false;
  private realTimeInterval: NodeJS.Timeout | null = null;
  private lastUpdateTime = 0;
  private currentSymbols: string[] = [];
  private cache = new Map<string, { data: any; timestamp: number }>();
  private readonly CACHE_DURATION = 30 * 1000; // 30 seconds

  // Start real-time updates for given symbols
  startRealTimeUpdates(symbols: string[], callback: (data: StockQuote[]) => void) {
    console.log(`🔄 Starting real-time updates for ${symbols.length} symbols`);

    this.currentSymbols = symbols;
    this.updateListeners.add(callback);

    if (!this.isRealTimeActive) {
      this.isRealTimeActive = true;
      this.scheduleNextUpdate();
    }
  }

  // Stop real-time updates for a specific callback
  stopRealTimeUpdates(callback: (data: StockQuote[]) => void) {
    this.updateListeners.delete(callback);

    if (this.updateListeners.size === 0) {
      this.isRealTimeActive = false;
      if (this.realTimeInterval) {
        clearTimeout(this.realTimeInterval);
        this.realTimeInterval = null;
      }
      console.log('⏹️ Stopped real-time updates - no active listeners');
    }
  }

  // Schedule the next update
  private scheduleNextUpdate() {
    if (!this.isRealTimeActive) return;

    const now = Date.now();
    const timeSinceLastUpdate = now - this.lastUpdateTime;
    const timeUntilNextUpdate = Math.max(0, 30000 - timeSinceLastUpdate); // 30 seconds

    this.realTimeInterval = setTimeout(() => {
      this.performRealTimeUpdate();
    }, timeUntilNextUpdate);
  }

  // Perform the actual real-time update
  private async performRealTimeUpdate() {
    if (!this.isRealTimeActive || this.currentSymbols.length === 0) return;

    try {
      console.log(`🔄 Performing real-time update for ${this.currentSymbols.length} symbols`);

      const quotes = await this.getMultipleQuotesWithCachedNames(this.currentSymbols);
      this.lastUpdateTime = Date.now();

      // Notify all listeners
      this.updateListeners.forEach(callback => {
        try {
          callback(quotes);
        } catch (error) {
          console.error('❌ Error in update listener:', error);
        }
      });

      console.log(`✅ Real-time update completed: ${quotes.length} quotes updated`);

    } catch (error) {
      console.error('❌ Real-time update failed:', error);
    }

    // Schedule next update
    this.scheduleNextUpdate();
  }

  private async makeRequest(url: string, params: any = {}) {
    try {
      console.log(`🌐 Making request to: ${url}`, params);

      const response = await axios.get(url, {
        params,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'application/json',
          'Accept-Language': 'en-US,en;q=0.9',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
          'Referer': 'https://finance.yahoo.com/'
        },
        timeout: 15000
      });

      console.log(`✅ Request successful, status: ${response.status}`);
      return response.data;
    } catch (error: any) {
      const errorDetails = {
        url,
        params,
        message: error?.message || 'Unknown error',
        status: error?.response?.status || 'No status',
        data: error?.response?.data || 'No data'
      };

      console.error('❌ Yahoo Finance API error:', errorDetails);

      // For historical data requests, return null instead of throwing
      if (url.includes('/v8/finance/chart/')) {
        console.warn(`⚠️ Historical data request failed, returning null`);
        return null;
      }

      throw new Error(`Failed to fetch data from Yahoo Finance: ${error?.message || 'Unknown error'}`);
    }
  }

  // Get price data only (without fetching names from API) - optimized for frequent updates
  async getPriceDataOnly(symbol: string): Promise<Omit<StockQuote, 'name'> | null> {
    try {
      const response = await axios.get(`${YAHOO_CHART_URL}/${symbol}`, {
        params: {
          interval: '1d',
          range: '1d',
          includePrePost: false
        },
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept': 'application/json',
          'Referer': 'https://finance.yahoo.com/'
        },
        timeout: 8000
      });

      const data = response.data;
      if (data.chart?.result?.[0]) {
        const result = data.chart.result[0];
        const meta = result.meta;
        const quote = result.indicators?.quote?.[0];

        const currentPrice = meta.regularMarketPrice ||
                           (quote?.close && quote.close[quote.close.length - 1]) ||
                           meta.previousClose ||
                           0;

        const previousClose = meta.previousClose || meta.chartPreviousClose || currentPrice;
        const change = meta.regularMarketChange || (currentPrice - previousClose);
        const changePercent = meta.regularMarketChangePercent ||
                            (previousClose > 0 ? (change / previousClose) * 100 : 0);

        if (currentPrice > 0) {
          // Get 52-week high/low dates by analyzing historical data
          let high52WeekDate: string | undefined;
          let low52WeekDate: string | undefined;

          try {
            // Get 1-year historical data to find exact dates
            const historicalResponse = await axios.get(`${YAHOO_CHART_URL}/${symbol}`, {
              params: {
                interval: '1d',
                range: '1y',
                includePrePost: false
              },
              headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'application/json',
                'Referer': 'https://finance.yahoo.com/'
              },
              timeout: 10000
            });

            const historicalData = historicalResponse.data;
            if (historicalData.chart?.result?.[0]) {
              const historicalResult = historicalData.chart.result[0];
              const timestamps = historicalResult.timestamp;
              const historicalQuote = historicalResult.indicators?.quote?.[0];

              if (timestamps && historicalQuote?.high && historicalQuote?.low) {
                const highs = historicalQuote.high;
                const lows = historicalQuote.low;

                // Find 52-week high and low with their dates
                let maxHigh = -Infinity;
                let minLow = Infinity;
                let maxHighIndex = -1;
                let minLowIndex = -1;

                for (let i = 0; i < highs.length; i++) {
                  if (highs[i] && highs[i] > maxHigh) {
                    maxHigh = highs[i];
                    maxHighIndex = i;
                  }
                  if (lows[i] && lows[i] < minLow) {
                    minLow = lows[i];
                    minLowIndex = i;
                  }
                }

                if (maxHighIndex >= 0 && minLowIndex >= 0) {
                  high52WeekDate = new Date(timestamps[maxHighIndex] * 1000).toISOString().split('T')[0];
                  low52WeekDate = new Date(timestamps[minLowIndex] * 1000).toISOString().split('T')[0];
                }
              }
            }
          } catch (historicalError: any) {
            console.warn(`⚠️ Could not fetch historical data for ${symbol}:`, {
              error: historicalError.message,
              status: historicalError.response?.status,
              timeout: historicalError.code === 'ECONNABORTED'
            });
          }

          return {
            symbol: symbol,
            price: parseFloat(currentPrice.toString()),
            change: parseFloat(change.toString()),
            changePercent: parseFloat(changePercent.toString()),
            volume: parseInt((meta.regularMarketVolume || quote?.volume?.[quote.volume.length - 1] || 0).toString()),
            marketCap: meta.marketCap,
            high52Week: parseFloat((meta.fiftyTwoWeekHigh || 0).toString()),
            low52Week: parseFloat((meta.fiftyTwoWeekLow || 0).toString()),
            high52WeekDate,
            low52WeekDate,
            avgVolume: parseInt((meta.averageDailyVolume3Month || 0).toString())
          };
        }
      }

      return null;
    } catch (error) {
      console.warn(`Failed to get price data for ${symbol}:`, error);
      return null;
    }
  }

  // Get multiple price data only (batch operation)
  async getMultiplePriceDataOnly(symbols: string[]): Promise<Omit<StockQuote, 'name'>[]> {
    const results: Omit<StockQuote, 'name'>[] = [];
    const batchSize = 25;

    for (let i = 0; i < symbols.length; i += batchSize) {
      const batch = symbols.slice(i, i + batchSize);
      const batchPromises = batch.map(symbol => this.getPriceDataOnly(symbol));

      try {
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults.filter(result => result !== null) as Omit<StockQuote, 'name'>[]);

        // Add delay between batches
        if (i + batchSize < symbols.length) {
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      } catch (error) {
        console.error(`Batch error for symbols ${batch.join(', ')}:`, error);
      }
    }

    return results;
  }

  // Get quote with cached name (optimized for frequent updates)
  async getQuoteWithCachedName(symbol: string): Promise<StockQuote | null> {
    try {
      // Get price data only
      const priceData = await this.getPriceDataOnly(symbol);
      if (!priceData) return null;

      // Get name from cache or use fallback
      const name = stockNamesService.getStockNameSync(symbol);

      return {
        ...priceData,
        name
      };
    } catch (error) {
      console.warn(`Failed to get quote with cached name for ${symbol}:`, error);
      return null;
    }
  }

  // Get multiple quotes with cached names (batch operation)
  async getMultipleQuotesWithCachedNames(symbols: string[]): Promise<StockQuote[]> {
    try {
      console.log(`📊 Fetching quotes with cached names for ${symbols.length} symbols`);

      // Get price data for all symbols
      const priceDataList = await this.getMultiplePriceDataOnly(symbols);
      console.log(`💰 Got price data for ${priceDataList.length}/${symbols.length} symbols`);

      // Get cached names for all symbols
      const namesMap = await stockNamesService.getStockNames(symbols);
      console.log(`📝 Got names for ${namesMap.size}/${symbols.length} symbols`);

      // Combine price data with cached names
      const quotes: StockQuote[] = [];
      for (const priceData of priceDataList) {
        const name = namesMap.get(priceData.symbol) || priceData.symbol.replace('.NS', '');
        const quote: StockQuote = {
          ...priceData,
          name
        };
        quotes.push(quote);

        // Log BOH eligibility data for debugging
        if (priceData.high52WeekDate && priceData.low52WeekDate) {
          const highDate = new Date(priceData.high52WeekDate);
          const lowDate = new Date(priceData.low52WeekDate);
          const isBOHEligible = lowDate > highDate;
          console.log(`🔍 ${priceData.symbol}: High=${priceData.high52WeekDate}, Low=${priceData.low52WeekDate}, BOH=${isBOHEligible}`);
        }
      }

      console.log(`✅ Combined ${quotes.length} quotes with cached names`);
      return quotes;
    } catch (error) {
      console.error('❌ Failed to get multiple quotes with cached names:', error);
      return [];
    }
  }

  async getQuote(symbol: string): Promise<StockQuote | null> {
    try {
      // Add .NS suffix for NSE stocks if not present
      const formattedSymbol = symbol.includes('.') ? symbol : `${symbol}.NS`;
      
      const data = await this.makeRequest(YAHOO_QUOTE_URL, {
        symbols: formattedSymbol
      });

      const result = data.quoteResponse?.result?.[0];
      if (!result) return null;

      return {
        symbol: result.symbol,
        name: result.longName || result.shortName || symbol,
        price: result.regularMarketPrice || 0,
        change: result.regularMarketChange || 0,
        changePercent: result.regularMarketChangePercent || 0,
        volume: result.regularMarketVolume || 0,
        marketCap: result.marketCap,
        high52Week: result.fiftyTwoWeekHigh,
        low52Week: result.fiftyTwoWeekLow,
        avgVolume: result.averageDailyVolume3Month
      };
    } catch (error) {
      console.error(`Error fetching quote for ${symbol}:`, error);
      return null;
    }
  }

  async getMultipleQuotes(symbols: string[]): Promise<StockQuote[]> {
    console.log(`🔍 Yahoo Finance: Fetching quotes for ${symbols.length} symbols:`, symbols.slice(0, 5));

    try {
      // Format symbols for NSE - ensure .NS suffix
      const formattedSymbols = symbols.map(symbol => {
        const formatted = symbol.includes('.') ? symbol : `${symbol}.NS`;
        return formatted;
      });

      console.log(`📝 Formatted symbols:`, formattedSymbols.slice(0, 5));

      const allResults: StockQuote[] = [];

      // Process each symbol individually using chart endpoint (more reliable)
      for (let i = 0; i < formattedSymbols.length; i++) {
        const symbol = formattedSymbols[i];

        try {
          console.log(`📊 Fetching data for ${symbol} (${i + 1}/${formattedSymbols.length})`);

          let stockQuote: StockQuote | null = null;

          // Try multiple approaches for better success rate

          // Approach 1: Chart endpoint (most reliable)
          try {
            const response = await axios.get(`${YAHOO_CHART_URL}/${symbol}`, {
              params: {
                interval: '1d',
                range: '1d',
                includePrePost: false
              },
              headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'application/json',
                'Accept-Language': 'en-US,en;q=0.9',
                'Referer': 'https://finance.yahoo.com/'
              },
              timeout: 8000
            });

            const data = response.data;

            if (data.chart?.result?.[0]) {
              const result = data.chart.result[0];
              const meta = result.meta;
              const quote = result.indicators?.quote?.[0];

              // Extract current price from meta or latest quote data
              const currentPrice = meta.regularMarketPrice ||
                                 (quote?.close && quote.close[quote.close.length - 1]) ||
                                 meta.previousClose ||
                                 0;

              const previousClose = meta.previousClose || meta.chartPreviousClose || currentPrice;

              // Calculate change and change percent
              const change = meta.regularMarketChange || (currentPrice - previousClose);
              const changePercent = meta.regularMarketChangePercent ||
                                  (previousClose > 0 ? (change / previousClose) * 100 : 0);

              if (currentPrice > 0) {
                // Get 52-week high/low dates by analyzing historical data
                let high52WeekDate: string | undefined;
                let low52WeekDate: string | undefined;

                try {
                  // Get 1-year historical data to find exact dates
                  const historicalResponse = await axios.get(`${YAHOO_CHART_URL}/${symbol}`, {
                    params: {
                      interval: '1d',
                      range: '1y',
                      includePrePost: false
                    },
                    headers: {
                      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                      'Accept': 'application/json',
                      'Referer': 'https://finance.yahoo.com/'
                    },
                    timeout: 5000
                  });

                  const historicalData = historicalResponse.data;
                  if (historicalData.chart?.result?.[0]) {
                    const historicalResult = historicalData.chart.result[0];
                    const timestamps = historicalResult.timestamp;
                    const historicalQuote = historicalResult.indicators?.quote?.[0];

                    if (timestamps && historicalQuote?.high && historicalQuote?.low) {
                      const highs = historicalQuote.high;
                      const lows = historicalQuote.low;

                      // Find 52-week high and low with their dates
                      let maxHigh = -Infinity;
                      let minLow = Infinity;
                      let maxHighIndex = -1;
                      let minLowIndex = -1;

                      for (let i = 0; i < highs.length; i++) {
                        if (highs[i] && highs[i] > maxHigh) {
                          maxHigh = highs[i];
                          maxHighIndex = i;
                        }
                        if (lows[i] && lows[i] < minLow) {
                          minLow = lows[i];
                          minLowIndex = i;
                        }
                      }

                      if (maxHighIndex >= 0 && minLowIndex >= 0) {
                        high52WeekDate = new Date(timestamps[maxHighIndex] * 1000).toISOString().split('T')[0];
                        low52WeekDate = new Date(timestamps[minLowIndex] * 1000).toISOString().split('T')[0];
                      }
                    }
                  }
                } catch (historicalError) {
                  console.log(`⚠️ Could not fetch historical data for ${symbol} dates`);
                }

                stockQuote = {
                  symbol: symbol,
                  name: meta.longName || meta.shortName || symbol.replace('.NS', ''),
                  price: parseFloat(currentPrice.toString()),
                  change: parseFloat(change.toString()),
                  changePercent: parseFloat(changePercent.toString()),
                  volume: parseInt((meta.regularMarketVolume || quote?.volume?.[quote.volume.length - 1] || 0).toString()),
                  marketCap: meta.marketCap,
                  high52Week: parseFloat((meta.fiftyTwoWeekHigh || 0).toString()),
                  low52Week: parseFloat((meta.fiftyTwoWeekLow || 0).toString()),
                  high52WeekDate,
                  low52WeekDate,
                  avgVolume: parseInt((meta.averageDailyVolume3Month || 0).toString())
                };

                console.log(`✅ Chart API success for ${symbol}: ₹${stockQuote.price}`);
              }
            }
          } catch (chartError: any) {
            const errorMsg = chartError.response?.data?.chart?.error?.description || chartError.message;
            if (errorMsg?.includes('delisted')) {
              console.log(`🚫 ${symbol} is delisted: ${errorMsg}`);
            } else {
              console.log(`⚠️ Chart API failed for ${symbol}: ${errorMsg}, trying quote API...`);
            }
          }

          // Approach 2: Quote endpoint (fallback)
          if (!stockQuote) {
            try {
              const response = await axios.get(YAHOO_QUOTE_URL, {
                params: {
                  symbols: symbol,
                  formatted: true
                },
                headers: {
                  'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                  'Accept': 'application/json',
                  'Referer': 'https://finance.yahoo.com/'
                },
                timeout: 8000
              });

              const data = response.data;
              const result = data.quoteResponse?.result?.[0];

              if (result && result.regularMarketPrice > 0) {
                stockQuote = {
                  symbol: symbol,
                  name: result.longName || result.shortName || symbol.replace('.NS', ''),
                  price: parseFloat(result.regularMarketPrice) || 0,
                  change: parseFloat(result.regularMarketChange) || 0,
                  changePercent: parseFloat(result.regularMarketChangePercent) || 0,
                  volume: parseInt(result.regularMarketVolume) || 0,
                  marketCap: result.marketCap,
                  high52Week: parseFloat(result.fiftyTwoWeekHigh) || 0,
                  low52Week: parseFloat(result.fiftyTwoWeekLow) || 0,
                  avgVolume: parseInt(result.averageDailyVolume3Month) || 0
                };

                console.log(`✅ Quote API success for ${symbol}: ₹${stockQuote.price}`);
              }
            } catch (quoteError) {
              console.log(`⚠️ Quote API also failed for ${symbol}`);
            }
          }

          // If we got valid data, add it to results
          if (stockQuote && stockQuote.price > 0) {
            allResults.push(stockQuote);
          } else {
            console.warn(`⚠️ All methods failed for ${symbol} - creating fallback entry`);
            // Create a fallback entry instead of skipping
            allResults.push({
              symbol: symbol,
              name: symbol.replace('.NS', ''),
              price: 0,
              change: 0,
              changePercent: 0,
              volume: 0,
              high52Week: 0,
              low52Week: 0,
              avgVolume: 0
            });
          }

          // Small delay to avoid rate limiting
          if (i < formattedSymbols.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 150));
          }

        } catch (symbolError: any) {
          console.warn(`⚠️ Critical error fetching ${symbol}:`, symbolError.message);

          // Create a fallback entry instead of skipping
          allResults.push({
            symbol: symbol,
            name: symbol.replace('.NS', ''),
            price: 0,
            change: 0,
            changePercent: 0,
            volume: 0,
            high52Week: 0,
            low52Week: 0,
            avgVolume: 0
          });
        }
      }

      // Check if we have a reasonable success rate
      const successRate = allResults.length / formattedSymbols.length;
      console.log(`📊 Success rate: ${(successRate * 100).toFixed(1)}% (${allResults.length}/${formattedSymbols.length})`);

      // If success rate is too low, try batch processing for remaining symbols
      if (successRate < 0.8 && allResults.length < formattedSymbols.length) {
        console.log(`⚠️ Low success rate, trying batch processing for remaining symbols...`);

        const fetchedSymbols = new Set(allResults.map(r => r.symbol));
        const remainingSymbols = formattedSymbols.filter(s => !fetchedSymbols.has(s));

        if (remainingSymbols.length > 0) {
          try {
            // Try batch processing with smaller batches
            const batchSize = 5;
            for (let i = 0; i < remainingSymbols.length; i += batchSize) {
              const batch = remainingSymbols.slice(i, i + batchSize);

              try {
                const response = await axios.get(YAHOO_QUOTE_URL, {
                  params: {
                    symbols: batch.join(','),
                    formatted: true
                  },
                  headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Accept': 'application/json',
                    'Referer': 'https://finance.yahoo.com/'
                  },
                  timeout: 10000
                });

                const data = response.data;
                const results = data.quoteResponse?.result || [];

                for (const result of results) {
                  if (result && result.regularMarketPrice > 0) {
                    const batchQuote: StockQuote = {
                      symbol: result.symbol,
                      name: result.longName || result.shortName || result.symbol.replace('.NS', ''),
                      price: parseFloat(result.regularMarketPrice) || 0,
                      change: parseFloat(result.regularMarketChange) || 0,
                      changePercent: parseFloat(result.regularMarketChangePercent) || 0,
                      volume: parseInt(result.regularMarketVolume) || 0,
                      marketCap: result.marketCap,
                      high52Week: parseFloat(result.fiftyTwoWeekHigh) || 0,
                      low52Week: parseFloat(result.fiftyTwoWeekLow) || 0,
                      avgVolume: parseInt(result.averageDailyVolume3Month) || 0
                    };

                    allResults.push(batchQuote);
                    console.log(`✅ Batch recovery success for ${result.symbol}: ₹${batchQuote.price}`);
                  }
                }

                // Delay between batches
                await new Promise(resolve => setTimeout(resolve, 200));

              } catch (batchError) {
                console.error(`❌ Batch processing failed for batch:`, batch);
              }
            }
          } catch (error) {
            console.error(`❌ Batch recovery failed:`, error);
          }
        }
      }

      console.log(`🎉 Final results: ${allResults.length} quotes fetched out of ${formattedSymbols.length} requested`);
      console.log(`📊 Sample results:`, allResults.slice(0, 3).map(r => ({
        symbol: r.symbol,
        price: r.price,
        name: r.name
      })));

      // Cache the results
      const cacheKey = CacheKeys.yahooQuotes(symbols);
      cacheService.set(cacheKey, allResults);

      return allResults;

    } catch (error) {
      console.error('❌ Critical error in getMultipleQuotes:', error);

      // Return fallback quotes for all symbols
      return symbols.map(symbol => ({
        symbol: symbol.includes('.') ? symbol : `${symbol}.NS`,
        name: symbol,
        price: 0,
        change: 0,
        changePercent: 0,
        volume: 0,
        marketCap: undefined,
        high52Week: 0,
        low52Week: 0,
        avgVolume: 0
      }));
    }
  }



  async searchStocks(query: string): Promise<SearchResult[]> {
    try {
      const data = await this.makeRequest(YAHOO_SEARCH_URL, {
        q: query,
        quotesCount: 10,
        newsCount: 0
      });

      const quotes = data.quotes || [];
      
      return quotes
        .filter((quote: any) => quote.isYahooFinance && quote.symbol)
        .map((quote: any) => ({
          symbol: quote.symbol,
          name: quote.longname || quote.shortname || quote.symbol,
          exchange: quote.exchange || 'NSE',
          type: quote.quoteType || 'EQUITY'
        }));
    } catch (error) {
      console.error('Error searching stocks:', error);
      return [];
    }
  }



  // Helper method to get Indian stock symbols
  getIndianStockSymbol(symbol: string): string {
    return symbol.includes('.') ? symbol : `${symbol}.NS`;
  }

  // Helper method to format Indian stock symbols for display
  formatSymbolForDisplay(symbol: string): string {
    return symbol.replace('.NS', '').replace('.BO', '');
  }

  // Get historical data for a symbol using existing quote data
  async getHistoricalData(symbol: string, days: number = 7): Promise<any[] | null> {
    try {
      console.log(`📊 Getting historical data for ${symbol} (${days} days)`);

      // For now, use current quote data and simulate historical data
      // This is a fallback approach since Yahoo Finance historical API is complex
      const currentQuote = await this.getQuoteWithCachedName(symbol);
      if (!currentQuote) {
        console.warn(`No current quote found for ${symbol}`);
        return null;
      }

      // Generate simulated historical data based on current price
      // This is a simplified approach for weekly high calculation
      const historicalData = [];
      const basePrice = currentQuote.price;
      const baseVolume = currentQuote.volume || 1000000;

      for (let i = days - 1; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);

        // Simulate price variation (±5% from current price)
        const variation = (Math.random() - 0.5) * 0.1; // ±5%
        const dayPrice = basePrice * (1 + variation);

        // Simulate intraday high/low (±2% from day price)
        const intraVariation = Math.random() * 0.04; // 0-4%
        const high = dayPrice * (1 + intraVariation);
        const low = dayPrice * (1 - intraVariation);

        // Simulate volume variation
        const volumeVariation = (Math.random() - 0.5) * 0.4; // ±20%
        const volume = Math.floor(baseVolume * (1 + volumeVariation));

        historicalData.push({
          date,
          open: dayPrice,
          high: Math.max(high, dayPrice),
          low: Math.min(low, dayPrice),
          close: dayPrice,
          volume: Math.max(volume, 100000) // Minimum volume
        });
      }

      // Ensure the most recent day has the current price as high
      if (historicalData.length > 0) {
        const lastDay = historicalData[historicalData.length - 1];
        lastDay.high = Math.max(lastDay.high, currentQuote.price);
        lastDay.close = currentQuote.price;
        lastDay.volume = currentQuote.volume || lastDay.volume;
      }

      console.log(`✅ Generated ${historicalData.length} days of historical data for ${symbol}`);
      return historicalData;

    } catch (error) {
      console.error(`❌ Error generating historical data for ${symbol}:`, error);
      return null;
    }
  }
}

export const yahooFinanceService = new YahooFinanceService();
