import { NextRequest, NextResponse } from 'next/server';
import { centralDataManager } from '@/lib/central-data-manager';

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing complete background data loading system...');

    const testResults = {
      timestamp: new Date().toISOString(),
      tests: [] as any[]
    };

    // Test 1: Central Data Manager Status
    console.log('📊 Test 1: Central Data Manager Status...');
    const status = centralDataManager.getStatus();
    
    testResults.tests.push({
      name: 'Central Data Manager Status',
      status: status.isInitialized && status.isRunning ? 'PASS' : 'FAIL',
      data: {
        isInitialized: status.isInitialized,
        isRunning: status.isRunning,
        isMarketOpen: status.isMarketOpen,
        config: status.config
      }
    });

    // Test 2: Data Availability and Freshness
    console.log('📈 Test 2: Data Availability and Freshness...');
    const cacheStatus = centralDataManager.getCacheStatus();
    
    const dataFreshness = {
      nifty200Fresh: cacheStatus.lastUpdated.nifty200 ? 
        Date.now() - new Date(cacheStatus.lastUpdated.nifty200).getTime() < 60000 : false,
      bohEligibleFresh: cacheStatus.lastUpdated.bohEligible ? 
        Date.now() - new Date(cacheStatus.lastUpdated.bohEligible).getTime() < 120000 : false,
      weeklyHighSignalsFresh: cacheStatus.lastUpdated.weeklyHighSignals ? 
        Date.now() - new Date(cacheStatus.lastUpdated.weeklyHighSignals).getTime() < 600000 : false,
      gttOrdersFresh: cacheStatus.lastUpdated.gttOrders ? 
        Date.now() - new Date(cacheStatus.lastUpdated.gttOrders).getTime() < 60000 : false
    };

    testResults.tests.push({
      name: 'Data Availability and Freshness',
      status: cacheStatus.nifty200Count > 0 && cacheStatus.bohEligibleCount > 0 ? 'PASS' : 'FAIL',
      data: {
        cacheStatus,
        dataFreshness,
        totalDataPoints: cacheStatus.nifty200Count + cacheStatus.bohEligibleCount + 
                        cacheStatus.weeklyHighSignalsCount + cacheStatus.gttOrdersCount
      }
    });

    // Test 3: API Response Times
    console.log('⚡ Test 3: API Response Times...');
    const apiTests = [];
    
    const endpoints = [
      { name: 'nifty200', endpoint: 'nifty200' },
      { name: 'bohEligible', endpoint: 'boh-eligible' },
      { name: 'weeklyHighSignals', endpoint: 'weekly-high-signals' },
      { name: 'gttOrders', endpoint: 'gtt-orders' }
    ];

    for (const { name, endpoint } of endpoints) {
      const startTime = performance.now();
      try {
        const requestUrl = new URL(request.url);
        const baseUrl = `${requestUrl.protocol}//${requestUrl.host}`;
        
        const response = await fetch(`${baseUrl}/api/data-manager?action=${endpoint}`);
        const endTime = performance.now();
        const responseTime = endTime - startTime;
        
        const result = await response.json();
        
        apiTests.push({
          endpoint: name,
          responseTime: Math.round(responseTime),
          isInstant: responseTime < 200,
          dataCount: result.data?.length || 0,
          status: response.ok ? 'SUCCESS' : 'ERROR'
        });
      } catch (error) {
        apiTests.push({
          endpoint: name,
          responseTime: -1,
          isInstant: false,
          dataCount: 0,
          status: 'ERROR',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    const averageResponseTime = apiTests.reduce((sum, test) => sum + (test.responseTime > 0 ? test.responseTime : 0), 0) / 
                               apiTests.filter(test => test.responseTime > 0).length;
    const instantResponses = apiTests.filter(test => test.isInstant).length;

    testResults.tests.push({
      name: 'API Response Times',
      status: averageResponseTime < 200 && instantResponses >= 3 ? 'PASS' : 'FAIL',
      data: {
        averageResponseTime: Math.round(averageResponseTime),
        instantResponses,
        totalEndpoints: apiTests.length,
        endpointTests: apiTests
      }
    });

    // Test 4: Real-time Updates
    console.log('🔄 Test 4: Real-time Updates...');
    const beforeRefresh = centralDataManager.getCacheStatus();
    
    // Trigger a refresh
    await centralDataManager.refreshNifty200();
    
    const afterRefresh = centralDataManager.getCacheStatus();
    const wasUpdated = afterRefresh.lastUpdated.nifty200 !== beforeRefresh.lastUpdated.nifty200;

    testResults.tests.push({
      name: 'Real-time Updates',
      status: wasUpdated ? 'PASS' : 'FAIL',
      data: {
        wasUpdated,
        beforeUpdate: beforeRefresh.lastUpdated.nifty200,
        afterUpdate: afterRefresh.lastUpdated.nifty200,
        dataCountChanged: afterRefresh.nifty200Count !== beforeRefresh.nifty200Count
      }
    });

    // Test 5: Cross-Page Data Consistency
    console.log('🔗 Test 5: Cross-Page Data Consistency...');
    const nifty200Data = centralDataManager.getNifty200Stocks();
    const bohEligibleData = centralDataManager.getBOHEligibleStocks();
    const weeklyHighSignals = centralDataManager.getWeeklyHighSignals();
    const gttOrders = centralDataManager.getGTTOrders();

    // Check if BOH eligible stocks are subset of Nifty 200
    const bohSymbols = new Set(bohEligibleData.map(stock => stock.symbol));
    const niftySymbols = new Set(nifty200Data.map(stock => stock.symbol));
    const bohIsSubsetOfNifty = [...bohSymbols].every(symbol => niftySymbols.has(symbol));

    // Check if signal stocks exist in BOH eligible
    const signalSymbols = new Set(weeklyHighSignals.map(signal => signal.symbol));
    const signalsInBOH = [...signalSymbols].every(symbol => bohSymbols.has(symbol));

    testResults.tests.push({
      name: 'Cross-Page Data Consistency',
      status: bohIsSubsetOfNifty && signalsInBOH ? 'PASS' : 'FAIL',
      data: {
        bohIsSubsetOfNifty,
        signalsInBOH,
        nifty200Count: nifty200Data.length,
        bohEligibleCount: bohEligibleData.length,
        weeklyHighSignalsCount: weeklyHighSignals.length,
        gttOrdersCount: gttOrders.length
      }
    });

    // Calculate overall results
    const passedTests = testResults.tests.filter(t => t.status === 'PASS').length;
    const totalTests = testResults.tests.length;
    const overallStatus = passedTests === totalTests ? 'ALL_PASS' : 
                         passedTests >= totalTests * 0.8 ? 'MOSTLY_PASS' : 'FAIL';

    const summary = {
      overallStatus,
      passedTests,
      totalTests,
      successRate: `${Math.round((passedTests / totalTests) * 100)}%`,
      backgroundLoadingWorking: overallStatus !== 'FAIL',
      instantNavigationReady: averageResponseTime < 200,
      realTimeUpdatesWorking: testResults.tests.find(t => t.name === 'Real-time Updates')?.status === 'PASS',
      dataConsistencyMaintained: testResults.tests.find(t => t.name === 'Cross-Page Data Consistency')?.status === 'PASS'
    };

    console.log(`🎯 Background system test: ${summary.successRate} success rate`);

    return NextResponse.json({
      success: true,
      message: 'Background data loading system test completed',
      summary,
      testResults
    });

  } catch (error) {
    console.error('❌ Background system test failed:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'System test failed',
        message: 'Background system test failed'
      },
      { status: 500 }
    );
  }
}
