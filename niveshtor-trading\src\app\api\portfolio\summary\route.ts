import { NextRequest, NextResponse } from 'next/server';
import { portfolioService } from '@/lib/portfolio-service';

export async function GET(request: NextRequest) {
  try {
    const portfolioSummary = await portfolioService.getPortfolioSummary();
    
    return NextResponse.json({
      success: true,
      data: portfolioSummary
    });

  } catch (error) {
    console.error('Error fetching portfolio summary:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch portfolio summary'
    }, { status: 500 });
  }
}
