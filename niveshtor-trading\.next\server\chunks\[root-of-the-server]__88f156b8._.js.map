{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Niveshtor/niveshtor-trading/src/app/api/fund-allocation/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\n// In-memory storage for fund allocations (replace with database in production)\nlet fundAllocations: { [key: string]: any } = {};\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const userId = searchParams.get('userId') || 'default-user';\n    const strategy = searchParams.get('strategy') || 'DARVAS_BOX';\n\n    const key = `${userId}-${strategy}`;\n\n    // Check if allocation exists in memory\n    let fundAllocation = fundAllocations[key];\n\n    if (!fundAllocation) {\n      // Create default allocation if none exists\n      fundAllocation = {\n        id: `allocation-${key}-${Date.now()}`,\n        userId,\n        strategyName: strategy,\n        totalAllocatedAmount: 50000,\n        maxPerStock: 10000,\n        maxPerTrade: 2000,\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n        stockAllocations: [\n          {\n            id: 'stock-reliance-1',\n            symbol: 'RELIANCE',\n            allocatedAmount: 8000,\n            usedAmount: 4000,\n            tradesCount: 2,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n          },\n          {\n            id: 'stock-tcs-1',\n            symbol: 'TCS',\n            allocatedAmount: 6000,\n            usedAmount: 2000,\n            tradesCount: 1,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n          }\n        ]\n      };\n\n      // Store in memory\n      fundAllocations[key] = fundAllocation;\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: fundAllocation\n    });\n\n  } catch (error) {\n    console.error('Error fetching fund allocation:', error);\n    return NextResponse.json({\n      success: false,\n      error: 'Failed to fetch fund allocation'\n    }, { status: 500 });\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const {\n      userId = 'default-user',\n      strategyName = 'DARVAS_BOX',\n      totalAllocatedAmount,\n      maxPerStock,\n      maxPerTrade\n    } = body;\n\n    const key = `${userId}-${strategyName}`;\n\n    // Get existing allocation or create new one\n    let fundAllocation = fundAllocations[key];\n\n    if (!fundAllocation) {\n      fundAllocation = {\n        id: `allocation-${key}-${Date.now()}`,\n        userId,\n        strategyName,\n        totalAllocatedAmount: 50000,\n        maxPerStock: 10000,\n        maxPerTrade: 2000,\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n        stockAllocations: []\n      };\n    }\n\n    // Update allocation with new values\n    fundAllocation.totalAllocatedAmount = parseFloat(totalAllocatedAmount) || fundAllocation.totalAllocatedAmount;\n    fundAllocation.maxPerStock = parseFloat(maxPerStock) || fundAllocation.maxPerStock;\n    fundAllocation.maxPerTrade = parseFloat(maxPerTrade) || fundAllocation.maxPerTrade;\n    fundAllocation.updatedAt = new Date().toISOString();\n\n    // Store updated allocation\n    fundAllocations[key] = fundAllocation;\n\n    return NextResponse.json({\n      success: true,\n      message: 'Fund allocation updated successfully',\n      data: fundAllocation\n    });\n\n  } catch (error) {\n    console.error('Error updating fund allocation:', error);\n    return NextResponse.json({\n      success: false,\n      error: 'Failed to update fund allocation'\n    }, { status: 500 });\n  }\n}\n\n// Update stock-wise allocation\nexport async function PUT(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const {\n      userId = 'default-user',\n      strategyName = 'DARVAS_BOX',\n      symbol,\n      allocatedAmount\n    } = body;\n\n    const key = `${userId}-${strategyName}`;\n\n    // Get fund allocation\n    let fundAllocation = fundAllocations[key];\n\n    if (!fundAllocation) {\n      return NextResponse.json({\n        success: false,\n        error: 'Fund allocation not found'\n      }, { status: 404 });\n    }\n\n    // Find existing stock allocation or create new one\n    let stockAllocation = fundAllocation.stockAllocations.find((stock: any) => stock.symbol === symbol);\n\n    if (stockAllocation) {\n      // Update existing allocation\n      stockAllocation.allocatedAmount = parseFloat(allocatedAmount);\n      stockAllocation.updatedAt = new Date().toISOString();\n    } else {\n      // Create new stock allocation\n      stockAllocation = {\n        id: `stock-${symbol}-${Date.now()}`,\n        symbol,\n        allocatedAmount: parseFloat(allocatedAmount),\n        usedAmount: 0,\n        tradesCount: 0,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      };\n\n      fundAllocation.stockAllocations.push(stockAllocation);\n    }\n\n    // Update fund allocation timestamp\n    fundAllocation.updatedAt = new Date().toISOString();\n\n    // Store updated allocation\n    fundAllocations[key] = fundAllocation;\n\n    return NextResponse.json({\n      success: true,\n      message: 'Stock allocation updated successfully',\n      data: stockAllocation\n    });\n\n  } catch (error) {\n    console.error('Error updating stock allocation:', error);\n    return NextResponse.json({\n      success: false,\n      error: 'Failed to update stock allocation'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,+EAA+E;AAC/E,IAAI,kBAA0C,CAAC;AAExC,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,WAAW,aAAa,GAAG,CAAC,eAAe;QAEjD,MAAM,MAAM,GAAG,OAAO,CAAC,EAAE,UAAU;QAEnC,uCAAuC;QACvC,IAAI,iBAAiB,eAAe,CAAC,IAAI;QAEzC,IAAI,CAAC,gBAAgB;YACnB,2CAA2C;YAC3C,iBAAiB;gBACf,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,EAAE,KAAK,GAAG,IAAI;gBACrC;gBACA,cAAc;gBACd,sBAAsB;gBACtB,aAAa;gBACb,aAAa;gBACb,UAAU;gBACV,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;gBACjC,kBAAkB;oBAChB;wBACE,IAAI;wBACJ,QAAQ;wBACR,iBAAiB;wBACjB,YAAY;wBACZ,aAAa;wBACb,WAAW,IAAI,OAAO,WAAW;wBACjC,WAAW,IAAI,OAAO,WAAW;oBACnC;oBACA;wBACE,IAAI;wBACJ,QAAQ;wBACR,iBAAiB;wBACjB,YAAY;wBACZ,aAAa;wBACb,WAAW,IAAI,OAAO,WAAW;wBACjC,WAAW,IAAI,OAAO,WAAW;oBACnC;iBACD;YACH;YAEA,kBAAkB;YAClB,eAAe,CAAC,IAAI,GAAG;QACzB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,SAAS,cAAc,EACvB,eAAe,YAAY,EAC3B,oBAAoB,EACpB,WAAW,EACX,WAAW,EACZ,GAAG;QAEJ,MAAM,MAAM,GAAG,OAAO,CAAC,EAAE,cAAc;QAEvC,4CAA4C;QAC5C,IAAI,iBAAiB,eAAe,CAAC,IAAI;QAEzC,IAAI,CAAC,gBAAgB;YACnB,iBAAiB;gBACf,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,EAAE,KAAK,GAAG,IAAI;gBACrC;gBACA;gBACA,sBAAsB;gBACtB,aAAa;gBACb,aAAa;gBACb,UAAU;gBACV,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;gBACjC,kBAAkB,EAAE;YACtB;QACF;QAEA,oCAAoC;QACpC,eAAe,oBAAoB,GAAG,WAAW,yBAAyB,eAAe,oBAAoB;QAC7G,eAAe,WAAW,GAAG,WAAW,gBAAgB,eAAe,WAAW;QAClF,eAAe,WAAW,GAAG,WAAW,gBAAgB,eAAe,WAAW;QAClF,eAAe,SAAS,GAAG,IAAI,OAAO,WAAW;QAEjD,2BAA2B;QAC3B,eAAe,CAAC,IAAI,GAAG;QAEvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,SAAS,cAAc,EACvB,eAAe,YAAY,EAC3B,MAAM,EACN,eAAe,EAChB,GAAG;QAEJ,MAAM,MAAM,GAAG,OAAO,CAAC,EAAE,cAAc;QAEvC,sBAAsB;QACtB,IAAI,iBAAiB,eAAe,CAAC,IAAI;QAEzC,IAAI,CAAC,gBAAgB;YACnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,mDAAmD;QACnD,IAAI,kBAAkB,eAAe,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAe,MAAM,MAAM,KAAK;QAE5F,IAAI,iBAAiB;YACnB,6BAA6B;YAC7B,gBAAgB,eAAe,GAAG,WAAW;YAC7C,gBAAgB,SAAS,GAAG,IAAI,OAAO,WAAW;QACpD,OAAO;YACL,8BAA8B;YAC9B,kBAAkB;gBAChB,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE,KAAK,GAAG,IAAI;gBACnC;gBACA,iBAAiB,WAAW;gBAC5B,YAAY;gBACZ,aAAa;gBACb,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,eAAe,gBAAgB,CAAC,IAAI,CAAC;QACvC;QAEA,mCAAmC;QACnC,eAAe,SAAS,GAAG,IAAI,OAAO,WAAW;QAEjD,2BAA2B;QAC3B,eAAe,CAAC,IAAI,GAAG;QAEvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}