// Background data service for preloading and auto-updating stock data
import { stockNamesService } from './stock-names-service';
import { cacheService, CacheKeys } from './cache-service';
import { NIFTY_200_SYMBOLS, getYahooSymbol } from './nifty-stocks';
import { yahooFinanceService } from './yahoo-finance';

interface BackgroundUpdateListener {
  onPriceUpdate: (data: any[]) => void;
  onNamesUpdate: (namesMap: Map<string, string>) => void;
  onError: (error: Error) => void;
}

interface RealTimeUpdateListener {
  onRealTimeUpdate: (quotes: any[]) => void;
  onError: (error: Error) => void;
}

class BackgroundDataService {
  private listeners: BackgroundUpdateListener[] = [];
  private realTimeListeners: RealTimeUpdateListener[] = [];
  private priceUpdateInterval: NodeJS.Timeout | null = null;
  private nameRefreshInterval: NodeJS.Timeout | null = null;
  private isInitialized = false;
  private isUpdating = false;
  private realTimeActive = false;
  
  // Configuration
  private readonly PRICE_UPDATE_INTERVAL = 30 * 1000; // 30 seconds
  private readonly NAME_REFRESH_INTERVAL = 23 * 60 * 60 * 1000; // 23 hours (refresh before 24h expiry)
  private readonly BATCH_SIZE = 25;
  private readonly BATCH_DELAY = 200; // ms between batches

  // Initialize background service
  async initialize(): Promise<void> {
    if (this.isInitialized) return;
    
    console.log('🚀 Initializing background data service...');
    
    try {
      // Step 1: Preload all stock names immediately
      await this.preloadAllStockNames();
      
      // Step 2: Start automatic price updates
      this.startPriceUpdates();
      
      // Step 3: Start automatic name refresh (before 24h expiry)
      this.startNameRefresh();
      
      this.isInitialized = true;
      console.log('✅ Background data service initialized successfully');
      
    } catch (error) {
      console.error('❌ Failed to initialize background data service:', error);
      this.notifyError(error as Error);
    }
  }

  // Preload all Nifty 200 stock names
  private async preloadAllStockNames(): Promise<void> {
    console.log('📝 Preloading all Nifty 200 stock names...');
    
    try {
      const yahooSymbols = NIFTY_200_SYMBOLS.map(getYahooSymbol).filter(s => s !== null);
      
      // Check if names are already cached
      const cachedCount = yahooSymbols.filter(symbol => 
        stockNamesService.isNameCached(symbol)
      ).length;
      
      if (cachedCount === yahooSymbols.length) {
        console.log('✅ All stock names already cached');
        return;
      }
      
      console.log(`📊 Found ${cachedCount}/${yahooSymbols.length} names in cache, fetching remaining...`);
      
      // Preload all names with better error handling
      try {
        await stockNamesService.preloadStockNames(yahooSymbols);

        // Get the names map and notify listeners
        const namesMap = await stockNamesService.getStockNames(yahooSymbols);
        this.notifyNamesUpdate(namesMap);

        console.log(`✅ Preloaded ${namesMap.size} stock names`);
      } catch (preloadError) {
        console.warn('⚠️ Some stocks failed during preloading, but continuing with available data:', preloadError);

        // Still try to get whatever names we have
        try {
          const namesMap = await stockNamesService.getStockNames(yahooSymbols);
          this.notifyNamesUpdate(namesMap);
          console.log(`✅ Loaded ${namesMap.size} stock names (with some failures)`);
        } catch (fallbackError) {
          console.error('❌ Failed to load any stock names:', fallbackError);
          // Create a basic names map with fallback names
          const fallbackNamesMap = new Map<string, string>();
          yahooSymbols.forEach(symbol => {
            fallbackNamesMap.set(symbol, symbol.replace('.NS', ''));
          });
          this.notifyNamesUpdate(fallbackNamesMap);
          console.log(`✅ Using fallback names for ${fallbackNamesMap.size} stocks`);
        }
      }
      
    } catch (error) {
      console.error('❌ Failed to preload stock names:', error);
      throw error;
    }
  }

  // Start automatic price updates
  private startPriceUpdates(): void {
    if (this.priceUpdateInterval) {
      clearInterval(this.priceUpdateInterval);
    }
    
    console.log(`🔄 Starting automatic price updates every ${this.PRICE_UPDATE_INTERVAL / 1000}s`);
    
    // Initial update
    this.updatePriceData();
    
    // Set up recurring updates
    this.priceUpdateInterval = setInterval(() => {
      this.updatePriceData();
    }, this.PRICE_UPDATE_INTERVAL);
  }

  // Update price data in background
  private async updatePriceData(): Promise<void> {
    if (this.isUpdating) {
      console.log('⏳ Price update already in progress, skipping...');
      return;
    }
    
    this.isUpdating = true;
    
    try {
      console.log('💰 Updating price data in background...');
      
      const yahooSymbols = NIFTY_200_SYMBOLS.map(getYahooSymbol).filter(s => s !== null);
      const allPriceData: any[] = [];
      
      // Process in batches to avoid overwhelming the API
      for (let i = 0; i < yahooSymbols.length; i += this.BATCH_SIZE) {
        const batch = yahooSymbols.slice(i, i + this.BATCH_SIZE);
        
        try {
          const { yahooFinanceService } = await import('./yahoo-finance');
          const batchData = await yahooFinanceService.getMultipleQuotesWithCachedNames(batch);
          allPriceData.push(...batchData);
          
          console.log(`📊 Updated batch ${Math.floor(i / this.BATCH_SIZE) + 1}/${Math.ceil(yahooSymbols.length / this.BATCH_SIZE)}: ${batchData.length} stocks`);
          
          // Add delay between batches
          if (i + this.BATCH_SIZE < yahooSymbols.length) {
            await new Promise(resolve => setTimeout(resolve, this.BATCH_DELAY));
          }
          
        } catch (batchError) {
          console.warn(`⚠️ Failed to update batch starting at index ${i}:`, batchError);
        }
      }
      
      // Notify all listeners of the price update
      this.notifyPriceUpdate(allPriceData);
      
      console.log(`✅ Background price update completed: ${allPriceData.length} stocks updated`);
      
    } catch (error) {
      console.error('❌ Background price update failed:', error);
      this.notifyError(error as Error);
    } finally {
      this.isUpdating = false;
    }
  }

  // Start automatic name refresh (before cache expiry)
  private startNameRefresh(): void {
    if (this.nameRefreshInterval) {
      clearInterval(this.nameRefreshInterval);
    }
    
    console.log(`🔄 Starting automatic name refresh every ${this.NAME_REFRESH_INTERVAL / (60 * 60 * 1000)}h`);
    
    this.nameRefreshInterval = setInterval(async () => {
      console.log('🔄 Refreshing stock names before cache expiry...');
      try {
        await this.preloadAllStockNames();
      } catch (error) {
        console.error('❌ Failed to refresh stock names:', error);
        this.notifyError(error as Error);
      }
    }, this.NAME_REFRESH_INTERVAL);
  }

  // Add listener for background updates
  addListener(listener: BackgroundUpdateListener): void {
    this.listeners.push(listener);
  }

  // Remove listener
  removeListener(listener: BackgroundUpdateListener): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  // Notify listeners of price updates
  private notifyPriceUpdate(data: any[]): void {
    this.listeners.forEach(listener => {
      try {
        listener.onPriceUpdate(data);
      } catch (error) {
        console.error('❌ Error in price update listener:', error);
      }
    });
  }

  // Notify listeners of name updates
  private notifyNamesUpdate(namesMap: Map<string, string>): void {
    this.listeners.forEach(listener => {
      try {
        listener.onNamesUpdate(namesMap);
      } catch (error) {
        console.error('❌ Error in names update listener:', error);
      }
    });
  }

  // Notify listeners of errors
  private notifyError(error: Error): void {
    this.listeners.forEach(listener => {
      try {
        listener.onError(error);
      } catch (listenerError) {
        console.error('❌ Error in error listener:', listenerError);
      }
    });
  }

  // Get current status
  getStatus(): {
    initialized: boolean;
    updating: boolean;
    listenersCount: number;
    cacheStats: any;
  } {
    return {
      initialized: this.isInitialized,
      updating: this.isUpdating,
      listenersCount: this.listeners.length,
      cacheStats: stockNamesService.getNamesCacheStats()
    };
  }

  // Force immediate update
  async forceUpdate(): Promise<void> {
    console.log('🔄 Forcing immediate data update...');
    await this.preloadAllStockNames();
    await this.updatePriceData();
  }

  // Stop all background processes
  stop(): void {
    console.log('🛑 Stopping background data service...');
    
    if (this.priceUpdateInterval) {
      clearInterval(this.priceUpdateInterval);
      this.priceUpdateInterval = null;
    }
    
    if (this.nameRefreshInterval) {
      clearInterval(this.nameRefreshInterval);
      this.nameRefreshInterval = null;
    }
    
    this.listeners = [];
    this.isInitialized = false;
    this.isUpdating = false;
    
    console.log('✅ Background data service stopped');
  }

  // Check if stock names are ready
  areNamesReady(): boolean {
    const yahooSymbols = NIFTY_200_SYMBOLS.map(getYahooSymbol).filter(s => s !== null);
    const cachedCount = yahooSymbols.filter(symbol => 
      stockNamesService.isNameCached(symbol)
    ).length;
    
    return cachedCount === yahooSymbols.length;
  }

  // Real-time update methods
  addRealTimeListener(listener: RealTimeUpdateListener): void {
    this.realTimeListeners.push(listener);

    if (!this.realTimeActive) {
      this.startRealTimeUpdates();
    }
  }

  removeRealTimeListener(listener: RealTimeUpdateListener): void {
    const index = this.realTimeListeners.indexOf(listener);
    if (index > -1) {
      this.realTimeListeners.splice(index, 1);
    }

    if (this.realTimeListeners.length === 0) {
      this.stopRealTimeUpdates();
    }
  }

  private startRealTimeUpdates(): void {
    if (this.realTimeActive) return;

    console.log('🔄 Starting real-time price updates...');
    this.realTimeActive = true;

    // Get all Nifty 200 symbols
    const allSymbols = NIFTY_200_SYMBOLS.map(symbol => getYahooSymbol(symbol));

    // Start Yahoo Finance real-time updates
    yahooFinanceService.startRealTimeUpdates(allSymbols, (quotes) => {
      // Notify all real-time listeners
      this.realTimeListeners.forEach(listener => {
        try {
          listener.onRealTimeUpdate(quotes);
        } catch (error) {
          console.error('❌ Error in real-time listener:', error);
          listener.onError(error as Error);
        }
      });
    });
  }

  private stopRealTimeUpdates(): void {
    if (!this.realTimeActive) return;

    console.log('⏹️ Stopping real-time price updates...');
    this.realTimeActive = false;

    // Stop Yahoo Finance real-time updates
    yahooFinanceService.stopRealTimeUpdates(() => {});
  }

  // Force immediate update for all real-time listeners
  async forceRealTimeUpdate(): Promise<void> {
    if (!this.realTimeActive || this.realTimeListeners.length === 0) return;

    try {
      console.log('🔄 Forcing immediate real-time update...');

      const allSymbols = NIFTY_200_SYMBOLS.map(symbol => getYahooSymbol(symbol));
      const quotes = await yahooFinanceService.getMultipleQuotesWithCachedNames(allSymbols);

      this.realTimeListeners.forEach(listener => {
        try {
          listener.onRealTimeUpdate(quotes);
        } catch (error) {
          console.error('❌ Error in forced real-time update listener:', error);
          listener.onError(error as Error);
        }
      });

      console.log(`✅ Forced real-time update completed: ${quotes.length} quotes`);

    } catch (error) {
      console.error('❌ Failed to force real-time update:', error);
      this.realTimeListeners.forEach(listener => {
        listener.onError(error as Error);
      });
    }
  }
}

// Create singleton instance
export const backgroundDataService = new BackgroundDataService();

// Note: Auto-initialization is handled by BackgroundDataProvider
// This prevents circular dependencies and ensures proper React lifecycle

// Export types
export type { BackgroundUpdateListener };
